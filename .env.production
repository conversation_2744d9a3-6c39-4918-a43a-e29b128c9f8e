# WePrint AI Production Environment Configuration
# IMPORTANT: Update these values for production deployment

# Database Configuration
DB_PASSWORD=your_secure_database_password_here
DB_HOST=database
DB_PORT=5432
DB_NAME=weprint_ai_prod
DB_USER=postgres

# JWT Configuration
JWT_SECRET=your_super_secure_jwt_secret_key_minimum_32_characters_long

# Multicaixa Payment Configuration
MULTICAIXA_API_KEY=your_multicaixa_api_key_here
MULTICAIXA_ENTITY_ID=your_multicaixa_entity_id_here
MULTICAIXA_ENVIRONMENT=production

# Email Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_email_app_password_here

# Redis Configuration
REDIS_PASSWORD=your_redis_password_here

# SSL Configuration
SSL_CERT_PATH=/etc/nginx/ssl/weprint.ai.crt
SSL_KEY_PATH=/etc/nginx/ssl/weprint.ai.key

# Monitoring Configuration
MONITORING_ENABLED=true
ALERT_EMAIL=<EMAIL>
PERFORMANCE_THRESHOLD_MS=1000

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=weprint-ai-backups

# Application Configuration
NODE_ENV=production
PORT=8000
CORS_ORIGIN=https://weprint.ai

# Feature Flags
ENABLE_ANALYTICS=true
ENABLE_MONITORING=true
ENABLE_RATE_LIMITING=true
ENABLE_CACHING=true

# Security Configuration
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
SESSION_SECRET=your_session_secret_here

# File Upload Configuration
MAX_FILE_SIZE=52428800
UPLOAD_PATH=/app/uploads
ALLOWED_FILE_TYPES=.pdf,.doc,.docx,.txt,.jpg,.jpeg,.png

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=/app/logs/weprint-ai.log
ERROR_LOG_FILE=/app/logs/error.log
