/**
 * Database connection and configuration module
 * Handles PostgreSQL connection using pg library
 */

import { Pool, PoolClient, QueryResult } from 'pg';
import config from '../config';

// Database connection pool
let pool: Pool | null = null;

/**
 * Initialize database connection pool
 */
export const initializeDatabase = async (): Promise<void> => {
  try {
    pool = new Pool({
      host: config.database.host,
      port: config.database.port,
      database: config.database.database,
      user: config.database.username,
      password: config.database.password,
      max: 20, // Maximum number of clients in the pool
      idleTimeoutMillis: 30000, // Close idle clients after 30 seconds
      connectionTimeoutMillis: 2000, // Return an error after 2 seconds if connection could not be established
    });

    // Test the connection
    const client = await pool.connect();
    await client.query('SELECT NOW()');
    client.release();

    console.log('✅ Database connected successfully');
  } catch (error) {
    console.error('❌ Database connection failed:', error);
    throw error;
  }
};

/**
 * Get database connection pool
 */
export const getPool = (): Pool => {
  if (!pool) {
    throw new Error('Database not initialized. Call initializeDatabase() first.');
  }
  return pool;
};

/**
 * Execute a query with parameters
 */
export const query = async (text: string, params?: any[]): Promise<QueryResult> => {
  const client = await getPool().connect();
  try {
    const result = await client.query(text, params);
    return result;
  } finally {
    client.release();
  }
};

/**
 * Execute a query and return the first row
 */
export const queryOne = async (text: string, params?: any[]): Promise<any> => {
  const result = await query(text, params);
  return result.rows[0] || null;
};

/**
 * Execute multiple queries in a transaction
 */
export const transaction = async (callback: (client: PoolClient) => Promise<any>): Promise<any> => {
  const client = await getPool().connect();
  try {
    await client.query('BEGIN');
    const result = await callback(client);
    await client.query('COMMIT');
    return result;
  } catch (error) {
    await client.query('ROLLBACK');
    throw error;
  } finally {
    client.release();
  }
};

/**
 * Close database connection pool
 */
export const closeDatabase = async (): Promise<void> => {
  if (pool) {
    await pool.end();
    pool = null;
    console.log('✅ Database connection closed');
  }
};

/**
 * Check database health
 */
export const checkDatabaseHealth = async (): Promise<{ status: string; timestamp: Date; version?: string }> => {
  try {
    const result = await query('SELECT version(), NOW() as timestamp');
    return {
      status: 'healthy',
      timestamp: result.rows[0].timestamp,
      version: result.rows[0].version
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      timestamp: new Date()
    };
  }
};

// Export database utilities
export default {
  initialize: initializeDatabase,
  getPool,
  query,
  queryOne,
  transaction,
  close: closeDatabase,
  health: checkDatabaseHealth
};
