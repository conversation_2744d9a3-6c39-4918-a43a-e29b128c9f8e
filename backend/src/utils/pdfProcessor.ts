/**
 * PDF processing utilities
 * Extract metadata, count pages, and analyze PDF files
 */

import fs from 'fs';
import pdfParse from 'pdf-parse';
import path from 'path';

// ============================================================================
// Types and Interfaces
// ============================================================================

export interface PDFMetadata {
  pages: number;
  title?: string;
  author?: string;
  subject?: string;
  creator?: string;
  producer?: string;
  creationDate?: Date;
  modificationDate?: Date;
  keywords?: string;
  version?: string;
  encrypted?: boolean;
  fileSize: number;
  textContent?: string;
  hasImages?: boolean;
  hasText?: boolean;
  printable?: boolean;
  copyable?: boolean;
}

export interface PDFProcessingResult {
  success: boolean;
  metadata?: PDFMetadata;
  error?: string;
  warnings?: string[];
}

// ============================================================================
// PDF Processing Functions
// ============================================================================

/**
 * Extract comprehensive metadata from PDF
 */
export const extractPDFMetadata = async (filePath: string): Promise<PDFProcessingResult> => {
  try {
    // Check if file exists
    if (!fs.existsSync(filePath)) {
      return {
        success: false,
        error: 'Arquivo PDF não encontrado'
      };
    }

    // Read PDF file
    const dataBuffer = await fs.promises.readFile(filePath);
    
    // Parse PDF
    const pdfData = await pdfParse(dataBuffer, {
      // Options for parsing
      max: 0, // Parse all pages
      version: 'v1.10.100' // Use specific version
    });

    // Get file stats
    const stats = await fs.promises.stat(filePath);

    // Extract metadata
    const metadata: PDFMetadata = {
      pages: pdfData.numpages,
      fileSize: stats.size,
      ...(pdfData.text && { textContent: pdfData.text.substring(0, 1000) }), // First 1000 chars
      hasText: Boolean(pdfData.text && pdfData.text.trim().length > 0),
      hasImages: false, // Will be determined by content analysis
      printable: true, // Default assumption
      copyable: true, // Default assumption
    };

    // Extract PDF info if available
    if (pdfData.info) {
      const info = pdfData.info;
      
      metadata.title = info.Title || undefined;
      metadata.author = info.Author || undefined;
      metadata.subject = info.Subject || undefined;
      metadata.creator = info.Creator || undefined;
      metadata.producer = info.Producer || undefined;
      metadata.keywords = info.Keywords || undefined;
      
      // Parse dates
      if (info.CreationDate) {
        try {
          metadata.creationDate = new Date(info.CreationDate);
        } catch {
          // Invalid date format
        }
      }
      
      if (info.ModDate) {
        try {
          metadata.modificationDate = new Date(info.ModDate);
        } catch {
          // Invalid date format
        }
      }
    }

    // Analyze content for images
    metadata.hasImages = await analyzeForImages(dataBuffer);

    // Check for encryption/restrictions
    const restrictions = await checkPDFRestrictions(dataBuffer);
    metadata.encrypted = restrictions.encrypted;
    metadata.printable = restrictions.printable;
    metadata.copyable = restrictions.copyable;

    // Determine PDF version
    const version = await getPDFVersion(dataBuffer);
    if (version) {
      metadata.version = version;
    }

    const warnings: string[] = [];

    // Add warnings based on analysis
    if (metadata.encrypted) {
      warnings.push('PDF está criptografado');
    }

    if (!metadata.printable) {
      warnings.push('PDF tem restrições de impressão');
    }

    if (!metadata.hasText && !metadata.hasImages) {
      warnings.push('PDF parece estar vazio');
    }

    if (metadata.pages > 100) {
      warnings.push('PDF tem muitas páginas (>100), pode demorar para processar');
    }

    return {
      success: true,
      metadata,
      ...(warnings.length > 0 && { warnings })
    };

  } catch (error) {
    console.error('Error processing PDF:', error);
    
    let errorMessage = 'Erro ao processar PDF';
    
    if (error instanceof Error) {
      if (error.message.includes('Invalid PDF')) {
        errorMessage = 'Arquivo PDF inválido ou corrompido';
      } else if (error.message.includes('encrypted')) {
        errorMessage = 'PDF criptografado não suportado';
      } else if (error.message.includes('password')) {
        errorMessage = 'PDF protegido por senha';
      }
    }

    return {
      success: false,
      error: errorMessage
    };
  }
};

/**
 * Quick page count extraction (faster for large files)
 */
export const getPageCount = async (filePath: string): Promise<number> => {
  try {
    const dataBuffer = await fs.promises.readFile(filePath);
    const pdfData = await pdfParse(dataBuffer, {
      max: 0,
      pagerender: () => '', // Don't render pages, just count
    });
    
    return pdfData.numpages;
  } catch (error) {
    console.error('Error counting PDF pages:', error);
    return 0;
  }
};

/**
 * Analyze PDF for images
 */
const analyzeForImages = async (dataBuffer: Buffer): Promise<boolean> => {
  try {
    // Simple heuristic: look for image-related keywords in PDF structure
    const pdfString = dataBuffer.toString('binary');
    
    // Look for common image markers
    const imageMarkers = ['/Image', '/DCTDecode', '/JPXDecode', '/JBIG2Decode', '/CCITTFaxDecode'];
    
    return imageMarkers.some(marker => pdfString.includes(marker));
  } catch {
    return false;
  }
};

/**
 * Check PDF restrictions and encryption
 */
const checkPDFRestrictions = async (dataBuffer: Buffer): Promise<{
  encrypted: boolean;
  printable: boolean;
  copyable: boolean;
}> => {
  try {
    const pdfString = dataBuffer.toString('binary');
    
    // Look for encryption markers
    const encrypted = pdfString.includes('/Encrypt') || pdfString.includes('/Filter');
    
    // If not encrypted, assume full permissions
    if (!encrypted) {
      return {
        encrypted: false,
        printable: true,
        copyable: true
      };
    }
    
    // For encrypted PDFs, we'd need more sophisticated analysis
    // For now, assume restrictions if encrypted
    return {
      encrypted: true,
      printable: false, // Conservative assumption
      copyable: false   // Conservative assumption
    };
    
  } catch {
    return {
      encrypted: false,
      printable: true,
      copyable: true
    };
  }
};

/**
 * Get PDF version
 */
const getPDFVersion = async (dataBuffer: Buffer): Promise<string | undefined> => {
  try {
    // PDF version is typically in the first line: %PDF-1.4
    const firstLine = dataBuffer.subarray(0, 20).toString('ascii');
    const versionMatch = firstLine.match(/%PDF-(\d+\.\d+)/);
    
    return versionMatch ? versionMatch[1] : undefined;
  } catch {
    return undefined;
  }
};

/**
 * Validate PDF file integrity
 */
export const validatePDF = async (filePath: string): Promise<{
  isValid: boolean;
  error?: string;
  warnings?: string[];
}> => {
  try {
    const result = await extractPDFMetadata(filePath);
    
    if (!result.success) {
      return {
        isValid: false,
        ...(result.error && { error: result.error })
      };
    }
    
    const warnings: string[] = [];
    
    // Check for common issues
    if (result.metadata!.pages === 0) {
      return {
        isValid: false,
        error: 'PDF não contém páginas'
      };
    }
    
    if (result.metadata!.encrypted) {
      warnings.push('PDF está criptografado');
    }
    
    if (!result.metadata!.hasText && !result.metadata!.hasImages) {
      warnings.push('PDF parece estar vazio');
    }
    
    return {
      isValid: true,
      ...(warnings.length > 0 && { warnings })
    };
    
  } catch (error) {
    return {
      isValid: false,
      error: 'Erro ao validar PDF'
    };
  }
};

/**
 * Calculate estimated print cost based on PDF metadata
 */
export const calculatePrintEstimate = (metadata: PDFMetadata, pricePerPage: number = 0.50): {
  pages: number;
  estimatedCost: number;
  hasColor: boolean;
  complexity: 'low' | 'medium' | 'high';
} => {
  const pages = metadata.pages;
  let complexity: 'low' | 'medium' | 'high' = 'low';
  
  // Determine complexity based on content
  if (metadata.hasImages && metadata.hasText) {
    complexity = 'high';
  } else if (metadata.hasImages || (metadata.textContent && metadata.textContent.length > 500)) {
    complexity = 'medium';
  }
  
  // Adjust price based on complexity
  let adjustedPrice = pricePerPage;
  if (complexity === 'medium') {
    adjustedPrice *= 1.2;
  } else if (complexity === 'high') {
    adjustedPrice *= 1.5;
  }
  
  return {
    pages,
    estimatedCost: Math.round(pages * adjustedPrice * 100) / 100,
    hasColor: metadata.hasImages || false, // Simplified assumption
    complexity
  };
};

export default {
  extractPDFMetadata,
  getPageCount,
  validatePDF,
  calculatePrintEstimate
};
