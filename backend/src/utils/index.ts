// ============================================================================
// WePrint AI Backend - Utilities
// ============================================================================

import { v4 as uuidv4 } from 'uuid';
import path from 'path';
import fs from 'fs/promises';
import { APP_CONSTANTS } from '../config';
import { PrintFormat, PaperType, FinishType } from '../types';

// ============================================================================
// File Utilities
// ============================================================================

export const generateUniqueFilename = (originalName: string): string => {
  const ext = path.extname(originalName);
  const name = path.basename(originalName, ext);
  const timestamp = Date.now();
  const uuid = uuidv4().slice(0, 8);
  
  return `${name}-${timestamp}-${uuid}${ext}`;
};

export const getFileExtension = (filename: string): string => {
  return path.extname(filename).toLowerCase().slice(1);
};

export const isValidFileType = (mimetype: string): boolean => {
  return Object.keys(APP_CONSTANTS.SUPPORTED_MIME_TYPES).includes(mimetype);
};

export const getMimeTypeExtension = (mimetype: string): string | null => {
  return APP_CONSTANTS.SUPPORTED_MIME_TYPES[mimetype as keyof typeof APP_CONSTANTS.SUPPORTED_MIME_TYPES] || null;
};

export const ensureDirectoryExists = async (dirPath: string): Promise<void> => {
  try {
    await fs.access(dirPath);
  } catch {
    await fs.mkdir(dirPath, { recursive: true });
  }
};

export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// ============================================================================
// Validation Utilities
// ============================================================================

export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

export const isValidPhone = (phone: string): boolean => {
  // Angola phone number format: +244 9XX XXX XXX or 9XX XXX XXX
  const phoneRegex = /^(\+244\s?)?9\d{2}\s?\d{3}\s?\d{3}$/;
  return phoneRegex.test(phone.replace(/\s/g, ''));
};

export const sanitizeString = (str: string): string => {
  return str.trim().replace(/[<>]/g, '');
};

// ============================================================================
// Pricing Utilities
// ============================================================================

export const calculatePrintPrice = (
  pages: number,
  copies: number,
  format: PrintFormat,
  paperType: PaperType,
  finish: FinishType
): number => {
  const basePrice = APP_CONSTANTS.PRICING.BASE_PRICE_PER_PAGE;
  const formatMultiplier = APP_CONSTANTS.PRICING.FORMAT_MULTIPLIERS[format] || 1.0;
  const paperMultiplier = APP_CONSTANTS.PRICING.PAPER_MULTIPLIERS[paperType] || 1.0;
  const finishMultiplier = APP_CONSTANTS.PRICING.FINISH_MULTIPLIERS[finish] || 1.0;
  
  const pricePerPage = basePrice * formatMultiplier * paperMultiplier * finishMultiplier;
  const totalPrice = pricePerPage * pages * copies;
  
  return Math.round(totalPrice); // Return price in cents
};

export const formatPrice = (priceInCents: number, currency: string = 'AOA'): string => {
  const price = priceInCents / 100;
  return `${price.toFixed(2)} ${currency}`;
};

// ============================================================================
// Date Utilities
// ============================================================================

export const formatDate = (date: Date): string => {
  return date.toISOString().split('T')[0];
};

export const formatDateTime = (date: Date): string => {
  return date.toISOString().replace('T', ' ').slice(0, 19);
};

export const addDays = (date: Date, days: number): Date => {
  const result = new Date(date);
  result.setDate(result.getDate() + days);
  return result;
};

export const isDateInRange = (date: Date, startDate: Date, endDate: Date): boolean => {
  return date >= startDate && date <= endDate;
};

// ============================================================================
// String Utilities
// ============================================================================

export const generateOrderNumber = (): string => {
  const timestamp = Date.now().toString().slice(-6);
  const random = Math.random().toString(36).substring(2, 6).toUpperCase();
  return `WP${timestamp}${random}`;
};

export const slugify = (text: string): string => {
  return text
    .toLowerCase()
    .replace(/[^\w\s-]/g, '')
    .replace(/[\s_-]+/g, '-')
    .replace(/^-+|-+$/g, '');
};

export const truncateString = (str: string, length: number): string => {
  if (str.length <= length) return str;
  return str.slice(0, length) + '...';
};

// ============================================================================
// Pagination Utilities
// ============================================================================

export interface PaginationParams {
  page: number;
  limit: number;
}

export interface PaginationResult {
  offset: number;
  limit: number;
  page: number;
  totalPages: number;
  total: number;
}

export const calculatePagination = (
  page: number = 1,
  limit: number = APP_CONSTANTS.DEFAULT_PAGE_SIZE,
  total: number
): PaginationResult => {
  const normalizedPage = Math.max(1, page);
  const normalizedLimit = Math.min(Math.max(1, limit), APP_CONSTANTS.MAX_PAGE_SIZE);
  const offset = (normalizedPage - 1) * normalizedLimit;
  const totalPages = Math.ceil(total / normalizedLimit);

  return {
    offset,
    limit: normalizedLimit,
    page: normalizedPage,
    totalPages,
    total
  };
};

// ============================================================================
// Error Utilities
// ============================================================================

export const createError = (message: string, statusCode: number = 400): Error => {
  const error = new Error(message) as any;
  error.statusCode = statusCode;
  return error;
};

// ============================================================================
// Environment Utilities
// ============================================================================

export const getEnvVar = (key: string, defaultValue?: string): string => {
  const value = process.env[key];
  if (!value && !defaultValue) {
    throw new Error(`Environment variable ${key} is required`);
  }
  return value || defaultValue!;
};

export const getEnvVarAsNumber = (key: string, defaultValue?: number): number => {
  const value = process.env[key];
  if (!value && defaultValue === undefined) {
    throw new Error(`Environment variable ${key} is required`);
  }
  return value ? parseInt(value, 10) : defaultValue!;
};

export const getEnvVarAsBoolean = (key: string, defaultValue?: boolean): boolean => {
  const value = process.env[key];
  if (!value && defaultValue === undefined) {
    throw new Error(`Environment variable ${key} is required`);
  }
  return value ? value.toLowerCase() === 'true' : defaultValue!;
};
