// ============================================================================
// WePrint AI Backend - Main Application Entry Point
// ============================================================================

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import path from 'path';
import { createServer } from 'http';

import config from './config';
import database from './database';
import {
  errorHandler,
  notFoundHandler,
  requestLogger,
  responseHelpers,
  corsOptions,
  securityHeaders
} from './middleware';

// ============================================================================
// Create Express Application
// ============================================================================

const app = express();

// ============================================================================
// Security Middleware
// ============================================================================

app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
  crossOriginEmbedderPolicy: false
}));

app.use(securityHeaders);

// ============================================================================
// CORS Configuration
// ============================================================================

app.use(cors(corsOptions));

// ============================================================================
// Body Parsing Middleware
// ============================================================================

app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// ============================================================================
// Logging Middleware
// ============================================================================

if (!config.log.silent) {
  app.use(morgan(config.log.format));
}
app.use(requestLogger);

// ============================================================================
// Response Helpers
// ============================================================================

app.use(responseHelpers);

// ============================================================================
// Static Files
// ============================================================================

app.use('/uploads', express.static(path.join(__dirname, '../uploads')));

// ============================================================================
// Health Check Endpoint
// ============================================================================

app.get('/health', async (req, res) => {
  try {
    const dbHealth = await database.health();

    res.success({
      status: 'OK',
      timestamp: new Date().toISOString(),
      version: config.app.VERSION,
      environment: config.server.nodeEnv,
      uptime: process.uptime(),
      database: dbHealth
    }, 'Service is healthy');
  } catch (error) {
    res.error('Service unhealthy', 503, {
      status: 'ERROR',
      timestamp: new Date().toISOString(),
      version: config.app.VERSION,
      environment: config.server.nodeEnv,
      uptime: process.uptime(),
      database: { status: 'unhealthy' }
    });
  }
});

// ============================================================================
// API Information Endpoint
// ============================================================================

app.get('/api', (req, res) => {
  res.success({
    name: config.app.NAME,
    description: config.app.DESCRIPTION,
    version: config.app.VERSION,
    environment: config.server.nodeEnv,
    endpoints: {
      health: '/health',
      api: '/api',
      files: '/api/files',
      orders: '/api/orders',
      notifications: '/api/notifications',
      admin: '/api/admin'
    },
    documentation: 'https://github.com/weprint-ai/backend/blob/main/README.md'
  }, 'WePrint AI Backend API');
});

// ============================================================================
// API Routes
// ============================================================================

// Import routes
import filesRoutes from './routes/files';
import ordersRoutes, { setOrderNotificationService } from './routes/orders';
import notificationsRoutes, { setNotificationService } from './routes/notifications';

// Import services
import { EmailService } from './services/EmailService';
import { NotificationService } from './services/NotificationService';
import { WebSocketService } from './services/WebSocketService';

// Mount routes
app.use('/api/files', filesRoutes);
app.use('/api/orders', ordersRoutes);
app.use('/api/notifications', notificationsRoutes);

// ============================================================================
// Error Handling
// ============================================================================

app.use(notFoundHandler);
app.use(errorHandler);

// ============================================================================
// Server Startup
// ============================================================================

// Global notification service instance
let notificationService: NotificationService;
let webSocketService: WebSocketService;

const startServer = async (): Promise<void> => {
  try {
    console.log('🔄 Initializing WePrint AI Backend...');

    // Initialize database connection
    try {
      await database.initialize();
    } catch (error) {
      console.log('⚠️  Database connection failed, continuing without database...');
      console.log('   Make sure PostgreSQL is running for full functionality');
    }

    // Create HTTP server for WebSocket integration
    const httpServer = createServer(app);

    // Initialize notification services
    try {
      console.log('🔔 Initializing notification services...');

      // Initialize email service
      const emailConfig = {
        host: process.env.SMTP_HOST || 'localhost',
        port: parseInt(process.env.SMTP_PORT || '587'),
        secure: process.env.SMTP_SECURE === 'true',
        auth: {
          user: process.env.SMTP_USER || '',
          pass: process.env.SMTP_PASS || ''
        },
        from: {
          name: process.env.SMTP_FROM_NAME || 'WePrint AI',
          email: process.env.SMTP_FROM_EMAIL || '<EMAIL>'
        }
      };

      const emailService = new EmailService(emailConfig);

      // Test email configuration
      const emailTest = await emailService.testConnection();
      if (emailTest.success) {
        console.log('✅ Email service configured successfully');
      } else {
        console.log('⚠️  Email service configuration issue:', emailTest.error);
      }

      // Initialize notification service
      notificationService = new NotificationService(emailService);

      // Initialize WebSocket service
      webSocketService = new WebSocketService(httpServer);

      // Set notification service for routes
      setNotificationService(notificationService);
      setOrderNotificationService(notificationService);

      console.log('✅ Notification services initialized');
    } catch (error) {
      console.log('⚠️  Notification services failed to initialize:', error);
    }

    const server = httpServer.listen(config.server.port, config.server.host, () => {
      console.log(`🚀 WePrint AI Backend started successfully!`);
      console.log(`📍 Server running on: http://${config.server.host}:${config.server.port}`);
      console.log(`🌍 Environment: ${config.server.nodeEnv}`);
      console.log(`📚 API Documentation: http://${config.server.host}:${config.server.port}/api`);
      console.log(`❤️  Health Check: http://${config.server.host}:${config.server.port}/health`);
      console.log(`🔔 Notifications: http://${config.server.host}:${config.server.port}/api/notifications`);
      console.log(`⚡ WebSocket: ws://${config.server.host}:${config.server.port}`);
    });

    // Graceful shutdown
    const gracefulShutdown = (signal: string) => {
      console.log(`\n🛑 Received ${signal}. Starting graceful shutdown...`);

      server.close(async () => {
        console.log('✅ HTTP server closed.');

        // Close WebSocket service
        if (webSocketService) {
          webSocketService.close();
          console.log('✅ WebSocket service closed.');
        }

        // Close database connection
        await database.close();

        process.exit(0);
      });

      // Force close after 10 seconds
      setTimeout(() => {
        console.log('⚠️  Forcing shutdown after timeout');
        process.exit(1);
      }, 10000);
    };

    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));

  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
};

// ============================================================================
// Start the application
// ============================================================================

if (require.main === module) {
  startServer().catch((error) => {
    console.error('❌ Unhandled error during startup:', error);
    process.exit(1);
  });
}

export default app;
