import { EventEmitter } from 'events';
import {
  NotificationType,
  NotificationChannel,
  NotificationStatus,
  CreateNotificationData,
  NotificationEvent,
  OrderStatus
} from '../types';
import { NotificationModel } from '../models/Notification';
import { EmailService } from './EmailService';

export class NotificationService extends EventEmitter {
  private emailService: EmailService;
  private isProcessing: boolean = false;

  constructor(emailService: EmailService) {
    super();
    this.emailService = emailService;
    this.setupEventListeners();
    this.startProcessingQueue();
  }

  /**
   * Setup event listeners for automatic notifications
   */
  private setupEventListeners(): void {
    // Order status change events
    this.on('order:created', this.handleOrderCreated.bind(this));
    this.on('order:confirmed', this.handleOrderConfirmed.bind(this));
    this.on('order:processing', this.handleOrderProcessing.bind(this));
    this.on('order:ready', this.handleOrderReady.bind(this));
    this.on('order:completed', this.handleOrderCompleted.bind(this));
    this.on('order:cancelled', this.handleOrderCancelled.bind(this));

    // File upload events
    this.on('file:uploaded', this.handleFileUploaded.bind(this));

    // System events
    this.on('system:alert', this.handleSystemAlert.bind(this));
  }

  /**
   * Create and queue a notification
   */
  async createNotification(data: CreateNotificationData): Promise<void> {
    try {
      await NotificationModel.create(data);
      console.log(`Notification queued: ${data.type} to ${data.recipientEmail}`);
    } catch (error) {
      console.error('Failed to create notification:', error);
    }
  }

  /**
   * Send notification immediately (bypass queue)
   */
  async sendImmediateNotification(data: CreateNotificationData): Promise<boolean> {
    try {
      const notification = await NotificationModel.create(data);
      
      if (data.channel === NotificationChannel.EMAIL) {
        const emailData: any = {
          to: data.recipientEmail,
          subject: data.subject,
          html: data.content
        };

        if (data.recipientName) {
          emailData.toName = data.recipientName;
        }

        const result = await this.emailService.sendEmail(emailData);

        if (result.success) {
          await NotificationModel.update(notification.id, {
            status: NotificationStatus.SENT,
            sentAt: new Date()
          });
          return true;
        } else {
          await NotificationModel.update(notification.id, {
            status: NotificationStatus.FAILED,
            errorMessage: result.error
          });
          return false;
        }
      }

      return true;
    } catch (error) {
      console.error('Failed to send immediate notification:', error);
      return false;
    }
  }

  /**
   * Process notification queue
   */
  private async startProcessingQueue(): Promise<void> {
    // Process every 30 seconds
    setInterval(async () => {
      if (!this.isProcessing) {
        await this.processQueue();
      }
    }, 30000);

    // Process retries every 5 minutes
    setInterval(async () => {
      if (!this.isProcessing) {
        await this.processRetries();
      }
    }, 300000);
  }

  /**
   * Process pending notifications
   */
  private async processQueue(): Promise<void> {
    if (this.isProcessing) return;

    this.isProcessing = true;
    
    try {
      const pendingNotifications = await NotificationModel.getPendingNotifications(10);
      
      for (const notification of pendingNotifications) {
        await this.processNotification(notification);
      }
    } catch (error) {
      console.error('Error processing notification queue:', error);
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * Process failed notifications for retry
   */
  private async processRetries(): Promise<void> {
    try {
      const retryableNotifications = await NotificationModel.getRetryableNotifications(5);
      
      for (const notification of retryableNotifications) {
        await this.processNotification(notification);
      }
    } catch (error) {
      console.error('Error processing notification retries:', error);
    }
  }

  /**
   * Process individual notification
   */
  private async processNotification(notification: any): Promise<void> {
    try {
      let success = false;
      let errorMessage = '';

      if (notification.channel === NotificationChannel.EMAIL) {
        const result = await this.emailService.sendEmail({
          to: notification.recipientEmail,
          toName: notification.recipientName,
          subject: notification.subject,
          html: notification.content
        });

        success = result.success;
        errorMessage = result.error || '';
      }

      if (success) {
        await NotificationModel.update(notification.id, {
          status: NotificationStatus.SENT,
          sentAt: new Date()
        });
        console.log(`Notification sent: ${notification.id}`);
      } else {
        await NotificationModel.update(notification.id, {
          status: NotificationStatus.FAILED,
          errorMessage,
          retryCount: notification.retryCount + 1
        });
        console.log(`Notification failed: ${notification.id} - ${errorMessage}`);
      }
    } catch (error) {
      console.error(`Error processing notification ${notification.id}:`, error);
      
      await NotificationModel.update(notification.id, {
        status: NotificationStatus.FAILED,
        errorMessage: error instanceof Error ? error.message : 'Unknown error',
        retryCount: notification.retryCount + 1
      });
    }
  }

  /**
   * Event Handlers
   */
  private async handleOrderCreated(orderData: any): Promise<void> {
    await this.createNotification({
      type: NotificationType.ORDER_CREATED,
      channel: NotificationChannel.EMAIL,
      recipientEmail: orderData.customerEmail,
      recipientName: orderData.customerName,
      subject: `Pedido ${orderData.orderNumber} - Recebido com Sucesso`,
      content: await this.generateOrderEmailContent(NotificationType.ORDER_CREATED, orderData),
      templateData: orderData,
      orderId: orderData.id
    });
  }

  private async handleOrderConfirmed(orderData: any): Promise<void> {
    await this.createNotification({
      type: NotificationType.ORDER_CONFIRMED,
      channel: NotificationChannel.EMAIL,
      recipientEmail: orderData.customerEmail,
      recipientName: orderData.customerName,
      subject: `Pedido ${orderData.orderNumber} - Confirmado`,
      content: await this.generateOrderEmailContent(NotificationType.ORDER_CONFIRMED, orderData),
      templateData: orderData,
      orderId: orderData.id
    });
  }

  private async handleOrderProcessing(orderData: any): Promise<void> {
    await this.createNotification({
      type: NotificationType.ORDER_PROCESSING,
      channel: NotificationChannel.EMAIL,
      recipientEmail: orderData.customerEmail,
      recipientName: orderData.customerName,
      subject: `Pedido ${orderData.orderNumber} - Em Produção`,
      content: await this.generateOrderEmailContent(NotificationType.ORDER_PROCESSING, orderData),
      templateData: orderData,
      orderId: orderData.id
    });
  }

  private async handleOrderReady(orderData: any): Promise<void> {
    await this.createNotification({
      type: NotificationType.ORDER_READY,
      channel: NotificationChannel.EMAIL,
      recipientEmail: orderData.customerEmail,
      recipientName: orderData.customerName,
      subject: `Pedido ${orderData.orderNumber} - Pronto para Levantamento`,
      content: await this.generateOrderEmailContent(NotificationType.ORDER_READY, orderData),
      templateData: orderData,
      orderId: orderData.id
    });
  }

  private async handleOrderCompleted(orderData: any): Promise<void> {
    await this.createNotification({
      type: NotificationType.ORDER_COMPLETED,
      channel: NotificationChannel.EMAIL,
      recipientEmail: orderData.customerEmail,
      recipientName: orderData.customerName,
      subject: `Pedido ${orderData.orderNumber} - Concluído`,
      content: await this.generateOrderEmailContent(NotificationType.ORDER_COMPLETED, orderData),
      templateData: orderData,
      orderId: orderData.id
    });
  }

  private async handleOrderCancelled(orderData: any): Promise<void> {
    await this.createNotification({
      type: NotificationType.ORDER_CANCELLED,
      channel: NotificationChannel.EMAIL,
      recipientEmail: orderData.customerEmail,
      recipientName: orderData.customerName,
      subject: `Pedido ${orderData.orderNumber} - Cancelado`,
      content: await this.generateOrderEmailContent(NotificationType.ORDER_CANCELLED, orderData),
      templateData: orderData,
      orderId: orderData.id
    });
  }

  private async handleFileUploaded(fileData: any): Promise<void> {
    // Only send notification if file is associated with an order
    if (fileData.orderId && fileData.customerEmail) {
      await this.createNotification({
        type: NotificationType.FILE_UPLOADED,
        channel: NotificationChannel.EMAIL,
        recipientEmail: fileData.customerEmail,
        recipientName: fileData.customerName,
        subject: `Arquivo Carregado - ${fileData.originalName}`,
        content: `O arquivo "${fileData.originalName}" foi carregado com sucesso.`,
        templateData: fileData,
        fileId: fileData.id,
        orderId: fileData.orderId
      });
    }
  }

  private async handleSystemAlert(alertData: any): Promise<void> {
    // Send system alerts to administrators
    const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';
    
    await this.createNotification({
      type: NotificationType.SYSTEM_ALERT,
      channel: NotificationChannel.EMAIL,
      recipientEmail: adminEmail,
      recipientName: 'Administrador',
      subject: `Alerta do Sistema - ${alertData.title}`,
      content: alertData.message,
      templateData: alertData
    });
  }

  /**
   * Generate email content using EmailService templates
   */
  private async generateOrderEmailContent(type: NotificationType, orderData: any): Promise<string> {
    // Use EmailService to generate the HTML content
    const template = (this.emailService as any).getOrderTemplate(type, orderData);
    return template.html;
  }

  /**
   * Emit order status change event
   */
  emitOrderStatusChange(orderData: any, newStatus: OrderStatus): void {
    const eventMap: Record<OrderStatus, string> = {
      [OrderStatus.PENDING]: 'order:created',
      [OrderStatus.CONFIRMED]: 'order:confirmed',
      [OrderStatus.PROCESSING]: 'order:processing',
      [OrderStatus.READY]: 'order:ready',
      [OrderStatus.COMPLETED]: 'order:completed',
      [OrderStatus.CANCELLED]: 'order:cancelled'
    };

    const eventName = eventMap[newStatus];
    if (eventName) {
      this.emit(eventName, orderData);
    }
  }

  /**
   * Emit file upload event
   */
  emitFileUploaded(fileData: any): void {
    this.emit('file:uploaded', fileData);
  }

  /**
   * Emit system alert
   */
  emitSystemAlert(title: string, message: string, severity: 'info' | 'warning' | 'error' = 'info'): void {
    this.emit('system:alert', { title, message, severity, timestamp: new Date() });
  }

  /**
   * Get notification statistics
   */
  async getStatistics(days: number = 30) {
    return await NotificationModel.getStatistics(days);
  }

  /**
   * Cleanup old notifications
   */
  async cleanupOldNotifications(daysOld: number = 90): Promise<number> {
    return await NotificationModel.deleteOldNotifications(daysOld);
  }
}
