import { Server as SocketIOServer } from 'socket.io';
import { Server as HttpServer } from 'http';
import { NotificationType, NotificationChannel } from '../types';

export interface WebSocketNotification {
  id: string;
  type: NotificationType;
  title: string;
  message: string;
  data?: any;
  timestamp: Date;
  read: boolean;
}

export class WebSocketService {
  private io: SocketIOServer;
  private connectedClients: Map<string, any> = new Map();

  constructor(httpServer: HttpServer) {
    this.io = new SocketIOServer(httpServer, {
      cors: {
        origin: process.env.FRONTEND_URL || "http://localhost:3000",
        methods: ["GET", "POST"],
        credentials: true
      },
      transports: ['websocket', 'polling']
    });

    this.setupEventHandlers();
  }

  /**
   * Setup WebSocket event handlers
   */
  private setupEventHandlers(): void {
    this.io.on('connection', (socket) => {
      console.log(`Client connected: ${socket.id}`);

      // Handle client authentication/identification
      socket.on('authenticate', (data) => {
        this.handleAuthentication(socket, data);
      });

      // Handle client joining specific rooms (e.g., order updates)
      socket.on('join-order', (orderId) => {
        socket.join(`order-${orderId}`);
        console.log(`Client ${socket.id} joined order room: ${orderId}`);
      });

      socket.on('leave-order', (orderId) => {
        socket.leave(`order-${orderId}`);
        console.log(`Client ${socket.id} left order room: ${orderId}`);
      });

      // Handle notification acknowledgment
      socket.on('notification-read', (notificationId) => {
        this.handleNotificationRead(socket, notificationId);
      });

      // Handle disconnection
      socket.on('disconnect', () => {
        this.handleDisconnection(socket);
      });

      // Send welcome message
      socket.emit('connected', {
        message: 'Conectado ao sistema de notificações WePrint AI',
        timestamp: new Date()
      });
    });
  }

  /**
   * Handle client authentication
   */
  private handleAuthentication(socket: any, data: any): void {
    const { userId, userEmail, userType } = data;

    if (userId && userEmail) {
      // Store client information
      this.connectedClients.set(socket.id, {
        userId,
        userEmail,
        userType: userType || 'customer',
        connectedAt: new Date()
      });

      // Join user-specific room
      socket.join(`user-${userId}`);
      
      // Join role-specific room
      if (userType === 'admin') {
        socket.join('admins');
      } else {
        socket.join('customers');
      }

      socket.emit('authenticated', {
        success: true,
        message: 'Autenticado com sucesso'
      });

      console.log(`Client authenticated: ${userEmail} (${userType})`);
    } else {
      socket.emit('authentication-error', {
        message: 'Dados de autenticação inválidos'
      });
    }
  }

  /**
   * Handle notification read acknowledgment
   */
  private handleNotificationRead(socket: any, notificationId: string): void {
    const client = this.connectedClients.get(socket.id);
    if (client) {
      console.log(`Notification ${notificationId} marked as read by ${client.userEmail}`);
      
      // Emit to other clients of the same user
      socket.to(`user-${client.userId}`).emit('notification-read', {
        notificationId,
        readBy: client.userEmail,
        timestamp: new Date()
      });
    }
  }

  /**
   * Handle client disconnection
   */
  private handleDisconnection(socket: any): void {
    const client = this.connectedClients.get(socket.id);
    if (client) {
      console.log(`Client disconnected: ${client.userEmail}`);
      this.connectedClients.delete(socket.id);
    }
  }

  /**
   * Send notification to specific user
   */
  sendToUser(userId: string, notification: WebSocketNotification): void {
    this.io.to(`user-${userId}`).emit('notification', notification);
    console.log(`Notification sent to user ${userId}: ${notification.type}`);
  }

  /**
   * Send notification to specific email
   */
  sendToEmail(email: string, notification: WebSocketNotification): void {
    // Find connected clients with this email
    for (const [socketId, client] of this.connectedClients.entries()) {
      if (client.userEmail === email) {
        this.io.to(socketId).emit('notification', notification);
        console.log(`Notification sent to ${email}: ${notification.type}`);
      }
    }
  }

  /**
   * Send order update to all clients following the order
   */
  sendOrderUpdate(orderId: number, notification: WebSocketNotification): void {
    this.io.to(`order-${orderId}`).emit('order-update', {
      orderId,
      ...notification
    });
    console.log(`Order update sent for order ${orderId}: ${notification.type}`);
  }

  /**
   * Send notification to all administrators
   */
  sendToAdmins(notification: WebSocketNotification): void {
    this.io.to('admins').emit('admin-notification', notification);
    console.log(`Admin notification sent: ${notification.type}`);
  }

  /**
   * Send notification to all customers
   */
  sendToCustomers(notification: WebSocketNotification): void {
    this.io.to('customers').emit('customer-notification', notification);
    console.log(`Customer notification sent: ${notification.type}`);
  }

  /**
   * Broadcast notification to all connected clients
   */
  broadcast(notification: WebSocketNotification): void {
    this.io.emit('broadcast', notification);
    console.log(`Broadcast notification sent: ${notification.type}`);
  }

  /**
   * Send system status update
   */
  sendSystemStatus(status: {
    online: boolean;
    message: string;
    timestamp: Date;
  }): void {
    this.io.emit('system-status', status);
    console.log(`System status update: ${status.message}`);
  }

  /**
   * Get connected clients count
   */
  getConnectedClientsCount(): number {
    return this.connectedClients.size;
  }

  /**
   * Get connected clients info
   */
  getConnectedClients(): Array<{
    socketId: string;
    userId: string;
    userEmail: string;
    userType: string;
    connectedAt: Date;
  }> {
    return Array.from(this.connectedClients.entries()).map(([socketId, client]) => ({
      socketId,
      ...client
    }));
  }

  /**
   * Create notification object for WebSocket
   */
  createNotification(
    type: NotificationType,
    title: string,
    message: string,
    data?: any
  ): WebSocketNotification {
    return {
      id: `ws-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      type,
      title,
      message,
      data,
      timestamp: new Date(),
      read: false
    };
  }

  /**
   * Send order status change notification
   */
  sendOrderStatusChange(orderData: any, newStatus: string): void {
    const statusMessages: Record<string, { title: string; message: string }> = {
      'confirmed': {
        title: 'Pedido Confirmado',
        message: `O seu pedido ${orderData.orderNumber} foi confirmado e está na fila de produção.`
      },
      'processing': {
        title: 'Pedido em Produção',
        message: `O seu pedido ${orderData.orderNumber} está sendo processado.`
      },
      'ready': {
        title: 'Pedido Pronto',
        message: `O seu pedido ${orderData.orderNumber} está pronto para levantamento!`
      },
      'completed': {
        title: 'Pedido Concluído',
        message: `O seu pedido ${orderData.orderNumber} foi concluído com sucesso.`
      },
      'cancelled': {
        title: 'Pedido Cancelado',
        message: `O seu pedido ${orderData.orderNumber} foi cancelado.`
      }
    };

    const statusInfo = statusMessages[newStatus] || {
      title: 'Atualização do Pedido',
      message: `O status do seu pedido ${orderData.orderNumber} foi atualizado.`
    };

    const notification = this.createNotification(
      NotificationType.ORDER_CONFIRMED, // Will be mapped based on status
      statusInfo.title,
      statusInfo.message,
      {
        orderId: orderData.id,
        orderNumber: orderData.orderNumber,
        status: newStatus,
        customerEmail: orderData.customerEmail
      }
    );

    // Send to specific customer
    if (orderData.customerEmail) {
      this.sendToEmail(orderData.customerEmail, notification);
    }

    // Send to order room
    this.sendOrderUpdate(orderData.id, notification);

    // Send to admins for monitoring
    this.sendToAdmins({
      ...notification,
      title: `Pedido ${orderData.orderNumber} - ${statusInfo.title}`,
      message: `Cliente: ${orderData.customerEmail} - ${statusInfo.message}`
    });
  }

  /**
   * Send file upload notification
   */
  sendFileUploadNotification(fileData: any): void {
    const notification = this.createNotification(
      NotificationType.FILE_UPLOADED,
      'Arquivo Carregado',
      `O arquivo "${fileData.originalName}" foi carregado com sucesso.`,
      {
        fileId: fileData.id,
        fileName: fileData.originalName,
        orderId: fileData.orderId
      }
    );

    if (fileData.customerEmail) {
      this.sendToEmail(fileData.customerEmail, notification);
    }

    if (fileData.orderId) {
      this.sendOrderUpdate(fileData.orderId, notification);
    }
  }

  /**
   * Send system alert
   */
  sendSystemAlert(title: string, message: string, severity: 'info' | 'warning' | 'error' = 'info'): void {
    const notification = this.createNotification(
      NotificationType.SYSTEM_ALERT,
      title,
      message,
      { severity }
    );

    // Send to all admins
    this.sendToAdmins(notification);

    // If critical, broadcast to all users
    if (severity === 'error') {
      this.broadcast(notification);
    }
  }

  /**
   * Close WebSocket server
   */
  close(): void {
    this.io.close();
    console.log('WebSocket server closed');
  }
}
