/**
 * Multicaixa Express Service - Handles all Multicaixa payment operations
 * Integrates with GPO (Gateway de Pagamento Online) for Angolan payments
 */

import axios from 'axios';
import crypto from 'crypto';

export interface CreateMulticaixaPaymentData {
  amount: number; // Amount in Kwanza (AOA)
  currency?: string;
  orderId: number;
  customerEmail: string;
  customerPhone?: string;
  description?: string;
  metadata?: Record<string, string>;
}

export interface MulticaixaPaymentResponse {
  transactionId: string;
  paymentUrl: string;
  qrCode?: string;
  status: MulticaixaPaymentStatus;
  amount: number;
  currency: string;
  expiresAt: Date;
}

export interface MulticaixaWebhookPayload {
  transactionId: string;
  status: MulticaixaPaymentStatus;
  amount: number;
  currency: string;
  orderId: string;
  customerEmail: string;
  paidAt?: string;
  failureReason?: string;
  signature: string;
}

export enum MulticaixaPaymentStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
  EXPIRED = 'expired'
}

export class MulticaixaService {
  private static readonly baseUrl = process.env.MULTICAIXA_GPO_ENDPOINT || 'https://gpo.multicaixa.ao/api/v1';
  private static readonly merchantId = process.env.MULTICAIXA_MERCHANT_ID!;
  private static readonly secretKey = process.env.MULTICAIXA_SECRET_KEY!;
  private static readonly webhookSecret = process.env.MULTICAIXA_WEBHOOK_SECRET!;
  private static readonly returnUrl = process.env.MULTICAIXA_RETURN_URL || 'http://localhost:3001/payment/return';
  private static readonly cancelUrl = process.env.MULTICAIXA_CANCEL_URL || 'http://localhost:3001/payment/cancel';

  /**
   * Create a payment with Multicaixa Express
   */
  static async createPayment(data: CreateMulticaixaPaymentData): Promise<MulticaixaPaymentResponse> {
    try {
      const paymentData = {
        merchant_id: this.merchantId,
        amount: data.amount,
        currency: data.currency || 'AOA',
        order_id: data.orderId.toString(),
        customer_email: data.customerEmail,
        customer_phone: data.customerPhone,
        description: data.description || `WePrint AI - Pedido #${data.orderId}`,
        return_url: this.returnUrl,
        cancel_url: this.cancelUrl,
        webhook_url: `${process.env.API_BASE_URL}/api/payments/multicaixa/webhook`,
        metadata: data.metadata || {},
        timestamp: new Date().toISOString()
      };

      // Generate signature for request authentication
      const signature = this.generateSignature(paymentData);
      
      const response = await axios.post(
        `${this.baseUrl}/payments`,
        {
          ...paymentData,
          signature
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.secretKey}`
          },
          timeout: 30000
        }
      );

      const responseData = response.data as any;

      return {
        transactionId: responseData.transaction_id,
        paymentUrl: responseData.payment_url,
        qrCode: responseData.qr_code,
        status: responseData.status as MulticaixaPaymentStatus,
        amount: responseData.amount,
        currency: responseData.currency,
        expiresAt: new Date(responseData.expires_at)
      };

    } catch (error) {
      console.error('Multicaixa createPayment error:', error);
      if (error && typeof error === 'object' && 'response' in error) {
        const axiosError = error as any;
        const message = axiosError.response?.data?.message || axiosError.message;
        throw new Error(`Failed to create Multicaixa payment: ${message}`);
      }
      throw new Error(`Failed to create Multicaixa payment: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get payment status from Multicaixa
   */
  static async getPaymentStatus(transactionId: string): Promise<MulticaixaPaymentStatus> {
    try {
      const response = await axios.get(
        `${this.baseUrl}/payments/${transactionId}`,
        {
          headers: {
            'Authorization': `Bearer ${this.secretKey}`
          },
          timeout: 15000
        }
      );

      return (response.data as any).status as MulticaixaPaymentStatus;

    } catch (error) {
      console.error('Multicaixa getPaymentStatus error:', error);
      throw new Error(`Failed to get payment status: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Cancel a payment
   */
  static async cancelPayment(transactionId: string): Promise<boolean> {
    try {
      const response = await axios.post(
        `${this.baseUrl}/payments/${transactionId}/cancel`,
        {},
        {
          headers: {
            'Authorization': `Bearer ${this.secretKey}`
          },
          timeout: 15000
        }
      );

      return (response.data as any).success === true;

    } catch (error) {
      console.error('Multicaixa cancelPayment error:', error);
      throw new Error(`Failed to cancel payment: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Verify webhook signature
   */
  static verifyWebhookSignature(payload: MulticaixaWebhookPayload): boolean {
    try {
      const { signature, ...dataToSign } = payload;
      const expectedSignature = this.generateSignature(dataToSign);
      
      return crypto.timingSafeEqual(
        Buffer.from(signature, 'hex'),
        Buffer.from(expectedSignature, 'hex')
      );
    } catch (error) {
      console.error('Multicaixa webhook signature verification error:', error);
      return false;
    }
  }

  /**
   * Process webhook payload
   */
  static processWebhook(payload: MulticaixaWebhookPayload): {
    transactionId: string;
    status: MulticaixaPaymentStatus;
    orderId: number;
    amount: number;
    paidAt?: Date;
    failureReason?: string;
  } {
    const result: {
      transactionId: string;
      status: MulticaixaPaymentStatus;
      orderId: number;
      amount: number;
      paidAt?: Date;
      failureReason?: string;
    } = {
      transactionId: payload.transactionId,
      status: payload.status,
      orderId: parseInt(payload.orderId),
      amount: payload.amount
    };

    if (payload.paidAt) {
      result.paidAt = new Date(payload.paidAt);
    }

    if (payload.failureReason) {
      result.failureReason = payload.failureReason;
    }

    return result;
  }

  /**
   * Generate signature for request authentication
   */
  private static generateSignature(data: any): string {
    // Sort keys alphabetically and create query string
    const sortedKeys = Object.keys(data).sort();
    const queryString = sortedKeys
      .map(key => `${key}=${encodeURIComponent(data[key])}`)
      .join('&');
    
    // Create HMAC signature
    const hmac = crypto.createHmac('sha256', this.webhookSecret);
    hmac.update(queryString);
    return hmac.digest('hex');
  }

  /**
   * Convert amount from Euros to Kwanza (AOA)
   * Using approximate exchange rate - should be updated with real-time rates
   */
  static eurosToKwanza(euros: number): number {
    const exchangeRate = parseFloat(process.env.EUR_TO_AOA_RATE || '850'); // Approximate rate
    return Math.round(euros * exchangeRate);
  }

  /**
   * Convert amount from Kwanza to Euros
   */
  static kwanzaToEuros(kwanza: number): number {
    const exchangeRate = parseFloat(process.env.EUR_TO_AOA_RATE || '850');
    return Math.round((kwanza / exchangeRate) * 100) / 100;
  }

  /**
   * Format amount for display
   */
  static formatAmount(amount: number, currency: string = 'AOA'): string {
    return new Intl.NumberFormat('pt-AO', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2
    }).format(amount);
  }

  /**
   * Validate configuration
   */
  static validateConfig(): boolean {
    const requiredEnvVars = [
      'MULTICAIXA_MERCHANT_ID',
      'MULTICAIXA_SECRET_KEY',
      'MULTICAIXA_WEBHOOK_SECRET'
    ];

    for (const envVar of requiredEnvVars) {
      if (!process.env[envVar]) {
        console.error(`Missing required environment variable: ${envVar}`);
        return false;
      }
    }

    return true;
  }
}

export default MulticaixaService;
