import database from '../database';
import { DashboardStats, OrderStatus, NotificationType, NotificationStatus } from '../types';
import os from 'os';
import fs from 'fs';
import path from 'path';

export class DashboardService {
  /**
   * Get comprehensive dashboard statistics
   */
  static async getDashboardStats(): Promise<DashboardStats> {
    try {
      const [
        overview,
        orderStats,
        fileStats,
        notificationStats,
        systemHealth
      ] = await Promise.all([
        this.getOverviewStats(),
        this.getOrderStats(),
        this.getFileStats(),
        this.getNotificationStats(),
        this.getSystemHealth()
      ]);

      return {
        overview,
        orderStats,
        fileStats,
        notificationStats,
        systemHealth
      };
    } catch (error) {
      console.error('Error getting dashboard stats:', error);
      throw new Error('Erro ao obter estatísticas do dashboard');
    }
  }

  /**
   * Get overview statistics
   */
  private static async getOverviewStats(): Promise<DashboardStats['overview']> {
    const queries = [
      'SELECT COUNT(*) as total FROM orders WHERE deleted_at IS NULL',
      'SELECT COUNT(*) as total FROM files WHERE deleted_at IS NULL',
      'SELECT COUNT(*) as total FROM notifications WHERE deleted_at IS NULL',
      'SELECT COALESCE(SUM(total_price), 0) as total FROM orders WHERE deleted_at IS NULL AND status IN (\'completed\', \'paid\')',
      'SELECT COUNT(*) as total FROM orders WHERE deleted_at IS NULL AND status IN (\'pending\', \'confirmed\', \'processing\')'
    ];

    const results = await Promise.all(
      queries.map(query => database.query(query))
    );

    return {
      totalOrders: parseInt(results[0].rows[0].total),
      totalFiles: parseInt(results[1].rows[0].total),
      totalNotifications: parseInt(results[2].rows[0].total),
      totalRevenue: parseFloat(results[3].rows[0].total),
      activeOrders: parseInt(results[4].rows[0].total)
    };
  }

  /**
   * Get order statistics
   */
  private static async getOrderStats(): Promise<DashboardStats['orderStats']> {
    // Orders by status
    const statusQuery = `
      SELECT status, COUNT(*) as count
      FROM orders 
      WHERE deleted_at IS NULL
      GROUP BY status
    `;
    const statusResult = await database.query(statusQuery);
    const byStatus = statusResult.rows.reduce((acc, row) => {
      acc[row.status as OrderStatus] = parseInt(row.count);
      return acc;
    }, {} as Record<OrderStatus, number>);

    // Orders by period
    const periodQueries = {
      today: `
        SELECT COUNT(*) as count, COALESCE(SUM(total_price), 0) as revenue
        FROM orders 
        WHERE deleted_at IS NULL 
        AND DATE(created_at) = CURRENT_DATE
      `,
      thisWeek: `
        SELECT COUNT(*) as count, COALESCE(SUM(total_price), 0) as revenue
        FROM orders 
        WHERE deleted_at IS NULL 
        AND created_at >= DATE_TRUNC('week', CURRENT_DATE)
      `,
      thisMonth: `
        SELECT COUNT(*) as count, COALESCE(SUM(total_price), 0) as revenue
        FROM orders 
        WHERE deleted_at IS NULL 
        AND created_at >= DATE_TRUNC('month', CURRENT_DATE)
      `,
      lastMonth: `
        SELECT COUNT(*) as count, COALESCE(SUM(total_price), 0) as revenue
        FROM orders 
        WHERE deleted_at IS NULL 
        AND created_at >= DATE_TRUNC('month', CURRENT_DATE - INTERVAL '1 month')
        AND created_at < DATE_TRUNC('month', CURRENT_DATE)
      `
    };

    const periodResults = await Promise.all(
      Object.values(periodQueries).map(query => database.query(query))
    );

    const periods = Object.keys(periodQueries);
    const byPeriod = periods.reduce((acc, period, index) => {
      acc[period] = parseInt(periodResults[index].rows[0].count);
      return acc;
    }, {} as any);

    const revenueByPeriod = periods.reduce((acc, period, index) => {
      acc[period] = parseFloat(periodResults[index].rows[0].revenue);
      return acc;
    }, {} as any);

    return {
      byStatus,
      byPeriod,
      revenueByPeriod
    };
  }

  /**
   * Get file statistics
   */
  private static async getFileStats(): Promise<DashboardStats['fileStats']> {
    // Total file size
    const sizeQuery = `
      SELECT COALESCE(SUM(size), 0) as total_size
      FROM files 
      WHERE deleted_at IS NULL
    `;
    const sizeResult = await database.query(sizeQuery);
    const totalSize = parseInt(sizeResult.rows[0].total_size);

    // Files by type
    const typeQuery = `
      SELECT mimetype, COUNT(*) as count
      FROM files 
      WHERE deleted_at IS NULL
      GROUP BY mimetype
      ORDER BY count DESC
    `;
    const typeResult = await database.query(typeQuery);
    const byType = typeResult.rows.reduce((acc, row) => {
      acc[row.mimetype] = parseInt(row.count);
      return acc;
    }, {} as Record<string, number>);

    // Uploads by period
    const uploadQueries = {
      today: `
        SELECT COUNT(*) as count
        FROM files 
        WHERE deleted_at IS NULL 
        AND DATE(created_at) = CURRENT_DATE
      `,
      thisWeek: `
        SELECT COUNT(*) as count
        FROM files 
        WHERE deleted_at IS NULL 
        AND created_at >= DATE_TRUNC('week', CURRENT_DATE)
      `,
      thisMonth: `
        SELECT COUNT(*) as count
        FROM files 
        WHERE deleted_at IS NULL 
        AND created_at >= DATE_TRUNC('month', CURRENT_DATE)
      `
    };

    const uploadResults = await Promise.all(
      Object.values(uploadQueries).map(query => database.query(query))
    );

    const uploadPeriods = Object.keys(uploadQueries);
    const uploadsByPeriod = uploadPeriods.reduce((acc, period, index) => {
      acc[period] = parseInt(uploadResults[index].rows[0].count);
      return acc;
    }, {} as any);

    return {
      totalSize,
      byType,
      uploadsByPeriod
    };
  }

  /**
   * Get notification statistics
   */
  private static async getNotificationStats(): Promise<DashboardStats['notificationStats']> {
    // Notifications by type
    const typeQuery = `
      SELECT type, COUNT(*) as count
      FROM notifications 
      WHERE deleted_at IS NULL
      GROUP BY type
    `;
    const typeResult = await database.query(typeQuery);
    const byType = typeResult.rows.reduce((acc, row) => {
      acc[row.type as NotificationType] = parseInt(row.count);
      return acc;
    }, {} as Record<NotificationType, number>);

    // Notifications by status
    const statusQuery = `
      SELECT status, COUNT(*) as count
      FROM notifications 
      WHERE deleted_at IS NULL
      GROUP BY status
    `;
    const statusResult = await database.query(statusQuery);
    const byStatus = statusResult.rows.reduce((acc, row) => {
      acc[row.status as NotificationStatus] = parseInt(row.count);
      return acc;
    }, {} as Record<NotificationStatus, number>);

    // Delivery and read rates
    const ratesQuery = `
      SELECT 
        COUNT(*) as total,
        COUNT(CASE WHEN status IN ('sent', 'delivered', 'read') THEN 1 END) as delivered,
        COUNT(CASE WHEN status = 'read' THEN 1 END) as read
      FROM notifications 
      WHERE deleted_at IS NULL
    `;
    const ratesResult = await database.query(ratesQuery);
    const rates = ratesResult.rows[0];
    
    const total = parseInt(rates.total);
    const delivered = parseInt(rates.delivered);
    const read = parseInt(rates.read);
    
    const deliveryRate = total > 0 ? (delivered / total) * 100 : 0;
    const readRate = delivered > 0 ? (read / delivered) * 100 : 0;

    return {
      byType,
      byStatus,
      deliveryRate: Math.round(deliveryRate * 100) / 100,
      readRate: Math.round(readRate * 100) / 100
    };
  }

  /**
   * Get system health information
   */
  private static async getSystemHealth(): Promise<DashboardStats['systemHealth']> {
    // System uptime
    const uptime = process.uptime();

    // Memory usage
    const memUsage = process.memoryUsage();
    const totalMem = os.totalmem();
    const memoryUsage = Math.round((memUsage.rss / totalMem) * 100 * 100) / 100;

    // Disk usage (approximate)
    let diskUsage = 0;
    try {
      const uploadDir = path.join(process.cwd(), 'uploads');
      if (fs.existsSync(uploadDir)) {
        const stats = fs.statSync(uploadDir);
        diskUsage = stats.size;
      }
    } catch (error) {
      console.warn('Could not get disk usage:', error);
    }

    // Database connections (if available)
    let databaseConnections = 0;
    try {
      const connQuery = `
        SELECT count(*) as connections
        FROM pg_stat_activity 
        WHERE datname = current_database()
      `;
      const connResult = await database.query(connQuery);
      databaseConnections = parseInt(connResult.rows[0].connections);
    } catch (error) {
      console.warn('Could not get database connections:', error);
    }

    // Last backup (placeholder - would need actual backup system)
    let lastBackup: Date | undefined;
    try {
      // This would be implemented based on your backup strategy
      // For now, we'll leave it undefined
    } catch (error) {
      console.warn('Could not get last backup info:', error);
    }

    return {
      uptime: Math.round(uptime),
      memoryUsage,
      diskUsage,
      databaseConnections,
      lastBackup
    };
  }

  /**
   * Get recent activity summary
   */
  static async getRecentActivity(limit: number = 10): Promise<Array<{
    type: string;
    description: string;
    timestamp: Date;
    details?: any;
  }>> {
    const activities: Array<{
      type: string;
      description: string;
      timestamp: Date;
      details?: any;
    }> = [];

    try {
      // Recent orders
      const ordersQuery = `
        SELECT id, customer_email, status, total_price, created_at
        FROM orders 
        WHERE deleted_at IS NULL
        ORDER BY created_at DESC
        LIMIT $1
      `;
      const ordersResult = await database.query(ordersQuery, [Math.ceil(limit / 3)]);
      
      ordersResult.rows.forEach(order => {
        activities.push({
          type: 'order',
          description: `Novo pedido de ${order.customer_email}`,
          timestamp: new Date(order.created_at),
          details: {
            orderId: order.id,
            status: order.status,
            totalPrice: order.total_price
          }
        });
      });

      // Recent file uploads
      const filesQuery = `
        SELECT id, filename, original_name, size, created_at
        FROM files 
        WHERE deleted_at IS NULL
        ORDER BY created_at DESC
        LIMIT $1
      `;
      const filesResult = await database.query(filesQuery, [Math.ceil(limit / 3)]);
      
      filesResult.rows.forEach(file => {
        activities.push({
          type: 'file',
          description: `Arquivo carregado: ${file.original_name}`,
          timestamp: new Date(file.created_at),
          details: {
            fileId: file.id,
            filename: file.filename,
            size: file.size
          }
        });
      });

      // Recent notifications
      const notificationsQuery = `
        SELECT id, type, recipient_email, subject, status, created_at
        FROM notifications 
        WHERE deleted_at IS NULL
        ORDER BY created_at DESC
        LIMIT $1
      `;
      const notificationsResult = await database.query(notificationsQuery, [Math.ceil(limit / 3)]);
      
      notificationsResult.rows.forEach(notification => {
        activities.push({
          type: 'notification',
          description: `Notificação enviada para ${notification.recipient_email}`,
          timestamp: new Date(notification.created_at),
          details: {
            notificationId: notification.id,
            type: notification.type,
            status: notification.status
          }
        });
      });

      // Sort by timestamp and limit
      return activities
        .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
        .slice(0, limit);

    } catch (error) {
      console.error('Error getting recent activity:', error);
      return [];
    }
  }
}
