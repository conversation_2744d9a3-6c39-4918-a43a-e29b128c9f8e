import nodemailer from 'nodemailer';
import { EmailConfig, NotificationType } from '../types';

export class EmailService {
  private transporter: nodemailer.Transporter;
  private config: EmailConfig;

  constructor(config: EmailConfig) {
    this.config = config;
    this.transporter = nodemailer.createTransport({
      host: config.host,
      port: config.port,
      secure: config.secure,
      auth: config.auth
    });
  }

  /**
   * Send email
   */
  async sendEmail(options: {
    to: string;
    toName?: string;
    subject: string;
    html: string;
    text?: string;
  }): Promise<{ success: boolean; messageId?: string; error?: string }> {
    try {
      const mailOptions = {
        from: `${this.config.from.name} <${this.config.from.email}>`,
        to: options.toName ? `${options.toName} <${options.to}>` : options.to,
        subject: options.subject,
        html: options.html,
        text: options.text || this.stripHtml(options.html)
      };

      const result = await this.transporter.sendMail(mailOptions);
      
      return {
        success: true,
        messageId: result.messageId
      };
    } catch (error) {
      console.error('Email sending failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Send order notification email
   */
  async sendOrderNotification(
    type: NotificationType,
    recipientEmail: string,
    recipientName: string,
    orderData: any
  ): Promise<{ success: boolean; messageId?: string; error?: string }> {
    const template = this.getOrderTemplate(type, orderData);
    
    return this.sendEmail({
      to: recipientEmail,
      toName: recipientName,
      subject: template.subject,
      html: template.html,
      text: template.text
    });
  }

  /**
   * Get order email template
   */
  private getOrderTemplate(type: NotificationType, orderData: any): {
    subject: string;
    html: string;
    text: string;
  } {
    const baseUrl = process.env.FRONTEND_URL || 'http://localhost:3000';
    const orderUrl = `${baseUrl}/orders/${orderData.orderNumber}`;

    switch (type) {
      case NotificationType.ORDER_CREATED:
        return {
          subject: `Pedido ${orderData.orderNumber} - Recebido com Sucesso`,
          html: this.getOrderCreatedHtml(orderData, orderUrl),
          text: this.getOrderCreatedText(orderData, orderUrl)
        };

      case NotificationType.ORDER_CONFIRMED:
        return {
          subject: `Pedido ${orderData.orderNumber} - Confirmado`,
          html: this.getOrderConfirmedHtml(orderData, orderUrl),
          text: this.getOrderConfirmedText(orderData, orderUrl)
        };

      case NotificationType.ORDER_PROCESSING:
        return {
          subject: `Pedido ${orderData.orderNumber} - Em Produção`,
          html: this.getOrderProcessingHtml(orderData, orderUrl),
          text: this.getOrderProcessingText(orderData, orderUrl)
        };

      case NotificationType.ORDER_READY:
        return {
          subject: `Pedido ${orderData.orderNumber} - Pronto para Levantamento`,
          html: this.getOrderReadyHtml(orderData, orderUrl),
          text: this.getOrderReadyText(orderData, orderUrl)
        };

      case NotificationType.ORDER_COMPLETED:
        return {
          subject: `Pedido ${orderData.orderNumber} - Concluído`,
          html: this.getOrderCompletedHtml(orderData, orderUrl),
          text: this.getOrderCompletedText(orderData, orderUrl)
        };

      case NotificationType.ORDER_CANCELLED:
        return {
          subject: `Pedido ${orderData.orderNumber} - Cancelado`,
          html: this.getOrderCancelledHtml(orderData, orderUrl),
          text: this.getOrderCancelledText(orderData, orderUrl)
        };

      default:
        return {
          subject: `Atualização do Pedido ${orderData.orderNumber}`,
          html: this.getGenericOrderHtml(orderData, orderUrl),
          text: this.getGenericOrderText(orderData, orderUrl)
        };
    }
  }

  /**
   * Order Created Templates
   */
  private getOrderCreatedHtml(orderData: any, orderUrl: string): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Pedido Recebido - WePrint AI</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #2563eb; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9fafb; }
          .order-details { background: white; padding: 15px; margin: 15px 0; border-radius: 5px; }
          .button { display: inline-block; background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; margin: 10px 0; }
          .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🎉 Pedido Recebido!</h1>
            <p>Obrigado por escolher a WePrint AI</p>
          </div>
          
          <div class="content">
            <p>Olá <strong>${orderData.customerName}</strong>,</p>
            
            <p>Recebemos o seu pedido com sucesso! Aqui estão os detalhes:</p>
            
            <div class="order-details">
              <h3>📋 Detalhes do Pedido</h3>
              <p><strong>Número do Pedido:</strong> ${orderData.orderNumber}</p>
              <p><strong>Formato:</strong> ${orderData.format}</p>
              <p><strong>Tipo de Papel:</strong> ${orderData.paperType}</p>
              <p><strong>Acabamento:</strong> ${orderData.finish}</p>
              <p><strong>Cópias:</strong> ${orderData.copies}</p>
              <p><strong>Páginas:</strong> ${orderData.pages || 'A determinar'}</p>
              <p><strong>Preço:</strong> ${(orderData.price / 100).toFixed(2)} AOA</p>
              <p><strong>Data Estimada:</strong> ${orderData.estimatedCompletion ? new Date(orderData.estimatedCompletion).toLocaleDateString('pt-PT') : 'A determinar'}</p>
            </div>
            
            <p>O seu pedido está agora na nossa fila de produção. Iremos notificá-lo assim que houver atualizações.</p>
            
            <a href="${orderUrl}" class="button">Ver Detalhes do Pedido</a>
            
            <p>Se tiver alguma dúvida, não hesite em contactar-nos.</p>
          </div>
          
          <div class="footer">
            <p>WePrint AI - Impressão Profissional</p>
            <p>Este é um email automático, por favor não responda.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  private getOrderCreatedText(orderData: any, orderUrl: string): string {
    return `
🎉 PEDIDO RECEBIDO - WePrint AI

Olá ${orderData.customerName},

Recebemos o seu pedido com sucesso!

DETALHES DO PEDIDO:
- Número: ${orderData.orderNumber}
- Formato: ${orderData.format}
- Tipo de Papel: ${orderData.paperType}
- Acabamento: ${orderData.finish}
- Cópias: ${orderData.copies}
- Páginas: ${orderData.pages || 'A determinar'}
- Preço: ${(orderData.price / 100).toFixed(2)} AOA
- Data Estimada: ${orderData.estimatedCompletion ? new Date(orderData.estimatedCompletion).toLocaleDateString('pt-PT') : 'A determinar'}

O seu pedido está agora na nossa fila de produção.

Ver detalhes: ${orderUrl}

WePrint AI - Impressão Profissional
    `;
  }

  /**
   * Order Ready Templates
   */
  private getOrderReadyHtml(orderData: any, orderUrl: string): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Pedido Pronto - WePrint AI</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #059669; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9fafb; }
          .order-details { background: white; padding: 15px; margin: 15px 0; border-radius: 5px; }
          .button { display: inline-block; background: #059669; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; margin: 10px 0; }
          .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
          .highlight { background: #dcfce7; padding: 10px; border-radius: 5px; margin: 10px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>✅ Pedido Pronto!</h1>
            <p>O seu pedido está pronto para levantamento</p>
          </div>
          
          <div class="content">
            <p>Olá <strong>${orderData.customerName}</strong>,</p>
            
            <div class="highlight">
              <p><strong>🎉 Boa notícia!</strong> O seu pedido <strong>${orderData.orderNumber}</strong> está pronto para levantamento!</p>
            </div>
            
            <div class="order-details">
              <h3>📋 Detalhes do Pedido</h3>
              <p><strong>Número do Pedido:</strong> ${orderData.orderNumber}</p>
              <p><strong>Formato:</strong> ${orderData.format}</p>
              <p><strong>Cópias:</strong> ${orderData.copies}</p>
              <p><strong>Preço Total:</strong> ${(orderData.price / 100).toFixed(2)} AOA</p>
            </div>
            
            <p><strong>📍 Local de Levantamento:</strong><br>
            WePrint AI<br>
            [Endereço da loja]<br>
            Horário: Segunda a Sexta, 9h às 18h</p>
            
            <p><strong>📞 Contacto:</strong> [Número de telefone]</p>
            
            <a href="${orderUrl}" class="button">Ver Detalhes do Pedido</a>
            
            <p>Por favor, traga este email ou o número do pedido quando vier levantar.</p>
          </div>
          
          <div class="footer">
            <p>WePrint AI - Impressão Profissional</p>
            <p>Este é um email automático, por favor não responda.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  private getOrderReadyText(orderData: any, orderUrl: string): string {
    return `
✅ PEDIDO PRONTO - WePrint AI

Olá ${orderData.customerName},

🎉 Boa notícia! O seu pedido ${orderData.orderNumber} está pronto para levantamento!

DETALHES DO PEDIDO:
- Número: ${orderData.orderNumber}
- Formato: ${orderData.format}
- Cópias: ${orderData.copies}
- Preço Total: ${(orderData.price / 100).toFixed(2)} AOA

📍 LOCAL DE LEVANTAMENTO:
WePrint AI
[Endereço da loja]
Horário: Segunda a Sexta, 9h às 18h

📞 Contacto: [Número de telefone]

Ver detalhes: ${orderUrl}

Por favor, traga este email ou o número do pedido quando vier levantar.

WePrint AI - Impressão Profissional
    `;
  }

  // Placeholder methods for other templates (to be implemented)
  private getOrderConfirmedHtml(orderData: any, orderUrl: string): string {
    return this.getGenericOrderHtml(orderData, orderUrl, 'Confirmado', '#2563eb');
  }

  private getOrderConfirmedText(orderData: any, orderUrl: string): string {
    return this.getGenericOrderText(orderData, orderUrl, 'CONFIRMADO');
  }

  private getOrderProcessingHtml(orderData: any, orderUrl: string): string {
    return this.getGenericOrderHtml(orderData, orderUrl, 'Em Produção', '#f59e0b');
  }

  private getOrderProcessingText(orderData: any, orderUrl: string): string {
    return this.getGenericOrderText(orderData, orderUrl, 'EM PRODUÇÃO');
  }

  private getOrderCompletedHtml(orderData: any, orderUrl: string): string {
    return this.getGenericOrderHtml(orderData, orderUrl, 'Concluído', '#059669');
  }

  private getOrderCompletedText(orderData: any, orderUrl: string): string {
    return this.getGenericOrderText(orderData, orderUrl, 'CONCLUÍDO');
  }

  private getOrderCancelledHtml(orderData: any, orderUrl: string): string {
    return this.getGenericOrderHtml(orderData, orderUrl, 'Cancelado', '#dc2626');
  }

  private getOrderCancelledText(orderData: any, orderUrl: string): string {
    return this.getGenericOrderText(orderData, orderUrl, 'CANCELADO');
  }

  private getGenericOrderHtml(orderData: any, orderUrl: string, status: string = 'Atualizado', color: string = '#2563eb'): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Pedido ${status} - WePrint AI</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: ${color}; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9fafb; }
          .button { display: inline-block; background: ${color}; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; margin: 10px 0; }
          .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Pedido ${status}</h1>
          </div>
          <div class="content">
            <p>Olá <strong>${orderData.customerName}</strong>,</p>
            <p>O seu pedido <strong>${orderData.orderNumber}</strong> foi ${status.toLowerCase()}.</p>
            <a href="${orderUrl}" class="button">Ver Detalhes</a>
          </div>
          <div class="footer">
            <p>WePrint AI - Impressão Profissional</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  private getGenericOrderText(orderData: any, orderUrl: string, status: string = 'ATUALIZADO'): string {
    return `
PEDIDO ${status} - WePrint AI

Olá ${orderData.customerName},

O seu pedido ${orderData.orderNumber} foi ${status.toLowerCase()}.

Ver detalhes: ${orderUrl}

WePrint AI - Impressão Profissional
    `;
  }

  /**
   * Strip HTML tags from text
   */
  private stripHtml(html: string): string {
    return html.replace(/<[^>]*>/g, '').replace(/\s+/g, ' ').trim();
  }

  /**
   * Test email configuration
   */
  async testConnection(): Promise<{ success: boolean; error?: string }> {
    try {
      await this.transporter.verify();
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
}
