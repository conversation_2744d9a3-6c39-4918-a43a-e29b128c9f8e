/**
 * Stripe Service - Handles all Stripe payment operations
 */

import <PERSON>e from 'stripe';

// Initialize Stripe with secret key
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-12-18.acacia',
  typescript: true,
});

export interface CreatePaymentIntentData {
  amount: number; // Amount in cents (euros)
  currency?: string;
  orderId: number;
  customerEmail: string;
  description?: string;
  metadata?: Record<string, string>;
}

export interface CreateCustomerData {
  email: string;
  name?: string;
  phone?: string;
  address?: {
    line1: string;
    line2?: string;
    city: string;
    postal_code: string;
    country: string;
  };
}

export interface RefundData {
  paymentIntentId: string;
  amount?: number; // Amount in cents, if partial refund
  reason?: 'duplicate' | 'fraudulent' | 'requested_by_customer';
  metadata?: Record<string, string>;
}

export class StripeService {
  /**
   * Create a payment intent for processing payment
   */
  static async createPaymentIntent(data: CreatePaymentIntentData): Promise<Stripe.PaymentIntent> {
    try {
      const paymentIntent = await stripe.paymentIntents.create({
        amount: Math.round(data.amount * 100), // Convert euros to cents
        currency: data.currency || process.env.STRIPE_CURRENCY || 'eur',
        description: data.description || `WePrint AI - Pedido #${data.orderId}`,
        metadata: {
          order_id: data.orderId.toString(),
          customer_email: data.customerEmail,
          ...data.metadata,
        },
        automatic_payment_methods: {
          enabled: true,
        },
        receipt_email: data.customerEmail,
      });

      return paymentIntent;
    } catch (error) {
      console.error('Stripe createPaymentIntent error:', error);
      throw new Error(`Failed to create payment intent: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Retrieve a payment intent by ID
   */
  static async getPaymentIntent(paymentIntentId: string): Promise<Stripe.PaymentIntent> {
    try {
      return await stripe.paymentIntents.retrieve(paymentIntentId);
    } catch (error) {
      console.error('Stripe getPaymentIntent error:', error);
      throw new Error(`Failed to retrieve payment intent: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Confirm a payment intent
   */
  static async confirmPaymentIntent(
    paymentIntentId: string, 
    paymentMethodId?: string
  ): Promise<Stripe.PaymentIntent> {
    try {
      const confirmData: Stripe.PaymentIntentConfirmParams = {};
      
      if (paymentMethodId) {
        confirmData.payment_method = paymentMethodId;
      }

      return await stripe.paymentIntents.confirm(paymentIntentId, confirmData);
    } catch (error) {
      console.error('Stripe confirmPaymentIntent error:', error);
      throw new Error(`Failed to confirm payment intent: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Cancel a payment intent
   */
  static async cancelPaymentIntent(paymentIntentId: string): Promise<Stripe.PaymentIntent> {
    try {
      return await stripe.paymentIntents.cancel(paymentIntentId);
    } catch (error) {
      console.error('Stripe cancelPaymentIntent error:', error);
      throw new Error(`Failed to cancel payment intent: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Create a customer in Stripe
   */
  static async createCustomer(data: CreateCustomerData): Promise<Stripe.Customer> {
    try {
      return await stripe.customers.create({
        email: data.email,
        name: data.name,
        phone: data.phone,
        address: data.address,
        metadata: {
          source: 'weprint_ai',
        },
      });
    } catch (error) {
      console.error('Stripe createCustomer error:', error);
      throw new Error(`Failed to create customer: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get customer by email
   */
  static async getCustomerByEmail(email: string): Promise<Stripe.Customer | null> {
    try {
      const customers = await stripe.customers.list({
        email: email,
        limit: 1,
      });

      return customers.data.length > 0 ? customers.data[0] : null;
    } catch (error) {
      console.error('Stripe getCustomerByEmail error:', error);
      throw new Error(`Failed to get customer: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Create a refund
   */
  static async createRefund(data: RefundData): Promise<Stripe.Refund> {
    try {
      const refundData: Stripe.RefundCreateParams = {
        payment_intent: data.paymentIntentId,
        reason: data.reason,
        metadata: data.metadata,
      };

      if (data.amount) {
        refundData.amount = Math.round(data.amount * 100); // Convert euros to cents
      }

      return await stripe.refunds.create(refundData);
    } catch (error) {
      console.error('Stripe createRefund error:', error);
      throw new Error(`Failed to create refund: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get refund by ID
   */
  static async getRefund(refundId: string): Promise<Stripe.Refund> {
    try {
      return await stripe.refunds.retrieve(refundId);
    } catch (error) {
      console.error('Stripe getRefund error:', error);
      throw new Error(`Failed to retrieve refund: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * List payment methods for a customer
   */
  static async listPaymentMethods(customerId: string): Promise<Stripe.PaymentMethod[]> {
    try {
      const paymentMethods = await stripe.paymentMethods.list({
        customer: customerId,
        type: 'card',
      });

      return paymentMethods.data;
    } catch (error) {
      console.error('Stripe listPaymentMethods error:', error);
      throw new Error(`Failed to list payment methods: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Attach payment method to customer
   */
  static async attachPaymentMethod(paymentMethodId: string, customerId: string): Promise<Stripe.PaymentMethod> {
    try {
      return await stripe.paymentMethods.attach(paymentMethodId, {
        customer: customerId,
      });
    } catch (error) {
      console.error('Stripe attachPaymentMethod error:', error);
      throw new Error(`Failed to attach payment method: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Detach payment method from customer
   */
  static async detachPaymentMethod(paymentMethodId: string): Promise<Stripe.PaymentMethod> {
    try {
      return await stripe.paymentMethods.detach(paymentMethodId);
    } catch (error) {
      console.error('Stripe detachPaymentMethod error:', error);
      throw new Error(`Failed to detach payment method: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Construct webhook event from raw body and signature
   */
  static constructWebhookEvent(payload: string | Buffer, signature: string): Stripe.Event {
    try {
      return stripe.webhooks.constructEvent(
        payload,
        signature,
        process.env.STRIPE_WEBHOOK_SECRET!
      );
    } catch (error) {
      console.error('Stripe webhook verification error:', error);
      throw new Error(`Webhook signature verification failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get Stripe instance for advanced operations
   */
  static getStripeInstance(): Stripe {
    return stripe;
  }

  /**
   * Convert amount from euros to cents
   */
  static eurosToCents(euros: number): number {
    return Math.round(euros * 100);
  }

  /**
   * Convert amount from cents to euros
   */
  static centsToEuros(cents: number): number {
    return Math.round(cents) / 100;
  }

  /**
   * Format amount for display
   */
  static formatAmount(amount: number, currency: string = 'EUR'): string {
    return new Intl.NumberFormat('pt-PT', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  }

  /**
   * Validate webhook signature
   */
  static validateWebhookSignature(payload: string | Buffer, signature: string): boolean {
    try {
      this.constructWebhookEvent(payload, signature);
      return true;
    } catch (error) {
      return false;
    }
  }
}

export default StripeService;
