// ============================================================================
// WePrint AI Backend - Type Definitions
// ============================================================================

import { Request } from 'express';

// ============================================================================
// Database Models
// ============================================================================

export interface File {
  id: number;
  filename: string;
  originalName: string;
  mimetype: string;
  size: number;
  url: string;
  uploadedAt: Date;
  deletedAt?: Date;
}

export interface Order {
  id: number;
  orderNumber: string;
  fileId: number;
  customerName?: string;
  customerEmail?: string;
  customerPhone?: string;
  format: PrintFormat;
  paperType: PaperType;
  finish: FinishType;
  copies: number;
  pages?: number;
  price: number;
  status: OrderStatus;
  notes?: string;
  hasColor?: boolean;
  complexity?: 'low' | 'medium' | 'high';
  estimatedCompletion?: Date;
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date;
  file?: File;
  statusHistory?: StatusHistory[];
}

export interface StatusHistory {
  id: number;
  orderId: number;
  status: OrderStatus;
  notes?: string;
  updatedAt: Date;
  updatedBy?: string;
}

// ============================================================================
// Enums
// ============================================================================

export enum OrderStatus {
  PENDING = 'pending',
  CONFIRMED = 'confirmed',
  PROCESSING = 'processing',
  READY = 'ready',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled'
}

export enum PrintFormat {
  A4 = 'A4',
  A3 = 'A3',
  A5 = 'A5',
  LETTER = 'letter',
  LEGAL = 'legal',
  CUSTOM = 'custom'
}

export enum PaperType {
  STANDARD = 'standard',
  PREMIUM = 'premium',
  PHOTO = 'photo',
  CARDSTOCK = 'cardstock'
}

export enum FinishType {
  NONE = 'none',
  LAMINATED = 'laminated',
  SPIRAL_BOUND = 'spiral_bound',
  STAPLED = 'stapled'
}

// ============================================================================
// API Request/Response Types
// ============================================================================

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  errors?: ValidationError[];
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface ValidationError {
  field: string;
  message: string;
}

// ============================================================================
// Request DTOs
// ============================================================================

export interface CreateOrderRequest {
  fileId: number;
  customerName?: string;
  customerEmail?: string;
  customerPhone?: string;
  format: PrintFormat;
  paperType: PaperType;
  finish: FinishType;
  copies: number;
  notes?: string;
}

export interface UpdateOrderRequest {
  status?: OrderStatus;
  notes?: string;
}

export interface FileUploadRequest extends Request {
  file?: Express.Multer.File;
}

// ============================================================================
// File Types
// ============================================================================

export interface CreateFileData {
  filename: string;
  originalName: string;
  mimetype: string;
  size: number;
  url: string;
}

export interface UpdateFileData {
  filename?: string;
  originalName?: string;
  mimetype?: string;
  size?: number;
  url?: string;
}

export interface CreateOrderData {
  fileId: number;
  customerName?: string;
  customerEmail?: string;
  customerPhone?: string;
  format: string;
  paperType: string;
  finish: string;
  copies?: number;
  pages?: number;
  price?: number;
  status?: string;
  notes?: string;
  hasColor?: boolean;
  complexity?: 'low' | 'medium' | 'high';
}

export interface UpdateOrderData {
  fileId?: number;
  customerName?: string;
  customerEmail?: string;
  customerPhone?: string;
  format?: string;
  paperType?: string;
  finish?: string;
  copies?: number;
  pages?: number;
  price?: number;
  status?: string;
  notes?: string | undefined;
  hasColor?: boolean;
  complexity?: 'low' | 'medium' | 'high';
  estimatedCompletion?: Date;
}

export interface CreateStatusHistoryData {
  orderId: number;
  status: string;
  notes?: string;
  updatedBy?: string;
}

// ============================================================================
// Configuration Types
// ============================================================================

export interface DatabaseConfig {
  host: string;
  port: number;
  database: string;
  username: string;
  password: string;
  ssl: boolean;
}

export interface ServerConfig {
  port: number;
  host: string;
  nodeEnv: string;
  corsOrigin: string;
}

export interface JwtConfig {
  secret: string;
  expiresIn: string;
}

export interface UploadConfig {
  uploadDir: string;
  maxFileSize: number;
  allowedFileTypes: string[];
}

// ============================================================================
// Utility Types
// ============================================================================

export type Omit<T, K extends keyof T> = Pick<T, Exclude<keyof T, K>>;
export type Partial<T> = { [P in keyof T]?: T[P] };

export interface QueryParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  search?: string;
  status?: OrderStatus;
  format?: PrintFormat;
  dateFrom?: string;
  dateTo?: string;
}

// ============================================================================
// Notification Types
// ============================================================================

export enum NotificationType {
  ORDER_CREATED = 'order_created',
  ORDER_CONFIRMED = 'order_confirmed',
  ORDER_PROCESSING = 'order_processing',
  ORDER_READY = 'order_ready',
  ORDER_COMPLETED = 'order_completed',
  ORDER_CANCELLED = 'order_cancelled',
  FILE_UPLOADED = 'file_uploaded',
  PAYMENT_RECEIVED = 'payment_received',
  SYSTEM_ALERT = 'system_alert'
}

export enum NotificationChannel {
  EMAIL = 'email',
  SMS = 'sms',
  PUSH = 'push',
  IN_APP = 'in_app'
}

export enum NotificationStatus {
  PENDING = 'pending',
  SENT = 'sent',
  DELIVERED = 'delivered',
  FAILED = 'failed',
  READ = 'read'
}

export interface Notification {
  id: number;
  type: NotificationType;
  channel: NotificationChannel;
  status: NotificationStatus;
  recipientEmail: string;
  recipientName?: string;
  subject: string;
  content: string;
  templateData?: Record<string, any>;
  orderId?: number;
  fileId?: number;
  sentAt?: Date;
  deliveredAt?: Date;
  readAt?: Date;
  errorMessage?: string;
  retryCount: number;
  maxRetries: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateNotificationData {
  type: NotificationType;
  channel: NotificationChannel;
  recipientEmail: string;
  recipientName?: string;
  subject: string;
  content: string;
  templateData?: Record<string, any>;
  orderId?: number;
  fileId?: number;
  maxRetries?: number;
}

export interface UpdateNotificationData {
  status?: NotificationStatus;
  sentAt?: Date;
  deliveredAt?: Date;
  readAt?: Date;
  errorMessage?: string;
  retryCount?: number;
}

export interface NotificationTemplate {
  id: string;
  name: string;
  type: NotificationType;
  channel: NotificationChannel;
  subject: string;
  htmlContent: string;
  textContent: string;
  variables: string[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface EmailConfig {
  host: string;
  port: number;
  secure: boolean;
  auth: {
    user: string;
    pass: string;
  };
  from: {
    name: string;
    email: string;
  };
}

export interface NotificationEvent {
  type: NotificationType;
  data: Record<string, any>;
  timestamp: Date;
  source: string;
}

export interface NotificationStats {
  totalSent: number;
  totalDelivered: number;
  totalFailed: number;
  totalRead: number;
  deliveryRate: number;
  readRate: number;
  byType: Record<NotificationType, number>;
  byChannel: Record<NotificationChannel, number>;
  byStatus: Record<NotificationStatus, number>;
  recentActivity: Array<{
    date: string;
    sent: number;
    delivered: number;
    failed: number;
  }>;
}
