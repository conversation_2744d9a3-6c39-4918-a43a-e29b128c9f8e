// ============================================================================
// WePrint AI Backend - Type Definitions
// ============================================================================

import { Request } from 'express';

// ============================================================================
// Database Models
// ============================================================================

export interface File {
  id: number;
  filename: string;
  originalName: string;
  mimetype: string;
  size: number;
  url: string;
  uploadedAt: Date;
  deletedAt?: Date;
}

export interface Order {
  id: number;
  orderNumber: string;
  fileId: number;
  customerName?: string;
  customerEmail?: string;
  customerPhone?: string;
  format: PrintFormat;
  paperType: PaperType;
  finish: FinishType;
  copies: number;
  pages?: number;
  price: number;
  status: OrderStatus;
  notes?: string;
  deliveryAddress?: string;
  hasColor?: boolean;
  complexity?: 'low' | 'medium' | 'high';
  estimatedCompletion?: Date;
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date;
  file?: File;
  statusHistory?: StatusHistory[];
}

export interface StatusHistory {
  id: number;
  orderId: number;
  status: OrderStatus;
  notes?: string;
  updatedAt: Date;
  updatedBy?: string;
}

// ============================================================================
// Enums
// ============================================================================

export enum OrderStatus {
  PENDING = 'pending',
  CONFIRMED = 'confirmed',
  PROCESSING = 'processing',
  READY = 'ready',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled'
}

export enum PrintFormat {
  A4 = 'A4',
  A3 = 'A3',
  A5 = 'A5',
  LETTER = 'letter',
  LEGAL = 'legal',
  CUSTOM = 'custom'
}

export enum PaperType {
  STANDARD = 'standard',
  PREMIUM = 'premium',
  PHOTO = 'photo',
  CARDSTOCK = 'cardstock'
}

export enum FinishType {
  NONE = 'none',
  LAMINATED = 'laminated',
  SPIRAL_BOUND = 'spiral_bound',
  STAPLED = 'stapled'
}

// ============================================================================
// API Request/Response Types
// ============================================================================

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  errors?: ValidationError[];
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface ValidationError {
  field: string;
  message: string;
}

// ============================================================================
// Request DTOs
// ============================================================================

export interface CreateOrderRequest {
  fileId: number;
  customerName?: string;
  customerEmail?: string;
  customerPhone?: string;
  format: PrintFormat;
  paperType: PaperType;
  finish: FinishType;
  copies: number;
  notes?: string;
}

export interface UpdateOrderRequest {
  status?: OrderStatus;
  notes?: string;
}

export interface FileUploadRequest extends Request {
  file?: Express.Multer.File;
}

// ============================================================================
// File Types
// ============================================================================

export interface CreateFileData {
  filename: string;
  originalName: string;
  mimetype: string;
  size: number;
  url: string;
}

export interface UpdateFileData {
  filename?: string;
  originalName?: string;
  mimetype?: string;
  size?: number;
  url?: string;
}

export interface CreateOrderData {
  fileId: number;
  customerName?: string;
  customerEmail?: string;
  customerPhone?: string;
  format: string;
  paperType: string;
  finish: string;
  copies?: number;
  pages?: number;
  price?: number;
  status?: string;
  notes?: string;
  hasColor?: boolean;
  complexity?: 'low' | 'medium' | 'high';
}

export interface UpdateOrderData {
  fileId?: number;
  customerName?: string;
  customerEmail?: string;
  customerPhone?: string;
  format?: string;
  paperType?: string;
  finish?: string;
  copies?: number;
  pages?: number;
  price?: number;
  status?: string;
  notes?: string | undefined;
  deliveryAddress?: string;
  hasColor?: boolean;
  complexity?: 'low' | 'medium' | 'high';
  estimatedCompletion?: Date;
}

export interface CreateStatusHistoryData {
  orderId: number;
  status: string;
  notes?: string;
  updatedBy?: string;
}

// ============================================================================
// Configuration Types
// ============================================================================

export interface DatabaseConfig {
  host: string;
  port: number;
  database: string;
  username: string;
  password: string;
  ssl: boolean;
}

export interface ServerConfig {
  port: number;
  host: string;
  nodeEnv: string;
  corsOrigin: string;
}

export interface JwtConfig {
  secret: string;
  expiresIn: string;
}

export interface UploadConfig {
  uploadDir: string;
  maxFileSize: number;
  allowedFileTypes: string[];
}

// ============================================================================
// Utility Types
// ============================================================================

export type Omit<T, K extends keyof T> = Pick<T, Exclude<keyof T, K>>;
export type Partial<T> = { [P in keyof T]?: T[P] };

export interface QueryParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  search?: string;
  status?: OrderStatus;
  format?: PrintFormat;
  dateFrom?: string;
  dateTo?: string;
}

// ============================================================================
// Admin System Types
// ============================================================================

export enum AdminRole {
  ADMIN = 'admin',
  SUPER_ADMIN = 'super_admin'
}

export enum AdminStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended'
}

export interface Admin {
  id: number;
  email: string;
  name: string;
  role: AdminRole;
  status: AdminStatus;
  lastLogin?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateAdminData {
  email: string;
  name: string;
  password: string;
  role: AdminRole;
  status?: AdminStatus;
}

export interface UpdateAdminData {
  email?: string;
  name?: string;
  password?: string;
  role?: AdminRole;
  status?: AdminStatus;
  lastLogin?: Date;
}

export interface AdminLoginData {
  email: string;
  password: string;
}

export interface AdminAuthResponse {
  admin: Admin;
  token: string;
  expiresIn: string;
}

// Dashboard Statistics Types
export interface DashboardStats {
  overview: {
    totalOrders: number;
    totalFiles: number;
    totalNotifications: number;
    totalRevenue: number;
    activeOrders: number;
  };
  orderStats: {
    byStatus: Record<OrderStatus, number>;
    byPeriod: {
      today: number;
      thisWeek: number;
      thisMonth: number;
      lastMonth: number;
    };
    revenueByPeriod: {
      today: number;
      thisWeek: number;
      thisMonth: number;
      lastMonth: number;
    };
  };
  fileStats: {
    totalSize: number;
    byType: Record<string, number>;
    uploadsByPeriod: {
      today: number;
      thisWeek: number;
      thisMonth: number;
    };
  };
  notificationStats: {
    byType: Record<NotificationType, number>;
    byStatus: Record<NotificationStatus, number>;
    deliveryRate: number;
    readRate: number;
  };
  systemHealth: {
    uptime: number;
    memoryUsage: number;
    diskUsage: number;
    databaseConnections: number;
    lastBackup?: Date;
  };
}

export interface AdminActivityLog {
  id: number;
  adminId: number;
  adminEmail: string;
  action: string;
  resource: string;
  resourceId?: number;
  details?: any;
  ipAddress?: string;
  userAgent?: string;
  createdAt: Date;
}

export interface CreateAdminActivityData {
  adminId: number;
  adminEmail: string;
  action: string;
  resource: string;
  resourceId?: number;
  details?: any;
  ipAddress?: string;
  userAgent?: string;
}

// ============================================================================
// Notification Types
// ============================================================================

export enum NotificationType {
  ORDER_CREATED = 'order_created',
  ORDER_CONFIRMED = 'order_confirmed',
  ORDER_PROCESSING = 'order_processing',
  ORDER_READY = 'order_ready',
  ORDER_COMPLETED = 'order_completed',
  ORDER_CANCELLED = 'order_cancelled',
  ORDER_STATUS = 'order_status',
  FILE_UPLOADED = 'file_uploaded',
  PAYMENT_RECEIVED = 'payment_received',
  SYSTEM_ALERT = 'system_alert'
}

export enum NotificationChannel {
  EMAIL = 'email',
  SMS = 'sms',
  PUSH = 'push',
  IN_APP = 'in_app'
}

export enum NotificationStatus {
  PENDING = 'pending',
  SENT = 'sent',
  DELIVERED = 'delivered',
  FAILED = 'failed',
  READ = 'read'
}

export interface Notification {
  id: number;
  type: NotificationType;
  channel: NotificationChannel;
  status: NotificationStatus;
  recipientEmail: string;
  recipientName?: string;
  subject: string;
  content: string;
  templateData?: Record<string, any>;
  orderId?: number;
  fileId?: number;
  sentAt?: Date;
  deliveredAt?: Date;
  readAt?: Date;
  errorMessage?: string;
  retryCount: number;
  maxRetries: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateNotificationData {
  type: NotificationType;
  channel: NotificationChannel;
  recipientEmail: string;
  recipientName?: string;
  subject: string;
  content: string;
  templateData?: Record<string, any>;
  orderId?: number;
  fileId?: number;
  maxRetries?: number;
}

export interface UpdateNotificationData {
  status?: NotificationStatus;
  sentAt?: Date;
  deliveredAt?: Date;
  readAt?: Date;
  errorMessage?: string;
  retryCount?: number;
}

export interface NotificationTemplate {
  id: string;
  name: string;
  type: NotificationType;
  channel: NotificationChannel;
  subject: string;
  htmlContent: string;
  textContent: string;
  variables: string[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface EmailConfig {
  host: string;
  port: number;
  secure: boolean;
  auth: {
    user: string;
    pass: string;
  };
  from: {
    name: string;
    email: string;
  };
}

export interface NotificationEvent {
  type: NotificationType;
  data: Record<string, any>;
  timestamp: Date;
  source: string;
}

export interface NotificationStats {
  totalSent: number;
  totalDelivered: number;
  totalFailed: number;
  totalRead: number;
  deliveryRate: number;
  readRate: number;
  byType: Record<NotificationType, number>;
  byChannel: Record<NotificationChannel, number>;
  byStatus: Record<NotificationStatus, number>;
  recentActivity: Array<{
    date: string;
    sent: number;
    delivered: number;
    failed: number;
  }>;
}

// =====================================================
// PAYMENT SYSTEM TYPES
// =====================================================

export enum PaymentStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
  REFUNDED = 'refunded',
  EXPIRED = 'expired'
}

export enum InvoiceStatus {
  DRAFT = 'draft',
  SENT = 'sent',
  PAID = 'paid',
  OVERDUE = 'overdue',
  CANCELLED = 'cancelled'
}

export enum RefundStatus {
  PENDING = 'pending',
  SUCCEEDED = 'succeeded',
  FAILED = 'failed',
  CANCELED = 'canceled'
}

export interface Payment {
  id: number;
  orderId: number;
  customerEmail: string;
  multicaixaTransactionId?: string;
  multicaixaPaymentUrl?: string;
  amount: number;
  currency: string;
  status: PaymentStatus;
  paymentMethodType?: string;
  description?: string;
  metadata?: any;
  processingFee?: number;
  netAmount?: number;
  failureReason?: string;
  failureCode?: string;
  receiptUrl?: string;
  createdAt: Date;
  updatedAt: Date;
  paidAt?: Date;
  failedAt?: Date;
  expiredAt?: Date;
  deletedAt?: Date;
}

export interface CreatePaymentData {
  orderId: number;
  customerEmail: string;
  multicaixaTransactionId?: string;
  multicaixaPaymentUrl?: string;
  amount: number;
  currency?: string;
  status?: PaymentStatus;
  paymentMethodType?: string;
  description?: string;
  metadata?: any;
}

export interface UpdatePaymentData {
  status?: PaymentStatus;
  multicaixaTransactionId?: string;
  multicaixaPaymentUrl?: string;
  paymentMethodType?: string;
  processingFee?: number;
  netAmount?: number;
  failureReason?: string;
  failureCode?: string;
  receiptUrl?: string;
  paidAt?: Date;
  failedAt?: Date;
  expiredAt?: Date;
  metadata?: any;
}

export interface Invoice {
  id: number;
  invoiceNumber: string;
  orderId: number;
  paymentId?: number;
  customerName: string;
  customerEmail: string;
  customerAddress?: string;
  customerPhone?: string;
  customerTaxId?: string;
  subtotal: number;
  taxRate: number;
  taxAmount: number;
  totalAmount: number;
  currency: string;
  status: InvoiceStatus;
  dueDate?: Date;
  issueDate: Date;
  paidDate?: Date;
  notes?: string;
  pdfPath?: string;
  pdfUrl?: string;
  sentAt?: Date;
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date;
}

export interface CreateInvoiceData {
  orderId: number;
  paymentId?: number;
  customerName: string;
  customerEmail: string;
  customerAddress?: string;
  customerPhone?: string;
  customerTaxId?: string;
  subtotal: number;
  taxRate?: number;
  currency?: string;
  dueDate?: Date;
  notes?: string;
}

export interface UpdateInvoiceData {
  status?: InvoiceStatus;
  customerAddress?: string;
  customerPhone?: string;
  customerTaxId?: string;
  dueDate?: Date;
  paidDate?: Date;
  notes?: string;
  pdfPath?: string;
  pdfUrl?: string;
  sentAt?: Date;
}

export interface Refund {
  id: number;
  paymentId: number;
  stripeRefundId: string;
  amount: number;
  currency: string;
  reason?: string;
  status: RefundStatus;
  description?: string;
  metadata?: any;
  failureReason?: string;
  receiptNumber?: string;
  createdBy?: number;
  createdAt: Date;
  updatedAt: Date;
  processedAt?: Date;
  deletedAt?: Date;
}

export interface CreateRefundData {
  paymentId: number;
  stripeRefundId: string;
  amount: number;
  currency?: string;
  reason?: string;
  status?: RefundStatus;
  description?: string;
  metadata?: any;
  createdBy?: number;
}

export interface UpdateRefundData {
  status?: RefundStatus;
  failureReason?: string;
  receiptNumber?: string;
  processedAt?: Date;
}





export interface CreateInvoiceData {
  orderId: number;
  paymentId?: number;
  customerName: string;
  customerEmail: string;
  customerAddress?: string;
  customerPhone?: string;
  customerTaxId?: string;
  subtotal: number;
  taxRate?: number;
  currency?: string;
  dueDate?: Date;
  notes?: string;
}

export interface UpdateInvoiceData {
  status?: InvoiceStatus;
  customerAddress?: string;
  customerPhone?: string;
  customerTaxId?: string;
  dueDate?: Date;
  paidDate?: Date;
  notes?: string;
  pdfPath?: string;
  pdfUrl?: string;
  sentAt?: Date;
}

export interface Refund {
  id: number;
  paymentId: number;
  stripeRefundId: string;
  amount: number;
  currency: string;
  reason?: string;
  status: RefundStatus;
  description?: string;
  metadata?: any;
  failureReason?: string;
  receiptNumber?: string;
  createdBy?: number;
  createdAt: Date;
  updatedAt: Date;
  processedAt?: Date;
  deletedAt?: Date;
}

export interface CreateRefundData {
  paymentId: number;
  stripeRefundId: string;
  amount: number;
  currency?: string;
  reason?: string;
  status?: RefundStatus;
  description?: string;
  metadata?: any;
  createdBy?: number;
}

export interface UpdateRefundData {
  status?: RefundStatus;
  failureReason?: string;
  receiptNumber?: string;
  processedAt?: Date;
}

// ============================================================================
// PRODUCT GALLERY SYSTEM TYPES
// ============================================================================

export interface ProductCategory {
  id: number;
  name: string;
  slug: string;
  description?: string;
  imageUrl?: string;
  parentId?: number;
  sortOrder: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  parent?: ProductCategory;
  children?: ProductCategory[];
}

export interface Material {
  id: number;
  name: string;
  slug: string;
  description?: string;
  properties: Record<string, any>;
  isActive: boolean;
  sortOrder: number;
  createdAt: Date;
  updatedAt: Date;
  prices?: MaterialPrice[];
}

export interface ProductSize {
  id: number;
  name: string;
  widthCm: number;
  heightCm: number;
  isCustom: boolean;
  isActive: boolean;
  sortOrder: number;
  createdAt: Date;
  updatedAt: Date;
  prices?: MaterialPrice[];
}

export interface Product {
  id: number;
  title: string;
  slug: string;
  description?: string;
  categoryId: number;
  imageUrl: string;
  imageWidth?: number;
  imageHeight?: number;
  imageSizeBytes?: number;
  hasAiEnhancement: boolean;
  aiEnhancedUrl?: string;
  artistName?: string;
  artistCredit?: string;
  tags: string[];
  basePrice: number;
  isFeatured: boolean;
  isActive: boolean;
  viewCount: number;
  createdAt: Date;
  updatedAt: Date;
  category?: ProductCategory;
}

export interface MaterialPrice {
  id: number;
  materialId: number;
  sizeId: number;
  price: number;
  currency: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  material?: Material;
  size?: ProductSize;
}

export enum CustomOrderStatus {
  PENDING = 'pending',
  QUOTED = 'quoted',
  APPROVED = 'approved',
  IN_PRODUCTION = 'in_production',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled'
}

export interface CustomOrder {
  id: number;
  orderId: number;
  productId?: number;
  materialId: number;
  sizeId?: number;
  customWidthCm?: number;
  customHeightCm?: number;
  customSpecifications?: string;
  specialInstructions?: string;
  quotedPrice?: number;
  finalPrice?: number;
  originalImageUrl?: string;
  aiEnhancedImageUrl?: string;
  enhancementApplied: boolean;
  status: CustomOrderStatus;
  createdAt: Date;
  updatedAt: Date;
  order?: Order;
  product?: Product;
  material?: Material;
  size?: ProductSize;
}

// ============================================================================
// PRODUCT GALLERY REQUEST/RESPONSE TYPES
// ============================================================================

export interface CreateProductCategoryData {
  name: string;
  slug: string;
  description?: string;
  imageUrl?: string;
  parentId?: number;
  sortOrder?: number;
  isActive?: boolean;
}

export interface UpdateProductCategoryData {
  name?: string;
  slug?: string;
  description?: string;
  imageUrl?: string;
  parentId?: number;
  sortOrder?: number;
  isActive?: boolean;
}

export interface CreateMaterialData {
  name: string;
  slug: string;
  description?: string;
  properties?: Record<string, any>;
  isActive?: boolean;
  sortOrder?: number;
}

export interface UpdateMaterialData {
  name?: string;
  slug?: string;
  description?: string;
  properties?: Record<string, any>;
  isActive?: boolean;
  sortOrder?: number;
}

export interface CreateProductSizeData {
  name: string;
  widthCm: number;
  heightCm: number;
  isCustom?: boolean;
  isActive?: boolean;
  sortOrder?: number;
}

export interface UpdateProductSizeData {
  name?: string;
  widthCm?: number;
  heightCm?: number;
  isCustom?: boolean;
  isActive?: boolean;
  sortOrder?: number;
}

export interface CreateProductData {
  title: string;
  slug: string;
  description?: string;
  categoryId: number;
  imageUrl: string;
  imageWidth?: number;
  imageHeight?: number;
  imageSizeBytes?: number;
  hasAiEnhancement?: boolean;
  aiEnhancedUrl?: string;
  artistName?: string;
  artistCredit?: string;
  tags?: string[];
  basePrice?: number;
  isFeatured?: boolean;
  isActive?: boolean;
}

export interface UpdateProductData {
  title?: string;
  slug?: string;
  description?: string;
  categoryId?: number;
  imageUrl?: string;
  imageWidth?: number;
  imageHeight?: number;
  imageSizeBytes?: number;
  hasAiEnhancement?: boolean;
  aiEnhancedUrl?: string;
  artistName?: string;
  artistCredit?: string;
  tags?: string[];
  basePrice?: number;
  isFeatured?: boolean;
  isActive?: boolean;
  viewCount?: number;
}

export interface CreateMaterialPriceData {
  materialId: number;
  sizeId: number;
  price: number;
  currency?: string;
  isActive?: boolean;
}

export interface UpdateMaterialPriceData {
  price?: number;
  currency?: string;
  isActive?: boolean;
}

export interface CreateCustomOrderData {
  orderId: number;
  productId?: number;
  materialId: number;
  sizeId?: number;
  customWidthCm?: number;
  customHeightCm?: number;
  customSpecifications?: string;
  specialInstructions?: string;
  quotedPrice?: number;
  originalImageUrl?: string;
  status?: CustomOrderStatus;
}

export interface UpdateCustomOrderData {
  productId?: number;
  materialId?: number;
  sizeId?: number;
  customWidthCm?: number;
  customHeightCm?: number;
  customSpecifications?: string;
  specialInstructions?: string;
  quotedPrice?: number;
  finalPrice?: number;
  originalImageUrl?: string;
  aiEnhancedImageUrl?: string;
  enhancementApplied?: boolean;
  status?: CustomOrderStatus;
}

// ============================================================================
// PRODUCT GALLERY QUERY TYPES
// ============================================================================

export interface ProductQueryParams extends QueryParams {
  categoryId?: number;
  categorySlug?: string;
  tags?: string[];
  isFeatured?: boolean;
  isActive?: boolean;
  priceMin?: number;
  priceMax?: number;
  hasAiEnhancement?: boolean;
  artistName?: string;
}

export interface CategoryQueryParams extends QueryParams {
  parentId?: number;
  isActive?: boolean;
  includeChildren?: boolean;
}

export interface MaterialQueryParams extends QueryParams {
  isActive?: boolean;
  includePrices?: boolean;
}

export interface ProductGalleryResponse {
  products: Product[];
  categories: ProductCategory[];
  materials: Material[];
  sizes: ProductSize[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  filters: {
    availableCategories: ProductCategory[];
    availableTags: string[];
    priceRange: {
      min: number;
      max: number;
    };
  };
}

export interface ProductDetailsResponse {
  product: Product;
  relatedProducts: Product[];
  availableMaterials: Material[];
  availableSizes: ProductSize[];
  pricing: MaterialPrice[];
}

export interface ProductConfigurationRequest {
  productId: number;
  materialId: number;
  sizeId?: number;
  customWidthCm?: number;
  customHeightCm?: number;
  quantity?: number;
  aiEnhancementRequested?: boolean;
}

export interface ProductConfigurationResponse {
  configuration: ProductConfigurationRequest;
  pricing: {
    basePrice: number;
    materialPrice: number;
    sizeMultiplier: number;
    aiEnhancementPrice: number;
    totalPrice: number;
    currency: string;
  };
  estimatedDelivery: {
    minDays: number;
    maxDays: number;
    estimatedDate: Date;
  };
  availability: {
    inStock: boolean;
    stockLevel?: number;
    backorderAvailable: boolean;
  };
}
