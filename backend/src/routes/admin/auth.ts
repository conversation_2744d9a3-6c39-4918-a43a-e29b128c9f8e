import { Router } from 'express';
import { AdminModel } from '../../models/Admin';
import { AdminActivityLogModel } from '../../models/AdminActivityLog';
import { authenticateAdmin, logAdminActivity } from '../../middleware/adminAuth';
import { AdminLoginData, CreateAdminData, AdminRole, AdminStatus } from '../../types';

const router = Router();

/**
 * POST /api/admin/auth/login
 * Admin login
 */
router.post('/login', async (req, res) => {
  try {
    const { email, password }: AdminLoginData = req.body;

    // Validate input
    if (!email || !password) {
      return res.error('Email e senha são obrigatórios', 400);
    }

    // Authenticate admin
    const authResult = await AdminModel.authenticate({ email, password });
    
    if (!authResult) {
      // Log failed login attempt
      await AdminActivityLogModel.create({
        adminId: 0,
        adminEmail: email,
        action: 'login_failed',
        resource: 'auth',
        details: { reason: 'invalid_credentials' },
        ipAddress: req.ip,
        userAgent: req.get('User-Agent')
      });

      return res.error('Credenciais inválidas', 401);
    }

    // Log successful login
    await AdminActivityLogModel.logAction(
      authResult.admin.id,
      authResult.admin.email,
      'login_success',
      'auth',
      undefined,
      { loginTime: new Date() },
      req
    );

    res.success(authResult, 'Login realizado com sucesso');
  } catch (error) {
    console.error('Admin login error:', error);
    res.error('Erro interno do servidor', 500);
  }
});

/**
 * POST /api/admin/auth/logout
 * Admin logout
 */
router.post('/logout', authenticateAdmin, logAdminActivity('logout', 'auth'), async (req, res) => {
  try {
    // In a real application, you might want to blacklist the token
    // For now, we'll just log the logout action
    
    res.success(null, 'Logout realizado com sucesso');
  } catch (error) {
    console.error('Admin logout error:', error);
    res.error('Erro interno do servidor', 500);
  }
});

/**
 * GET /api/admin/auth/me
 * Get current admin info
 */
router.get('/me', authenticateAdmin, async (req, res) => {
  try {
    if (!req.admin) {
      return res.error('Administrador não encontrado', 404);
    }

    // Get full admin details
    const admin = await AdminModel.findById(req.admin.id);
    
    if (!admin) {
      return res.error('Administrador não encontrado', 404);
    }

    res.success(admin, 'Informações do administrador');
  } catch (error) {
    console.error('Get admin info error:', error);
    res.error('Erro interno do servidor', 500);
  }
});

/**
 * PUT /api/admin/auth/profile
 * Update admin profile
 */
router.put('/profile', 
  authenticateAdmin, 
  logAdminActivity('update_profile', 'admin'),
  async (req, res) => {
    try {
      if (!req.admin) {
        return res.error('Administrador não encontrado', 404);
      }

      const { name, email, currentPassword, newPassword } = req.body;

      // Validate current password if changing password
      if (newPassword) {
        if (!currentPassword) {
          return res.error('Senha atual é obrigatória para alterar a senha', 400);
        }

        const adminWithPassword = await AdminModel.findByEmailWithPassword(req.admin.email);
        if (!adminWithPassword) {
          return res.error('Administrador não encontrado', 404);
        }

        const bcrypt = require('bcryptjs');
        const isCurrentPasswordValid = await bcrypt.compare(currentPassword, adminWithPassword.password);
        if (!isCurrentPasswordValid) {
          return res.error('Senha atual incorreta', 400);
        }
      }

      // Update admin
      const updateData: any = {};
      if (name) updateData.name = name;
      if (email && email !== req.admin.email) {
        // Check if email is already taken
        const existingAdmin = await AdminModel.findByEmail(email);
        if (existingAdmin && existingAdmin.id !== req.admin.id) {
          return res.error('Email já está em uso', 400);
        }
        updateData.email = email;
      }
      if (newPassword) updateData.password = newPassword;

      const updatedAdmin = await AdminModel.update(req.admin.id, updateData);
      
      if (!updatedAdmin) {
        return res.error('Erro ao atualizar perfil', 500);
      }

      res.success(updatedAdmin, 'Perfil atualizado com sucesso');
    } catch (error) {
      console.error('Update admin profile error:', error);
      res.error('Erro interno do servidor', 500);
    }
  }
);

/**
 * POST /api/admin/auth/refresh
 * Refresh admin token
 */
router.post('/refresh', authenticateAdmin, async (req, res) => {
  try {
    if (!req.admin) {
      return res.error('Administrador não encontrado', 404);
    }

    // Get admin from database
    const admin = await AdminModel.findById(req.admin.id);
    if (!admin || admin.status !== AdminStatus.ACTIVE) {
      return res.error('Administrador não encontrado ou inativo', 404);
    }

    // Generate new token
    const jwt = require('jsonwebtoken');
    const token = jwt.sign(
      { 
        adminId: admin.id, 
        email: admin.email, 
        role: admin.role 
      },
      process.env.JWT_SECRET || 'default-secret',
      { expiresIn: process.env.JWT_EXPIRES_IN || '7d' }
    );

    res.success({
      admin,
      token,
      expiresIn: process.env.JWT_EXPIRES_IN || '7d'
    }, 'Token renovado com sucesso');
  } catch (error) {
    console.error('Refresh token error:', error);
    res.error('Erro interno do servidor', 500);
  }
});

/**
 * POST /api/admin/auth/change-password
 * Change admin password
 */
router.post('/change-password', 
  authenticateAdmin, 
  logAdminActivity('change_password', 'admin'),
  async (req, res) => {
    try {
      if (!req.admin) {
        return res.error('Administrador não encontrado', 404);
      }

      const { currentPassword, newPassword, confirmPassword } = req.body;

      // Validate input
      if (!currentPassword || !newPassword || !confirmPassword) {
        return res.error('Todos os campos são obrigatórios', 400);
      }

      if (newPassword !== confirmPassword) {
        return res.error('Nova senha e confirmação não coincidem', 400);
      }

      if (newPassword.length < 8) {
        return res.error('Nova senha deve ter pelo menos 8 caracteres', 400);
      }

      // Verify current password
      const adminWithPassword = await AdminModel.findByEmailWithPassword(req.admin.email);
      if (!adminWithPassword) {
        return res.error('Administrador não encontrado', 404);
      }

      const bcrypt = require('bcryptjs');
      const isCurrentPasswordValid = await bcrypt.compare(currentPassword, adminWithPassword.password);
      if (!isCurrentPasswordValid) {
        return res.error('Senha atual incorreta', 400);
      }

      // Update password
      await AdminModel.update(req.admin.id, { password: newPassword });

      res.success(null, 'Senha alterada com sucesso');
    } catch (error) {
      console.error('Change password error:', error);
      res.error('Erro interno do servidor', 500);
    }
  }
);

/**
 * GET /api/admin/auth/activity
 * Get admin activity logs
 */
router.get('/activity', authenticateAdmin, async (req, res) => {
  try {
    if (!req.admin) {
      return res.error('Administrador não encontrado', 404);
    }

    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;

    const result = await AdminActivityLogModel.findByAdminId(req.admin.id, { page, limit });

    res.success(result, 'Histórico de atividades');
  } catch (error) {
    console.error('Get admin activity error:', error);
    res.error('Erro interno do servidor', 500);
  }
});

export default router;
