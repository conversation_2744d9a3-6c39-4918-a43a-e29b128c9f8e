import { Router } from 'express';
import { DashboardService } from '../../services/DashboardService';
import { AdminActivityLogModel } from '../../models/AdminActivityLog';
import { authenticateAdmin, logAdminActivity } from '../../middleware/adminAuth';

const router = Router();

/**
 * GET /api/admin/dashboard/stats
 * Get comprehensive dashboard statistics
 */
router.get('/stats', 
  authenticateAdmin, 
  logAdminActivity('view_dashboard_stats', 'dashboard'),
  async (req, res) => {
    try {
      const stats = await DashboardService.getDashboardStats();
      res.success(stats, 'Estatísticas do dashboard');
    } catch (error) {
      console.error('Get dashboard stats error:', error);
      res.error('Erro ao obter estatísticas do dashboard', 500);
    }
  }
);

/**
 * GET /api/admin/dashboard/overview
 * Get dashboard overview (quick stats)
 */
router.get('/overview', 
  authenticateAdmin, 
  logAdminActivity('view_dashboard_overview', 'dashboard'),
  async (req, res) => {
    try {
      const fullStats = await DashboardService.getDashboardStats();
      
      // Return only overview data for faster loading
      const overview = {
        overview: fullStats.overview,
        systemHealth: {
          uptime: fullStats.systemHealth.uptime,
          memoryUsage: fullStats.systemHealth.memoryUsage,
          databaseConnections: fullStats.systemHealth.databaseConnections
        }
      };

      res.success(overview, 'Visão geral do dashboard');
    } catch (error) {
      console.error('Get dashboard overview error:', error);
      res.error('Erro ao obter visão geral do dashboard', 500);
    }
  }
);

/**
 * GET /api/admin/dashboard/recent-activity
 * Get recent system activity
 */
router.get('/recent-activity', 
  authenticateAdmin, 
  logAdminActivity('view_recent_activity', 'dashboard'),
  async (req, res) => {
    try {
      const limit = parseInt(req.query.limit as string) || 20;
      const activity = await DashboardService.getRecentActivity(limit);
      
      res.success(activity, 'Atividade recente do sistema');
    } catch (error) {
      console.error('Get recent activity error:', error);
      res.error('Erro ao obter atividade recente', 500);
    }
  }
);

/**
 * GET /api/admin/dashboard/admin-activity
 * Get admin activity logs with filters
 */
router.get('/admin-activity', 
  authenticateAdmin, 
  logAdminActivity('view_admin_activity', 'dashboard'),
  async (req, res) => {
    try {
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 50;
      const adminId = req.query.adminId ? parseInt(req.query.adminId as string) : undefined;
      const action = req.query.action as string;
      const resource = req.query.resource as string;
      
      let dateFrom: Date | undefined;
      let dateTo: Date | undefined;
      
      if (req.query.dateFrom) {
        dateFrom = new Date(req.query.dateFrom as string);
      }
      
      if (req.query.dateTo) {
        dateTo = new Date(req.query.dateTo as string);
      }

      const queryOptions: any = {
        page,
        limit,
        action,
        resource
      };

      if (adminId !== undefined) queryOptions.adminId = adminId;
      if (dateFrom !== undefined) queryOptions.dateFrom = dateFrom;
      if (dateTo !== undefined) queryOptions.dateTo = dateTo;

      const result = await AdminActivityLogModel.findMany(queryOptions);

      res.success(result, 'Logs de atividade dos administradores');
    } catch (error) {
      console.error('Get admin activity error:', error);
      res.error('Erro ao obter logs de atividade', 500);
    }
  }
);

/**
 * GET /api/admin/dashboard/admin-activity/stats
 * Get admin activity statistics
 */
router.get('/admin-activity/stats', 
  authenticateAdmin, 
  logAdminActivity('view_admin_activity_stats', 'dashboard'),
  async (req, res) => {
    try {
      const days = parseInt(req.query.days as string) || 30;
      const stats = await AdminActivityLogModel.getStatistics(days);
      
      res.success(stats, 'Estatísticas de atividade dos administradores');
    } catch (error) {
      console.error('Get admin activity stats error:', error);
      res.error('Erro ao obter estatísticas de atividade', 500);
    }
  }
);

/**
 * GET /api/admin/dashboard/system-health
 * Get detailed system health information
 */
router.get('/system-health', 
  authenticateAdmin, 
  logAdminActivity('view_system_health', 'dashboard'),
  async (req, res) => {
    try {
      const fullStats = await DashboardService.getDashboardStats();
      
      // Add additional system information
      const systemHealth = {
        ...fullStats.systemHealth,
        nodeVersion: process.version,
        platform: process.platform,
        architecture: process.arch,
        environment: process.env.NODE_ENV || 'development',
        processId: process.pid,
        startTime: new Date(Date.now() - (process.uptime() * 1000))
      };

      res.success(systemHealth, 'Informações de saúde do sistema');
    } catch (error) {
      console.error('Get system health error:', error);
      res.error('Erro ao obter informações do sistema', 500);
    }
  }
);

/**
 * GET /api/admin/dashboard/performance
 * Get system performance metrics
 */
router.get('/performance', 
  authenticateAdmin, 
  logAdminActivity('view_performance_metrics', 'dashboard'),
  async (req, res) => {
    try {
      const memUsage = process.memoryUsage();
      const cpuUsage = process.cpuUsage();
      
      // Get database performance (if available)
      let dbPerformance = null;
      try {
        const database = require('../../database').default;
        const dbQuery = `
          SELECT 
            count(*) as active_connections,
            (SELECT count(*) FROM pg_stat_activity WHERE state = 'active') as active_queries,
            (SELECT count(*) FROM pg_stat_activity WHERE state = 'idle') as idle_connections
          FROM pg_stat_activity 
          WHERE datname = current_database()
        `;
        const dbResult = await database.query(dbQuery);
        dbPerformance = dbResult.rows[0];
      } catch (dbError) {
        console.warn('Could not get database performance:', dbError);
      }

      const performance = {
        memory: {
          rss: memUsage.rss,
          heapTotal: memUsage.heapTotal,
          heapUsed: memUsage.heapUsed,
          external: memUsage.external,
          arrayBuffers: memUsage.arrayBuffers
        },
        cpu: {
          user: cpuUsage.user,
          system: cpuUsage.system
        },
        uptime: process.uptime(),
        database: dbPerformance,
        timestamp: new Date()
      };

      res.success(performance, 'Métricas de performance do sistema');
    } catch (error) {
      console.error('Get performance metrics error:', error);
      res.error('Erro ao obter métricas de performance', 500);
    }
  }
);

/**
 * POST /api/admin/dashboard/cleanup
 * Cleanup old data (logs, notifications, etc.)
 */
router.post('/cleanup', 
  authenticateAdmin, 
  logAdminActivity('system_cleanup', 'dashboard'),
  async (req, res) => {
    try {
      const { 
        cleanupLogs = false, 
        cleanupNotifications = false,
        daysOld = 365 
      } = req.body;

      const results: any = {};

      if (cleanupLogs) {
        const deletedLogs = await AdminActivityLogModel.cleanup(daysOld);
        results.deletedLogs = deletedLogs;
      }

      if (cleanupNotifications) {
        // This would be implemented in NotificationModel
        // const deletedNotifications = await NotificationModel.cleanup(daysOld);
        // results.deletedNotifications = deletedNotifications;
        results.deletedNotifications = 0; // Placeholder
      }

      res.success(results, 'Limpeza do sistema concluída');
    } catch (error) {
      console.error('System cleanup error:', error);
      res.error('Erro durante a limpeza do sistema', 500);
    }
  }
);

/**
 * GET /api/admin/dashboard/export
 * Export dashboard data
 */
router.get('/export', 
  authenticateAdmin, 
  logAdminActivity('export_dashboard_data', 'dashboard'),
  async (req, res) => {
    try {
      const format = req.query.format as string || 'json';
      const type = req.query.type as string || 'stats';

      let data: any;

      switch (type) {
        case 'stats':
          data = await DashboardService.getDashboardStats();
          break;
        case 'activity':
          const activityResult = await AdminActivityLogModel.findMany({ limit: 1000 });
          data = activityResult.logs;
          break;
        case 'recent':
          data = await DashboardService.getRecentActivity(100);
          break;
        default:
          return res.error('Tipo de exportação inválido', 400);
      }

      if (format === 'csv') {
        // Convert to CSV (simplified implementation)
        const csv = JSON.stringify(data); // In a real app, use a proper CSV library
        res.setHeader('Content-Type', 'text/csv');
        res.setHeader('Content-Disposition', `attachment; filename="dashboard-${type}-${Date.now()}.csv"`);
        return res.send(csv);
      }

      // Default to JSON
      res.setHeader('Content-Type', 'application/json');
      res.setHeader('Content-Disposition', `attachment; filename="dashboard-${type}-${Date.now()}.json"`);
      res.send(JSON.stringify(data, null, 2));
    } catch (error) {
      console.error('Export dashboard data error:', error);
      res.error('Erro ao exportar dados do dashboard', 500);
    }
  }
);

export default router;
