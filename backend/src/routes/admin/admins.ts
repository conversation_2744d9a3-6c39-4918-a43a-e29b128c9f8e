import { Router } from 'express';
import { AdminModel } from '../../models/Admin';
import { 
  authenticateAdmin, 
  requireSuperAdmin, 
  logAdminActivity,
  preventSelfModification,
  canModifyResource
} from '../../middleware/adminAuth';
import { CreateAdminData, UpdateAdminData, AdminRole, AdminStatus } from '../../types';

const router = Router();

/**
 * GET /api/admin/admins
 * Get all admins with pagination and filters
 */
router.get('/', 
  authenticateAdmin, 
  requireSuperAdmin,
  logAdminActivity('list_admins', 'admins'),
  async (req, res) => {
    try {
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 20;
      const role = req.query.role as AdminRole;
      const status = req.query.status as AdminStatus;

      const result = await AdminModel.findMany({
        page,
        limit,
        role,
        status
      });

      res.success(result, 'Lista de administradores');
    } catch (error) {
      console.error('Get admins error:', error);
      res.error('Erro ao obter lista de administradores', 500);
    }
  }
);

/**
 * GET /api/admin/admins/:id
 * Get specific admin by ID
 */
router.get('/:id', 
  authenticateAdmin, 
  requireSuperAdmin,
  logAdminActivity('view_admin', 'admins'),
  async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      
      if (isNaN(id)) {
        return res.error('ID inválido', 400);
      }

      const admin = await AdminModel.findById(id);
      
      if (!admin) {
        return res.error('Administrador não encontrado', 404);
      }

      res.success(admin, 'Detalhes do administrador');
    } catch (error) {
      console.error('Get admin error:', error);
      res.error('Erro ao obter administrador', 500);
    }
  }
);

/**
 * POST /api/admin/admins
 * Create new admin
 */
router.post('/', 
  authenticateAdmin, 
  requireSuperAdmin,
  canModifyResource('admins'),
  logAdminActivity('create_admin', 'admins'),
  async (req, res) => {
    try {
      const { email, name, password, role, status }: CreateAdminData = req.body;

      // Validate input
      if (!email || !name || !password || !role) {
        return res.error('Email, nome, senha e papel são obrigatórios', 400);
      }

      if (!Object.values(AdminRole).includes(role)) {
        return res.error('Papel de administrador inválido', 400);
      }

      if (status && !Object.values(AdminStatus).includes(status)) {
        return res.error('Status de administrador inválido', 400);
      }

      if (password.length < 8) {
        return res.error('Senha deve ter pelo menos 8 caracteres', 400);
      }

      // Check if email already exists
      const existingAdmin = await AdminModel.existsByEmail(email);
      if (existingAdmin) {
        return res.error('Email já está em uso', 400);
      }

      // Create admin
      const admin = await AdminModel.create({
        email,
        name,
        password,
        role,
        status: status || AdminStatus.ACTIVE
      });

      res.success(admin, 'Administrador criado com sucesso', 201);
    } catch (error) {
      console.error('Create admin error:', error);
      res.error('Erro ao criar administrador', 500);
    }
  }
);

/**
 * PUT /api/admin/admins/:id
 * Update admin
 */
router.put('/:id', 
  authenticateAdmin, 
  requireSuperAdmin,
  canModifyResource('admins'),
  preventSelfModification,
  logAdminActivity('update_admin', 'admins'),
  async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      
      if (isNaN(id)) {
        return res.error('ID inválido', 400);
      }

      const { email, name, password, role, status }: UpdateAdminData = req.body;

      // Validate role if provided
      if (role && !Object.values(AdminRole).includes(role)) {
        return res.error('Papel de administrador inválido', 400);
      }

      // Validate status if provided
      if (status && !Object.values(AdminStatus).includes(status)) {
        return res.error('Status de administrador inválido', 400);
      }

      // Validate password if provided
      if (password && password.length < 8) {
        return res.error('Senha deve ter pelo menos 8 caracteres', 400);
      }

      // Check if email is already taken by another admin
      if (email) {
        const existingAdmin = await AdminModel.findByEmail(email);
        if (existingAdmin && existingAdmin.id !== id) {
          return res.error('Email já está em uso', 400);
        }
      }

      // Update admin
      const updatedAdmin = await AdminModel.update(id, {
        email,
        name,
        password,
        role,
        status
      });

      if (!updatedAdmin) {
        return res.error('Administrador não encontrado', 404);
      }

      res.success(updatedAdmin, 'Administrador atualizado com sucesso');
    } catch (error) {
      console.error('Update admin error:', error);
      res.error('Erro ao atualizar administrador', 500);
    }
  }
);

/**
 * DELETE /api/admin/admins/:id
 * Delete admin (soft delete)
 */
router.delete('/:id', 
  authenticateAdmin, 
  requireSuperAdmin,
  canModifyResource('admins'),
  preventSelfModification,
  logAdminActivity('delete_admin', 'admins'),
  async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      
      if (isNaN(id)) {
        return res.error('ID inválido', 400);
      }

      // Prevent deleting self
      if (req.admin && req.admin.id === id) {
        return res.error('Não é possível excluir a própria conta', 403);
      }

      const deleted = await AdminModel.delete(id);
      
      if (!deleted) {
        return res.error('Administrador não encontrado', 404);
      }

      res.success(null, 'Administrador excluído com sucesso');
    } catch (error) {
      console.error('Delete admin error:', error);
      res.error('Erro ao excluir administrador', 500);
    }
  }
);

/**
 * PUT /api/admin/admins/:id/status
 * Update admin status
 */
router.put('/:id/status', 
  authenticateAdmin, 
  requireSuperAdmin,
  canModifyResource('admins'),
  preventSelfModification,
  logAdminActivity('update_admin_status', 'admins'),
  async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const { status }: { status: AdminStatus } = req.body;
      
      if (isNaN(id)) {
        return res.error('ID inválido', 400);
      }

      if (!status || !Object.values(AdminStatus).includes(status)) {
        return res.error('Status inválido', 400);
      }

      // Prevent changing own status
      if (req.admin && req.admin.id === id) {
        return res.error('Não é possível alterar o próprio status', 403);
      }

      const updatedAdmin = await AdminModel.update(id, { status });
      
      if (!updatedAdmin) {
        return res.error('Administrador não encontrado', 404);
      }

      res.success(updatedAdmin, 'Status do administrador atualizado com sucesso');
    } catch (error) {
      console.error('Update admin status error:', error);
      res.error('Erro ao atualizar status do administrador', 500);
    }
  }
);

/**
 * PUT /api/admin/admins/:id/role
 * Update admin role
 */
router.put('/:id/role', 
  authenticateAdmin, 
  requireSuperAdmin,
  canModifyResource('admins'),
  preventSelfModification,
  logAdminActivity('update_admin_role', 'admins'),
  async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const { role }: { role: AdminRole } = req.body;
      
      if (isNaN(id)) {
        return res.error('ID inválido', 400);
      }

      if (!role || !Object.values(AdminRole).includes(role)) {
        return res.error('Papel inválido', 400);
      }

      // Prevent changing own role
      if (req.admin && req.admin.id === id) {
        return res.error('Não é possível alterar o próprio papel', 403);
      }

      const updatedAdmin = await AdminModel.update(id, { role });
      
      if (!updatedAdmin) {
        return res.error('Administrador não encontrado', 404);
      }

      res.success(updatedAdmin, 'Papel do administrador atualizado com sucesso');
    } catch (error) {
      console.error('Update admin role error:', error);
      res.error('Erro ao atualizar papel do administrador', 500);
    }
  }
);

/**
 * POST /api/admin/admins/:id/reset-password
 * Reset admin password
 */
router.post('/:id/reset-password', 
  authenticateAdmin, 
  requireSuperAdmin,
  canModifyResource('admins'),
  logAdminActivity('reset_admin_password', 'admins'),
  async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const { newPassword }: { newPassword: string } = req.body;
      
      if (isNaN(id)) {
        return res.error('ID inválido', 400);
      }

      if (!newPassword || newPassword.length < 8) {
        return res.error('Nova senha deve ter pelo menos 8 caracteres', 400);
      }

      const updatedAdmin = await AdminModel.update(id, { password: newPassword });
      
      if (!updatedAdmin) {
        return res.error('Administrador não encontrado', 404);
      }

      res.success(null, 'Senha do administrador redefinida com sucesso');
    } catch (error) {
      console.error('Reset admin password error:', error);
      res.error('Erro ao redefinir senha do administrador', 500);
    }
  }
);

export default router;
