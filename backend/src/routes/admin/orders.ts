import { Router } from 'express';
import { OrderModel } from '../../models/Order';
import { FileModel } from '../../models/File';
import { NotificationService } from '../../services/NotificationService';
import { 
  authenticateAdmin, 
  logAdminActivity,
  canModifyResource
} from '../../middleware/adminAuth';
import { OrderStatus, UpdateOrderData } from '../../types';

const router = Router();

/**
 * GET /api/admin/orders
 * Get all orders with advanced filtering and pagination
 */
router.get('/', 
  authenticateAdmin, 
  logAdminActivity('list_orders', 'orders'),
  async (req, res) => {
    try {
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 20;
      const status = req.query.status as OrderStatus;
      const customerEmail = req.query.customerEmail as string;
      const search = req.query.search as string;
      
      let dateFrom: Date | undefined;
      let dateTo: Date | undefined;
      
      if (req.query.dateFrom) {
        dateFrom = new Date(req.query.dateFrom as string);
      }
      
      if (req.query.dateTo) {
        dateTo = new Date(req.query.dateTo as string);
      }

      const result = await OrderModel.findAll({
        limit,
        offset: (page - 1) * limit,
        status,
        customerEmail
      });

      res.success({
        orders: result.orders,
        total: result.total,
        page,
        limit,
        totalPages: Math.ceil(result.total / limit)
      }, 'Lista de pedidos');
    } catch (error) {
      console.error('Get orders error:', error);
      res.error('Erro ao obter lista de pedidos', 500);
    }
  }
);

/**
 * GET /api/admin/orders/stats
 * Get order statistics
 */
router.get('/stats', 
  authenticateAdmin, 
  logAdminActivity('view_order_stats', 'orders'),
  async (req, res) => {
    try {
      const stats = await OrderModel.getStatistics();

      // Transform response to match test expectations
      const transformedStats = {
        total: stats.totalOrders,
        byStatus: stats.statusBreakdown,
        revenue: stats.totalRevenue,
        averageValue: stats.averageOrderValue,
        revenueByPeriod: stats.revenueByPeriod
      };

      res.success(transformedStats, 'Estatísticas de pedidos');
    } catch (error) {
      console.error('Get order stats error:', error);
      res.error('Erro ao obter estatísticas de pedidos', 500);
    }
  }
);

/**
 * GET /api/admin/orders/:id
 * Get specific order with full details
 */
router.get('/:id', 
  authenticateAdmin, 
  logAdminActivity('view_order', 'orders'),
  async (req, res) => {
    try {
      const id = parseInt(req.params.id || '0');

      if (isNaN(id) || id === 0) {
        return res.error('ID inválido', 400);
      }

      const order = await OrderModel.findById(id);
      
      if (!order) {
        return res.error('Pedido não encontrado', 404);
      }

      // Get associated files
      const files = await FileModel.findByOrderId(id);

      const orderWithFiles = {
        ...order,
        files
      };

      res.success(orderWithFiles, 'Detalhes do pedido');
    } catch (error) {
      console.error('Get order error:', error);
      res.error('Erro ao obter pedido', 500);
    }
  }
);

/**
 * PUT /api/admin/orders/:id
 * Update order details
 */
router.put('/:id', 
  authenticateAdmin, 
  canModifyResource('orders'),
  logAdminActivity('update_order', 'orders'),
  async (req, res) => {
    try {
      const id = parseInt(req.params.id || '0');

      if (isNaN(id) || id === 0) {
        return res.error('ID inválido', 400);
      }

      const updateData: UpdateOrderData = req.body;

      // Validate status if provided
      if (updateData.status && !Object.values(OrderStatus).includes(updateData.status as OrderStatus)) {
        return res.error('Status inválido', 400);
      }

      // Get current order for comparison
      const currentOrder = await OrderModel.findById(id);
      if (!currentOrder) {
        return res.error('Pedido não encontrado', 404);
      }

      // Update order
      const updatedOrder = await OrderModel.update(id, updateData);
      
      if (!updatedOrder) {
        return res.error('Erro ao atualizar pedido', 500);
      }

      // Send notification if status changed
      if (updateData.status && updateData.status !== currentOrder.status && req.admin) {
        try {
          const notificationService = NotificationService.getInstance();
          await notificationService.sendOrderStatusNotification(
            updatedOrder.id,
            updatedOrder.status,
            updatedOrder.customerEmail
          );
        } catch (notificationError) {
          console.error('Failed to send status notification:', notificationError);
          // Don't fail the request if notification fails
        }
      }

      res.success(updatedOrder, 'Pedido atualizado com sucesso');
    } catch (error) {
      console.error('Update order error:', error);
      res.error('Erro ao atualizar pedido', 500);
    }
  }
);

/**
 * PUT /api/admin/orders/:id/status
 * Update order status
 */
router.put('/:id/status', 
  authenticateAdmin, 
  canModifyResource('orders'),
  logAdminActivity('update_order_status', 'orders'),
  async (req, res) => {
    try {
      const id = parseInt(req.params.id || '0');
      const { status, notes }: { status: OrderStatus; notes?: string } = req.body;

      if (isNaN(id) || id === 0) {
        return res.error('ID inválido', 400);
      }

      if (!status || !Object.values(OrderStatus).includes(status)) {
        return res.error('Status inválido', 400);
      }

      // Get current order
      const currentOrder = await OrderModel.findById(id);
      if (!currentOrder) {
        return res.error('Pedido não encontrado', 404);
      }

      // Update order status
      const updateData: UpdateOrderData = { status };
      if (notes) {
        updateData.notes = notes;
      }

      const updatedOrder = await OrderModel.update(id, updateData);
      
      if (!updatedOrder) {
        return res.error('Erro ao atualizar status do pedido', 500);
      }

      // Send notification
      if (req.admin) {
        try {
          const notificationService = NotificationService.getInstance();
          await notificationService.sendOrderStatusNotification(
            updatedOrder.id,
            updatedOrder.status,
            updatedOrder.customerEmail
          );
        } catch (notificationError) {
          console.error('Failed to send status notification:', notificationError);
        }
      }

      res.success(updatedOrder, 'Status do pedido atualizado com sucesso');
    } catch (error) {
      console.error('Update order status error:', error);
      res.error('Erro ao atualizar status do pedido', 500);
    }
  }
);

/**
 * DELETE /api/admin/orders/:id
 * Delete order (soft delete)
 */
router.delete('/:id', 
  authenticateAdmin, 
  canModifyResource('orders'),
  logAdminActivity('delete_order', 'orders'),
  async (req, res) => {
    try {
      const id = parseInt(req.params.id || '0');

      if (isNaN(id) || id === 0) {
        return res.error('ID inválido', 400);
      }

      const deleted = await OrderModel.delete(id);
      
      if (!deleted) {
        return res.error('Pedido não encontrado', 404);
      }

      res.success(null, 'Pedido excluído com sucesso');
    } catch (error) {
      console.error('Delete order error:', error);
      res.error('Erro ao excluir pedido', 500);
    }
  }
);

/**
 * POST /api/admin/orders/:id/notes
 * Add notes to order
 */
router.post('/:id/notes', 
  authenticateAdmin, 
  canModifyResource('orders'),
  logAdminActivity('add_order_notes', 'orders'),
  async (req, res) => {
    try {
      const id = parseInt(req.params.id || '0');
      const { notes }: { notes: string } = req.body;

      if (isNaN(id) || id === 0) {
        return res.error('ID inválido', 400);
      }

      if (!notes || notes.trim().length === 0) {
        return res.error('Notas são obrigatórias', 400);
      }

      const updatedOrder = await OrderModel.update(id, { notes: notes.trim() });
      
      if (!updatedOrder) {
        return res.error('Pedido não encontrado', 404);
      }

      res.success(updatedOrder, 'Notas adicionadas ao pedido com sucesso');
    } catch (error) {
      console.error('Add order notes error:', error);
      res.error('Erro ao adicionar notas ao pedido', 500);
    }
  }
);

/**
 * GET /api/admin/orders/:id/files
 * Get files associated with order
 */
router.get('/:id/files', 
  authenticateAdmin, 
  logAdminActivity('view_order_files', 'orders'),
  async (req, res) => {
    try {
      const id = parseInt(req.params.id || '0');

      if (isNaN(id) || id === 0) {
        return res.error('ID inválido', 400);
      }

      // Verify order exists
      const order = await OrderModel.findById(id);
      if (!order) {
        return res.error('Pedido não encontrado', 404);
      }

      const files = await FileModel.findByOrderId(id);

      res.success(files, 'Arquivos do pedido');
    } catch (error) {
      console.error('Get order files error:', error);
      res.error('Erro ao obter arquivos do pedido', 500);
    }
  }
);

/**
 * POST /api/admin/orders/bulk-update
 * Bulk update multiple orders
 */
router.post('/bulk-update', 
  authenticateAdmin, 
  canModifyResource('orders'),
  logAdminActivity('bulk_update_orders', 'orders'),
  async (req, res) => {
    try {
      const { orderIds, updateData }: { 
        orderIds: number[]; 
        updateData: UpdateOrderData 
      } = req.body;

      if (!orderIds || !Array.isArray(orderIds) || orderIds.length === 0) {
        return res.error('IDs de pedidos são obrigatórios', 400);
      }

      if (!updateData || Object.keys(updateData).length === 0) {
        return res.error('Dados de atualização são obrigatórios', 400);
      }

      // Validate status if provided
      if (updateData.status && !Object.values(OrderStatus).includes(updateData.status as OrderStatus)) {
        return res.error('Status inválido', 400);
      }

      const results = [];
      const errors = [];

      for (const orderId of orderIds) {
        try {
          const updatedOrder = await OrderModel.update(orderId, updateData);
          if (updatedOrder) {
            results.push(updatedOrder);
            
            // Send notification if status changed
            if (updateData.status && req.admin) {
              try {
                const notificationService = NotificationService.getInstance();
                await notificationService.sendOrderStatusNotification(
                  updatedOrder.id,
                  updatedOrder.status,
                  updatedOrder.customerEmail
                );
              } catch (notificationError) {
                console.error('Failed to send notification for order', orderId, notificationError);
              }
            }
          } else {
            errors.push({ orderId, error: 'Pedido não encontrado' });
          }
        } catch (error) {
          errors.push({ orderId, error: error instanceof Error ? error.message : 'Erro desconhecido' });
        }
      }

      res.success({
        updated: results,
        errors,
        totalProcessed: orderIds.length,
        successCount: results.length,
        errorCount: errors.length
      }, 'Atualização em lote concluída');
    } catch (error) {
      console.error('Bulk update orders error:', error);
      res.error('Erro na atualização em lote', 500);
    }
  }
);

export default router;
