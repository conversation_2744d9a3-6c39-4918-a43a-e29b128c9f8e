/**
 * File management routes
 * Handles file upload, validation, processing, and management
 */

import express, { Request, Response } from 'express';
import path from 'path';
import { FileModel } from '../models';
import {
  uploadSingle,
  uploadMultiple,
  handleUploadError,
  getRelativeFilePath,
  getAbsoluteFilePath,
  fileExists,
  deleteFile
} from '../middleware/upload';
import { validateFile, getFileCategory, formatFileSize } from '../utils/fileValidation';
import { extractPDFMetadata, calculatePrintEstimate } from '../utils/pdfProcessor';
import { CreateFileData } from '../types';

const router = express.Router();

// ============================================================================
// File Upload Routes
// ============================================================================

/**
 * Upload single file
 * POST /api/files/upload
 */
router.post('/upload', uploadSingle, handleUploadError, async (req: Request, res: Response) => {
  try {
    if (!req.file) {
      return res.error('Nenhum arquivo foi enviado', 400);
    }

    const file = req.file;
    const relativePath = getRelativeFilePath(file.path);

    // Prepare file info for validation
    const fileInfo = {
      filename: file.filename,
      originalName: file.originalname,
      mimetype: file.mimetype,
      size: file.size,
      path: file.path,
      extension: path.extname(file.originalname).toLowerCase()
    };

    // Validate file
    const validation = await validateFile(fileInfo);
    
    if (!validation.isValid) {
      // Delete uploaded file if validation fails
      await deleteFile(file.path);
      return res.error('Arquivo inválido', 400, {
        errors: validation.errors,
        warnings: validation.warnings
      });
    }

    // Process PDF metadata if it's a PDF
    let pdfMetadata = null;
    let estimatedCost = null;
    
    if (file.mimetype === 'application/pdf') {
      const pdfResult = await extractPDFMetadata(file.path);
      if (pdfResult.success && pdfResult.metadata) {
        pdfMetadata = pdfResult.metadata;
        estimatedCost = calculatePrintEstimate(pdfResult.metadata);
      }
    }

    // Save file to database
    const fileData: CreateFileData = {
      filename: file.filename,
      originalName: file.originalname,
      mimetype: file.mimetype,
      size: file.size,
      url: `/uploads/${relativePath}`
    };

    const savedFile = await FileModel.create(fileData);

    // Prepare response
    const response = {
      file: {
        id: savedFile.id,
        filename: savedFile.filename,
        originalName: savedFile.originalName,
        mimetype: savedFile.mimetype,
        size: savedFile.size,
        formattedSize: formatFileSize(savedFile.size),
        url: savedFile.url,
        category: getFileCategory(savedFile.mimetype),
        uploadedAt: savedFile.uploadedAt
      },
      validation: {
        warnings: validation.warnings,
        metadata: validation.metadata
      }
    };

    // Add PDF-specific data
    if (pdfMetadata) {
      (response as any).pdf = {
        pages: pdfMetadata.pages,
        hasText: pdfMetadata.hasText,
        hasImages: pdfMetadata.hasImages,
        title: pdfMetadata.title,
        author: pdfMetadata.author,
        estimatedCost
      };
    }

    res.success(response, 'Arquivo enviado com sucesso');

  } catch (error) {
    console.error('Error uploading file:', error);
    
    // Clean up uploaded file on error
    if (req.file) {
      await deleteFile(req.file.path);
    }
    
    res.error('Erro interno no upload do arquivo', 500);
  }
});

/**
 * Upload multiple files
 * POST /api/files/upload-multiple
 */
router.post('/upload-multiple', uploadMultiple, handleUploadError, async (req: Request, res: Response) => {
  try {
    if (!req.files || !Array.isArray(req.files) || req.files.length === 0) {
      return res.error('Nenhum arquivo foi enviado', 400);
    }

    const files = req.files as Express.Multer.File[];
    const results = [];
    const errors = [];

    for (const file of files) {
      try {
        const relativePath = getRelativeFilePath(file.path);

        // Prepare file info for validation
        const fileInfo = {
          filename: file.filename,
          originalName: file.originalname,
          mimetype: file.mimetype,
          size: file.size,
          path: file.path,
          extension: path.extname(file.originalname).toLowerCase()
        };

        // Validate file
        const validation = await validateFile(fileInfo);
        
        if (!validation.isValid) {
          // Delete uploaded file if validation fails
          await deleteFile(file.path);
          errors.push({
            filename: file.originalname,
            errors: validation.errors
          });
          continue;
        }

        // Process PDF metadata if it's a PDF
        let pdfMetadata = null;
        if (file.mimetype === 'application/pdf') {
          const pdfResult = await extractPDFMetadata(file.path);
          if (pdfResult.success && pdfResult.metadata) {
            pdfMetadata = pdfResult.metadata;
          }
        }

        // Save file to database
        const fileData: CreateFileData = {
          filename: file.filename,
          originalName: file.originalname,
          mimetype: file.mimetype,
          size: file.size,
          url: `/uploads/${relativePath}`
        };

        const savedFile = await FileModel.create(fileData);

        // Prepare result
        const result = {
          file: {
            id: savedFile.id,
            filename: savedFile.filename,
            originalName: savedFile.originalName,
            mimetype: savedFile.mimetype,
            size: savedFile.size,
            formattedSize: formatFileSize(savedFile.size),
            url: savedFile.url,
            category: getFileCategory(savedFile.mimetype),
            uploadedAt: savedFile.uploadedAt
          },
          validation: {
            warnings: validation.warnings
          }
        };

        // Add PDF-specific data
        if (pdfMetadata) {
          (result as any).pdf = {
            pages: pdfMetadata.pages,
            hasText: pdfMetadata.hasText,
            hasImages: pdfMetadata.hasImages
          };
        }

        results.push(result);

      } catch (error) {
        console.error(`Error processing file ${file.originalname}:`, error);
        
        // Clean up uploaded file on error
        await deleteFile(file.path);
        
        errors.push({
          filename: file.originalname,
          errors: ['Erro interno no processamento do arquivo']
        });
      }
    }

    const response = {
      uploaded: results,
      errors: errors.length > 0 ? errors : undefined,
      summary: {
        total: files.length,
        successful: results.length,
        failed: errors.length
      }
    };

    if (results.length > 0) {
      res.success(response, `${results.length} arquivo(s) enviado(s) com sucesso`);
    } else {
      res.error('Nenhum arquivo foi processado com sucesso', 400, response);
    }

  } catch (error) {
    console.error('Error uploading multiple files:', error);
    
    // Clean up all uploaded files on error
    if (req.files && Array.isArray(req.files)) {
      for (const file of req.files) {
        await deleteFile(file.path);
      }
    }
    
    res.error('Erro interno no upload dos arquivos', 500);
  }
});

/**
 * Validate file without uploading
 * POST /api/files/validate
 */
router.post('/validate', uploadSingle, handleUploadError, async (req: Request, res: Response) => {
  try {
    if (!req.file) {
      return res.error('Nenhum arquivo foi enviado para validação', 400);
    }

    const file = req.file;

    // Prepare file info for validation
    const fileInfo = {
      filename: file.filename,
      originalName: file.originalname,
      mimetype: file.mimetype,
      size: file.size,
      path: file.path,
      extension: path.extname(file.originalname).toLowerCase()
    };

    // Validate file
    const validation = await validateFile(fileInfo);

    // Process PDF metadata if it's a PDF and valid
    let pdfMetadata = null;
    let estimatedCost = null;
    
    if (validation.isValid && file.mimetype === 'application/pdf') {
      const pdfResult = await extractPDFMetadata(file.path);
      if (pdfResult.success && pdfResult.metadata) {
        pdfMetadata = pdfResult.metadata;
        estimatedCost = calculatePrintEstimate(pdfResult.metadata);
      }
    }

    // Always delete the validation file
    await deleteFile(file.path);

    const response = {
      validation: {
        isValid: validation.isValid,
        errors: validation.errors,
        warnings: validation.warnings,
        metadata: validation.metadata
      },
      file: {
        originalName: file.originalname,
        mimetype: file.mimetype,
        size: file.size,
        formattedSize: formatFileSize(file.size),
        category: getFileCategory(file.mimetype)
      }
    };

    // Add PDF-specific data
    if (pdfMetadata) {
      (response as any).pdf = {
        pages: pdfMetadata.pages,
        hasText: pdfMetadata.hasText,
        hasImages: pdfMetadata.hasImages,
        title: pdfMetadata.title,
        author: pdfMetadata.author,
        estimatedCost
      };
    }

    if (validation.isValid) {
      res.success(response, 'Arquivo válido para upload');
    } else {
      res.error('Arquivo inválido', 400, response);
    }

  } catch (error) {
    console.error('Error validating file:', error);
    
    // Clean up uploaded file on error
    if (req.file) {
      await deleteFile(req.file.path);
    }
    
    res.error('Erro interno na validação do arquivo', 500);
  }
});

// ============================================================================
// File Management Routes
// ============================================================================

/**
 * Get all files with pagination and filters
 * GET /api/files
 */
router.get('/', async (req: Request, res: Response) => {
  try {
    const limit = parseInt(req.query.limit as string) || 20;
    const offset = parseInt(req.query.offset as string) || 0;
    const mimetype = req.query.mimetype as string;
    const category = req.query.category as string;

    const result = await FileModel.findAll(limit, offset);

    // Filter by category if specified
    let filteredFiles = result.files;
    if (category) {
      filteredFiles = result.files.filter(file =>
        getFileCategory(file.mimetype) === category
      );
    }

    const response = {
      files: filteredFiles.map(file => ({
        id: file.id,
        filename: file.filename,
        originalName: file.originalName,
        mimetype: file.mimetype,
        size: file.size,
        formattedSize: formatFileSize(file.size),
        url: file.url,
        category: getFileCategory(file.mimetype),
        uploadedAt: file.uploadedAt
      })),
      pagination: {
        total: result.total,
        limit,
        offset,
        hasMore: offset + limit < result.total
      }
    };

    res.success(response, 'Arquivos recuperados com sucesso');

  } catch (error) {
    console.error('Error getting files:', error);
    res.error('Erro ao recuperar arquivos', 500);
  }
});

/**
 * Get file by ID
 * GET /api/files/:id
 */
router.get('/:id', async (req: Request, res: Response) => {
  try {
    const fileId = parseInt(req.params.id || '0');

    if (isNaN(fileId)) {
      return res.error('ID de arquivo inválido', 400);
    }

    const file = await FileModel.findById(fileId);

    if (!file) {
      return res.error('Arquivo não encontrado', 404);
    }

    // Check if physical file exists
    const absolutePath = getAbsoluteFilePath(file.url.replace('/uploads/', ''));
    const exists = fileExists(absolutePath);

    const response = {
      file: {
        id: file.id,
        filename: file.filename,
        originalName: file.originalName,
        mimetype: file.mimetype,
        size: file.size,
        formattedSize: formatFileSize(file.size),
        url: file.url,
        category: getFileCategory(file.mimetype),
        uploadedAt: file.uploadedAt,
        physicalFileExists: exists
      }
    };

    // Add PDF metadata if it's a PDF and file exists
    if (file.mimetype === 'application/pdf' && exists) {
      try {
        const pdfResult = await extractPDFMetadata(absolutePath);
        if (pdfResult.success && pdfResult.metadata) {
          (response as any).pdf = {
            pages: pdfResult.metadata.pages,
            hasText: pdfResult.metadata.hasText,
            hasImages: pdfResult.metadata.hasImages,
            title: pdfResult.metadata.title,
            author: pdfResult.metadata.author,
            estimatedCost: calculatePrintEstimate(pdfResult.metadata)
          };
        }
      } catch (error) {
        console.error('Error extracting PDF metadata:', error);
      }
    }

    res.success(response, 'Arquivo recuperado com sucesso');

  } catch (error) {
    console.error('Error getting file:', error);
    res.error('Erro ao recuperar arquivo', 500);
  }
});

/**
 * Download file
 * GET /api/files/:id/download
 */
router.get('/:id/download', async (req: Request, res: Response) => {
  try {
    const fileId = parseInt(req.params.id);

    if (isNaN(fileId)) {
      return res.error('ID de arquivo inválido', 400);
    }

    const file = await FileModel.findById(fileId);

    if (!file) {
      return res.error('Arquivo não encontrado', 404);
    }

    const absolutePath = getAbsoluteFilePath(file.url.replace('/uploads/', ''));

    if (!fileExists(absolutePath)) {
      return res.error('Arquivo físico não encontrado', 404);
    }

    // Set appropriate headers
    res.setHeader('Content-Disposition', `attachment; filename="${file.originalName}"`);
    res.setHeader('Content-Type', file.mimetype);

    // Send file
    res.sendFile(absolutePath);

  } catch (error) {
    console.error('Error downloading file:', error);
    res.error('Erro ao baixar arquivo', 500);
  }
});

/**
 * Delete file
 * DELETE /api/files/:id
 */
router.delete('/:id', async (req: Request, res: Response) => {
  try {
    const fileId = parseInt(req.params.id);

    if (isNaN(fileId)) {
      return res.error('ID de arquivo inválido', 400);
    }

    const file = await FileModel.findById(fileId);

    if (!file) {
      return res.error('Arquivo não encontrado', 404);
    }

    // Check if file is used in orders
    const isUsed = await FileModel.isUsedInOrders(fileId);

    if (isUsed) {
      return res.error('Arquivo não pode ser deletado pois está sendo usado em pedidos', 400);
    }

    // Delete from database (soft delete)
    const deleted = await FileModel.delete(fileId);

    if (!deleted) {
      return res.error('Erro ao deletar arquivo do banco de dados', 500);
    }

    // Optionally delete physical file
    const absolutePath = getAbsoluteFilePath(file.url.replace('/uploads/', ''));
    if (fileExists(absolutePath)) {
      await deleteFile(absolutePath);
    }

    res.success({ fileId }, 'Arquivo deletado com sucesso');

  } catch (error) {
    console.error('Error deleting file:', error);
    res.error('Erro ao deletar arquivo', 500);
  }
});

/**
 * Get storage statistics
 * GET /api/files/stats
 */
router.get('/stats', async (req: Request, res: Response) => {
  try {
    const stats = await FileModel.getStorageStats();

    const response = {
      storage: {
        totalFiles: stats.totalFiles,
        totalSize: stats.totalSize,
        formattedTotalSize: formatFileSize(stats.totalSize),
        averageSize: stats.averageSize,
        formattedAverageSize: formatFileSize(stats.averageSize)
      },
      mimetypes: stats.mimetypeStats.map(stat => ({
        mimetype: stat.mimetype,
        category: getFileCategory(stat.mimetype),
        count: stat.count,
        totalSize: stat.totalSize,
        formattedTotalSize: formatFileSize(stat.totalSize),
        percentage: Math.round((stat.count / stats.totalFiles) * 100)
      }))
    };

    res.success(response, 'Estatísticas recuperadas com sucesso');

  } catch (error) {
    console.error('Error getting storage stats:', error);
    res.error('Erro ao recuperar estatísticas', 500);
  }
});

export default router;
