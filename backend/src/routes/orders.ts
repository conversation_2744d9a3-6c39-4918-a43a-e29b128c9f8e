/**
 * Orders routes - Handle order management endpoints
 */

import { Router, Request, Response } from 'express';
import { OrderModel } from '../models';
import { CreateOrderData, UpdateOrderData, OrderStatus } from '../types';

// Global notification service (will be set from main app)
let notificationService: any = null;

export const setOrderNotificationService = (service: any) => {
  notificationService = service;
};

const router = Router();

/**
 * POST /api/orders
 * Create a new order
 */
router.post('/', async (req: Request, res: Response) => {
  try {
    const orderData: CreateOrderData = req.body;

    // Validate required fields
    if (!orderData.fileId || !orderData.format || !orderData.paperType || !orderData.finish) {
      return res.error('Missing required fields: fileId, format, paperType, finish', 400);
    }

    // Validate file exists (optional - could be done with foreign key constraint)
    // const file = await FileModel.findById(orderData.fileId);
    // if (!file) {
    //   return res.error('File not found', 404);
    // }

    const order = await OrderModel.create(orderData);

    // Send order creation notification
    if (notificationService && order) {
      try {
        notificationService.emitOrderStatusChange(order, OrderStatus.PENDING);
      } catch (error) {
        console.error('Failed to send order creation notification:', error);
      }
    }

    res.success({
      order,
      priceBreakdown: orderData.pages ? OrderModel.calculatePrice({
        pages: orderData.pages,
        copies: orderData.copies || 1,
        format: orderData.format,
        paperType: orderData.paperType,
        finish: orderData.finish,
        ...(orderData.hasColor !== undefined && { hasColor: orderData.hasColor }),
        ...(orderData.complexity !== undefined && { complexity: orderData.complexity })
      }) : null
    }, 'Order created successfully');
  } catch (error) {
    console.error('Error creating order:', error);
    res.error('Failed to create order', 500);
  }
});

/**
 * GET /api/orders
 * Get orders with pagination and filters
 */
router.get('/', async (req: Request, res: Response) => {
  try {
    const {
      page = '1',
      limit = '50',
      status,
      customerEmail,
      orderBy = 'created_at',
      orderDirection = 'DESC'
    } = req.query;

    const pageNum = parseInt(page as string);
    const limitNum = parseInt(limit as string);
    const offset = (pageNum - 1) * limitNum;

    const result = await OrderModel.findAll({
      limit: limitNum,
      offset,
      status: status as OrderStatus,
      customerEmail: customerEmail as string,
      orderBy: orderBy as 'created_at' | 'updated_at' | 'price',
      orderDirection: orderDirection as 'ASC' | 'DESC'
    });

    res.success({
      orders: result.orders,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total: result.total,
        totalPages: Math.ceil(result.total / limitNum)
      }
    }, 'Orders retrieved successfully');
  } catch (error) {
    console.error('Error fetching orders:', error);
    res.error('Failed to fetch orders', 500);
  }
});

/**
 * GET /api/orders/stats
 * Get order statistics
 */
router.get('/stats', async (req: Request, res: Response) => {
  try {
    const { period = 'month' } = req.query;
    
    const stats = await OrderModel.getStatistics(period as 'day' | 'week' | 'month' | 'year');
    
    res.success({ stats }, 'Statistics retrieved successfully');
  } catch (error) {
    console.error('Error fetching statistics:', error);
    res.error('Failed to fetch statistics', 500);
  }
});

/**
 * GET /api/orders/ready
 * Get orders ready for pickup/delivery
 */
router.get('/ready', async (req: Request, res: Response) => {
  try {
    const { limit = '20' } = req.query;
    const orders = await OrderModel.getReadyOrders(parseInt(limit as string));
    
    res.success({ orders }, 'Ready orders retrieved successfully');
  } catch (error) {
    console.error('Error fetching ready orders:', error);
    res.error('Failed to fetch ready orders', 500);
  }
});

/**
 * GET /api/orders/overdue
 * Get overdue orders
 */
router.get('/overdue', async (req: Request, res: Response) => {
  try {
    const orders = await OrderModel.getOverdueOrders();
    
    res.success({ orders }, 'Overdue orders retrieved successfully');
  } catch (error) {
    console.error('Error fetching overdue orders:', error);
    res.error('Failed to fetch overdue orders', 500);
  }
});

/**
 * GET /api/orders/customer/:email
 * Get orders by customer email
 */
router.get('/customer/:email', async (req: Request, res: Response) => {
  try {
    const { email } = req.params;
    const { limit = '50' } = req.query;
    
    const orders = await OrderModel.findByCustomerEmail(email!, parseInt(limit as string));

    res.success({ orders }, 'Customer orders retrieved successfully');
  } catch (error) {
    console.error('Error fetching customer orders:', error);
    res.error('Failed to fetch customer orders', 500);
  }
});

/**
 * GET /api/orders/:id
 * Get order by ID
 */
router.get('/:id', async (req: Request, res: Response) => {
  try {
    const orderId = parseInt(req.params.id || '0');
    
    if (!orderId) {
      return res.error('Invalid order ID', 400);
    }

    const order = await OrderModel.findByIdWithFile(orderId);
    
    if (!order) {
      return res.error('Order not found', 404);
    }

    res.success({ order }, 'Order retrieved successfully');
  } catch (error) {
    console.error('Error fetching order:', error);
    res.error('Failed to fetch order', 500);
  }
});

/**
 * PUT /api/orders/:id
 * Update order
 */
router.put('/:id', async (req: Request, res: Response) => {
  try {
    const orderId = parseInt(req.params.id || '0');
    
    if (!orderId) {
      return res.error('Invalid order ID', 400);
    }

    const updateData: UpdateOrderData = req.body;
    const order = await OrderModel.update(orderId, updateData);
    
    if (!order) {
      return res.error('Order not found', 404);
    }

    res.success({ order }, 'Order updated successfully');
  } catch (error) {
    console.error('Error updating order:', error);
    res.error('Failed to update order', 500);
  }
});

/**
 * PUT /api/orders/:id/status
 * Update order status
 */
router.put('/:id/status', async (req: Request, res: Response) => {
  try {
    const orderId = parseInt(req.params.id || '0');
    const { status, notes } = req.body;
    
    if (!orderId) {
      return res.error('Invalid order ID', 400);
    }

    if (!status) {
      return res.error('Status is required', 400);
    }

    const validStatuses: OrderStatus[] = [
      OrderStatus.PENDING,
      OrderStatus.CONFIRMED,
      OrderStatus.PROCESSING,
      OrderStatus.READY,
      OrderStatus.COMPLETED,
      OrderStatus.CANCELLED
    ];
    if (!validStatuses.includes(status)) {
      return res.error('Invalid status', 400);
    }

    // Get current order to check status change
    const currentOrder = await OrderModel.findById(orderId);
    if (!currentOrder) {
      return res.error('Order not found', 404);
    }

    const order = await OrderModel.updateStatus(orderId, status, notes);

    if (!order) {
      return res.error('Order not found', 404);
    }

    // Send status change notification if status actually changed
    if (notificationService && currentOrder.status !== status) {
      try {
        notificationService.emitOrderStatusChange(order, status);
      } catch (error) {
        console.error('Failed to send order status change notification:', error);
      }
    }

    res.success({ order }, 'Order status updated successfully');
  } catch (error) {
    console.error('Error updating order status:', error);
    if (error instanceof Error && error.message.includes('Invalid status transition')) {
      res.error(error.message, 400);
    } else {
      res.error('Failed to update order status', 500);
    }
  }
});

/**
 * POST /api/orders/calculate-price
 * Calculate order price without creating order
 */
router.post('/calculate-price', async (req: Request, res: Response) => {
  try {
    const { pages, copies = 1, format, paperType, finish, hasColor, complexity } = req.body;

    if (!pages || !format || !paperType || !finish) {
      return res.error('Missing required fields: pages, format, paperType, finish', 400);
    }

    const priceCalculation = OrderModel.calculatePrice({
      pages,
      copies,
      format,
      paperType,
      finish,
      hasColor,
      complexity
    });

    res.success({ priceCalculation }, 'Price calculated successfully');
  } catch (error) {
    console.error('Error calculating price:', error);
    res.error('Failed to calculate price', 500);
  }
});

/**
 * DELETE /api/orders/:id
 * Delete order (soft delete)
 */
router.delete('/:id', async (req: Request, res: Response) => {
  try {
    const orderId = parseInt(req.params.id || '0');
    
    if (!orderId) {
      return res.error('Invalid order ID', 400);
    }

    const success = await OrderModel.delete(orderId);
    
    if (!success) {
      return res.error('Order not found', 404);
    }

    res.success('Order deleted successfully');
  } catch (error) {
    console.error('Error deleting order:', error);
    res.error('Failed to delete order', 500);
  }
});

export default router;
