import { Router } from 'express';
import { NotificationModel } from '../models/Notification';
import { NotificationService } from '../services/NotificationService';
import { 
  NotificationType, 
  NotificationChannel, 
  NotificationStatus,
  CreateNotificationData 
} from '../types';

const router = Router();

// Get notification service instance (will be injected)
let notificationService: NotificationService;

export const setNotificationService = (service: NotificationService) => {
  notificationService = service;
};

/**
 * GET /api/notifications
 * Get notifications with filters and pagination
 */
router.get('/', async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      type,
      channel,
      status,
      recipientEmail,
      orderId
    } = req.query;

    const options: any = {
      page: parseInt(page as string),
      limit: parseInt(limit as string),
      type: type as NotificationType,
      channel: channel as NotificationChannel,
      status: status as NotificationStatus,
      recipientEmail: recipientEmail as string
    };

    if (orderId) {
      options.orderId = parseInt(orderId as string);
    }

    const result = await NotificationModel.findMany(options);

    res.paginated(result.notifications, {
      total: result.total,
      page: options.page,
      limit: options.limit,
      message: 'Notificações recuperadas com sucesso'
    });
  } catch (error) {
    console.error('Error fetching notifications:', error);
    res.error('Erro ao buscar notificações', 500);
  }
});

/**
 * GET /api/notifications/:id
 * Get notification by ID
 */
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const notificationId = parseInt(id);

    if (isNaN(notificationId)) {
      return res.error('ID de notificação inválido', 400);
    }

    const notification = await NotificationModel.findById(notificationId);

    if (!notification) {
      return res.error('Notificação não encontrada', 404);
    }

    res.success(notification, 'Notificação encontrada');
  } catch (error) {
    console.error('Error fetching notification:', error);
    res.error('Erro ao buscar notificação', 500);
  }
});

/**
 * POST /api/notifications
 * Create a new notification
 */
router.post('/', async (req, res) => {
  try {
    const {
      type,
      channel,
      recipientEmail,
      recipientName,
      subject,
      content,
      templateData,
      orderId,
      fileId,
      maxRetries
    } = req.body;

    // Validate required fields
    if (!type || !channel || !recipientEmail || !subject || !content) {
      return res.error('Campos obrigatórios: type, channel, recipientEmail, subject, content', 400);
    }

    // Validate enum values
    if (!Object.values(NotificationType).includes(type)) {
      return res.error('Tipo de notificação inválido', 400);
    }

    if (!Object.values(NotificationChannel).includes(channel)) {
      return res.error('Canal de notificação inválido', 400);
    }

    const notificationData: CreateNotificationData = {
      type,
      channel,
      recipientEmail,
      recipientName,
      subject,
      content,
      templateData,
      orderId,
      fileId,
      maxRetries
    };

    const notification = await NotificationModel.create(notificationData);

    res.status(201);
    res.success(notification, 'Notificação criada com sucesso');
  } catch (error) {
    console.error('Error creating notification:', error);
    res.error('Erro ao criar notificação', 500);
  }
});

/**
 * POST /api/notifications/send
 * Send notification immediately (bypass queue)
 */
router.post('/send', async (req, res) => {
  try {
    if (!notificationService) {
      return res.error('Serviço de notificações não disponível', 503);
    }

    const {
      type,
      channel,
      recipientEmail,
      recipientName,
      subject,
      content,
      templateData,
      orderId,
      fileId
    } = req.body;

    // Validate required fields
    if (!type || !channel || !recipientEmail || !subject || !content) {
      return res.error('Campos obrigatórios: type, channel, recipientEmail, subject, content', 400);
    }

    const notificationData: CreateNotificationData = {
      type,
      channel,
      recipientEmail,
      recipientName,
      subject,
      content,
      templateData,
      orderId,
      fileId
    };

    const success = await notificationService.sendImmediateNotification(notificationData);

    if (success) {
      res.success({ sent: true }, 'Notificação enviada com sucesso');
    } else {
      res.error('Falha ao enviar notificação', 500);
    }
  } catch (error) {
    console.error('Error sending immediate notification:', error);
    res.error('Erro ao enviar notificação', 500);
  }
});

/**
 * PUT /api/notifications/:id
 * Update notification status
 */
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const notificationId = parseInt(id);

    if (isNaN(notificationId)) {
      return res.error('ID de notificação inválido', 400);
    }

    const {
      status,
      sentAt,
      deliveredAt,
      readAt,
      errorMessage,
      retryCount
    } = req.body;

    // Validate status if provided
    if (status && !Object.values(NotificationStatus).includes(status)) {
      return res.error('Status de notificação inválido', 400);
    }

    const updateData: any = {};

    if (status !== undefined) updateData.status = status;
    if (sentAt !== undefined) updateData.sentAt = new Date(sentAt);
    if (deliveredAt !== undefined) updateData.deliveredAt = new Date(deliveredAt);
    if (readAt !== undefined) updateData.readAt = new Date(readAt);
    if (errorMessage !== undefined) updateData.errorMessage = errorMessage;
    if (retryCount !== undefined) updateData.retryCount = retryCount;

    const notification = await NotificationModel.update(notificationId, updateData);

    if (!notification) {
      return res.error('Notificação não encontrada', 404);
    }

    res.success(notification, 'Notificação atualizada com sucesso');
  } catch (error) {
    console.error('Error updating notification:', error);
    res.error('Erro ao atualizar notificação', 500);
  }
});

/**
 * POST /api/notifications/:id/mark-read
 * Mark notification as read
 */
router.post('/:id/mark-read', async (req, res) => {
  try {
    const { id } = req.params;
    const notificationId = parseInt(id);

    if (isNaN(notificationId)) {
      return res.error('ID de notificação inválido', 400);
    }

    const notification = await NotificationModel.update(notificationId, {
      status: NotificationStatus.READ,
      readAt: new Date()
    });

    if (!notification) {
      return res.error('Notificação não encontrada', 404);
    }

    res.success(notification, 'Notificação marcada como lida');
  } catch (error) {
    console.error('Error marking notification as read:', error);
    res.error('Erro ao marcar notificação como lida', 500);
  }
});

/**
 * GET /api/notifications/stats
 * Get notification statistics
 */
router.get('/stats/overview', async (req, res) => {
  try {
    const { days = 30 } = req.query;
    const daysNumber = parseInt(days as string);

    if (!notificationService) {
      return res.error('Serviço de notificações não disponível', 503);
    }

    const stats = await notificationService.getStatistics(daysNumber);

    res.success(stats, 'Estatísticas de notificações recuperadas');
  } catch (error) {
    console.error('Error fetching notification stats:', error);
    res.error('Erro ao buscar estatísticas de notificações', 500);
  }
});

/**
 * GET /api/notifications/pending
 * Get pending notifications
 */
router.get('/queue/pending', async (req, res) => {
  try {
    const { limit = 50 } = req.query;
    const limitNumber = parseInt(limit as string);

    const notifications = await NotificationModel.getPendingNotifications(limitNumber);

    res.success(notifications, 'Notificações pendentes recuperadas');
  } catch (error) {
    console.error('Error fetching pending notifications:', error);
    res.error('Erro ao buscar notificações pendentes', 500);
  }
});

/**
 * GET /api/notifications/failed
 * Get failed notifications that can be retried
 */
router.get('/queue/failed', async (req, res) => {
  try {
    const { limit = 20 } = req.query;
    const limitNumber = parseInt(limit as string);

    const notifications = await NotificationModel.getRetryableNotifications(limitNumber);

    res.success(notifications, 'Notificações falhadas recuperadas');
  } catch (error) {
    console.error('Error fetching failed notifications:', error);
    res.error('Erro ao buscar notificações falhadas', 500);
  }
});

/**
 * POST /api/notifications/cleanup
 * Cleanup old notifications
 */
router.post('/cleanup', async (req, res) => {
  try {
    const { daysOld = 90 } = req.body;

    if (!notificationService) {
      return res.error('Serviço de notificações não disponível', 503);
    }

    const deletedCount = await notificationService.cleanupOldNotifications(daysOld);

    res.success(
      { deletedCount }, 
      `${deletedCount} notificações antigas foram removidas`
    );
  } catch (error) {
    console.error('Error cleaning up notifications:', error);
    res.error('Erro ao limpar notificações antigas', 500);
  }
});

/**
 * POST /api/notifications/test-email
 * Test email configuration
 */
router.post('/test-email', async (req, res) => {
  try {
    const { email, name = 'Teste' } = req.body;

    if (!email) {
      return res.error('Email é obrigatório', 400);
    }

    if (!notificationService) {
      return res.error('Serviço de notificações não disponível', 503);
    }

    const success = await notificationService.sendImmediateNotification({
      type: NotificationType.SYSTEM_ALERT,
      channel: NotificationChannel.EMAIL,
      recipientEmail: email,
      recipientName: name,
      subject: 'Teste de Email - WePrint AI',
      content: `
        <h2>Teste de Email</h2>
        <p>Este é um email de teste do sistema WePrint AI.</p>
        <p>Se você recebeu este email, a configuração está funcionando corretamente!</p>
        <p><strong>Data/Hora:</strong> ${new Date().toLocaleString('pt-PT')}</p>
      `
    });

    if (success) {
      res.success({ sent: true }, 'Email de teste enviado com sucesso');
    } else {
      res.error('Falha ao enviar email de teste', 500);
    }
  } catch (error) {
    console.error('Error sending test email:', error);
    res.error('Erro ao enviar email de teste', 500);
  }
});

export default router;
