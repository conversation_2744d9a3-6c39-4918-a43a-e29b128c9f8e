/**
 * Payment Routes - Handles payment-related API endpoints
 */

import express from 'express';
import rateLimit from 'express-rate-limit';
import { PaymentModel, InvoiceModel, RefundModel } from '../models';
import { MulticaixaService, MulticaixaPaymentStatus } from '../services/MulticaixaService';
import { adminAuth } from '../middleware/adminAuth';
import { PaymentStatus, RefundStatus } from '../types';

// Helper function to map Multicaixa status to our PaymentStatus
function mapMulticaixaStatusToPaymentStatus(multicaixaStatus: MulticaixaPaymentStatus): PaymentStatus {
  switch (multicaixaStatus) {
    case MulticaixaPaymentStatus.PENDING:
      return PaymentStatus.PENDING;
    case MulticaixaPaymentStatus.PROCESSING:
      return PaymentStatus.PROCESSING;
    case MulticaixaPaymentStatus.COMPLETED:
      return PaymentStatus.COMPLETED;
    case MulticaixaPaymentStatus.FAILED:
      return PaymentStatus.FAILED;
    case MulticaixaPaymentStatus.CANCELLED:
      return PaymentStatus.CANCELLED;
    case MulticaixaPaymentStatus.EXPIRED:
      return PaymentStatus.EXPIRED;
    default:
      return PaymentStatus.PENDING;
  }
}

const router = express.Router();

// Rate limiting for payment endpoints
const paymentRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10, // limit each IP to 10 requests per windowMs
  message: {
    error: 'Too many payment requests from this IP, please try again later.'
  }
});

const refundRateLimit = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 5, // limit each IP to 5 refund requests per hour
  message: {
    error: 'Too many refund requests from this IP, please try again later.'
  }
});

/**
 * POST /api/payments/create
 * Create a payment with Multicaixa Express
 */
router.post('/create', paymentRateLimit, async (req, res) => {
  try {
    const { orderId, amount, currency = 'AOA', customerEmail, customerPhone, description } = req.body;

    // Validate required fields
    if (!orderId || !amount || !customerEmail) {
      return res.error('Missing required fields: orderId, amount, customerEmail', 400);
    }

    // Validate amount (minimum 100 AOA)
    if (amount < 100) {
      return res.error('Amount must be at least 100 AOA', 400);
    }

    // Check if payment already exists for this order
    const existingPayments = await PaymentModel.findByOrderId(orderId);
    const pendingPayment = existingPayments.find(p =>
      p.status === PaymentStatus.PENDING || p.status === PaymentStatus.PROCESSING
    );

    if (pendingPayment) {
      return res.error('A payment is already pending for this order', 400, {
        paymentId: pendingPayment.id
      });
    }

    // Create payment with Multicaixa Express
    const multicaixaPayment = await MulticaixaService.createPayment({
      amount,
      currency,
      orderId,
      customerEmail,
      customerPhone,
      description: description || `WePrint AI - Pedido #${orderId}`,
      metadata: {
        orderId: orderId.toString(),
        customerEmail
      }
    });

    // Create payment record in database
    const payment = await PaymentModel.create({
      orderId,
      customerEmail,
      multicaixaTransactionId: multicaixaPayment.transactionId,
      multicaixaPaymentUrl: multicaixaPayment.paymentUrl,
      amount,
      currency,
      status: PaymentStatus.PENDING,
      paymentMethodType: 'MULTICAIXA_EXPRESS',
      description: description || `Payment for order #${orderId}`
    });

    res.success({
      paymentId: payment.id,
      transactionId: multicaixaPayment.transactionId,
      paymentUrl: multicaixaPayment.paymentUrl,
      qrCode: multicaixaPayment.qrCode,
      amount: payment.amount,
      currency: payment.currency,
      expiresAt: multicaixaPayment.expiresAt
    }, 'Payment created successfully');

  } catch (error) {
    console.error('Error creating Multicaixa payment:', error);
    const message = error instanceof Error ? error.message : 'Unknown error';
    res.error('Failed to create payment', 500, {
      details: process.env.NODE_ENV === 'development' ? message : undefined
    });
  }
});

/**
 * POST /api/payments/multicaixa/webhook
 * Handle Multicaixa webhook notifications
 */
router.post('/multicaixa/webhook', async (req, res) => {
  try {
    const webhookPayload = req.body;

    // Verify webhook signature
    if (!MulticaixaService.verifyWebhookSignature(webhookPayload)) {
      return res.error('Invalid webhook signature', 401);
    }

    // Process webhook data
    const webhookData = MulticaixaService.processWebhook(webhookPayload);

    // Find payment in database
    const payment = await PaymentModel.findByMulticaixaTransactionId(webhookData.transactionId);
    if (!payment) {
      return res.error('Payment not found', 404);
    }

    // Update payment status based on webhook
    const mappedStatus = mapMulticaixaStatusToPaymentStatus(webhookData.status);
    const updateData: any = {
      status: mappedStatus
    };

    if (webhookData.status === MulticaixaPaymentStatus.COMPLETED && webhookData.paidAt) {
      updateData.paidAt = webhookData.paidAt;
    } else if (webhookData.status === MulticaixaPaymentStatus.FAILED) {
      updateData.failedAt = new Date();
      updateData.failureReason = webhookData.failureReason;
    } else if (webhookData.status === MulticaixaPaymentStatus.EXPIRED) {
      updateData.expiredAt = new Date();
    }

    const updatedPayment = await PaymentModel.update(payment.id, updateData);

    // Send success response to Multicaixa
    res.success({ received: true }, 'Webhook processed successfully');

  } catch (error) {
    console.error('Error processing Multicaixa webhook:', error);
    const message = error instanceof Error ? error.message : 'Unknown error';
    res.error('Failed to process webhook', 500, {
      details: process.env.NODE_ENV === 'development' ? message : undefined
    });
  }
});

/**
 * POST /api/payments/:id/check-status
 * Check payment status with Multicaixa
 */
router.post('/:id/check-status', async (req, res) => {
  try {
    const paymentId = parseInt(req.params.id!);
    if (isNaN(paymentId)) {
      return res.error('Invalid payment ID', 400);
    }

    const payment = await PaymentModel.findById(paymentId);
    if (!payment) {
      return res.error('Payment not found', 404);
    }

    if (!payment.multicaixaTransactionId) {
      return res.error('No Multicaixa transaction ID found', 400);
    }

    // Check status with Multicaixa
    const currentMulticaixaStatus = await MulticaixaService.getPaymentStatus(payment.multicaixaTransactionId);
    const currentMappedStatus = mapMulticaixaStatusToPaymentStatus(currentMulticaixaStatus);

    // Update payment if status changed
    if (currentMappedStatus !== payment.status) {
      const updateData: any = { status: currentMappedStatus };

      if (currentMulticaixaStatus === MulticaixaPaymentStatus.COMPLETED) {
        updateData.paidAt = new Date();
      } else if (currentMulticaixaStatus === MulticaixaPaymentStatus.FAILED) {
        updateData.failedAt = new Date();
      } else if (currentMulticaixaStatus === MulticaixaPaymentStatus.EXPIRED) {
        updateData.expiredAt = new Date();
      }

      const updatedPayment = await PaymentModel.update(paymentId, updateData);
      return res.success(updatedPayment, 'Payment status updated');
    }

    res.success(payment, 'Payment status checked');

  } catch (error) {
    console.error('Error checking payment status:', error);
    const message = error instanceof Error ? error.message : 'Unknown error';
    res.error('Failed to check payment status', 500, {
      details: process.env.NODE_ENV === 'development' ? message : undefined
    });
  }
});

/**
 * GET /api/payments/:id
 * Get payment details
 */
router.get('/:id', async (req, res) => {
  try {
    const paymentId = parseInt(req.params.id!);

    if (isNaN(paymentId)) {
      return res.error('Invalid payment ID', 400);
    }

    const payment = await PaymentModel.findById(paymentId);
    if (!payment) {
      return res.error('Payment not found', 404);
    }

    // Get related invoices and refunds
    const invoices = await InvoiceModel.findByPaymentId(paymentId);
    const refunds = await RefundModel.findByPaymentId(paymentId);

    res.success({
      payment,
      invoices,
      refunds
    }, 'Payment details retrieved successfully');

  } catch (error) {
    console.error('Error fetching payment:', error);
    const message = error instanceof Error ? error.message : 'Unknown error';
    res.error('Failed to fetch payment', 500, {
      details: process.env.NODE_ENV === 'development' ? message : undefined
    });
  }
});

/**
 * GET /api/payments/order/:orderId
 * Get payments for an order
 */
router.get('/order/:orderId', async (req, res) => {
  try {
    const orderId = parseInt(req.params.orderId!);

    if (isNaN(orderId)) {
      return res.error('Invalid order ID', 400);
    }

    const payments = await PaymentModel.findByOrderId(orderId);

    res.success(payments, 'Payments retrieved successfully');

  } catch (error) {
    console.error('Error fetching payments for order:', error);
    const message = error instanceof Error ? error.message : 'Unknown error';
    res.error('Failed to fetch payments for order', 500, {
      details: process.env.NODE_ENV === 'development' ? message : undefined
    });
  }
});

/**
 * POST /api/payments/:id/refund
 * Create a refund for a payment (Admin only)
 */
router.post('/:id/refund', adminAuth, refundRateLimit, async (req, res) => {
  try {
    const paymentId = parseInt(req.params.id!);
    const { amount, reason, description } = req.body;
    const adminId = req.admin?.id;

    if (isNaN(paymentId)) {
      return res.status(400).json({
        error: 'Invalid payment ID'
      });
    }

    // Find payment
    const payment = await PaymentModel.findById(paymentId);
    if (!payment) {
      return res.status(404).json({
        error: 'Payment not found'
      });
    }

    // Check if payment is refundable
    if (payment.status !== PaymentStatus.COMPLETED) {
      return res.error('Payment must be completed to be refunded', 400);
    }

    // Check refund amount
    const totalRefunded = await RefundModel.getTotalRefundedForPayment(paymentId);
    const maxRefundAmount = payment.amount - totalRefunded;

    if (amount > maxRefundAmount) {
      return res.status(400).json({
        error: `Refund amount cannot exceed ${maxRefundAmount} cents (already refunded: ${totalRefunded} cents)`
      });
    }

    // For Multicaixa, refunds need to be processed manually
    // Create refund record with pending status
    const refundData: any = {
      paymentId,
      amount,
      currency: payment.currency,
      reason: reason || 'requested_by_customer',
      status: RefundStatus.PENDING,
      description: description || `Refund for payment #${paymentId}`
    };

    if (adminId !== undefined) {
      refundData.createdBy = adminId;
    }

    const refund = await RefundModel.create(refundData);

    res.success(refund, 'Refund request created successfully');

  } catch (error) {
    console.error('Error creating refund:', error);
    const message = error instanceof Error ? error.message : 'Unknown error';
    res.error('Failed to create refund', 500, {
      details: process.env.NODE_ENV === 'development' ? message : undefined
    });
  }
});

/**
 * GET /api/payments
 * Get all payments with pagination (Admin only)
 */
router.get('/', adminAuth, async (req, res) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 50;
    const status = req.query.status as PaymentStatus;
    const offset = (page - 1) * limit;

    const result = await PaymentModel.findAll(offset, limit, status);

    res.json({
      success: true,
      data: result.payments,
      pagination: {
        page,
        limit,
        total: result.total,
        pages: Math.ceil(result.total / limit)
      }
    });

  } catch (error) {
    console.error('Error fetching payments:', error);
    const message = error instanceof Error ? error.message : 'Unknown error';
    res.error('Failed to fetch payments', 500, {
      details: process.env.NODE_ENV === 'development' ? message : undefined
    });
  }
});

/**
 * GET /api/payments/stats
 * Get payment statistics (Admin only)
 */
router.get('/stats', adminAuth, async (req, res) => {
  try {
    const stats = await PaymentModel.getStatistics();

    res.json({
      success: true,
      data: stats
    });

  } catch (error) {
    console.error('Error fetching payment statistics:', error);
    const message = error instanceof Error ? error.message : 'Unknown error';
    res.error('Failed to fetch payment statistics', 500, {
      details: process.env.NODE_ENV === 'development' ? message : undefined
    });
  }
});

export default router;
