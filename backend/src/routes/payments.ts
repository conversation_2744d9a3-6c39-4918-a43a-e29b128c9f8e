/**
 * Payment Routes - Handles payment-related API endpoints
 */

import express from 'express';
import rateLimit from 'express-rate-limit';
import { PaymentModel, InvoiceModel, RefundModel } from '../models';
import { StripeService } from '../services/StripeService';
import { adminAuth } from '../middleware/adminAuth';
import { PaymentStatus, RefundStatus } from '../types';

const router = express.Router();

// Rate limiting for payment endpoints
const paymentRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10, // limit each IP to 10 requests per windowMs
  message: {
    error: 'Too many payment requests from this IP, please try again later.'
  }
});

const refundRateLimit = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 5, // limit each IP to 5 refund requests per hour
  message: {
    error: 'Too many refund requests from this IP, please try again later.'
  }
});

/**
 * POST /api/payments/create-intent
 * Create a payment intent with Stripe
 */
router.post('/create-intent', paymentRateLimit, async (req, res) => {
  try {
    const { orderId, amount, currency = 'EUR', customerEmail, description } = req.body;

    // Validate required fields
    if (!orderId || !amount || !customerEmail) {
      return res.status(400).json({
        error: 'Missing required fields: orderId, amount, customerEmail'
      });
    }

    // Validate amount (minimum 50 cents)
    if (amount < 50) {
      return res.status(400).json({
        error: 'Amount must be at least 50 cents'
      });
    }

    // Check if payment already exists for this order
    const existingPayments = await PaymentModel.findByOrderId(orderId);
    const pendingPayment = existingPayments.find(p => 
      p.status === PaymentStatus.PENDING || p.status === PaymentStatus.PROCESSING
    );

    if (pendingPayment) {
      return res.status(400).json({
        error: 'A payment is already pending for this order',
        paymentId: pendingPayment.id
      });
    }

    // Create payment intent with Stripe
    const paymentIntent = await StripeService.createPaymentIntent({
      amount,
      currency,
      metadata: {
        orderId: orderId.toString(),
        customerEmail
      }
    });

    // Create payment record in database
    const payment = await PaymentModel.create({
      orderId,
      customerEmail,
      stripePaymentIntentId: paymentIntent.id,
      amount,
      currency,
      status: PaymentStatus.PENDING,
      description: description || `Payment for order #${orderId}`
    });

    res.status(201).json({
      success: true,
      data: {
        paymentId: payment.id,
        clientSecret: paymentIntent.client_secret,
        amount: payment.amount,
        currency: payment.currency
      }
    });

  } catch (error) {
    console.error('Error creating payment intent:', error);
    res.status(500).json({
      error: 'Failed to create payment intent',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * POST /api/payments/confirm
 * Confirm a payment intent
 */
router.post('/confirm', paymentRateLimit, async (req, res) => {
  try {
    const { paymentIntentId, paymentMethodId } = req.body;

    if (!paymentIntentId) {
      return res.status(400).json({
        error: 'Missing required field: paymentIntentId'
      });
    }

    // Find payment in database
    const payment = await PaymentModel.findByStripePaymentIntentId(paymentIntentId);
    if (!payment) {
      return res.status(404).json({
        error: 'Payment not found'
      });
    }

    // Confirm payment intent with Stripe
    const confirmedIntent = await StripeService.confirmPaymentIntent(paymentIntentId, paymentMethodId);

    // Update payment record
    const updatedPayment = await PaymentModel.update(payment.id, {
      status: confirmedIntent.status as PaymentStatus,
      stripePaymentMethodId: paymentMethodId,
      paymentMethodType: confirmedIntent.payment_method?.type,
      paidAt: confirmedIntent.status === 'succeeded' ? new Date() : undefined,
      failedAt: confirmedIntent.status === 'failed' ? new Date() : undefined,
      failureReason: confirmedIntent.last_payment_error?.message,
      failureCode: confirmedIntent.last_payment_error?.code,
      receiptUrl: confirmedIntent.charges?.data[0]?.receipt_url
    });

    res.json({
      success: true,
      data: {
        payment: updatedPayment,
        status: confirmedIntent.status
      }
    });

  } catch (error) {
    console.error('Error confirming payment:', error);
    res.status(500).json({
      error: 'Failed to confirm payment',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * GET /api/payments/:id
 * Get payment details
 */
router.get('/:id', async (req, res) => {
  try {
    const paymentId = parseInt(req.params.id);

    if (isNaN(paymentId)) {
      return res.status(400).json({
        error: 'Invalid payment ID'
      });
    }

    const payment = await PaymentModel.findById(paymentId);
    if (!payment) {
      return res.status(404).json({
        error: 'Payment not found'
      });
    }

    // Get related invoices and refunds
    const invoices = await InvoiceModel.findByPaymentId(paymentId);
    const refunds = await RefundModel.findByPaymentId(paymentId);

    res.json({
      success: true,
      data: {
        payment,
        invoices,
        refunds
      }
    });

  } catch (error) {
    console.error('Error fetching payment:', error);
    res.status(500).json({
      error: 'Failed to fetch payment',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * GET /api/payments/order/:orderId
 * Get payments for an order
 */
router.get('/order/:orderId', async (req, res) => {
  try {
    const orderId = parseInt(req.params.orderId);

    if (isNaN(orderId)) {
      return res.status(400).json({
        error: 'Invalid order ID'
      });
    }

    const payments = await PaymentModel.findByOrderId(orderId);

    res.json({
      success: true,
      data: payments
    });

  } catch (error) {
    console.error('Error fetching payments for order:', error);
    res.status(500).json({
      error: 'Failed to fetch payments for order',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * POST /api/payments/:id/refund
 * Create a refund for a payment (Admin only)
 */
router.post('/:id/refund', adminAuth, refundRateLimit, async (req, res) => {
  try {
    const paymentId = parseInt(req.params.id);
    const { amount, reason, description } = req.body;
    const adminId = req.admin?.id;

    if (isNaN(paymentId)) {
      return res.status(400).json({
        error: 'Invalid payment ID'
      });
    }

    // Find payment
    const payment = await PaymentModel.findById(paymentId);
    if (!payment) {
      return res.status(404).json({
        error: 'Payment not found'
      });
    }

    // Check if payment is refundable
    if (payment.status !== PaymentStatus.SUCCEEDED) {
      return res.status(400).json({
        error: 'Payment must be succeeded to be refunded'
      });
    }

    // Check refund amount
    const totalRefunded = await RefundModel.getTotalRefundedForPayment(paymentId);
    const maxRefundAmount = payment.amount - totalRefunded;

    if (amount > maxRefundAmount) {
      return res.status(400).json({
        error: `Refund amount cannot exceed ${maxRefundAmount} cents (already refunded: ${totalRefunded} cents)`
      });
    }

    // Create refund with Stripe
    const stripeRefund = await StripeService.createRefund({
      paymentIntentId: payment.stripePaymentIntentId,
      amount,
      reason: reason || 'requested_by_customer'
    });

    // Create refund record
    const refund = await RefundModel.create({
      paymentId,
      stripeRefundId: stripeRefund.id,
      amount,
      currency: payment.currency,
      reason,
      status: stripeRefund.status as RefundStatus,
      description,
      createdBy: adminId
    });

    res.status(201).json({
      success: true,
      data: refund
    });

  } catch (error) {
    console.error('Error creating refund:', error);
    res.status(500).json({
      error: 'Failed to create refund',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * GET /api/payments
 * Get all payments with pagination (Admin only)
 */
router.get('/', adminAuth, async (req, res) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 50;
    const status = req.query.status as PaymentStatus;
    const offset = (page - 1) * limit;

    const result = await PaymentModel.findAll(offset, limit, status);

    res.json({
      success: true,
      data: result.payments,
      pagination: {
        page,
        limit,
        total: result.total,
        pages: Math.ceil(result.total / limit)
      }
    });

  } catch (error) {
    console.error('Error fetching payments:', error);
    res.status(500).json({
      error: 'Failed to fetch payments',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * GET /api/payments/stats
 * Get payment statistics (Admin only)
 */
router.get('/stats', adminAuth, async (req, res) => {
  try {
    const stats = await PaymentModel.getStatistics();

    res.json({
      success: true,
      data: stats
    });

  } catch (error) {
    console.error('Error fetching payment statistics:', error);
    res.status(500).json({
      error: 'Failed to fetch payment statistics',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

export default router;
