import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import database from '../database';
import { 
  Admin, 
  CreateAdminData, 
  UpdateAdminData, 
  AdminRole, 
  AdminStatus,
  AdminLoginData,
  AdminAuthResponse 
} from '../types';

export class AdminModel {
  /**
   * Create a new admin
   */
  static async create(data: CreateAdminData): Promise<Admin> {
    const hashedPassword = await bcrypt.hash(data.password, 12);
    
    const query = `
      INSERT INTO admins (email, name, password, role, status)
      VALUES ($1, $2, $3, $4, $5)
      RETURNING id, email, name, role, status, last_login, created_at, updated_at
    `;
    
    const values = [
      data.email,
      data.name,
      hashedPassword,
      data.role,
      data.status || AdminStatus.ACTIVE
    ];
    
    const result = await database.query(query, values);
    return this.mapRowToAdmin(result.rows[0]);
  }

  /**
   * Find admin by ID
   */
  static async findById(id: number): Promise<Admin | null> {
    const query = `
      SELECT id, email, name, role, status, last_login, created_at, updated_at
      FROM admins 
      WHERE id = $1 AND deleted_at IS NULL
    `;
    
    const result = await database.query(query, [id]);
    return result.rows[0] ? this.mapRowToAdmin(result.rows[0]) : null;
  }

  /**
   * Find admin by email
   */
  static async findByEmail(email: string): Promise<Admin | null> {
    const query = `
      SELECT id, email, name, role, status, last_login, created_at, updated_at
      FROM admins 
      WHERE email = $1 AND deleted_at IS NULL
    `;
    
    const result = await database.query(query, [email]);
    return result.rows[0] ? this.mapRowToAdmin(result.rows[0]) : null;
  }

  /**
   * Find admin by email with password (for authentication)
   */
  static async findByEmailWithPassword(email: string): Promise<(Admin & { password: string }) | null> {
    const query = `
      SELECT id, email, name, password, role, status, last_login, created_at, updated_at
      FROM admins 
      WHERE email = $1 AND deleted_at IS NULL
    `;
    
    const result = await database.query(query, [email]);
    if (!result.rows[0]) return null;
    
    const row = result.rows[0];
    return {
      ...this.mapRowToAdmin(row),
      password: row.password
    };
  }

  /**
   * Authenticate admin
   */
  static async authenticate(data: AdminLoginData): Promise<AdminAuthResponse | null> {
    const admin = await this.findByEmailWithPassword(data.email);
    
    if (!admin || admin.status !== AdminStatus.ACTIVE) {
      return null;
    }

    const isPasswordValid = await bcrypt.compare(data.password, admin.password);
    if (!isPasswordValid) {
      return null;
    }

    // Update last login
    await this.updateLastLogin(admin.id);

    // Generate JWT token
    const jwtSecret = process.env.JWT_SECRET;
    if (!jwtSecret) {
      throw new Error('JWT_SECRET não configurado');
    }

    const token = jwt.sign(
      {
        adminId: admin.id,
        email: admin.email,
        role: admin.role
      },
      jwtSecret,
      { expiresIn: process.env.JWT_EXPIRES_IN || '7d' }
    );

    const { password, ...adminWithoutPassword } = admin;

    return {
      admin: adminWithoutPassword,
      token,
      expiresIn: process.env.JWT_EXPIRES_IN || '7d'
    };
  }

  /**
   * Update admin
   */
  static async update(id: number, data: UpdateAdminData): Promise<Admin | null> {
    const updates: string[] = [];
    const values: any[] = [];
    let paramCount = 1;

    if (data.email !== undefined) {
      updates.push(`email = $${paramCount++}`);
      values.push(data.email);
    }

    if (data.name !== undefined) {
      updates.push(`name = $${paramCount++}`);
      values.push(data.name);
    }

    if (data.password !== undefined) {
      const hashedPassword = await bcrypt.hash(data.password, 12);
      updates.push(`password = $${paramCount++}`);
      values.push(hashedPassword);
    }

    if (data.role !== undefined) {
      updates.push(`role = $${paramCount++}`);
      values.push(data.role);
    }

    if (data.status !== undefined) {
      updates.push(`status = $${paramCount++}`);
      values.push(data.status);
    }

    if (data.lastLogin !== undefined) {
      updates.push(`last_login = $${paramCount++}`);
      values.push(data.lastLogin);
    }

    if (updates.length === 0) {
      return this.findById(id);
    }

    updates.push(`updated_at = NOW()`);
    values.push(id);

    const query = `
      UPDATE admins 
      SET ${updates.join(', ')}
      WHERE id = $${paramCount} AND deleted_at IS NULL
      RETURNING id, email, name, role, status, last_login, created_at, updated_at
    `;

    const result = await database.query(query, values);
    return result.rows[0] ? this.mapRowToAdmin(result.rows[0]) : null;
  }

  /**
   * Update last login timestamp
   */
  static async updateLastLogin(id: number): Promise<void> {
    const query = `
      UPDATE admins 
      SET last_login = NOW(), updated_at = NOW()
      WHERE id = $1 AND deleted_at IS NULL
    `;
    
    await database.query(query, [id]);
  }

  /**
   * Get all admins with pagination
   */
  static async findMany(options: {
    page?: number;
    limit?: number;
    role?: AdminRole;
    status?: AdminStatus;
  } = {}): Promise<{
    admins: Admin[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    const page = options.page || 1;
    const limit = options.limit || 20;
    const offset = (page - 1) * limit;

    let whereConditions = ['deleted_at IS NULL'];
    const values: any[] = [];
    let paramCount = 1;

    if (options.role) {
      whereConditions.push(`role = $${paramCount++}`);
      values.push(options.role);
    }

    if (options.status) {
      whereConditions.push(`status = $${paramCount++}`);
      values.push(options.status);
    }

    const whereClause = whereConditions.join(' AND ');

    // Get total count
    const countQuery = `SELECT COUNT(*) FROM admins WHERE ${whereClause}`;
    const countResult = await database.query(countQuery, values);
    const total = parseInt(countResult.rows[0].count);

    // Get admins
    const query = `
      SELECT id, email, name, role, status, last_login, created_at, updated_at
      FROM admins 
      WHERE ${whereClause}
      ORDER BY created_at DESC
      LIMIT $${paramCount++} OFFSET $${paramCount++}
    `;

    values.push(limit, offset);
    const result = await database.query(query, values);

    return {
      admins: result.rows.map(row => this.mapRowToAdmin(row)),
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    };
  }

  /**
   * Soft delete admin
   */
  static async delete(id: number): Promise<boolean> {
    const query = `
      UPDATE admins 
      SET deleted_at = NOW(), updated_at = NOW()
      WHERE id = $1 AND deleted_at IS NULL
    `;
    
    const result = await database.query(query, [id]);
    return (result.rowCount || 0) > 0;
  }

  /**
   * Verify JWT token
   */
  static verifyToken(token: string): { adminId: number; email: string; role: AdminRole } | null {
    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET || 'default-secret') as any;
      return {
        adminId: decoded.adminId,
        email: decoded.email,
        role: decoded.role
      };
    } catch (error) {
      return null;
    }
  }

  /**
   * Check if admin exists by email
   */
  static async existsByEmail(email: string): Promise<boolean> {
    const query = `
      SELECT 1 FROM admins 
      WHERE email = $1 AND deleted_at IS NULL
    `;
    
    const result = await database.query(query, [email]);
    return result.rows.length > 0;
  }

  /**
   * Map database row to Admin object
   */
  private static mapRowToAdmin(row: any): Admin {
    const admin: Admin = {
      id: row.id,
      email: row.email,
      name: row.name,
      role: row.role as AdminRole,
      status: row.status as AdminStatus,
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at)
    };

    if (row.last_login) {
      admin.lastLogin = new Date(row.last_login);
    }

    return admin;
  }
}
