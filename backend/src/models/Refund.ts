/**
 * Refund Model - Handles refund database operations
 */

import { query, queryOne, transaction } from '../database';
import { 
  Refund, 
  CreateRefundData, 
  UpdateRefundData, 
  RefundStatus 
} from '../types';

export class RefundModel {
  /**
   * Create a new refund
   */
  static async create(data: CreateRefundData): Promise<Refund> {
    const sql = `
      INSERT INTO refunds (
        payment_id, stripe_refund_id, amount, currency, reason,
        status, description, metadata, created_by
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
      RETURNING *
    `;
    
    const values = [
      data.paymentId,
      data.stripeRefundId,
      data.amount,
      data.currency || 'EUR',
      data.reason || null,
      data.status || RefundStatus.PENDING,
      data.description || null,
      data.metadata ? JSON.stringify(data.metadata) : null,
      data.createdBy || null
    ];

    const result = await queryOne(sql, values);
    return this.mapRowToRefund(result);
  }

  /**
   * Find refund by ID
   */
  static async findById(id: number): Promise<Refund | null> {
    const sql = `
      SELECT * FROM refunds 
      WHERE id = $1 AND deleted_at IS NULL
    `;
    
    const result = await queryOne(sql, [id]);
    return result ? this.mapRowToRefund(result) : null;
  }

  /**
   * Find refund by external refund ID (for future payment system integration)
   */
  static async findByExternalRefundId(externalRefundId: string): Promise<Refund | null> {
    const sql = `
      SELECT * FROM refunds
      WHERE external_refund_id = $1 AND deleted_at IS NULL
    `;

    const result = await queryOne(sql, [externalRefundId]);
    return result ? this.mapRowToRefund(result) : null;
  }

  /**
   * Find refunds by payment ID
   */
  static async findByPaymentId(paymentId: number): Promise<Refund[]> {
    const sql = `
      SELECT * FROM refunds 
      WHERE payment_id = $1 AND deleted_at IS NULL
      ORDER BY created_at DESC
    `;
    
    const result = await query(sql, [paymentId]);
    return result.rows.map(row => this.mapRowToRefund(row));
  }

  /**
   * Update refund
   */
  static async update(id: number, data: UpdateRefundData): Promise<Refund | null> {
    const updateFields: string[] = [];
    const values: any[] = [];
    let paramCount = 1;

    if (data.status !== undefined) {
      updateFields.push(`status = $${paramCount++}`);
      values.push(data.status);
    }

    if (data.failureReason !== undefined) {
      updateFields.push(`failure_reason = $${paramCount++}`);
      values.push(data.failureReason);
    }

    if (data.receiptNumber !== undefined) {
      updateFields.push(`receipt_number = $${paramCount++}`);
      values.push(data.receiptNumber);
    }

    if (data.processedAt !== undefined) {
      updateFields.push(`processed_at = $${paramCount++}`);
      values.push(data.processedAt);
    }

    if (updateFields.length === 0) {
      return this.findById(id);
    }

    updateFields.push(`updated_at = CURRENT_TIMESTAMP`);
    values.push(id);

    const sql = `
      UPDATE refunds 
      SET ${updateFields.join(', ')}
      WHERE id = $${paramCount} AND deleted_at IS NULL
      RETURNING *
    `;

    const result = await queryOne(sql, values);
    return result ? this.mapRowToRefund(result) : null;
  }

  /**
   * Get all refunds with pagination
   */
  static async findAll(
    offset: number = 0, 
    limit: number = 50,
    status?: RefundStatus
  ): Promise<{ refunds: Refund[], total: number }> {
    let whereClause = 'WHERE r.deleted_at IS NULL';
    const values: any[] = [];
    let paramCount = 1;

    if (status) {
      whereClause += ` AND r.status = $${paramCount++}`;
      values.push(status);
    }

    // Get total count
    const countSql = `SELECT COUNT(*) FROM refunds r ${whereClause}`;
    const countResult = await queryOne(countSql, values);
    const total = parseInt(countResult.count);

    // Get refunds with payment info
    values.push(limit, offset);
    const sql = `
      SELECT r.*, p.customer_email, p.order_id
      FROM refunds r
      LEFT JOIN payments p ON r.payment_id = p.id
      ${whereClause}
      ORDER BY r.created_at DESC
      LIMIT $${paramCount++} OFFSET $${paramCount++}
    `;

    const result = await query(sql, values);
    const refunds = result.rows.map(row => this.mapRowToRefund(row));

    return { refunds, total };
  }

  /**
   * Get refund statistics
   */
  static async getStatistics(): Promise<{
    totalRefunds: number;
    totalAmount: number;
    successfulRefunds: number;
    failedRefunds: number;
    pendingRefunds: number;
  }> {
    const sql = `
      SELECT 
        COUNT(*) as total_refunds,
        COALESCE(SUM(amount), 0) as total_amount,
        COUNT(CASE WHEN status = 'succeeded' THEN 1 END) as successful_refunds,
        COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_refunds,
        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_refunds
      FROM refunds 
      WHERE deleted_at IS NULL
    `;

    const result = await queryOne(sql);

    return {
      totalRefunds: parseInt(result.total_refunds),
      totalAmount: parseFloat(result.total_amount),
      successfulRefunds: parseInt(result.successful_refunds),
      failedRefunds: parseInt(result.failed_refunds),
      pendingRefunds: parseInt(result.pending_refunds)
    };
  }

  /**
   * Get total refunded amount for a payment
   */
  static async getTotalRefundedForPayment(paymentId: number): Promise<number> {
    const sql = `
      SELECT COALESCE(SUM(amount), 0) as total_refunded
      FROM refunds 
      WHERE payment_id = $1 
      AND status = 'succeeded' 
      AND deleted_at IS NULL
    `;
    
    const result = await queryOne(sql, [paymentId]);
    return parseFloat(result.total_refunded);
  }

  /**
   * Soft delete refund
   */
  static async delete(id: number): Promise<boolean> {
    const sql = `
      UPDATE refunds 
      SET deleted_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
      WHERE id = $1 AND deleted_at IS NULL
    `;
    
    const result = await query(sql, [id]);
    return (result.rowCount || 0) > 0;
  }

  /**
   * Map database row to Refund object
   */
  private static mapRowToRefund(row: any): Refund {
    const refund: Refund = {
      id: row.id,
      paymentId: row.payment_id,
      stripeRefundId: row.stripe_refund_id,
      amount: parseFloat(row.amount),
      currency: row.currency,
      reason: row.reason,
      status: row.status as RefundStatus,
      description: row.description,
      metadata: row.metadata ? JSON.parse(row.metadata) : null,
      failureReason: row.failure_reason,
      receiptNumber: row.receipt_number,
      createdBy: row.created_by,
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at)
    };

    // Add optional fields only if they exist
    if (row.processed_at) refund.processedAt = new Date(row.processed_at);
    if (row.deleted_at) refund.deletedAt = new Date(row.deleted_at);

    return refund;
  }
}

export default RefundModel;
