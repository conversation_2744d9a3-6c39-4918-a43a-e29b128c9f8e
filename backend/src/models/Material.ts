/**
 * Material model - Handles material operations in the database
 */

import { QueryResult } from 'pg';
import { query, queryOne, transaction } from '../database';
import { 
  Material, 
  CreateMaterialData, 
  UpdateMaterialData,
  MaterialQueryParams 
} from '../types';

export class MaterialModel {
  /**
   * Get all materials with optional filtering
   */
  static async findAll(params: MaterialQueryParams = {}): Promise<Material[]> {
    const {
      page = 1,
      limit = 50,
      sortBy = 'sort_order',
      sortOrder = 'asc',
      search,
      isActive,
      includePrices = false
    } = params;

    let whereConditions: string[] = [];
    let queryParams: any[] = [];
    let paramIndex = 1;

    // Build WHERE conditions
    if (search) {
      whereConditions.push(`(name ILIKE $${paramIndex} OR description ILIKE $${paramIndex})`);
      queryParams.push(`%${search}%`);
      paramIndex++;
    }

    if (isActive !== undefined) {
      whereConditions.push(`is_active = $${paramIndex}`);
      queryParams.push(isActive);
      paramIndex++;
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';
    const offset = (page - 1) * limit;

    const sql = `
      SELECT * FROM materials
      ${whereClause}
      ORDER BY ${sortBy} ${sortOrder}
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;

    queryParams.push(limit, offset);

    const result = await query(sql, queryParams);
    const materials = result.rows.map(this.mapRowToMaterial);

    // Include prices if requested
    if (includePrices) {
      for (const material of materials) {
        material.prices = await this.getMaterialPrices(material.id);
      }
    }

    return materials;
  }

  /**
   * Get material by ID
   */
  static async findById(id: number, includePrices = false): Promise<Material | null> {
    const sql = `SELECT * FROM materials WHERE id = $1`;
    const result = await queryOne(sql, [id]);
    
    if (!result) return null;

    const material = this.mapRowToMaterial(result);

    if (includePrices) {
      material.prices = await this.getMaterialPrices(id);
    }

    return material;
  }

  /**
   * Get material by slug
   */
  static async findBySlug(slug: string, includePrices = false): Promise<Material | null> {
    const sql = `SELECT * FROM materials WHERE slug = $1`;
    const result = await queryOne(sql, [slug]);
    
    if (!result) return null;

    const material = this.mapRowToMaterial(result);

    if (includePrices) {
      material.prices = await this.getMaterialPrices(material.id);
    }

    return material;
  }

  /**
   * Get material prices
   */
  static async getMaterialPrices(materialId: number) {
    const sql = `
      SELECT 
        mp.*,
        ps.name as size_name,
        ps.width_cm,
        ps.height_cm,
        ps.is_custom
      FROM material_prices mp
      JOIN product_sizes ps ON mp.size_id = ps.id
      WHERE mp.material_id = $1 AND mp.is_active = true
      ORDER BY ps.sort_order ASC
    `;

    const result = await query(sql, [materialId]);
    return result.rows.map(row => ({
      id: row.id,
      materialId: row.material_id,
      sizeId: row.size_id,
      price: parseFloat(row.price),
      currency: row.currency,
      isActive: row.is_active,
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at),
      size: {
        id: row.size_id,
        name: row.size_name,
        widthCm: parseFloat(row.width_cm),
        heightCm: parseFloat(row.height_cm),
        isCustom: row.is_custom
      }
    }));
  }

  /**
   * Create new material
   */
  static async create(data: CreateMaterialData): Promise<Material> {
    const {
      name,
      slug,
      description,
      properties = {},
      isActive = true,
      sortOrder = 0
    } = data;

    const sql = `
      INSERT INTO materials (
        name, slug, description, properties, is_active, sort_order
      ) VALUES ($1, $2, $3, $4, $5, $6)
      RETURNING *
    `;

    const result = await queryOne(sql, [
      name, slug, description, JSON.stringify(properties), isActive, sortOrder
    ]);

    return this.mapRowToMaterial(result);
  }

  /**
   * Update material
   */
  static async update(id: number, data: UpdateMaterialData): Promise<Material | null> {
    const fields: string[] = [];
    const values: any[] = [];
    let paramIndex = 1;

    // Build dynamic update query
    Object.entries(data).forEach(([key, value]) => {
      if (value !== undefined) {
        const dbField = this.camelToSnake(key);
        if (key === 'properties') {
          fields.push(`${dbField} = $${paramIndex}`);
          values.push(JSON.stringify(value));
        } else {
          fields.push(`${dbField} = $${paramIndex}`);
          values.push(value);
        }
        paramIndex++;
      }
    });

    if (fields.length === 0) {
      return this.findById(id);
    }

    fields.push(`updated_at = CURRENT_TIMESTAMP`);
    values.push(id);

    const sql = `
      UPDATE materials 
      SET ${fields.join(', ')}
      WHERE id = $${paramIndex}
      RETURNING *
    `;

    const result = await queryOne(sql, values);
    return result ? this.mapRowToMaterial(result) : null;
  }

  /**
   * Delete material (soft delete)
   */
  static async delete(id: number): Promise<boolean> {
    const sql = `
      UPDATE materials 
      SET is_active = false, updated_at = CURRENT_TIMESTAMP
      WHERE id = $1
    `;

    const result = await query(sql, [id]);
    return (result.rowCount || 0) > 0;
  }

  /**
   * Get active materials for dropdown/selection
   */
  static async getActiveForSelection(): Promise<Material[]> {
    const sql = `
      SELECT id, name, slug, description, properties
      FROM materials 
      WHERE is_active = true 
      ORDER BY sort_order ASC, name ASC
    `;

    const result = await query(sql);
    return result.rows.map(this.mapRowToMaterial);
  }

  /**
   * Check if material slug exists
   */
  static async slugExists(slug: string, excludeId?: number): Promise<boolean> {
    let sql = `SELECT id FROM materials WHERE slug = $1`;
    const params = [slug];

    if (excludeId) {
      sql += ` AND id != $2`;
      params.push(excludeId);
    }

    const result = await queryOne(sql, params);
    return !!result;
  }

  /**
   * Map database row to Material object
   */
  private static mapRowToMaterial(row: any): Material {
    return {
      id: row.id,
      name: row.name,
      slug: row.slug,
      description: row.description,
      properties: typeof row.properties === 'string' ? JSON.parse(row.properties) : row.properties,
      isActive: row.is_active,
      sortOrder: row.sort_order,
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at)
    };
  }

  /**
   * Convert camelCase to snake_case
   */
  private static camelToSnake(str: string): string {
    return str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
  }
}
