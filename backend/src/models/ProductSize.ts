/**
 * ProductSize model - Handles product size operations in the database
 */

import { QueryResult } from 'pg';
import { query, queryOne, transaction } from '../database';
import { 
  ProductSize, 
  CreateProductSizeData, 
  UpdateProductSizeData,
  QueryParams 
} from '../types';

export class ProductSizeModel {
  /**
   * Get all sizes with optional filtering
   */
  static async findAll(params: QueryParams = {}): Promise<ProductSize[]> {
    const {
      page = 1,
      limit = 50,
      sortBy = 'sort_order',
      sortOrder = 'asc',
      search
    } = params;

    let whereConditions: string[] = [];
    let queryParams: any[] = [];
    let paramIndex = 1;

    // Build WHERE conditions
    if (search) {
      whereConditions.push(`name ILIKE $${paramIndex}`);
      queryParams.push(`%${search}%`);
      paramIndex++;
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';
    const offset = (page - 1) * limit;

    const sql = `
      SELECT * FROM product_sizes
      ${whereClause}
      ORDER BY ${sortBy} ${sortOrder}
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;

    queryParams.push(limit, offset);

    const result = await query(sql, queryParams);
    return result.rows.map(this.mapRowToSize);
  }

  /**
   * Get size by ID
   */
  static async findById(id: number): Promise<ProductSize | null> {
    const sql = `SELECT * FROM product_sizes WHERE id = $1`;
    const result = await queryOne(sql, [id]);
    
    return result ? this.mapRowToSize(result) : null;
  }

  /**
   * Get active sizes for selection
   */
  static async getActiveForSelection(): Promise<ProductSize[]> {
    const sql = `
      SELECT * FROM product_sizes 
      WHERE is_active = true 
      ORDER BY sort_order ASC, name ASC
    `;

    const result = await query(sql);
    return result.rows.map(this.mapRowToSize);
  }

  /**
   * Get standard (non-custom) sizes
   */
  static async getStandardSizes(): Promise<ProductSize[]> {
    const sql = `
      SELECT * FROM product_sizes 
      WHERE is_active = true AND is_custom = false
      ORDER BY sort_order ASC, name ASC
    `;

    const result = await query(sql);
    return result.rows.map(this.mapRowToSize);
  }

  /**
   * Get custom size option
   */
  static async getCustomSize(): Promise<ProductSize | null> {
    const sql = `
      SELECT * FROM product_sizes 
      WHERE is_custom = true AND is_active = true
      LIMIT 1
    `;

    const result = await queryOne(sql);
    return result ? this.mapRowToSize(result) : null;
  }

  /**
   * Create new size
   */
  static async create(data: CreateProductSizeData): Promise<ProductSize> {
    const {
      name,
      widthCm,
      heightCm,
      isCustom = false,
      isActive = true,
      sortOrder = 0
    } = data;

    const sql = `
      INSERT INTO product_sizes (
        name, width_cm, height_cm, is_custom, is_active, sort_order
      ) VALUES ($1, $2, $3, $4, $5, $6)
      RETURNING *
    `;

    const result = await queryOne(sql, [
      name, widthCm, heightCm, isCustom, isActive, sortOrder
    ]);

    return this.mapRowToSize(result);
  }

  /**
   * Update size
   */
  static async update(id: number, data: UpdateProductSizeData): Promise<ProductSize | null> {
    const fields: string[] = [];
    const values: any[] = [];
    let paramIndex = 1;

    // Build dynamic update query
    Object.entries(data).forEach(([key, value]) => {
      if (value !== undefined) {
        const dbField = this.camelToSnake(key);
        fields.push(`${dbField} = $${paramIndex}`);
        values.push(value);
        paramIndex++;
      }
    });

    if (fields.length === 0) {
      return this.findById(id);
    }

    fields.push(`updated_at = CURRENT_TIMESTAMP`);
    values.push(id);

    const sql = `
      UPDATE product_sizes 
      SET ${fields.join(', ')}
      WHERE id = $${paramIndex}
      RETURNING *
    `;

    const result = await queryOne(sql, values);
    return result ? this.mapRowToSize(result) : null;
  }

  /**
   * Delete size (soft delete)
   */
  static async delete(id: number): Promise<boolean> {
    const sql = `
      UPDATE product_sizes 
      SET is_active = false, updated_at = CURRENT_TIMESTAMP
      WHERE id = $1
    `;

    const result = await query(sql, [id]);
    return result.rowCount > 0;
  }

  /**
   * Find size by dimensions
   */
  static async findByDimensions(widthCm: number, heightCm: number): Promise<ProductSize | null> {
    const sql = `
      SELECT * FROM product_sizes 
      WHERE width_cm = $1 AND height_cm = $2 AND is_active = true
      LIMIT 1
    `;

    const result = await queryOne(sql, [widthCm, heightCm]);
    return result ? this.mapRowToSize(result) : null;
  }

  /**
   * Calculate area in square centimeters
   */
  static calculateArea(size: ProductSize): number {
    return size.widthCm * size.heightCm;
  }

  /**
   * Calculate aspect ratio
   */
  static calculateAspectRatio(size: ProductSize): number {
    return size.widthCm / size.heightCm;
  }

  /**
   * Get sizes compatible with material
   */
  static async getSizesForMaterial(materialId: number): Promise<ProductSize[]> {
    const sql = `
      SELECT DISTINCT ps.*
      FROM product_sizes ps
      JOIN material_prices mp ON ps.id = mp.size_id
      WHERE mp.material_id = $1 AND mp.is_active = true AND ps.is_active = true
      ORDER BY ps.sort_order ASC, ps.name ASC
    `;

    const result = await query(sql, [materialId]);
    return result.rows.map(this.mapRowToSize);
  }

  /**
   * Check if size name exists
   */
  static async nameExists(name: string, excludeId?: number): Promise<boolean> {
    let sql = `SELECT id FROM product_sizes WHERE name = $1`;
    const params = [name];

    if (excludeId) {
      sql += ` AND id != $2`;
      params.push(excludeId);
    }

    const result = await queryOne(sql, params);
    return !!result;
  }

  /**
   * Map database row to ProductSize object
   */
  private static mapRowToSize(row: any): ProductSize {
    return {
      id: row.id,
      name: row.name,
      widthCm: parseFloat(row.width_cm),
      heightCm: parseFloat(row.height_cm),
      isCustom: row.is_custom,
      isActive: row.is_active,
      sortOrder: row.sort_order,
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at)
    };
  }

  /**
   * Convert camelCase to snake_case
   */
  private static camelToSnake(str: string): string {
    return str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
  }
}
