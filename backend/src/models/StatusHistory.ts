/**
 * StatusHistory model - Handles status history operations in the database
 */

import { QueryResult } from 'pg';
import { query, queryOne } from '../database';
import { StatusHistory, CreateStatusHistoryData, OrderStatus } from '../types';

export class StatusHistoryModel {
  /**
   * Create a new status history entry
   */
  static async create(data: CreateStatusHistoryData): Promise<StatusHistory> {
    const sql = `
      INSERT INTO status_history (order_id, status, notes, updated_by)
      VALUES ($1, $2, $3, $4)
      RETURNING *
    `;
    
    const values = [
      data.orderId,
      data.status,
      data.notes || null,
      data.updatedBy || 'system'
    ];

    const result = await queryOne(sql, values);
    return this.mapRowToStatusHistory(result);
  }

  /**
   * Get status history for an order
   */
  static async findByOrderId(orderId: number): Promise<StatusHistory[]> {
    const sql = `
      SELECT * FROM status_history 
      WHERE order_id = $1 
      ORDER BY updated_at ASC
    `;
    
    const result = await query(sql, [orderId]);
    return result.rows.map(row => this.mapRowToStatusHistory(row));
  }

  /**
   * Get latest status for an order
   */
  static async getLatestStatus(orderId: number): Promise<StatusHistory | null> {
    const sql = `
      SELECT * FROM status_history 
      WHERE order_id = $1 
      ORDER BY updated_at DESC 
      LIMIT 1
    `;
    
    const result = await queryOne(sql, [orderId]);
    return result ? this.mapRowToStatusHistory(result) : null;
  }

  /**
   * Get status history with pagination
   */
  static async findAll(
    limit: number = 50, 
    offset: number = 0,
    orderId?: number
  ): Promise<{ history: StatusHistory[]; total: number }> {
    let whereClause = '';
    let values: any[] = [];
    
    if (orderId) {
      whereClause = 'WHERE order_id = $1';
      values.push(orderId);
    }

    const countSql = `
      SELECT COUNT(*) as total FROM status_history ${whereClause}
    `;
    
    const historySql = `
      SELECT * FROM status_history 
      ${whereClause}
      ORDER BY updated_at DESC
      LIMIT $${values.length + 1} OFFSET $${values.length + 2}
    `;

    values.push(limit, offset);

    const [countResult, historyResult] = await Promise.all([
      queryOne(countSql, orderId ? [orderId] : []),
      query(historySql, values)
    ]);

    return {
      history: historyResult.rows.map(row => this.mapRowToStatusHistory(row)),
      total: parseInt(countResult.total)
    };
  }

  /**
   * Get status history by status
   */
  static async findByStatus(status: OrderStatus, limit: number = 50): Promise<StatusHistory[]> {
    const sql = `
      SELECT * FROM status_history 
      WHERE status = $1 
      ORDER BY updated_at DESC 
      LIMIT $2
    `;
    
    const result = await query(sql, [status, limit]);
    return result.rows.map(row => this.mapRowToStatusHistory(row));
  }

  /**
   * Get status history by user
   */
  static async findByUpdatedBy(updatedBy: string, limit: number = 50): Promise<StatusHistory[]> {
    const sql = `
      SELECT * FROM status_history 
      WHERE updated_by = $1 
      ORDER BY updated_at DESC 
      LIMIT $2
    `;
    
    const result = await query(sql, [updatedBy, limit]);
    return result.rows.map(row => this.mapRowToStatusHistory(row));
  }

  /**
   * Get status transition statistics
   */
  static async getStatusTransitionStats(): Promise<Array<{
    fromStatus: string | null;
    toStatus: string;
    count: number;
    averageTimeInStatus?: number;
  }>> {
    const sql = `
      WITH status_transitions AS (
        SELECT 
          sh1.order_id,
          LAG(sh1.status) OVER (PARTITION BY sh1.order_id ORDER BY sh1.updated_at) as from_status,
          sh1.status as to_status,
          sh1.updated_at,
          LAG(sh1.updated_at) OVER (PARTITION BY sh1.order_id ORDER BY sh1.updated_at) as prev_updated_at
        FROM status_history sh1
      )
      SELECT 
        from_status,
        to_status,
        COUNT(*) as count,
        AVG(EXTRACT(EPOCH FROM (updated_at - prev_updated_at))) as avg_time_seconds
      FROM status_transitions
      GROUP BY from_status, to_status
      ORDER BY count DESC
    `;
    
    const result = await query(sql);
    return result.rows.map(row => ({
      fromStatus: row.from_status,
      toStatus: row.to_status,
      count: parseInt(row.count),
      averageTimeInStatus: row.avg_time_seconds ? parseFloat(row.avg_time_seconds) : undefined
    }));
  }

  /**
   * Get orders that have been in a status for too long
   */
  static async getStaleOrders(
    status: OrderStatus, 
    hoursThreshold: number = 24
  ): Promise<Array<{ orderId: number; status: string; hoursInStatus: number; lastUpdated: Date }>> {
    const sql = `
      SELECT DISTINCT ON (order_id)
        order_id,
        status,
        EXTRACT(EPOCH FROM (NOW() - updated_at)) / 3600 as hours_in_status,
        updated_at as last_updated
      FROM status_history
      WHERE status = $1
      ORDER BY order_id, updated_at DESC
      HAVING EXTRACT(EPOCH FROM (NOW() - updated_at)) / 3600 > $2
    `;
    
    const result = await query(sql, [status, hoursThreshold]);
    return result.rows.map(row => ({
      orderId: row.order_id,
      status: row.status,
      hoursInStatus: parseFloat(row.hours_in_status),
      lastUpdated: row.last_updated
    }));
  }

  /**
   * Delete status history for an order (cascade delete)
   */
  static async deleteByOrderId(orderId: number): Promise<number> {
    const sql = `
      DELETE FROM status_history 
      WHERE order_id = $1 
      RETURNING id
    `;
    
    const result = await query(sql, [orderId]);
    return result.rowCount || 0;
  }

  /**
   * Get status history with order information
   */
  static async findWithOrderInfo(
    limit: number = 50, 
    offset: number = 0
  ): Promise<Array<StatusHistory & { orderNumber: string; customerEmail?: string }>> {
    const sql = `
      SELECT 
        sh.*,
        o.order_number,
        o.customer_email
      FROM status_history sh
      JOIN orders o ON sh.order_id = o.id
      ORDER BY sh.updated_at DESC
      LIMIT $1 OFFSET $2
    `;
    
    const result = await query(sql, [limit, offset]);
    return result.rows.map(row => ({
      ...this.mapRowToStatusHistory(row),
      orderNumber: row.order_number,
      customerEmail: row.customer_email
    }));
  }

  /**
   * Map database row to StatusHistory object
   */
  private static mapRowToStatusHistory(row: any): StatusHistory {
    return {
      id: row.id,
      orderId: row.order_id,
      status: row.status,
      notes: row.notes,
      updatedBy: row.updated_by,
      updatedAt: row.updated_at
    };
  }
}
