import {
  Notification,
  CreateNotificationData,
  UpdateNotificationData,
  NotificationType,
  NotificationChannel,
  NotificationStatus,
  NotificationStats
} from '../types';
import { query, queryOne } from '../database';

export class NotificationModel {
  /**
   * Create a new notification
   */
  static async create(data: CreateNotificationData): Promise<Notification> {
    const sql = `
      INSERT INTO notifications (
        type, channel, recipient_email, recipient_name, subject, content,
        template_data, order_id, file_id, max_retries, status, retry_count
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
      RETURNING *
    `;

    const values = [
      data.type,
      data.channel,
      data.recipientEmail,
      data.recipientName || null,
      data.subject,
      data.content,
      data.templateData ? JSON.stringify(data.templateData) : null,
      data.orderId || null,
      data.fileId || null,
      data.maxRetries || 3,
      NotificationStatus.PENDING,
      0
    ];

    const result = await queryOne(sql, values);
    return this.mapRowToNotification(result);
  }

  /**
   * Find notification by ID
   */
  static async findById(id: number): Promise<Notification | null> {
    const sql = `
      SELECT * FROM notifications 
      WHERE id = $1
    `;

    try {
      const result = await queryOne(sql, [id]);
      return this.mapRowToNotification(result);
    } catch (error) {
      return null;
    }
  }

  /**
   * Find notifications with filters and pagination
   */
  static async findMany(options: {
    page?: number;
    limit?: number;
    type?: NotificationType;
    status?: NotificationStatus;
    recipientEmail?: string;
    orderId?: number;
  } = {}): Promise<{ notifications: Notification[]; total: number }> {
    const {
      page = 1,
      limit = 20,
      type,
      status,
      recipientEmail,
      orderId
    } = options;

    const offset = (page - 1) * limit;
    const conditions: string[] = [];
    const values: any[] = [];
    let paramCount = 0;

    if (type) {
      conditions.push(`type = $${++paramCount}`);
      values.push(type);
    }



    if (status) {
      conditions.push(`status = $${++paramCount}`);
      values.push(status);
    }

    if (recipientEmail) {
      conditions.push(`recipient_email ILIKE $${++paramCount}`);
      values.push(`%${recipientEmail}%`);
    }

    if (orderId) {
      conditions.push(`order_id = $${++paramCount}`);
      values.push(orderId);
    }

    const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

    // Get total count
    const countSql = `
      SELECT COUNT(*) as total 
      FROM notifications 
      ${whereClause}
    `;
    const countResult = await queryOne(countSql, values);
    const total = parseInt(countResult.total);

    // Get notifications
    const sql = `
      SELECT * FROM notifications 
      ${whereClause}
      ORDER BY created_at DESC
      LIMIT $${++paramCount} OFFSET $${++paramCount}
    `;
    values.push(limit, offset);

    const result = await query(sql, values);
    const notifications = result.rows.map(row => this.mapRowToNotification(row));

    return { notifications, total };
  }

  /**
   * Update notification
   */
  static async update(id: number, data: UpdateNotificationData): Promise<Notification | null> {
    const fields: string[] = [];
    const values: any[] = [];
    let paramCount = 0;

    if (data.status !== undefined) {
      fields.push(`status = $${++paramCount}`);
      values.push(data.status);
    }

    if (data.sentAt !== undefined) {
      fields.push(`sent_at = $${++paramCount}`);
      values.push(data.sentAt);
    }

    if (data.deliveredAt !== undefined) {
      fields.push(`delivered_at = $${++paramCount}`);
      values.push(data.deliveredAt);
    }

    if (data.readAt !== undefined) {
      fields.push(`read_at = $${++paramCount}`);
      values.push(data.readAt);
    }

    if (data.errorMessage !== undefined) {
      fields.push(`error_message = $${++paramCount}`);
      values.push(data.errorMessage);
    }

    if (data.retryCount !== undefined) {
      fields.push(`retry_count = $${++paramCount}`);
      values.push(data.retryCount);
    }

    if (fields.length === 0) {
      return this.findById(id);
    }

    // Note: notifications table doesn't have updated_at column

    const sql = `
      UPDATE notifications 
      SET ${fields.join(', ')}
      WHERE id = $${++paramCount}
      RETURNING *
    `;
    values.push(id);

    try {
      const result = await queryOne(sql, values);
      return this.mapRowToNotification(result);
    } catch (error) {
      return null;
    }
  }

  /**
   * Get pending notifications for processing
   */
  static async getPendingNotifications(limit: number = 50): Promise<Notification[]> {
    const sql = `
      SELECT * FROM notifications 
      WHERE status = $1 
        AND retry_count < max_retries
      ORDER BY created_at ASC
      LIMIT $2
    `;

    const result = await query(sql, [NotificationStatus.PENDING, limit]);
    return result.rows.map(row => this.mapRowToNotification(row));
  }

  /**
   * Get failed notifications that can be retried
   */
  static async getRetryableNotifications(limit: number = 20): Promise<Notification[]> {
    const sql = `
      SELECT * FROM notifications
      WHERE status = $1
        AND retry_count < max_retries
        AND created_at < NOW() - INTERVAL '5 minutes'
      ORDER BY created_at ASC
      LIMIT $2
    `;

    const result = await query(sql, [NotificationStatus.FAILED, limit]);
    return result.rows.map(row => this.mapRowToNotification(row));
  }

  /**
   * Get notification statistics
   */
  static async getStatistics(days: number = 30): Promise<NotificationStats> {
    // Basic stats
    const basicStatsSql = `
      SELECT 
        COUNT(*) FILTER (WHERE status = 'sent') as total_sent,
        COUNT(*) FILTER (WHERE status = 'delivered') as total_delivered,
        COUNT(*) FILTER (WHERE status = 'failed') as total_failed,
        COUNT(*) FILTER (WHERE status = 'read') as total_read
      FROM notifications
      WHERE created_at >= NOW() - INTERVAL '${days} days'
    `;

    const basicStats = await queryOne(basicStatsSql);

    // Stats by type
    const typeStatsSql = `
      SELECT type, COUNT(*) as count
      FROM notifications
      WHERE created_at >= NOW() - INTERVAL '${days} days'
        AND status = 'sent'
      GROUP BY type
    `;

    const typeStats = await query(typeStatsSql);
    const byType: Record<NotificationType, number> = {} as any;
    typeStats.rows.forEach(row => {
      byType[row.type as NotificationType] = parseInt(row.count);
    });

    // Stats by channel
    const channelStatsSql = `
      SELECT channel, COUNT(*) as count
      FROM notifications
      WHERE created_at >= NOW() - INTERVAL '${days} days'
        AND status = 'sent'
      GROUP BY channel
    `;

    const channelStats = await query(channelStatsSql);
    const byChannel: Record<NotificationChannel, number> = {} as any;
    channelStats.rows.forEach(row => {
      byChannel[row.channel as NotificationChannel] = parseInt(row.count);
    });

    // Stats by status
    const statusStatsSql = `
      SELECT status, COUNT(*) as count
      FROM notifications
      WHERE created_at >= NOW() - INTERVAL '${days} days'
      GROUP BY status
    `;

    const statusStats = await query(statusStatsSql);
    const byStatus: Record<NotificationStatus, number> = {} as any;
    statusStats.rows.forEach(row => {
      byStatus[row.status as NotificationStatus] = parseInt(row.count);
    });

    // Recent activity (last 7 days)
    const activitySql = `
      SELECT 
        DATE(created_at) as date,
        COUNT(*) FILTER (WHERE status = 'sent') as sent,
        COUNT(*) FILTER (WHERE status = 'delivered') as delivered,
        COUNT(*) FILTER (WHERE status = 'failed') as failed
      FROM notifications
      WHERE created_at >= NOW() - INTERVAL '7 days'
      GROUP BY DATE(created_at)
      ORDER BY date DESC
    `;

    const activityStats = await query(activitySql);
    const recentActivity = activityStats.rows.map(row => ({
      date: row.date,
      sent: parseInt(row.sent),
      delivered: parseInt(row.delivered),
      failed: parseInt(row.failed)
    }));

    const totalSent = parseInt(basicStats.total_sent);
    const totalDelivered = parseInt(basicStats.total_delivered);
    const totalFailed = parseInt(basicStats.total_failed);
    const totalRead = parseInt(basicStats.total_read);

    return {
      totalSent,
      totalDelivered,
      totalFailed,
      totalRead,
      deliveryRate: totalSent > 0 ? (totalDelivered / totalSent) * 100 : 0,
      readRate: totalDelivered > 0 ? (totalRead / totalDelivered) * 100 : 0,
      byType,
      byChannel,
      byStatus,
      recentActivity
    };
  }

  /**
   * Delete old notifications (cleanup)
   */
  static async deleteOldNotifications(daysOld: number = 90): Promise<number> {
    const sql = `
      DELETE FROM notifications 
      WHERE created_at < NOW() - INTERVAL '${daysOld} days'
        AND status IN ('delivered', 'read', 'failed')
    `;

    const result = await query(sql);
    return result.rowCount || 0;
  }

  /**
   * Map database row to Notification object
   */
  private static mapRowToNotification(row: any): Notification {
    return {
      id: row.id,
      type: row.type,
      channel: row.channel,
      status: row.status,
      recipientEmail: row.recipient_email,
      recipientName: row.recipient_name,
      subject: row.subject,
      content: row.content,
      templateData: row.template_data ? JSON.parse(row.template_data) : undefined,
      orderId: row.order_id,
      fileId: row.file_id,
      sentAt: row.sent_at,
      deliveredAt: row.delivered_at,
      readAt: row.read_at,
      errorMessage: row.error_message,
      retryCount: row.retry_count,
      maxRetries: row.max_retries,
      createdAt: row.created_at,
      updatedAt: row.updated_at || row.created_at
    };
  }
}
