/**
 * Payment Model - Handles payment database operations
 */

import { query, queryOne, transaction } from '../database';
import { 
  Payment, 
  CreatePaymentData, 
  UpdatePaymentData, 
  PaymentStatus 
} from '../types';

export class PaymentModel {
  /**
   * Create a new payment
   */
  static async create(data: CreatePaymentData): Promise<Payment> {
    const sql = `
      INSERT INTO payments (
        order_id, customer_email, multicaixa_transaction_id, multicaixa_payment_url,
        amount, currency, status, payment_method_type, description, metadata
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
      RETURNING *
    `;

    const values = [
      data.orderId,
      data.customerEmail,
      data.multicaixaTransactionId || null,
      data.multicaixaPaymentUrl || null,
      data.amount,
      data.currency || 'AOA',
      data.status || PaymentStatus.PENDING,
      data.paymentMethodType || 'MULTICAIXA_EXPRESS',
      data.description || null,
      data.metadata ? JSON.stringify(data.metadata) : null
    ];

    const result = await queryOne(sql, values);
    return this.mapRowToPayment(result);
  }

  /**
   * Find payment by ID
   */
  static async findById(id: number): Promise<Payment | null> {
    const sql = `
      SELECT * FROM payments 
      WHERE id = $1 AND deleted_at IS NULL
    `;
    
    const result = await queryOne(sql, [id]);
    return result ? this.mapRowToPayment(result) : null;
  }

  /**
   * Find payment by Multicaixa Transaction ID
   */
  static async findByMulticaixaTransactionId(transactionId: string): Promise<Payment | null> {
    const sql = `
      SELECT * FROM payments
      WHERE multicaixa_transaction_id = $1 AND deleted_at IS NULL
    `;

    const result = await queryOne(sql, [transactionId]);
    return result ? this.mapRowToPayment(result) : null;
  }

  /**
   * Find payments by order ID
   */
  static async findByOrderId(orderId: number): Promise<Payment[]> {
    const sql = `
      SELECT * FROM payments 
      WHERE order_id = $1 AND deleted_at IS NULL
      ORDER BY created_at DESC
    `;
    
    const result = await query(sql, [orderId]);
    return result.rows.map(row => this.mapRowToPayment(row));
  }

  /**
   * Update payment
   */
  static async update(id: number, data: UpdatePaymentData): Promise<Payment | null> {
    const updateFields: string[] = [];
    const values: any[] = [];
    let paramCount = 1;

    if (data.status !== undefined) {
      updateFields.push(`status = $${paramCount++}`);
      values.push(data.status);
    }

    if (data.multicaixaTransactionId !== undefined) {
      updateFields.push(`multicaixa_transaction_id = $${paramCount++}`);
      values.push(data.multicaixaTransactionId);
    }

    if (data.multicaixaPaymentUrl !== undefined) {
      updateFields.push(`multicaixa_payment_url = $${paramCount++}`);
      values.push(data.multicaixaPaymentUrl);
    }

    if (data.paymentMethodType !== undefined) {
      updateFields.push(`payment_method_type = $${paramCount++}`);
      values.push(data.paymentMethodType);
    }

    if (data.paidAt !== undefined) {
      updateFields.push(`paid_at = $${paramCount++}`);
      values.push(data.paidAt);
    }

    if (data.failedAt !== undefined) {
      updateFields.push(`failed_at = $${paramCount++}`);
      values.push(data.failedAt);
    }

    if (data.failureReason !== undefined) {
      updateFields.push(`failure_reason = $${paramCount++}`);
      values.push(data.failureReason);
    }

    if (data.failureCode !== undefined) {
      updateFields.push(`failure_code = $${paramCount++}`);
      values.push(data.failureCode);
    }

    if (data.receiptUrl !== undefined) {
      updateFields.push(`receipt_url = $${paramCount++}`);
      values.push(data.receiptUrl);
    }

    if (data.metadata !== undefined) {
      updateFields.push(`metadata = $${paramCount++}`);
      values.push(data.metadata ? JSON.stringify(data.metadata) : null);
    }

    if (updateFields.length === 0) {
      return this.findById(id);
    }

    updateFields.push(`updated_at = CURRENT_TIMESTAMP`);
    values.push(id);

    const sql = `
      UPDATE payments 
      SET ${updateFields.join(', ')}
      WHERE id = $${paramCount} AND deleted_at IS NULL
      RETURNING *
    `;

    const result = await queryOne(sql, values);
    return result ? this.mapRowToPayment(result) : null;
  }

  /**
   * Get all payments with pagination
   */
  static async findAll(
    offset: number = 0, 
    limit: number = 50,
    status?: PaymentStatus
  ): Promise<{ payments: Payment[], total: number }> {
    let whereClause = 'WHERE deleted_at IS NULL';
    const values: any[] = [];
    let paramCount = 1;

    if (status) {
      whereClause += ` AND status = $${paramCount++}`;
      values.push(status);
    }

    // Get total count
    const countSql = `SELECT COUNT(*) FROM payments ${whereClause}`;
    const countResult = await queryOne(countSql, values);
    const total = parseInt(countResult.count);

    // Get payments
    values.push(limit, offset);
    const sql = `
      SELECT * FROM payments 
      ${whereClause}
      ORDER BY created_at DESC
      LIMIT $${paramCount++} OFFSET $${paramCount++}
    `;

    const result = await query(sql, values);
    const payments = result.rows.map(row => this.mapRowToPayment(row));

    return { payments, total };
  }

  /**
   * Get payment statistics
   */
  static async getStatistics(): Promise<{
    totalPayments: number;
    totalAmount: number;
    successfulPayments: number;
    failedPayments: number;
    pendingPayments: number;
  }> {
    const sql = `
      SELECT 
        COUNT(*) as total_payments,
        COALESCE(SUM(amount), 0) as total_amount,
        COUNT(CASE WHEN status = 'succeeded' THEN 1 END) as successful_payments,
        COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_payments,
        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_payments
      FROM payments 
      WHERE deleted_at IS NULL
    `;

    const result = await queryOne(sql);

    return {
      totalPayments: parseInt(result.total_payments),
      totalAmount: parseFloat(result.total_amount),
      successfulPayments: parseInt(result.successful_payments),
      failedPayments: parseInt(result.failed_payments),
      pendingPayments: parseInt(result.pending_payments)
    };
  }

  /**
   * Soft delete payment
   */
  static async delete(id: number): Promise<boolean> {
    const sql = `
      UPDATE payments 
      SET deleted_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
      WHERE id = $1 AND deleted_at IS NULL
    `;
    
    const result = await query(sql, [id]);
    return (result.rowCount || 0) > 0;
  }

  /**
   * Map database row to Payment object
   */
  private static mapRowToPayment(row: any): Payment {
    const payment: Payment = {
      id: row.id,
      orderId: row.order_id,
      customerEmail: row.customer_email,
      multicaixaTransactionId: row.multicaixa_transaction_id,
      multicaixaPaymentUrl: row.multicaixa_payment_url,
      amount: parseFloat(row.amount),
      currency: row.currency,
      status: row.status as PaymentStatus,
      processingFee: parseFloat(row.processing_fee || '0'),
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at)
    };

    // Add optional fields only if they exist
    if (row.multicaixa_transaction_id) payment.multicaixaTransactionId = row.multicaixa_transaction_id;
    if (row.multicaixa_payment_url) payment.multicaixaPaymentUrl = row.multicaixa_payment_url;
    if (row.payment_method_type) payment.paymentMethodType = row.payment_method_type;
    if (row.description) payment.description = row.description;
    if (row.metadata) payment.metadata = JSON.parse(row.metadata);
    if (row.net_amount) payment.netAmount = parseFloat(row.net_amount);
    if (row.failure_reason) payment.failureReason = row.failure_reason;
    if (row.failure_code) payment.failureCode = row.failure_code;
    if (row.receipt_url) payment.receiptUrl = row.receipt_url;
    if (row.paid_at) payment.paidAt = new Date(row.paid_at);
    if (row.failed_at) payment.failedAt = new Date(row.failed_at);
    if (row.deleted_at) payment.deletedAt = new Date(row.deleted_at);

    return payment;
  }
}

export default PaymentModel;
