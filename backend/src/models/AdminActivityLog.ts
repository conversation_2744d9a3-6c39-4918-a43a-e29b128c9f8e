import database from '../database';
import { AdminActivityLog, CreateAdminActivityData } from '../types';

export class AdminActivityLogModel {
  /**
   * Create a new activity log entry
   */
  static async create(data: CreateAdminActivityData): Promise<AdminActivityLog> {
    const query = `
      INSERT INTO admin_activity_logs (
        admin_id, admin_email, action, resource, resource_id, 
        details, ip_address, user_agent
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      RETURNING id, admin_id, admin_email, action, resource, resource_id, 
                details, ip_address, user_agent, created_at
    `;
    
    const values = [
      data.adminId,
      data.adminEmail,
      data.action,
      data.resource,
      data.resourceId || null,
      data.details ? JSON.stringify(data.details) : null,
      data.ipAddress || null,
      data.userAgent || null
    ];
    
    const result = await database.query(query, values);
    return this.mapRowToActivityLog(result.rows[0]);
  }

  /**
   * Get activity logs with pagination and filters
   */
  static async findMany(options: {
    page?: number;
    limit?: number;
    adminId?: number;
    action?: string;
    resource?: string;
    dateFrom?: Date;
    dateTo?: Date;
  } = {}): Promise<{
    logs: AdminActivityLog[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    const page = options.page || 1;
    const limit = options.limit || 50;
    const offset = (page - 1) * limit;

    let whereConditions: string[] = [];
    const values: any[] = [];
    let paramCount = 1;

    if (options.adminId) {
      whereConditions.push(`admin_id = $${paramCount++}`);
      values.push(options.adminId);
    }

    if (options.action) {
      whereConditions.push(`action ILIKE $${paramCount++}`);
      values.push(`%${options.action}%`);
    }

    if (options.resource) {
      whereConditions.push(`resource = $${paramCount++}`);
      values.push(options.resource);
    }

    if (options.dateFrom) {
      whereConditions.push(`created_at >= $${paramCount++}`);
      values.push(options.dateFrom);
    }

    if (options.dateTo) {
      whereConditions.push(`created_at <= $${paramCount++}`);
      values.push(options.dateTo);
    }

    const whereClause = whereConditions.length > 0 
      ? `WHERE ${whereConditions.join(' AND ')}`
      : '';

    // Get total count
    const countQuery = `SELECT COUNT(*) FROM admin_activity_logs ${whereClause}`;
    const countResult = await database.query(countQuery, values);
    const total = parseInt(countResult.rows[0].count);

    // Get logs
    const query = `
      SELECT id, admin_id, admin_email, action, resource, resource_id, 
             details, ip_address, user_agent, created_at
      FROM admin_activity_logs 
      ${whereClause}
      ORDER BY created_at DESC
      LIMIT $${paramCount++} OFFSET $${paramCount++}
    `;

    values.push(limit, offset);
    const result = await database.query(query, values);

    return {
      logs: result.rows.map(row => this.mapRowToActivityLog(row)),
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    };
  }

  /**
   * Get activity logs for specific admin
   */
  static async findByAdminId(
    adminId: number, 
    options: { page?: number; limit?: number } = {}
  ): Promise<{
    logs: AdminActivityLog[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    return this.findMany({ ...options, adminId });
  }

  /**
   * Get recent activity logs
   */
  static async getRecentActivity(limit: number = 20): Promise<AdminActivityLog[]> {
    const query = `
      SELECT id, admin_id, admin_email, action, resource, resource_id, 
             details, ip_address, user_agent, created_at
      FROM admin_activity_logs 
      ORDER BY created_at DESC
      LIMIT $1
    `;
    
    const result = await database.query(query, [limit]);
    return result.rows.map(row => this.mapRowToActivityLog(row));
  }

  /**
   * Get activity statistics
   */
  static async getStatistics(days: number = 30): Promise<{
    totalActions: number;
    actionsByType: Record<string, number>;
    actionsByResource: Record<string, number>;
    actionsByAdmin: Record<string, number>;
    actionsPerDay: Array<{ date: string; count: number }>;
  }> {
    const dateFrom = new Date();
    dateFrom.setDate(dateFrom.getDate() - days);

    // Total actions
    const totalQuery = `
      SELECT COUNT(*) as total
      FROM admin_activity_logs 
      WHERE created_at >= $1
    `;
    const totalResult = await database.query(totalQuery, [dateFrom]);
    const totalActions = parseInt(totalResult.rows[0].total);

    // Actions by type
    const actionTypeQuery = `
      SELECT action, COUNT(*) as count
      FROM admin_activity_logs 
      WHERE created_at >= $1
      GROUP BY action
      ORDER BY count DESC
    `;
    const actionTypeResult = await database.query(actionTypeQuery, [dateFrom]);
    const actionsByType = actionTypeResult.rows.reduce((acc, row) => {
      acc[row.action] = parseInt(row.count);
      return acc;
    }, {});

    // Actions by resource
    const resourceQuery = `
      SELECT resource, COUNT(*) as count
      FROM admin_activity_logs 
      WHERE created_at >= $1
      GROUP BY resource
      ORDER BY count DESC
    `;
    const resourceResult = await database.query(resourceQuery, [dateFrom]);
    const actionsByResource = resourceResult.rows.reduce((acc, row) => {
      acc[row.resource] = parseInt(row.count);
      return acc;
    }, {});

    // Actions by admin
    const adminQuery = `
      SELECT admin_email, COUNT(*) as count
      FROM admin_activity_logs 
      WHERE created_at >= $1
      GROUP BY admin_email
      ORDER BY count DESC
    `;
    const adminResult = await database.query(adminQuery, [dateFrom]);
    const actionsByAdmin = adminResult.rows.reduce((acc, row) => {
      acc[row.admin_email] = parseInt(row.count);
      return acc;
    }, {});

    // Actions per day
    const dailyQuery = `
      SELECT DATE(created_at) as date, COUNT(*) as count
      FROM admin_activity_logs 
      WHERE created_at >= $1
      GROUP BY DATE(created_at)
      ORDER BY date DESC
    `;
    const dailyResult = await database.query(dailyQuery, [dateFrom]);
    const actionsPerDay = dailyResult.rows.map(row => ({
      date: row.date,
      count: parseInt(row.count)
    }));

    return {
      totalActions,
      actionsByType,
      actionsByResource,
      actionsByAdmin,
      actionsPerDay
    };
  }

  /**
   * Clean up old activity logs
   */
  static async cleanup(daysOld: number = 365): Promise<number> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysOld);

    const query = `
      DELETE FROM admin_activity_logs 
      WHERE created_at < $1
    `;
    
    const result = await database.query(query, [cutoffDate]);
    return result.rowCount || 0;
  }

  /**
   * Log admin action (convenience method)
   */
  static async logAction(
    adminId: number,
    adminEmail: string,
    action: string,
    resource: string,
    resourceId?: number,
    details?: any,
    req?: any
  ): Promise<AdminActivityLog> {
    const data: CreateAdminActivityData = {
      adminId,
      adminEmail,
      action,
      resource,
      resourceId,
      details,
      ipAddress: req?.ip || req?.connection?.remoteAddress,
      userAgent: req?.get('User-Agent')
    };

    return this.create(data);
  }

  /**
   * Map database row to AdminActivityLog object
   */
  private static mapRowToActivityLog(row: any): AdminActivityLog {
    return {
      id: row.id,
      adminId: row.admin_id,
      adminEmail: row.admin_email,
      action: row.action,
      resource: row.resource,
      resourceId: row.resource_id,
      details: row.details ? JSON.parse(row.details) : null,
      ipAddress: row.ip_address,
      userAgent: row.user_agent,
      createdAt: new Date(row.created_at)
    };
  }
}
