/**
 * Invoice Model - Handles invoice database operations
 */

import { query, queryOne, transaction } from '../database';
import { 
  Invoice, 
  CreateInvoiceData, 
  UpdateInvoiceData, 
  InvoiceStatus 
} from '../types';

export class InvoiceModel {
  /**
   * Create a new invoice
   */
  static async create(data: CreateInvoiceData): Promise<Invoice> {
    // Generate invoice number and calculate totals using database functions
    const sql = `
      WITH invoice_data AS (
        SELECT 
          generate_invoice_number() as invoice_number,
          (calculate_invoice_totals($3, $10)).tax_amount as tax_amount,
          (calculate_invoice_totals($3, $10)).total_amount as total_amount
      )
      INSERT INTO invoices (
        invoice_number, order_id, payment_id, customer_name, customer_email,
        customer_address, customer_phone, customer_tax_id, subtotal, tax_rate,
        tax_amount, total_amount, currency, due_date, notes
      ) 
      SELECT 
        invoice_data.invoice_number, $1, $2, $4, $5, $6, $7, $8, $9, $10,
        invoice_data.tax_amount, invoice_data.total_amount, $11, $12, $13
      FROM invoice_data
      RETURNING *
    `;
    
    const values = [
      data.orderId,
      data.paymentId || null,
      data.subtotal,
      data.customerName,
      data.customerEmail,
      data.customerAddress || null,
      data.customerPhone || null,
      data.customerTaxId || null,
      data.subtotal,
      data.taxRate || 23.00,
      data.currency || 'EUR',
      data.dueDate || null,
      data.notes || null
    ];

    const result = await queryOne(sql, values);
    return this.mapRowToInvoice(result);
  }

  /**
   * Find invoice by ID
   */
  static async findById(id: number): Promise<Invoice | null> {
    const sql = `
      SELECT * FROM invoices 
      WHERE id = $1 AND deleted_at IS NULL
    `;
    
    const result = await queryOne(sql, [id]);
    return result ? this.mapRowToInvoice(result) : null;
  }

  /**
   * Find invoice by invoice number
   */
  static async findByInvoiceNumber(invoiceNumber: string): Promise<Invoice | null> {
    const sql = `
      SELECT * FROM invoices 
      WHERE invoice_number = $1 AND deleted_at IS NULL
    `;
    
    const result = await queryOne(sql, [invoiceNumber]);
    return result ? this.mapRowToInvoice(result) : null;
  }

  /**
   * Find invoices by order ID
   */
  static async findByOrderId(orderId: number): Promise<Invoice[]> {
    const sql = `
      SELECT * FROM invoices 
      WHERE order_id = $1 AND deleted_at IS NULL
      ORDER BY created_at DESC
    `;
    
    const result = await query(sql, [orderId]);
    return result.rows.map(row => this.mapRowToInvoice(row));
  }

  /**
   * Find invoices by payment ID
   */
  static async findByPaymentId(paymentId: number): Promise<Invoice[]> {
    const sql = `
      SELECT * FROM invoices 
      WHERE payment_id = $1 AND deleted_at IS NULL
      ORDER BY created_at DESC
    `;
    
    const result = await query(sql, [paymentId]);
    return result.rows.map(row => this.mapRowToInvoice(row));
  }

  /**
   * Update invoice
   */
  static async update(id: number, data: UpdateInvoiceData): Promise<Invoice | null> {
    const updateFields: string[] = [];
    const values: any[] = [];
    let paramCount = 1;

    if (data.status !== undefined) {
      updateFields.push(`status = $${paramCount++}`);
      values.push(data.status);
    }

    if (data.customerAddress !== undefined) {
      updateFields.push(`customer_address = $${paramCount++}`);
      values.push(data.customerAddress);
    }

    if (data.customerPhone !== undefined) {
      updateFields.push(`customer_phone = $${paramCount++}`);
      values.push(data.customerPhone);
    }

    if (data.dueDate !== undefined) {
      updateFields.push(`due_date = $${paramCount++}`);
      values.push(data.dueDate);
    }

    if (data.paidDate !== undefined) {
      updateFields.push(`paid_date = $${paramCount++}`);
      values.push(data.paidDate);
    }

    if (data.notes !== undefined) {
      updateFields.push(`notes = $${paramCount++}`);
      values.push(data.notes);
    }

    if (data.pdfPath !== undefined) {
      updateFields.push(`pdf_path = $${paramCount++}`);
      values.push(data.pdfPath);
    }

    if (data.pdfUrl !== undefined) {
      updateFields.push(`pdf_url = $${paramCount++}`);
      values.push(data.pdfUrl);
    }

    if (data.sentAt !== undefined) {
      updateFields.push(`sent_at = $${paramCount++}`);
      values.push(data.sentAt);
    }

    if (updateFields.length === 0) {
      return this.findById(id);
    }

    updateFields.push(`updated_at = CURRENT_TIMESTAMP`);
    values.push(id);

    const sql = `
      UPDATE invoices 
      SET ${updateFields.join(', ')}
      WHERE id = $${paramCount} AND deleted_at IS NULL
      RETURNING *
    `;

    const result = await queryOne(sql, values);
    return result ? this.mapRowToInvoice(result) : null;
  }

  /**
   * Get all invoices with pagination
   */
  static async findAll(
    offset: number = 0, 
    limit: number = 50,
    status?: InvoiceStatus
  ): Promise<{ invoices: Invoice[], total: number }> {
    let whereClause = 'WHERE deleted_at IS NULL';
    const values: any[] = [];
    let paramCount = 1;

    if (status) {
      whereClause += ` AND status = $${paramCount++}`;
      values.push(status);
    }

    // Get total count
    const countSql = `SELECT COUNT(*) FROM invoices ${whereClause}`;
    const countResult = await queryOne(countSql, values);
    const total = parseInt(countResult.count);

    // Get invoices
    values.push(limit, offset);
    const sql = `
      SELECT * FROM invoices 
      ${whereClause}
      ORDER BY created_at DESC
      LIMIT $${paramCount++} OFFSET $${paramCount++}
    `;

    const result = await query(sql, values);
    const invoices = result.rows.map(row => this.mapRowToInvoice(row));

    return { invoices, total };
  }

  /**
   * Get invoice statistics
   */
  static async getStatistics(): Promise<{
    totalInvoices: number;
    totalAmount: number;
    paidInvoices: number;
    overdueInvoices: number;
    draftInvoices: number;
  }> {
    const sql = `
      SELECT 
        COUNT(*) as total_invoices,
        COALESCE(SUM(total_amount), 0) as total_amount,
        COUNT(CASE WHEN status = 'paid' THEN 1 END) as paid_invoices,
        COUNT(CASE WHEN status = 'overdue' THEN 1 END) as overdue_invoices,
        COUNT(CASE WHEN status = 'draft' THEN 1 END) as draft_invoices
      FROM invoices 
      WHERE deleted_at IS NULL
    `;

    const result = await queryOne(sql);

    return {
      totalInvoices: parseInt(result.total_invoices),
      totalAmount: parseFloat(result.total_amount),
      paidInvoices: parseInt(result.paid_invoices),
      overdueInvoices: parseInt(result.overdue_invoices),
      draftInvoices: parseInt(result.draft_invoices)
    };
  }

  /**
   * Soft delete invoice
   */
  static async delete(id: number): Promise<boolean> {
    const sql = `
      UPDATE invoices 
      SET deleted_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
      WHERE id = $1 AND deleted_at IS NULL
    `;
    
    const result = await query(sql, [id]);
    return (result.rowCount || 0) > 0;
  }

  /**
   * Map database row to Invoice object
   */
  private static mapRowToInvoice(row: any): Invoice {
    const invoice: Invoice = {
      id: row.id,
      invoiceNumber: row.invoice_number,
      orderId: row.order_id,
      customerName: row.customer_name,
      customerEmail: row.customer_email,
      subtotal: parseFloat(row.subtotal),
      taxRate: parseFloat(row.tax_rate),
      taxAmount: parseFloat(row.tax_amount),
      totalAmount: parseFloat(row.total_amount),
      currency: row.currency,
      status: row.status as InvoiceStatus,
      issueDate: new Date(row.issue_date),
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at)
    };

    // Add optional fields only if they exist
    if (row.payment_id) invoice.paymentId = row.payment_id;
    if (row.customer_address) invoice.customerAddress = row.customer_address;
    if (row.customer_phone) invoice.customerPhone = row.customer_phone;
    if (row.customer_tax_id) invoice.customerTaxId = row.customer_tax_id;
    if (row.due_date) invoice.dueDate = new Date(row.due_date);
    if (row.paid_date) invoice.paidDate = new Date(row.paid_date);
    if (row.notes) invoice.notes = row.notes;
    if (row.pdf_path) invoice.pdfPath = row.pdf_path;
    if (row.pdf_url) invoice.pdfUrl = row.pdf_url;
    if (row.sent_at) invoice.sentAt = new Date(row.sent_at);
    if (row.deleted_at) invoice.deletedAt = new Date(row.deleted_at);

    return invoice;
  }
}

export default InvoiceModel;
