/**
 * File model - Handles file operations in the database
 */

import { QueryResult } from 'pg';
import { query, queryOne, transaction } from '../database';
import { File, CreateFileData, UpdateFileData } from '../types';

export class FileModel {
  /**
   * Create a new file record
   */
  static async create(data: CreateFileData): Promise<File> {
    const sql = `
      INSERT INTO files (filename, original_name, mime_type, file_size, file_path)
      VALUES ($1, $2, $3, $4, $5)
      RETURNING *
    `;

    const values = [
      data.filename,
      data.originalName,
      data.mimetype,
      data.size,
      data.url
    ];

    const result = await queryOne(sql, values);
    return this.mapRowToFile(result);
  }

  /**
   * Find file by ID
   */
  static async findById(id: number): Promise<File | null> {
    const sql = `
      SELECT * FROM files
      WHERE id = $1
    `;
    
    const result = await queryOne(sql, [id]);
    return result ? this.mapRowToFile(result) : null;
  }

  /**
   * Find file by filename
   */
  static async findByFilename(filename: string): Promise<File | null> {
    const sql = `
      SELECT * FROM files
      WHERE filename = $1
    `;
    
    const result = await queryOne(sql, [filename]);
    return result ? this.mapRowToFile(result) : null;
  }

  /**
   * Get all files with pagination
   */
  static async findAll(limit: number = 50, offset: number = 0): Promise<{ files: File[]; total: number }> {
    const countSql = `
      SELECT COUNT(*) as total FROM files
    `;

    const filesSql = `
      SELECT * FROM files
      ORDER BY created_at DESC
      LIMIT $1 OFFSET $2
    `;

    const [countResult, filesResult] = await Promise.all([
      queryOne(countSql),
      query(filesSql, [limit, offset])
    ]);

    return {
      files: filesResult.rows.map(row => this.mapRowToFile(row)),
      total: parseInt(countResult.total)
    };
  }

  /**
   * Update file record
   */
  static async update(id: number, data: UpdateFileData): Promise<File | null> {
    const fields = [];
    const values = [];
    let paramCount = 1;

    if (data.originalName !== undefined) {
      fields.push(`original_name = $${paramCount++}`);
      values.push(data.originalName);
    }

    if (data.url !== undefined) {
      fields.push(`url = $${paramCount++}`);
      values.push(data.url);
    }

    if (fields.length === 0) {
      return this.findById(id);
    }

    values.push(id);
    
    const sql = `
      UPDATE files 
      SET ${fields.join(', ')}
      WHERE id = $${paramCount}
      RETURNING *
    `;

    const result = await queryOne(sql, values);
    return result ? this.mapRowToFile(result) : null;
  }

  /**
   * Soft delete file
   */
  static async delete(id: number): Promise<boolean> {
    const sql = `
      DELETE FROM files
      WHERE id = $1
      RETURNING id
    `;

    const result = await queryOne(sql, [id]);
    return !!result;
  }

  /**
   * Check if file is used in any orders
   */
  static async isUsedInOrders(id: number): Promise<boolean> {
    const sql = `
      SELECT COUNT(*) as count FROM orders 
      WHERE file_id = $1
    `;

    const result = await queryOne(sql, [id]);
    return parseInt(result.count) > 0;
  }

  /**
   * Get files by mimetype
   */
  static async findByMimetype(mimetype: string, limit: number = 50): Promise<File[]> {
    const sql = `
      SELECT * FROM files
      WHERE mimetype = $1
      ORDER BY created_at DESC
      LIMIT $2
    `;

    const result = await query(sql, [mimetype, limit]);
    return result.rows.map(row => this.mapRowToFile(row));
  }

  /**
   * Get storage statistics
   */
  static async getStorageStats(): Promise<{
    totalFiles: number;
    totalSize: number;
    averageSize: number;
    mimetypeStats: Array<{ mimetype: string; count: number; totalSize: number }>;
  }> {
    const statsSql = `
      SELECT 
        COUNT(*) as total_files,
        COALESCE(SUM(size), 0) as total_size,
        COALESCE(AVG(size), 0) as average_size
      FROM files 
      WHERE deleted_at IS NULL
    `;

    const mimetypeSql = `
      SELECT 
        mimetype,
        COUNT(*) as count,
        COALESCE(SUM(size), 0) as total_size
      FROM files 
      WHERE deleted_at IS NULL
      GROUP BY mimetype
      ORDER BY count DESC
    `;

    const [statsResult, mimetypeResult] = await Promise.all([
      queryOne(statsSql),
      query(mimetypeSql)
    ]);

    return {
      totalFiles: parseInt(statsResult.total_files),
      totalSize: parseInt(statsResult.total_size),
      averageSize: parseFloat(statsResult.average_size),
      mimetypeStats: mimetypeResult.rows.map(row => ({
        mimetype: row.mimetype,
        count: parseInt(row.count),
        totalSize: parseInt(row.total_size)
      }))
    };
  }

  /**
   * Map database row to File object
   */
  private static mapRowToFile(row: any): File {
    return {
      id: row.id,
      filename: row.filename,
      originalName: row.original_name,
      mimetype: row.mime_type,
      size: row.file_size,
      url: row.file_path,
      uploadedAt: row.created_at,
      deletedAt: row.deleted_at
    };
  }

  /**
   * Find files by order ID
   */
  static async findByOrderId(orderId: number): Promise<File[]> {
    const sql = `
      SELECT * FROM files
      WHERE order_id = $1
      ORDER BY created_at DESC
    `;

    const result = await query(sql, [orderId]);
    return result.rows.map(row => this.mapRowToFile(row));
  }
}
