/**
 * Order model - Handles order operations in the database
 */

import { QueryResult } from 'pg';
import { query, queryOne, transaction } from '../database';
import { Order, CreateOrderData, UpdateOrderData, OrderStatus } from '../types';

// Extended interface for orders with file information
interface OrderWithFile extends Order {
  file: {
    filename: string;
    originalName: string;
    mimetype: string;
    size: number;
    uploadedAt: Date;
  };
}

export class OrderModel {
  /**
   * Create a new order
   */
  static async create(data: CreateOrderData): Promise<Order> {
    const orderNumber = await this.generateOrderNumber();
    
    const sql = `
      INSERT INTO orders (
        order_number, file_id, customer_name, customer_email, customer_phone,
        format, paper_type, finish, copies, pages, price, status, notes
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
      RETURNING *
    `;
    
    const values = [
      orderNumber,
      data.fileId,
      data.customerName || null,
      data.customerEmail || null,
      data.customerPhone || null,
      data.format,
      data.paperType,
      data.finish,
      data.copies,
      data.pages || null,
      data.price,
      data.status || 'pending',
      data.notes || null
    ];

    const result = await queryOne(sql, values);
    return this.mapRowToOrder(result);
  }

  /**
   * Find order by ID
   */
  static async findById(id: number): Promise<Order | null> {
    const sql = `SELECT * FROM orders WHERE id = $1`;
    const result = await queryOne(sql, [id]);
    return result ? this.mapRowToOrder(result) : null;
  }

  /**
   * Find order by order number
   */
  static async findByOrderNumber(orderNumber: string): Promise<Order | null> {
    const sql = `SELECT * FROM orders WHERE order_number = $1`;
    const result = await queryOne(sql, [orderNumber]);
    return result ? this.mapRowToOrder(result) : null;
  }

  /**
   * Find order with file information
   */
  static async findByIdWithFile(id: number): Promise<OrderWithFile | null> {
    const sql = `
      SELECT 
        o.*,
        f.filename,
        f.original_name,
        f.mimetype,
        f.size as file_size,
        f.uploaded_at as file_uploaded_at
      FROM orders o
      JOIN files f ON o.file_id = f.id
      WHERE o.id = $1 AND f.deleted_at IS NULL
    `;
    
    const result = await queryOne(sql, [id]);
    return result ? this.mapRowToOrderWithFile(result) : null;
  }

  /**
   * Get all orders with pagination and filters
   */
  static async findAll(options: {
    limit?: number;
    offset?: number;
    status?: OrderStatus;
    customerEmail?: string;
    orderBy?: 'created_at' | 'updated_at' | 'price';
    orderDirection?: 'ASC' | 'DESC';
  } = {}): Promise<{ orders: Order[]; total: number }> {
    const {
      limit = 50,
      offset = 0,
      status,
      customerEmail,
      orderBy = 'created_at',
      orderDirection = 'DESC'
    } = options;

    let whereConditions = [];
    let values = [];
    let paramCount = 1;

    if (status) {
      whereConditions.push(`status = $${paramCount++}`);
      values.push(status);
    }

    if (customerEmail) {
      whereConditions.push(`customer_email ILIKE $${paramCount++}`);
      values.push(`%${customerEmail}%`);
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

    const countSql = `
      SELECT COUNT(*) as total FROM orders ${whereClause}
    `;

    const ordersSql = `
      SELECT * FROM orders 
      ${whereClause}
      ORDER BY ${orderBy} ${orderDirection}
      LIMIT $${paramCount++} OFFSET $${paramCount++}
    `;

    values.push(limit, offset);

    const [countResult, ordersResult] = await Promise.all([
      queryOne(countSql, values.slice(0, -2)), // Remove limit and offset for count
      query(ordersSql, values)
    ]);

    return {
      orders: ordersResult.rows.map(row => this.mapRowToOrder(row)),
      total: parseInt(countResult.total)
    };
  }

  /**
   * Update order
   */
  static async update(id: number, data: UpdateOrderData): Promise<Order | null> {
    const fields = [];
    const values = [];
    let paramCount = 1;

    // Build dynamic update query
    Object.entries(data).forEach(([key, value]) => {
      if (value !== undefined) {
        const dbField = this.camelToSnake(key);
        fields.push(`${dbField} = $${paramCount++}`);
        values.push(value);
      }
    });

    if (fields.length === 0) {
      return this.findById(id);
    }

    values.push(id);
    
    const sql = `
      UPDATE orders 
      SET ${fields.join(', ')}
      WHERE id = $${paramCount}
      RETURNING *
    `;

    const result = await queryOne(sql, values);
    return result ? this.mapRowToOrder(result) : null;
  }

  /**
   * Update order status
   */
  static async updateStatus(id: number, status: OrderStatus, notes?: string): Promise<Order | null> {
    return this.update(id, { status, notes });
  }

  /**
   * Delete order (hard delete)
   */
  static async delete(id: number): Promise<boolean> {
    const sql = `DELETE FROM orders WHERE id = $1 RETURNING id`;
    const result = await queryOne(sql, [id]);
    return !!result;
  }

  /**
   * Get orders by status
   */
  static async findByStatus(status: OrderStatus, limit: number = 50): Promise<Order[]> {
    const sql = `
      SELECT * FROM orders 
      WHERE status = $1 
      ORDER BY created_at DESC 
      LIMIT $2
    `;
    
    const result = await query(sql, [status, limit]);
    return result.rows.map(row => this.mapRowToOrder(row));
  }

  /**
   * Get orders by customer email
   */
  static async findByCustomerEmail(email: string): Promise<Order[]> {
    const sql = `
      SELECT * FROM orders 
      WHERE customer_email = $1 
      ORDER BY created_at DESC
    `;
    
    const result = await query(sql, [email]);
    return result.rows.map(row => this.mapRowToOrder(row));
  }

  /**
   * Get order statistics
   */
  static async getStats(): Promise<{
    totalOrders: number;
    totalRevenue: number;
    averageOrderValue: number;
    statusCounts: Array<{ status: string; count: number }>;
  }> {
    const statsSql = `
      SELECT 
        COUNT(*) as total_orders,
        COALESCE(SUM(price), 0) as total_revenue,
        COALESCE(AVG(price), 0) as average_order_value
      FROM orders
    `;

    const statusSql = `
      SELECT status, COUNT(*) as count
      FROM orders
      GROUP BY status
      ORDER BY count DESC
    `;

    const [statsResult, statusResult] = await Promise.all([
      queryOne(statsSql),
      query(statusSql)
    ]);

    return {
      totalOrders: parseInt(statsResult.total_orders),
      totalRevenue: parseFloat(statsResult.total_revenue),
      averageOrderValue: parseFloat(statsResult.average_order_value),
      statusCounts: statusResult.rows.map(row => ({
        status: row.status,
        count: parseInt(row.count)
      }))
    };
  }

  /**
   * Generate unique order number
   */
  private static async generateOrderNumber(): Promise<string> {
    const prefix = 'WP';
    const timestamp = Date.now().toString().slice(-8); // Last 8 digits
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    
    let orderNumber = `${prefix}${timestamp}${random}`;
    
    // Ensure uniqueness
    let attempts = 0;
    while (attempts < 10) {
      const existing = await this.findByOrderNumber(orderNumber);
      if (!existing) break;
      
      attempts++;
      const newRandom = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
      orderNumber = `${prefix}${timestamp}${newRandom}`;
    }
    
    return orderNumber;
  }

  /**
   * Convert camelCase to snake_case
   */
  private static camelToSnake(str: string): string {
    return str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
  }

  /**
   * Map database row to Order object
   */
  private static mapRowToOrder(row: any): Order {
    return {
      id: row.id,
      orderNumber: row.order_number,
      fileId: row.file_id,
      customerName: row.customer_name,
      customerEmail: row.customer_email,
      customerPhone: row.customer_phone,
      format: row.format,
      paperType: row.paper_type,
      finish: row.finish,
      copies: row.copies,
      pages: row.pages,
      price: parseFloat(row.price),
      status: row.status,
      notes: row.notes,
      createdAt: row.created_at,
      updatedAt: row.updated_at
    };
  }

  /**
   * Map database row to OrderWithFile object
   */
  private static mapRowToOrderWithFile(row: any): OrderWithFile {
    return {
      ...this.mapRowToOrder(row),
      file: {
        filename: row.filename,
        originalName: row.original_name,
        mimetype: row.mimetype,
        size: row.file_size,
        uploadedAt: row.file_uploaded_at
      }
    };
  }
}
