/**
 * Order model - Handles order operations in the database
 */

import { QueryResult } from 'pg';
import { query, queryOne, transaction } from '../database';
import { Order, CreateOrderData, UpdateOrderData, OrderStatus } from '../types';

// Extended interface for orders with file information
interface OrderWithFile extends Order {
  file: {
    id: number;
    filename: string;
    originalName: string;
    mimetype: string;
    size: number;
    url: string;
    uploadedAt: Date;
  };
}

export class OrderModel {
  /**
   * Calculate order price based on specifications
   */
  static calculatePrice(specs: {
    pages: number;
    copies: number;
    format: string;
    paperType: string;
    finish: string;
    hasColor?: boolean;
    complexity?: 'low' | 'medium' | 'high';
  }): {
    basePrice: number;
    paperCost: number;
    finishCost: number;
    complexityCost: number;
    totalPrice: number;
    breakdown: {
      baseCostPerPage: number;
      paperMultiplier: number;
      finishCost: number;
      complexityMultiplier: number;
    };
  } {
    // Base prices per page
    const basePrices: Record<string, number> = {
      'A4': 0.50,
      'A3': 1.00,
      'A5': 0.30,
      'Letter': 0.50,
      'Legal': 0.60
    };

    // Paper type multipliers
    const paperMultipliers: Record<string, number> = {
      'standard': 1.0,
      'premium': 1.5,
      'photo': 2.0,
      'cardstock': 1.8
    };

    // Finish costs (fixed per order)
    const finishCosts: Record<string, number> = {
      'none': 0,
      'binding': 5.00,
      'lamination': 3.00,
      'stapling': 1.00,
      'hole-punch': 0.50
    };

    // Complexity multipliers
    const complexityMultipliers: Record<string, number> = {
      'low': 1.0,
      'medium': 1.2,
      'high': 1.5
    };

    const baseCostPerPage = basePrices[specs.format] || basePrices['A4'] || 0.50;
    const paperMultiplier = paperMultipliers[specs.paperType] || 1.0;
    const finishCost = finishCosts[specs.finish] || 0;
    const complexityMultiplier = complexityMultipliers[specs.complexity || 'low'] || 1.0;

    // Color printing adds 50% to base cost
    const colorMultiplier = specs.hasColor ? 1.5 : 1.0;

    const basePrice = specs.pages * specs.copies * baseCostPerPage * colorMultiplier;
    const paperCost = basePrice * (paperMultiplier - 1);
    const complexityCost = basePrice * (complexityMultiplier - 1);
    const totalPrice = basePrice + paperCost + finishCost + complexityCost;

    return {
      basePrice,
      paperCost,
      finishCost,
      complexityCost,
      totalPrice: Math.round(totalPrice * 100) / 100, // Round to 2 decimal places
      breakdown: {
        baseCostPerPage,
        paperMultiplier,
        finishCost,
        complexityMultiplier
      }
    };
  }

  /**
   * Create a new order with automatic price calculation
   */
  static async create(data: CreateOrderData): Promise<Order> {
    const orderNumber = await this.generateOrderNumber();

    // Calculate price if not provided
    let finalPrice = data.price;
    if (!finalPrice && data.pages) {
      const priceCalc = this.calculatePrice({
        pages: data.pages,
        copies: data.copies || 1,
        format: data.format,
        paperType: data.paperType,
        finish: data.finish,
        ...(data.hasColor !== undefined && { hasColor: data.hasColor }),
        ...(data.complexity !== undefined && { complexity: data.complexity })
      });
      finalPrice = priceCalc.totalPrice;
    }

    const sql = `
      INSERT INTO orders (
        order_number, file_id, customer_name, customer_email, customer_phone,
        format, paper_type, finish, copies, pages, price, status, notes,
        has_color, complexity, estimated_completion
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16)
      RETURNING *
    `;

    // Calculate estimated completion (2-5 business days based on complexity)
    const estimatedDays = data.complexity === 'high' ? 5 : data.complexity === 'medium' ? 3 : 2;
    const estimatedCompletion = new Date();
    estimatedCompletion.setDate(estimatedCompletion.getDate() + estimatedDays);

    const values = [
      orderNumber,
      data.fileId,
      data.customerName || null,
      data.customerEmail || null,
      data.customerPhone || null,
      data.format,
      data.paperType,
      data.finish,
      data.copies || 1,
      data.pages || null,
      finalPrice,
      data.status || 'pending',
      data.notes || null,
      data.hasColor || false,
      data.complexity || 'low',
      estimatedCompletion
    ];

    const result = await queryOne(sql, values);
    return this.mapRowToOrder(result);
  }

  /**
   * Find order by ID
   */
  static async findById(id: number): Promise<Order | null> {
    const sql = `SELECT * FROM orders WHERE id = $1`;
    const result = await queryOne(sql, [id]);
    return result ? this.mapRowToOrder(result) : null;
  }

  /**
   * Find order by order number
   */
  static async findByOrderNumber(orderNumber: string): Promise<Order | null> {
    const sql = `SELECT * FROM orders WHERE order_number = $1`;
    const result = await queryOne(sql, [orderNumber]);
    return result ? this.mapRowToOrder(result) : null;
  }

  /**
   * Find order with file information
   */
  static async findByIdWithFile(id: number): Promise<OrderWithFile | null> {
    const sql = `
      SELECT 
        o.*,
        f.filename,
        f.original_name,
        f.mimetype,
        f.size as file_size,
        f.uploaded_at as file_uploaded_at
      FROM orders o
      JOIN files f ON o.file_id = f.id
      WHERE o.id = $1 AND f.deleted_at IS NULL
    `;
    
    const result = await queryOne(sql, [id]);
    return result ? this.mapRowToOrderWithFile(result) : null;
  }

  /**
   * Get orders by status with advanced filtering
   */
  static async findByStatus(
    status: OrderStatus,
    limit: number = 50,
    options: {
      customerEmail?: string;
      dateFrom?: Date;
      dateTo?: Date;
      minPrice?: number;
      maxPrice?: number;
    } = {}
  ): Promise<Order[]> {
    const conditions = ['status = $1'];
    const values: any[] = [status];
    let paramCount = 2;

    if (options.customerEmail) {
      conditions.push(`customer_email ILIKE $${paramCount++}`);
      values.push(`%${options.customerEmail}%`);
    }

    if (options.dateFrom) {
      conditions.push(`created_at >= $${paramCount++}`);
      values.push(options.dateFrom);
    }

    if (options.dateTo) {
      conditions.push(`created_at <= $${paramCount++}`);
      values.push(options.dateTo);
    }

    if (options.minPrice) {
      conditions.push(`price >= $${paramCount++}`);
      values.push(options.minPrice);
    }

    if (options.maxPrice) {
      conditions.push(`price <= $${paramCount++}`);
      values.push(options.maxPrice);
    }

    const sql = `
      SELECT * FROM orders
      WHERE ${conditions.join(' AND ')}
      ORDER BY created_at DESC
      LIMIT $${paramCount}
    `;

    values.push(limit);
    const result = await query(sql, values);
    return result.rows.map(row => this.mapRowToOrder(row));
  }

  /**
   * Get orders statistics
   */
  static async getStatistics(period: 'day' | 'week' | 'month' | 'year' = 'month'): Promise<{
    totalOrders: number;
    totalRevenue: number;
    averageOrderValue: number;
    statusBreakdown: Record<OrderStatus, number>;
    revenueByPeriod: Array<{ period: string; revenue: number; orders: number }>;
  }> {
    // Get total statistics
    const totalSql = `
      SELECT
        COUNT(*) as total_orders,
        COALESCE(SUM(price), 0) as total_revenue,
        COALESCE(AVG(price), 0) as average_order_value
      FROM orders
      WHERE deleted_at IS NULL
    `;

    const totalResult = await queryOne(totalSql);

    // Get status breakdown
    const statusSql = `
      SELECT status, COUNT(*) as count
      FROM orders
      WHERE deleted_at IS NULL
      GROUP BY status
    `;

    const statusResult = await query(statusSql);
    const statusBreakdown: Record<OrderStatus, number> = {
      'pending': 0,
      'confirmed': 0,
      'processing': 0,
      'ready': 0,
      'completed': 0,
      'cancelled': 0
    };

    statusResult.rows.forEach(row => {
      statusBreakdown[row.status as OrderStatus] = parseInt(row.count);
    });

    // Get revenue by period
    const periodFormat = {
      'day': 'YYYY-MM-DD',
      'week': 'YYYY-"W"WW',
      'month': 'YYYY-MM',
      'year': 'YYYY'
    }[period];

    const revenueSql = `
      SELECT
        TO_CHAR(created_at, '${periodFormat}') as period,
        COALESCE(SUM(price), 0) as revenue,
        COUNT(*) as orders
      FROM orders
      WHERE deleted_at IS NULL
        AND created_at >= NOW() - INTERVAL '30 days'
      GROUP BY TO_CHAR(created_at, '${periodFormat}')
      ORDER BY period DESC
      LIMIT 10
    `;

    const revenueResult = await query(revenueSql);

    return {
      totalOrders: parseInt(totalResult.total_orders),
      totalRevenue: parseFloat(totalResult.total_revenue),
      averageOrderValue: parseFloat(totalResult.average_order_value),
      statusBreakdown,
      revenueByPeriod: revenueResult.rows.map(row => ({
        period: row.period,
        revenue: parseFloat(row.revenue),
        orders: parseInt(row.orders)
      }))
    };
  }

  /**
   * Get all orders with pagination and filters
   */
  static async findAll(options: {
    limit?: number;
    offset?: number;
    status?: OrderStatus;
    customerEmail?: string;
    orderBy?: 'created_at' | 'updated_at' | 'price';
    orderDirection?: 'ASC' | 'DESC';
  } = {}): Promise<{ orders: Order[]; total: number }> {
    const {
      limit = 50,
      offset = 0,
      status,
      customerEmail,
      orderBy = 'created_at',
      orderDirection = 'DESC'
    } = options;

    let whereConditions = [];
    let values = [];
    let paramCount = 1;

    if (status) {
      whereConditions.push(`status = $${paramCount++}`);
      values.push(status);
    }

    if (customerEmail) {
      whereConditions.push(`customer_email ILIKE $${paramCount++}`);
      values.push(`%${customerEmail}%`);
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

    const countSql = `
      SELECT COUNT(*) as total FROM orders ${whereClause}
    `;

    const ordersSql = `
      SELECT * FROM orders 
      ${whereClause}
      ORDER BY ${orderBy} ${orderDirection}
      LIMIT $${paramCount++} OFFSET $${paramCount++}
    `;

    values.push(limit, offset);

    const [countResult, ordersResult] = await Promise.all([
      queryOne(countSql, values.slice(0, -2)), // Remove limit and offset for count
      query(ordersSql, values)
    ]);

    return {
      orders: ordersResult.rows.map(row => this.mapRowToOrder(row)),
      total: parseInt(countResult.total)
    };
  }

  /**
   * Update order
   */
  static async update(id: number, data: UpdateOrderData): Promise<Order | null> {
    const fields: string[] = [];
    const values: any[] = [];
    let paramCount = 1;

    // Build dynamic update query
    Object.entries(data).forEach(([key, value]) => {
      if (value !== undefined) {
        const dbField = this.camelToSnake(key);
        fields.push(`${dbField} = $${paramCount++}`);
        values.push(value);
      }
    });

    if (fields.length === 0) {
      return this.findById(id);
    }

    values.push(id);
    
    const sql = `
      UPDATE orders 
      SET ${fields.join(', ')}
      WHERE id = $${paramCount}
      RETURNING *
    `;

    const result = await queryOne(sql, values);
    return result ? this.mapRowToOrder(result) : null;
  }

  /**
   * Update order status with workflow validation
   */
  static async updateStatus(id: number, status: OrderStatus, notes?: string): Promise<Order | null> {
    // Get current order to validate status transition
    const currentOrder = await this.findById(id);
    if (!currentOrder) {
      throw new Error('Order not found');
    }

    // Validate status transition
    const validTransitions: Record<OrderStatus, OrderStatus[]> = {
      [OrderStatus.PENDING]: [OrderStatus.CONFIRMED, OrderStatus.CANCELLED],
      [OrderStatus.CONFIRMED]: [OrderStatus.PROCESSING, OrderStatus.CANCELLED],
      [OrderStatus.PROCESSING]: [OrderStatus.READY, OrderStatus.CANCELLED],
      [OrderStatus.READY]: [OrderStatus.COMPLETED, OrderStatus.PROCESSING], // Can go back to processing if needed
      [OrderStatus.COMPLETED]: [], // Final state
      [OrderStatus.CANCELLED]: [OrderStatus.PENDING] // Can reopen cancelled orders
    };

    const allowedStatuses = validTransitions[currentOrder.status];
    if (!allowedStatuses.includes(status)) {
      throw new Error(`Invalid status transition from ${currentOrder.status} to ${status}`);
    }

    // Update with status history tracking
    const result = await transaction(async (client) => {
      // Update the order
      const updateSql = `
        UPDATE orders
        SET status = $1, notes = $2, updated_at = NOW()
        WHERE id = $3
        RETURNING *
      `;

      const orderResult = await client.query(updateSql, [status, notes || currentOrder.notes, id]);

      // Add to status history
      const historySql = `
        INSERT INTO status_history (order_id, from_status, to_status, notes, updated_by)
        VALUES ($1, $2, $3, $4, $5)
      `;

      await client.query(historySql, [
        id,
        currentOrder.status,
        status,
        notes || `Status changed from ${currentOrder.status} to ${status}`,
        'system' // In a real app, this would be the user ID
      ]);

      return orderResult.rows[0];
    });

    return this.mapRowToOrder(result);
  }

  /**
   * Get orders ready for pickup/delivery
   */
  static async getReadyOrders(limit: number = 20): Promise<Order[]> {
    const sql = `
      SELECT * FROM orders
      WHERE status = 'ready'
        AND deleted_at IS NULL
      ORDER BY updated_at ASC
      LIMIT $1
    `;

    const result = await query(sql, [limit]);
    return result.rows.map(row => this.mapRowToOrder(row));
  }

  /**
   * Get overdue orders (estimated completion passed)
   */
  static async getOverdueOrders(): Promise<Order[]> {
    const sql = `
      SELECT * FROM orders
      WHERE estimated_completion < NOW()
        AND status NOT IN ('completed', 'cancelled')
        AND deleted_at IS NULL
      ORDER BY estimated_completion ASC
    `;

    const result = await query(sql);
    return result.rows.map(row => this.mapRowToOrder(row));
  }

  /**
   * Get orders by customer email
   */
  static async findByCustomerEmail(email: string, limit: number = 50): Promise<Order[]> {
    const sql = `
      SELECT * FROM orders
      WHERE customer_email = $1
        AND deleted_at IS NULL
      ORDER BY created_at DESC
      LIMIT $2
    `;

    const result = await query(sql, [email, limit]);
    return result.rows.map(row => this.mapRowToOrder(row));
  }

  /**
   * Calculate estimated completion time based on current workload
   */
  static async calculateEstimatedCompletion(complexity: 'low' | 'medium' | 'high' = 'low'): Promise<Date> {
    // Get current pending/processing orders
    const workloadSql = `
      SELECT COUNT(*) as pending_orders
      FROM orders
      WHERE status IN ('pending', 'confirmed', 'processing')
        AND deleted_at IS NULL
    `;

    const workloadResult = await queryOne(workloadSql);
    const pendingOrders = parseInt(workloadResult.pending_orders);

    // Base days + workload factor + complexity factor
    const baseDays = {
      'low': 1,
      'medium': 2,
      'high': 3
    }[complexity];

    const workloadDays = Math.ceil(pendingOrders / 10); // 10 orders per day capacity
    const totalDays = baseDays + workloadDays;

    const estimatedDate = new Date();
    estimatedDate.setDate(estimatedDate.getDate() + totalDays);

    return estimatedDate;
  }

  /**
   * Delete order (hard delete)
   */
  static async delete(id: number): Promise<boolean> {
    const sql = `DELETE FROM orders WHERE id = $1 RETURNING id`;
    const result = await queryOne(sql, [id]);
    return !!result;
  }



  /**
   * Get order statistics
   */
  static async getStats(): Promise<{
    totalOrders: number;
    totalRevenue: number;
    averageOrderValue: number;
    statusCounts: Array<{ status: string; count: number }>;
  }> {
    const statsSql = `
      SELECT 
        COUNT(*) as total_orders,
        COALESCE(SUM(price), 0) as total_revenue,
        COALESCE(AVG(price), 0) as average_order_value
      FROM orders
    `;

    const statusSql = `
      SELECT status, COUNT(*) as count
      FROM orders
      GROUP BY status
      ORDER BY count DESC
    `;

    const [statsResult, statusResult] = await Promise.all([
      queryOne(statsSql),
      query(statusSql)
    ]);

    return {
      totalOrders: parseInt(statsResult.total_orders),
      totalRevenue: parseFloat(statsResult.total_revenue),
      averageOrderValue: parseFloat(statsResult.average_order_value),
      statusCounts: statusResult.rows.map(row => ({
        status: row.status,
        count: parseInt(row.count)
      }))
    };
  }

  /**
   * Generate unique order number
   */
  private static async generateOrderNumber(): Promise<string> {
    const prefix = 'WP';
    const timestamp = Date.now().toString().slice(-8); // Last 8 digits
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    
    let orderNumber = `${prefix}${timestamp}${random}`;
    
    // Ensure uniqueness
    let attempts = 0;
    while (attempts < 10) {
      const existing = await this.findByOrderNumber(orderNumber);
      if (!existing) break;
      
      attempts++;
      const newRandom = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
      orderNumber = `${prefix}${timestamp}${newRandom}`;
    }
    
    return orderNumber;
  }

  /**
   * Convert camelCase to snake_case
   */
  private static camelToSnake(str: string): string {
    return str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
  }

  /**
   * Map database row to Order object
   */
  private static mapRowToOrder(row: any): Order {
    return {
      id: row.id,
      orderNumber: row.order_number,
      fileId: row.file_id,
      customerName: row.customer_name,
      customerEmail: row.customer_email,
      customerPhone: row.customer_phone,
      format: row.format,
      paperType: row.paper_type,
      finish: row.finish,
      copies: row.copies,
      pages: row.pages,
      price: parseFloat(row.price),
      status: row.status,
      notes: row.notes,
      createdAt: row.created_at,
      updatedAt: row.updated_at
    };
  }

  /**
   * Map database row to OrderWithFile object
   */
  private static mapRowToOrderWithFile(row: any): OrderWithFile {
    return {
      ...this.mapRowToOrder(row),
      file: {
        id: row.file_id,
        filename: row.filename,
        originalName: row.original_name,
        mimetype: row.mimetype,
        size: row.file_size,
        url: row.file_url,
        uploadedAt: row.file_uploaded_at
      }
    };
  }
}
