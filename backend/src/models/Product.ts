/**
 * Product model - Handles product operations in the database
 */

import { QueryResult } from 'pg';
import { query, queryOne, transaction } from '../database';
import { 
  Product, 
  CreateProductData, 
  UpdateProductData,
  ProductQueryParams 
} from '../types';

export class ProductModel {
  /**
   * Get all products with optional filtering
   */
  static async findAll(params: ProductQueryParams = {}): Promise<Product[]> {
    const {
      page = 1,
      limit = 20,
      sortBy = 'created_at',
      sortOrder = 'desc',
      search,
      categoryId,
      categorySlug,
      tags,
      isFeatured,
      isActive,
      priceMin,
      priceMax,
      hasAiEnhancement,
      artistName
    } = params;

    let whereConditions: string[] = [];
    let queryParams: any[] = [];
    let paramIndex = 1;

    // Build WHERE conditions
    if (search) {
      whereConditions.push(`(p.title ILIKE $${paramIndex} OR p.description ILIKE $${paramIndex} OR p.artist_name ILIKE $${paramIndex})`);
      queryParams.push(`%${search}%`);
      paramIndex++;
    }

    if (categoryId) {
      whereConditions.push(`p.category_id = $${paramIndex}`);
      queryParams.push(categoryId);
      paramIndex++;
    }

    if (categorySlug) {
      whereConditions.push(`pc.slug = $${paramIndex}`);
      queryParams.push(categorySlug);
      paramIndex++;
    }

    if (tags && tags.length > 0) {
      whereConditions.push(`p.tags && $${paramIndex}`);
      queryParams.push(tags);
      paramIndex++;
    }

    if (isFeatured !== undefined) {
      whereConditions.push(`p.is_featured = $${paramIndex}`);
      queryParams.push(isFeatured);
      paramIndex++;
    }

    if (isActive !== undefined) {
      whereConditions.push(`p.is_active = $${paramIndex}`);
      queryParams.push(isActive);
      paramIndex++;
    }

    if (priceMin !== undefined) {
      whereConditions.push(`p.base_price >= $${paramIndex}`);
      queryParams.push(priceMin);
      paramIndex++;
    }

    if (priceMax !== undefined) {
      whereConditions.push(`p.base_price <= $${paramIndex}`);
      queryParams.push(priceMax);
      paramIndex++;
    }

    if (hasAiEnhancement !== undefined) {
      whereConditions.push(`p.has_ai_enhancement = $${paramIndex}`);
      queryParams.push(hasAiEnhancement);
      paramIndex++;
    }

    if (artistName) {
      whereConditions.push(`p.artist_name ILIKE $${paramIndex}`);
      queryParams.push(`%${artistName}%`);
      paramIndex++;
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';
    const offset = (page - 1) * limit;

    const sql = `
      SELECT 
        p.*,
        pc.name as category_name,
        pc.slug as category_slug
      FROM products p
      JOIN product_categories pc ON p.category_id = pc.id
      ${whereClause}
      ORDER BY p.${sortBy} ${sortOrder}
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;

    queryParams.push(limit, offset);

    const result = await query(sql, queryParams);
    return result.rows.map(this.mapRowToProduct);
  }

  /**
   * Get product by ID
   */
  static async findById(id: number): Promise<Product | null> {
    const sql = `
      SELECT 
        p.*,
        pc.name as category_name,
        pc.slug as category_slug
      FROM products p
      JOIN product_categories pc ON p.category_id = pc.id
      WHERE p.id = $1
    `;

    const result = await queryOne(sql, [id]);
    return result ? this.mapRowToProduct(result) : null;
  }

  /**
   * Get product by slug
   */
  static async findBySlug(slug: string): Promise<Product | null> {
    const sql = `
      SELECT 
        p.*,
        pc.name as category_name,
        pc.slug as category_slug
      FROM products p
      JOIN product_categories pc ON p.category_id = pc.id
      WHERE p.slug = $1 AND p.is_active = true
    `;

    const result = await queryOne(sql, [slug]);
    
    if (result) {
      // Increment view count
      await this.incrementViewCount(result.id);
      return this.mapRowToProduct(result);
    }
    
    return null;
  }

  /**
   * Get featured products
   */
  static async getFeatured(limit = 8): Promise<Product[]> {
    const sql = `
      SELECT 
        p.*,
        pc.name as category_name,
        pc.slug as category_slug
      FROM products p
      JOIN product_categories pc ON p.category_id = pc.id
      WHERE p.is_featured = true AND p.is_active = true
      ORDER BY p.created_at DESC
      LIMIT $1
    `;

    const result = await query(sql, [limit]);
    return result.rows.map(this.mapRowToProduct);
  }

  /**
   * Get related products
   */
  static async getRelated(productId: number, limit = 6): Promise<Product[]> {
    const sql = `
      SELECT 
        p.*,
        pc.name as category_name,
        pc.slug as category_slug
      FROM products p
      JOIN product_categories pc ON p.category_id = pc.id
      WHERE p.id != $1 
        AND p.category_id = (SELECT category_id FROM products WHERE id = $1)
        AND p.is_active = true
      ORDER BY p.view_count DESC, p.created_at DESC
      LIMIT $2
    `;

    const result = await query(sql, [productId, limit]);
    return result.rows.map(this.mapRowToProduct);
  }

  /**
   * Create new product
   */
  static async create(data: CreateProductData): Promise<Product> {
    const {
      title,
      slug,
      description,
      categoryId,
      imageUrl,
      imageWidth,
      imageHeight,
      imageSizeBytes,
      hasAiEnhancement = false,
      aiEnhancedUrl,
      artistName,
      artistCredit,
      tags = [],
      basePrice = 0,
      isFeatured = false,
      isActive = true
    } = data;

    const sql = `
      INSERT INTO products (
        title, slug, description, category_id, image_url, image_width, image_height,
        image_size_bytes, has_ai_enhancement, ai_enhanced_url, artist_name, artist_credit,
        tags, base_price, is_featured, is_active
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16)
      RETURNING *
    `;

    const result = await queryOne(sql, [
      title, slug, description, categoryId, imageUrl, imageWidth, imageHeight,
      imageSizeBytes, hasAiEnhancement, aiEnhancedUrl, artistName, artistCredit,
      tags, basePrice, isFeatured, isActive
    ]);

    return this.mapRowToProduct(result);
  }

  /**
   * Update product
   */
  static async update(id: number, data: UpdateProductData): Promise<Product | null> {
    const fields: string[] = [];
    const values: any[] = [];
    let paramIndex = 1;

    // Build dynamic update query
    Object.entries(data).forEach(([key, value]) => {
      if (value !== undefined) {
        const dbField = this.camelToSnake(key);
        fields.push(`${dbField} = $${paramIndex}`);
        values.push(value);
        paramIndex++;
      }
    });

    if (fields.length === 0) {
      return this.findById(id);
    }

    fields.push(`updated_at = CURRENT_TIMESTAMP`);
    values.push(id);

    const sql = `
      UPDATE products 
      SET ${fields.join(', ')}
      WHERE id = $${paramIndex}
      RETURNING *
    `;

    const result = await queryOne(sql, values);
    return result ? this.mapRowToProduct(result) : null;
  }

  /**
   * Delete product (soft delete)
   */
  static async delete(id: number): Promise<boolean> {
    const sql = `
      UPDATE products 
      SET is_active = false, updated_at = CURRENT_TIMESTAMP
      WHERE id = $1
    `;

    const result = await query(sql, [id]);
    return result.rowCount > 0;
  }

  /**
   * Increment view count
   */
  static async incrementViewCount(id: number): Promise<void> {
    const sql = `
      UPDATE products 
      SET view_count = view_count + 1, updated_at = CURRENT_TIMESTAMP
      WHERE id = $1
    `;

    await query(sql, [id]);
  }

  /**
   * Get all unique tags
   */
  static async getAllTags(): Promise<string[]> {
    const sql = `
      SELECT DISTINCT unnest(tags) as tag
      FROM products 
      WHERE is_active = true AND array_length(tags, 1) > 0
      ORDER BY tag ASC
    `;

    const result = await query(sql);
    return result.rows.map(row => row.tag);
  }

  /**
   * Check if slug exists
   */
  static async slugExists(slug: string, excludeId?: number): Promise<boolean> {
    let sql = `SELECT id FROM products WHERE slug = $1`;
    const params = [slug];

    if (excludeId) {
      sql += ` AND id != $2`;
      params.push(excludeId);
    }

    const result = await queryOne(sql, params);
    return !!result;
  }

  /**
   * Map database row to Product object
   */
  private static mapRowToProduct(row: any): Product {
    return {
      id: row.id,
      title: row.title,
      slug: row.slug,
      description: row.description,
      categoryId: row.category_id,
      imageUrl: row.image_url,
      imageWidth: row.image_width,
      imageHeight: row.image_height,
      imageSizeBytes: row.image_size_bytes,
      hasAiEnhancement: row.has_ai_enhancement,
      aiEnhancedUrl: row.ai_enhanced_url,
      artistName: row.artist_name,
      artistCredit: row.artist_credit,
      tags: row.tags || [],
      basePrice: parseFloat(row.base_price),
      isFeatured: row.is_featured,
      isActive: row.is_active,
      viewCount: row.view_count,
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at),
      category: row.category_name ? {
        id: row.category_id,
        name: row.category_name,
        slug: row.category_slug
      } as any : undefined
    };
  }

  /**
   * Convert camelCase to snake_case
   */
  private static camelToSnake(str: string): string {
    return str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
  }
}
