/**
 * ProductCategory model - Handles product category operations in the database
 */

import { QueryResult } from 'pg';
import { query, queryOne, transaction } from '../database';
import { 
  ProductCategory, 
  CreateProductCategoryData, 
  UpdateProductCategoryData,
  CategoryQueryParams 
} from '../types';

export class ProductCategoryModel {
  /**
   * Get all categories with optional filtering
   */
  static async findAll(params: CategoryQueryParams = {}): Promise<ProductCategory[]> {
    const {
      page = 1,
      limit = 50,
      sortBy = 'sort_order',
      sortOrder = 'asc',
      search,
      parentId,
      isActive,
      includeChildren = false
    } = params;

    let whereConditions: string[] = [];
    let queryParams: any[] = [];
    let paramIndex = 1;

    // Build WHERE conditions
    if (search) {
      whereConditions.push(`(name ILIKE $${paramIndex} OR description ILIKE $${paramIndex})`);
      queryParams.push(`%${search}%`);
      paramIndex++;
    }

    if (parentId !== undefined) {
      if (parentId === null) {
        whereConditions.push('parent_id IS NULL');
      } else {
        whereConditions.push(`parent_id = $${paramIndex}`);
        queryParams.push(parentId);
        paramIndex++;
      }
    }

    if (isActive !== undefined) {
      whereConditions.push(`is_active = $${paramIndex}`);
      queryParams.push(isActive);
      paramIndex++;
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';
    const offset = (page - 1) * limit;

    const sql = `
      SELECT 
        pc.*,
        parent.name as parent_name,
        parent.slug as parent_slug
      FROM product_categories pc
      LEFT JOIN product_categories parent ON pc.parent_id = parent.id
      ${whereClause}
      ORDER BY ${sortBy} ${sortOrder}
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;

    queryParams.push(limit, offset);

    const result = await query(sql, queryParams);
    return result.rows.map(this.mapRowToCategory);
  }

  /**
   * Get category by ID with optional children
   */
  static async findById(id: number, includeChildren = false): Promise<ProductCategory | null> {
    const sql = `
      SELECT 
        pc.*,
        parent.name as parent_name,
        parent.slug as parent_slug
      FROM product_categories pc
      LEFT JOIN product_categories parent ON pc.parent_id = parent.id
      WHERE pc.id = $1
    `;

    const result = await queryOne(sql, [id]);
    if (!result) return null;

    const category = this.mapRowToCategory(result);

    if (includeChildren) {
      category.children = await this.findChildren(id);
    }

    return category;
  }

  /**
   * Get category by slug
   */
  static async findBySlug(slug: string, includeChildren = false): Promise<ProductCategory | null> {
    const sql = `
      SELECT 
        pc.*,
        parent.name as parent_name,
        parent.slug as parent_slug
      FROM product_categories pc
      LEFT JOIN product_categories parent ON pc.parent_id = parent.id
      WHERE pc.slug = $1
    `;

    const result = await queryOne(sql, [slug]);
    if (!result) return null;

    const category = this.mapRowToCategory(result);

    if (includeChildren) {
      category.children = await this.findChildren(category.id);
    }

    return category;
  }

  /**
   * Get children categories
   */
  static async findChildren(parentId: number): Promise<ProductCategory[]> {
    const sql = `
      SELECT * FROM product_categories 
      WHERE parent_id = $1 AND is_active = true
      ORDER BY sort_order ASC, name ASC
    `;

    const result = await query(sql, [parentId]);
    return result.rows.map(this.mapRowToCategory);
  }

  /**
   * Create new category
   */
  static async create(data: CreateProductCategoryData): Promise<ProductCategory> {
    const {
      name,
      slug,
      description,
      imageUrl,
      parentId,
      sortOrder = 0,
      isActive = true
    } = data;

    const sql = `
      INSERT INTO product_categories (
        name, slug, description, image_url, parent_id, sort_order, is_active
      ) VALUES ($1, $2, $3, $4, $5, $6, $7)
      RETURNING *
    `;

    const result = await queryOne(sql, [
      name, slug, description, imageUrl, parentId, sortOrder, isActive
    ]);

    return this.mapRowToCategory(result);
  }

  /**
   * Update category
   */
  static async update(id: number, data: UpdateProductCategoryData): Promise<ProductCategory | null> {
    const fields: string[] = [];
    const values: any[] = [];
    let paramIndex = 1;

    // Build dynamic update query
    Object.entries(data).forEach(([key, value]) => {
      if (value !== undefined) {
        const dbField = this.camelToSnake(key);
        fields.push(`${dbField} = $${paramIndex}`);
        values.push(value);
        paramIndex++;
      }
    });

    if (fields.length === 0) {
      return this.findById(id);
    }

    fields.push(`updated_at = CURRENT_TIMESTAMP`);
    values.push(id);

    const sql = `
      UPDATE product_categories 
      SET ${fields.join(', ')}
      WHERE id = $${paramIndex}
      RETURNING *
    `;

    const result = await queryOne(sql, values);
    return result ? this.mapRowToCategory(result) : null;
  }

  /**
   * Delete category (soft delete)
   */
  static async delete(id: number): Promise<boolean> {
    const sql = `
      UPDATE product_categories 
      SET is_active = false, updated_at = CURRENT_TIMESTAMP
      WHERE id = $1
    `;

    const result = await query(sql, [id]);
    return result.rowCount > 0;
  }

  /**
   * Get category hierarchy (tree structure)
   */
  static async getHierarchy(): Promise<ProductCategory[]> {
    const sql = `
      WITH RECURSIVE category_tree AS (
        -- Base case: root categories
        SELECT 
          id, name, slug, description, image_url, parent_id, sort_order, is_active,
          created_at, updated_at, 0 as level
        FROM product_categories 
        WHERE parent_id IS NULL AND is_active = true
        
        UNION ALL
        
        -- Recursive case: children
        SELECT 
          pc.id, pc.name, pc.slug, pc.description, pc.image_url, pc.parent_id, 
          pc.sort_order, pc.is_active, pc.created_at, pc.updated_at, ct.level + 1
        FROM product_categories pc
        INNER JOIN category_tree ct ON pc.parent_id = ct.id
        WHERE pc.is_active = true
      )
      SELECT * FROM category_tree 
      ORDER BY level, sort_order, name
    `;

    const result = await query(sql);
    return result.rows.map(this.mapRowToCategory);
  }

  /**
   * Count products in category
   */
  static async countProducts(categoryId: number): Promise<number> {
    const sql = `
      SELECT COUNT(*) as count 
      FROM products 
      WHERE category_id = $1 AND is_active = true
    `;

    const result = await queryOne(sql, [categoryId]);
    return parseInt(result.count);
  }

  /**
   * Map database row to ProductCategory object
   */
  private static mapRowToCategory(row: any): ProductCategory {
    return {
      id: row.id,
      name: row.name,
      slug: row.slug,
      description: row.description,
      imageUrl: row.image_url,
      parentId: row.parent_id,
      sortOrder: row.sort_order,
      isActive: row.is_active,
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at),
      parent: row.parent_name ? {
        id: row.parent_id,
        name: row.parent_name,
        slug: row.parent_slug
      } as ProductCategory : undefined
    };
  }

  /**
   * Convert camelCase to snake_case
   */
  private static camelToSnake(str: string): string {
    return str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
  }
}
