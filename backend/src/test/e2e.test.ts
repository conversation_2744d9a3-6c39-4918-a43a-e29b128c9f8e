// ============================================================================
// WePrint AI Backend - End-to-End Tests
// ============================================================================

import request from 'supertest';
import app from '../app';

describe('End-to-End Tests - Complete User Journey', () => {
  let fileId: number;
  let orderId: number;
  let orderNumber: string;
  let paymentId: string;

  describe('Complete Customer Journey', () => {
    it('Step 1: Customer uploads a document', async () => {
      // Create a test PDF content
      const pdfContent = Buffer.from('%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n2 0 obj\n<<\n/Type /Pages\n/Kids [3 0 R]\n/Count 1\n>>\nendobj\n3 0 obj\n<<\n/Type /Page\n/Parent 2 0 R\n/MediaBox [0 0 612 792]\n>>\nendobj\nxref\n0 4\n0000000000 65535 f \n0000000009 00000 n \n0000000074 00000 n \n0000000120 00000 n \ntrailer\n<<\n/Size 4\n/Root 1 0 R\n>>\nstartxref\n179\n%%EOF');

      const response = await request(app)
        .post('/api/files/upload')
        .attach('file', pdfContent, 'customer-document.pdf')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.file).toHaveProperty('id');
      expect(response.body.data.file.originalName).toBe('customer-document.pdf');
      expect(response.body.data.file.mimetype).toBe('application/pdf');

      fileId = response.body.data.file.id;
      console.log(`✅ File uploaded successfully with ID: ${fileId}`);
    });

    it('Step 2: Customer calculates printing price', async () => {
      const response = await request(app)
        .post('/api/orders/calculate-price')
        .send({
          pages: 3,
          copies: 2,
          format: 'A4',
          paperType: 'premium',
          finish: 'glossy',
          hasColor: true,
          complexity: 'medium'
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.price).toHaveProperty('total');
      expect(response.body.data.price).toHaveProperty('breakdown');
      expect(response.body.data.price.total).toBeGreaterThan(0);

      console.log(`✅ Price calculated: ${response.body.data.price.total} AOA`);
    });

    it('Step 3: Customer creates an order', async () => {
      const orderData = {
        fileId: fileId,
        customerName: 'Maria Santos',
        customerEmail: '<EMAIL>',
        customerPhone: '+244923456789',
        format: 'A4',
        paperType: 'premium',
        finish: 'glossy',
        copies: 2,
        pages: 3,
        notes: 'Urgent printing needed for presentation',
        hasColor: true,
        complexity: 'medium'
      };

      const response = await request(app)
        .post('/api/orders')
        .send(orderData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.order).toHaveProperty('id');
      expect(response.body.data.order).toHaveProperty('orderNumber');
      expect(response.body.data.order.customerName).toBe(orderData.customerName);
      expect(response.body.data.order.customerEmail).toBe(orderData.customerEmail);
      expect(response.body.data.order.status).toBe('pending');

      orderId = response.body.data.order.id;
      orderNumber = response.body.data.order.orderNumber;
      console.log(`✅ Order created: ${orderNumber} (ID: ${orderId})`);
    });

    it('Step 4: Customer initiates payment', async () => {
      const paymentData = {
        orderId: orderId,
        amount: 1500, // 15.00 AOA
        customerEmail: '<EMAIL>',
        customerPhone: '+244923456789',
        description: `Payment for order ${orderNumber} - Premium A4 printing`
      };

      const response = await request(app)
        .post('/api/payments/create')
        .send(paymentData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.payment).toHaveProperty('id');
      expect(response.body.data.payment).toHaveProperty('reference');
      expect(response.body.data.payment.status).toBe('pending');
      expect(response.body.data.payment.amount).toBe(paymentData.amount);

      paymentId = response.body.data.payment.id;
      console.log(`✅ Payment initiated: ${response.body.data.payment.reference}`);
    });

    it('Step 5: Customer checks order status', async () => {
      const response = await request(app)
        .get(`/api/orders/${orderId}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.order.id).toBe(orderId);
      expect(response.body.data.order.orderNumber).toBe(orderNumber);
      expect(response.body.data.order.status).toBe('pending');

      console.log(`✅ Order status checked: ${response.body.data.order.status}`);
    });

    it('Step 6: Customer views their order history', async () => {
      const response = await request(app)
        .get('/api/orders/customer/<EMAIL>')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data.orders)).toBe(true);
      expect(response.body.data.orders.length).toBeGreaterThan(0);

      const customerOrder = response.body.data.orders.find((order: any) => order.id === orderId);
      expect(customerOrder).toBeDefined();
      expect(customerOrder.orderNumber).toBe(orderNumber);

      console.log(`✅ Customer order history retrieved: ${response.body.data.orders.length} orders`);
    });
  });

  describe('Complete Admin Journey', () => {
    let adminToken: string;

    it('Step 1: Admin logs in', async () => {
      const response = await request(app)
        .post('/api/admin/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'admin123'
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('token');
      expect(response.body.data.admin.email).toBe('<EMAIL>');

      adminToken = response.body.data.token;
      console.log(`✅ Admin logged in successfully`);
    });

    it('Step 2: Admin views dashboard statistics', async () => {
      const response = await request(app)
        .get('/api/admin/dashboard/stats')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('overview');
      expect(response.body.data).toHaveProperty('orderStats');
      expect(response.body.data).toHaveProperty('fileStats');
      expect(response.body.data).toHaveProperty('systemHealth');

      console.log(`✅ Dashboard stats retrieved: ${response.body.data.overview.totalOrders} total orders`);
    });

    it('Step 3: Admin views all orders', async () => {
      const response = await request(app)
        .get('/api/admin/orders')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data.orders)).toBe(true);

      const adminOrder = response.body.data.orders.find((order: any) => order.id === orderId);
      expect(adminOrder).toBeDefined();

      console.log(`✅ Admin order list retrieved: ${response.body.data.orders.length} orders`);
    });

    it('Step 4: Admin updates order status to processing', async () => {
      const response = await request(app)
        .put(`/api/admin/orders/${orderId}/status`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          status: 'processing',
          notes: 'Order received and being prepared for printing'
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.order.status).toBe('processing');
      expect(response.body.data.order.notes).toContain('being prepared');

      console.log(`✅ Order status updated to: processing`);
    });

    it('Step 5: Admin completes the order', async () => {
      const response = await request(app)
        .put(`/api/admin/orders/${orderId}/status`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          status: 'completed',
          notes: 'Order printed successfully and ready for delivery'
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.order.status).toBe('completed');

      console.log(`✅ Order completed successfully`);
    });

    it('Step 6: Admin views recent activity', async () => {
      const response = await request(app)
        .get('/api/admin/dashboard/recent-activity')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data.activities)).toBe(true);

      console.log(`✅ Recent activity retrieved: ${response.body.data.activities.length} activities`);
    });
  });

  describe('Complete Workflow Validation', () => {
    it('Should verify final order state', async () => {
      const response = await request(app)
        .get(`/api/orders/${orderId}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.order.status).toBe('completed');
      expect(response.body.data.order.customerName).toBe('Maria Santos');
      expect(response.body.data.order.customerEmail).toBe('<EMAIL>');

      console.log(`✅ Final verification: Order ${orderNumber} is completed`);
    });

    it('Should verify customer can see completed order', async () => {
      const response = await request(app)
        .get('/api/orders/customer/<EMAIL>')
        .expect(200);

      expect(response.body.success).toBe(true);
      const completedOrder = response.body.data.orders.find((order: any) => order.id === orderId);
      expect(completedOrder).toBeDefined();
      expect(completedOrder.status).toBe('completed');

      console.log(`✅ Customer can see completed order in their history`);
    });

    it('Should verify notifications were created', async () => {
      const response = await request(app)
        .get('/api/notifications')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data.notifications)).toBe(true);

      // Should have notifications for order creation and status updates
      const orderNotifications = response.body.data.notifications.filter(
        (notification: any) => notification.data && notification.data.orderId === orderId
      );
      expect(orderNotifications.length).toBeGreaterThan(0);

      console.log(`✅ Notifications created for order lifecycle`);
    });
  });

  afterAll(() => {
    console.log('\n🎉 End-to-End Test Summary:');
    console.log(`📄 File uploaded: ID ${fileId}`);
    console.log(`📋 Order created: ${orderNumber} (ID: ${orderId})`);
    console.log(`💳 Payment initiated: ID ${paymentId}`);
    console.log(`✅ Order completed successfully`);
    console.log(`🔔 Notifications generated`);
    console.log('\n✨ Complete workflow validated successfully!');
  });
});
