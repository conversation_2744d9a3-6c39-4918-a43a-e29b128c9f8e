// ============================================================================
// WePrint AI Backend - Integration Tests
// ============================================================================

import request from 'supertest';
import app from '../app';
import { testData } from './setup';

describe('Integration Tests - Complete Workflow', () => {
  let uploadedFileId: number;
  let createdOrderId: number;
  let adminToken: string;

  beforeAll(async () => {
    // Login as admin to get token
    const loginResponse = await request(app)
      .post('/api/admin/auth/login')
      .send({
        email: '<EMAIL>',
        password: 'admin123'
      });
    
    expect(loginResponse.status).toBe(200);
    adminToken = loginResponse.body.data.token;
  });

  describe('File Upload Workflow', () => {
    it('should upload a file successfully', async () => {
      const response = await request(app)
        .post('/api/files/upload')
        .attach('file', Buffer.from('Test file content'), 'test-document.txt')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.file).toHaveProperty('id');
      expect(response.body.data.file.originalName).toBe('test-document.txt');
      
      uploadedFileId = response.body.data.file.id;
    });

    it('should retrieve uploaded file info', async () => {
      const response = await request(app)
        .get(`/api/files/${uploadedFileId}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.file.id).toBe(uploadedFileId);
    });

    it('should list all files', async () => {
      const response = await request(app)
        .get('/api/files')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data.files)).toBe(true);
      expect(response.body.data.files.length).toBeGreaterThan(0);
    });
  });

  describe('Order Creation Workflow', () => {
    it('should calculate price for order', async () => {
      const response = await request(app)
        .post('/api/orders/calculate-price')
        .send({
          pages: 5,
          copies: 2,
          format: 'A4',
          paperType: 'premium',
          finish: 'laminated',
          hasColor: true,
          complexity: 'medium'
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.price).toHaveProperty('total');
      expect(response.body.data.price.total).toBeGreaterThan(0);
    });

    it('should create an order successfully', async () => {
      const orderData = {
        fileId: uploadedFileId,
        customerName: 'João Silva',
        customerEmail: '<EMAIL>',
        customerPhone: '+244900000000',
        format: 'A4',
        paperType: 'standard',
        finish: 'none',
        copies: 1,
        notes: 'Integration test order'
      };

      const response = await request(app)
        .post('/api/orders')
        .send(orderData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.order).toHaveProperty('id');
      expect(response.body.data.order).toHaveProperty('orderNumber');
      expect(response.body.data.order.customerName).toBe(orderData.customerName);
      expect(response.body.data.order.status).toBe('pending');
      
      createdOrderId = response.body.data.order.id;
    });

    it('should retrieve created order', async () => {
      const response = await request(app)
        .get(`/api/orders/${createdOrderId}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.order.id).toBe(createdOrderId);
    });

    it('should list customer orders', async () => {
      const response = await request(app)
        .get('/api/orders/customer/<EMAIL>')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data.orders)).toBe(true);
      expect(response.body.data.orders.length).toBeGreaterThan(0);
      
      const order = response.body.data.orders.find((o: any) => o.id === createdOrderId);
      expect(order).toBeDefined();
    });
  });

  describe('Admin Workflow', () => {
    it('should get dashboard stats', async () => {
      const response = await request(app)
        .get('/api/admin/dashboard/stats')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('overview');
      expect(response.body.data).toHaveProperty('orderStats');
      expect(response.body.data).toHaveProperty('fileStats');
    });

    it('should list orders as admin', async () => {
      const response = await request(app)
        .get('/api/admin/orders')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data.orders)).toBe(true);
    });

    it('should update order status as admin', async () => {
      const response = await request(app)
        .put(`/api/admin/orders/${createdOrderId}/status`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          status: 'processing',
          notes: 'Order is being processed - integration test'
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.order.status).toBe('processing');
    });
  });

  describe('Payment Workflow', () => {
    it('should create payment for order', async () => {
      const response = await request(app)
        .post('/api/payments/create')
        .send({
          orderId: createdOrderId,
          amount: 500,
          customerEmail: '<EMAIL>',
          customerPhone: '+244900000000',
          description: 'Integration test payment'
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.payment).toHaveProperty('id');
      expect(response.body.data.payment.status).toBe('pending');
    });
  });

  describe('Notifications Workflow', () => {
    it('should list notifications', async () => {
      const response = await request(app)
        .get('/api/notifications')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data.notifications)).toBe(true);
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid file upload', async () => {
      const response = await request(app)
        .post('/api/files/upload')
        .attach('file', Buffer.from('x'.repeat(20 * 1024 * 1024)), 'huge-file.txt')
        .expect(400);

      expect(response.body.success).toBe(false);
    });

    it('should handle invalid order creation', async () => {
      const response = await request(app)
        .post('/api/orders')
        .send({
          fileId: 99999, // Non-existent file
          customerName: 'Test',
          customerEmail: 'invalid-email',
          customerPhone: 'invalid-phone'
        })
        .expect(400);

      expect(response.body.success).toBe(false);
    });

    it('should handle unauthorized admin access', async () => {
      const response = await request(app)
        .get('/api/admin/dashboard/stats')
        .expect(401);

      expect(response.body.success).toBe(false);
    });
  });

  describe('Data Validation', () => {
    it('should validate email formats', async () => {
      const invalidEmails = ['invalid', '@domain.com', 'user@', 'user@domain'];
      
      for (const email of invalidEmails) {
        const response = await request(app)
          .post('/api/orders')
          .send({
            ...testData.validOrder,
            fileId: uploadedFileId,
            customerEmail: email
          })
          .expect(400);

        expect(response.body.success).toBe(false);
      }
    });

    it('should validate phone formats', async () => {
      const invalidPhones = ['123', 'abc', '+123456789', '244123456789'];
      
      for (const phone of invalidPhones) {
        const response = await request(app)
          .post('/api/orders')
          .send({
            ...testData.validOrder,
            fileId: uploadedFileId,
            customerPhone: phone
          })
          .expect(400);

        expect(response.body.success).toBe(false);
      }
    });
  });
});
