/**
 * Database models test suite
 * Tests all database operations and models
 */

import { FileModel, OrderModel, StatusHistoryModel } from '../models';
import database from '../database';
import { CreateFileData, CreateOrderData, OrderStatus } from '../types';

describe('Database Models', () => {
  beforeAll(async () => {
    // Initialize database connection for tests
    try {
      await database.initialize();
    } catch (error) {
      console.log('Database not available for tests, skipping...');
      return;
    }
  });

  afterAll(async () => {
    // Close database connection
    await database.close();
  });

  describe('FileModel', () => {
    let testFileId: number;

    const testFileData: CreateFileData = {
      filename: 'test-document.pdf',
      originalName: 'My Document.pdf',
      mimetype: 'application/pdf',
      size: 1024000,
      url: '/uploads/test-document.pdf'
    };

    it('should create a new file', async () => {
      const file = await FileModel.create(testFileData);
      
      expect(file).toBeDefined();
      expect(file.id).toBeDefined();
      expect(file.filename).toBe(testFileData.filename);
      expect(file.originalName).toBe(testFileData.originalName);
      expect(file.mimetype).toBe(testFileData.mimetype);
      expect(file.size).toBe(testFileData.size);
      expect(file.url).toBe(testFileData.url);
      expect(file.uploadedAt).toBeDefined();
      
      testFileId = file.id;
    });

    it('should find file by ID', async () => {
      const file = await FileModel.findById(testFileId);
      
      expect(file).toBeDefined();
      expect(file!.id).toBe(testFileId);
      expect(file!.filename).toBe(testFileData.filename);
    });

    it('should find file by filename', async () => {
      const file = await FileModel.findByFilename(testFileData.filename);
      
      expect(file).toBeDefined();
      expect(file!.id).toBe(testFileId);
      expect(file!.filename).toBe(testFileData.filename);
    });

    it('should update file', async () => {
      const updatedData = {
        originalName: 'Updated Document.pdf'
      };
      
      const file = await FileModel.update(testFileId, updatedData);
      
      expect(file).toBeDefined();
      expect(file!.originalName).toBe(updatedData.originalName);
    });

    it('should get all files with pagination', async () => {
      const result = await FileModel.findAll(10, 0);
      
      expect(result).toBeDefined();
      expect(result.files).toBeInstanceOf(Array);
      expect(result.total).toBeGreaterThanOrEqual(1);
      expect(result.files.length).toBeGreaterThanOrEqual(1);
    });

    it('should get storage statistics', async () => {
      const stats = await FileModel.getStorageStats();
      
      expect(stats).toBeDefined();
      expect(stats.totalFiles).toBeGreaterThanOrEqual(1);
      expect(stats.totalSize).toBeGreaterThanOrEqual(testFileData.size);
      expect(stats.averageSize).toBeGreaterThan(0);
      expect(stats.mimetypeStats).toBeInstanceOf(Array);
    });

    it('should check if file is used in orders', async () => {
      const isUsed = await FileModel.isUsedInOrders(testFileId);
      expect(typeof isUsed).toBe('boolean');
    });

    it('should soft delete file', async () => {
      const deleted = await FileModel.delete(testFileId);
      expect(deleted).toBe(true);
      
      // Should not find deleted file
      const file = await FileModel.findById(testFileId);
      expect(file).toBeNull();
    });
  });

  describe('OrderModel', () => {
    let testOrderId: number;
    let testFileId: number;

    beforeAll(async () => {
      // Create a test file for orders
      const testFile = await FileModel.create({
        filename: 'order-test-document.pdf',
        originalName: 'Order Test Document.pdf',
        mimetype: 'application/pdf',
        size: 2048000,
        url: '/uploads/order-test-document.pdf'
      });
      testFileId = testFile.id;
    });

    const testOrderData: CreateOrderData = {
      fileId: 0, // Will be set in beforeAll
      customerName: 'João Silva',
      customerEmail: '<EMAIL>',
      customerPhone: '+244 912 345 678',
      format: 'A4',
      paperType: 'standard',
      finish: 'none',
      copies: 2,
      pages: 10,
      price: 15.50,
      status: 'pending' as OrderStatus,
      notes: 'Urgent order'
    };

    it('should create a new order', async () => {
      testOrderData.fileId = testFileId;
      const order = await OrderModel.create(testOrderData);
      
      expect(order).toBeDefined();
      expect(order.id).toBeDefined();
      expect(order.orderNumber).toBeDefined();
      expect(order.orderNumber).toMatch(/^WP\d+$/);
      expect(order.fileId).toBe(testFileId);
      expect(order.customerName).toBe(testOrderData.customerName);
      expect(order.customerEmail).toBe(testOrderData.customerEmail);
      expect(order.format).toBe(testOrderData.format);
      expect(order.copies).toBe(testOrderData.copies);
      expect(order.price).toBe(testOrderData.price);
      expect(order.status).toBe(testOrderData.status);
      
      testOrderId = order.id;
    });

    it('should find order by ID', async () => {
      const order = await OrderModel.findById(testOrderId);
      
      expect(order).toBeDefined();
      expect(order!.id).toBe(testOrderId);
      expect(order!.customerName).toBe(testOrderData.customerName);
    });

    it('should find order with file information', async () => {
      const orderWithFile = await OrderModel.findByIdWithFile(testOrderId);
      
      expect(orderWithFile).toBeDefined();
      expect(orderWithFile!.id).toBe(testOrderId);
      expect(orderWithFile!.file).toBeDefined();
      expect(orderWithFile!.file.filename).toBe('order-test-document.pdf');
    });

    it('should update order status', async () => {
      const newStatus: OrderStatus = OrderStatus.CONFIRMED;
      const order = await OrderModel.updateStatus(testOrderId, newStatus, 'Order confirmed by admin');
      
      expect(order).toBeDefined();
      expect(order!.status).toBe(newStatus);
      expect(order!.notes).toBe('Order confirmed by admin');
    });

    it('should get all orders with pagination', async () => {
      const result = await OrderModel.findAll({ limit: 10, offset: 0 });
      
      expect(result).toBeDefined();
      expect(result.orders).toBeInstanceOf(Array);
      expect(result.total).toBeGreaterThanOrEqual(1);
      expect(result.orders.length).toBeGreaterThanOrEqual(1);
    });

    it('should find orders by status', async () => {
      const orders = await OrderModel.findByStatus(OrderStatus.CONFIRMED, 10);
      
      expect(orders).toBeInstanceOf(Array);
      expect(orders.length).toBeGreaterThanOrEqual(1);
      expect(orders[0]?.status).toBe(OrderStatus.CONFIRMED);
    });

    it('should find orders by customer email', async () => {
      const orders = await OrderModel.findByCustomerEmail(testOrderData.customerEmail!);
      
      expect(orders).toBeInstanceOf(Array);
      expect(orders.length).toBeGreaterThanOrEqual(1);
      expect(orders[0]?.customerEmail).toBe(testOrderData.customerEmail);
    });

    it('should get order statistics', async () => {
      const stats = await OrderModel.getStats();
      
      expect(stats).toBeDefined();
      expect(stats.totalOrders).toBeGreaterThanOrEqual(1);
      expect(stats.totalRevenue).toBeGreaterThanOrEqual(testOrderData.price || 0);
      expect(stats.averageOrderValue).toBeGreaterThan(0);
      expect(stats.statusCounts).toBeInstanceOf(Array);
      expect(stats.statusCounts.length).toBeGreaterThan(0);
    });
  });

  describe('StatusHistoryModel', () => {
    let testOrderId: number;

    beforeAll(async () => {
      // Get the test order ID from previous tests
      const orders = await OrderModel.findAll({ limit: 1 });
      if (orders.orders.length > 0) {
        testOrderId = orders.orders[0]?.id;
      }
    });

    it('should get status history for order', async () => {
      if (!testOrderId) {
        console.log('No test order available, skipping status history tests');
        return;
      }

      const history = await StatusHistoryModel.findByOrderId(testOrderId);
      
      expect(history).toBeInstanceOf(Array);
      expect(history.length).toBeGreaterThanOrEqual(1);
      
      if (history.length > 0) {
        expect(history[0]?.orderId).toBe(testOrderId);
        expect(history[0]?.status).toBeDefined();
        expect(history[0]?.updatedAt).toBeDefined();
      }
    });

    it('should get latest status for order', async () => {
      if (!testOrderId) return;

      const latestStatus = await StatusHistoryModel.getLatestStatus(testOrderId);
      
      expect(latestStatus).toBeDefined();
      expect(latestStatus!.orderId).toBe(testOrderId);
      expect(latestStatus!.status).toBeDefined();
    });

    it('should get status transition statistics', async () => {
      const stats = await StatusHistoryModel.getStatusTransitionStats();
      
      expect(stats).toBeInstanceOf(Array);
      
      if (stats.length > 0) {
        expect(stats[0]?.toStatus).toBeDefined();
        expect(stats[0]?.count).toBeGreaterThan(0);
      }
    });

    it('should get status history with pagination', async () => {
      const result = await StatusHistoryModel.findAll(10, 0);
      
      expect(result).toBeDefined();
      expect(result.history).toBeInstanceOf(Array);
      expect(result.total).toBeGreaterThanOrEqual(0);
    });
  });
});
