/**
 * Orders Test Suite
 * Tests for order management functionality
 */

import { OrderModel } from '../models/Order';
import { CreateOrderData, OrderStatus } from '../types';

describe('Order Management', () => {
  describe('Price Calculation', () => {
    test('should calculate basic A4 price correctly', () => {
      const specs = {
        pages: 10,
        copies: 1,
        format: 'A4',
        paperType: 'standard',
        finish: 'none'
      };

      const result = OrderModel.calculatePrice(specs);

      expect(result.totalPrice).toBe(5.00); // 10 pages * 1 copy * 0.50 per page
      expect(result.basePrice).toBe(5.00);
      expect(result.paperCost).toBe(0);
      expect(result.finishCost).toBe(0);
      expect(result.complexityCost).toBe(0);
    });

    test('should apply color printing multiplier', () => {
      const specs = {
        pages: 10,
        copies: 1,
        format: 'A4',
        paperType: 'standard',
        finish: 'none',
        hasColor: true
      };

      const result = OrderModel.calculatePrice(specs);

      expect(result.totalPrice).toBe(7.50); // 10 * 1 * 0.50 * 1.5 (color)
      expect(result.basePrice).toBe(7.50);
    });

    test('should apply premium paper multiplier', () => {
      const specs = {
        pages: 10,
        copies: 1,
        format: 'A4',
        paperType: 'premium',
        finish: 'none'
      };

      const result = OrderModel.calculatePrice(specs);

      expect(result.totalPrice).toBe(7.50); // 5.00 base + 2.50 paper cost (50% extra)
      expect(result.basePrice).toBe(5.00);
      expect(result.paperCost).toBe(2.50);
    });

    test('should add finish costs', () => {
      const specs = {
        pages: 10,
        copies: 1,
        format: 'A4',
        paperType: 'standard',
        finish: 'binding'
      };

      const result = OrderModel.calculatePrice(specs);

      expect(result.totalPrice).toBe(10.00); // 5.00 base + 5.00 binding
      expect(result.basePrice).toBe(5.00);
      expect(result.finishCost).toBe(5.00);
    });

    test('should apply complexity multiplier', () => {
      const specs = {
        pages: 10,
        copies: 1,
        format: 'A4',
        paperType: 'standard',
        finish: 'none',
        complexity: 'high' as const
      };

      const result = OrderModel.calculatePrice(specs);

      expect(result.totalPrice).toBe(7.50); // 5.00 base + 2.50 complexity (50% extra)
      expect(result.basePrice).toBe(5.00);
      expect(result.complexityCost).toBe(2.50);
    });

    test('should handle multiple copies', () => {
      const specs = {
        pages: 5,
        copies: 3,
        format: 'A4',
        paperType: 'standard',
        finish: 'none'
      };

      const result = OrderModel.calculatePrice(specs);

      expect(result.totalPrice).toBe(7.50); // 5 pages * 3 copies * 0.50 per page
      expect(result.basePrice).toBe(7.50);
    });

    test('should handle A3 format pricing', () => {
      const specs = {
        pages: 5,
        copies: 1,
        format: 'A3',
        paperType: 'standard',
        finish: 'none'
      };

      const result = OrderModel.calculatePrice(specs);

      expect(result.totalPrice).toBe(5.00); // 5 pages * 1 copy * 1.00 per A3 page
      expect(result.basePrice).toBe(5.00);
    });

    test('should combine all factors correctly', () => {
      const specs = {
        pages: 10,
        copies: 2,
        format: 'A4',
        paperType: 'premium',
        finish: 'lamination',
        hasColor: true,
        complexity: 'medium' as const
      };

      const result = OrderModel.calculatePrice(specs);

      // Base: 10 * 2 * 0.50 * 1.5 (color) = 15.00
      // Paper: 15.00 * 0.5 (premium extra) = 7.50
      // Finish: 3.00 (lamination)
      // Complexity: 15.00 * 0.2 (medium extra) = 3.00
      // Total: 15.00 + 7.50 + 3.00 + 3.00 = 28.50

      expect(result.totalPrice).toBeCloseTo(28.50, 2);
      expect(result.basePrice).toBeCloseTo(15.00, 2);
      expect(result.paperCost).toBeCloseTo(7.50, 2);
      expect(result.finishCost).toBeCloseTo(3.00, 2);
      expect(result.complexityCost).toBeCloseTo(3.00, 2);
    });

    test('should round to 2 decimal places', () => {
      const specs = {
        pages: 3,
        copies: 1,
        format: 'A4',
        paperType: 'standard',
        finish: 'none'
      };

      const result = OrderModel.calculatePrice(specs);

      expect(result.totalPrice).toBe(1.50); // Should be exactly 1.50, not 1.4999999
      expect(Number.isInteger(result.totalPrice * 100)).toBe(true);
    });

    test('should handle unknown format with A4 default', () => {
      const specs = {
        pages: 10,
        copies: 1,
        format: 'Unknown',
        paperType: 'standard',
        finish: 'none'
      };

      const result = OrderModel.calculatePrice(specs);

      expect(result.totalPrice).toBe(5.00); // Should default to A4 pricing
      expect(result.breakdown.baseCostPerPage).toBe(0.50);
    });

    test('should handle unknown paper type with standard default', () => {
      const specs = {
        pages: 10,
        copies: 1,
        format: 'A4',
        paperType: 'unknown',
        finish: 'none'
      };

      const result = OrderModel.calculatePrice(specs);

      expect(result.totalPrice).toBe(5.00); // Should default to standard paper
      expect(result.paperCost).toBe(0);
    });

    test('should handle unknown finish with no cost', () => {
      const specs = {
        pages: 10,
        copies: 1,
        format: 'A4',
        paperType: 'standard',
        finish: 'unknown'
      };

      const result = OrderModel.calculatePrice(specs);

      expect(result.totalPrice).toBe(5.00); // Should default to no finish cost
      expect(result.finishCost).toBe(0);
    });
  });

  describe('Order Creation Data Validation', () => {
    test('should validate required fields for order creation', () => {
      const validOrderData: CreateOrderData = {
        fileId: 1,
        format: 'A4',
        paperType: 'standard',
        finish: 'none',
        copies: 1,
        pages: 10
      };

      // Test that all required fields are present
      expect(validOrderData.fileId).toBeDefined();
      expect(validOrderData.format).toBeDefined();
      expect(validOrderData.paperType).toBeDefined();
      expect(validOrderData.finish).toBeDefined();
    });

    test('should handle optional customer information', () => {
      const orderData: CreateOrderData = {
        fileId: 1,
        format: 'A4',
        paperType: 'standard',
        finish: 'none',
        customerName: 'João Silva',
        customerEmail: '<EMAIL>',
        customerPhone: '+351 912 345 678'
      };

      expect(orderData.customerName).toBe('João Silva');
      expect(orderData.customerEmail).toBe('<EMAIL>');
      expect(orderData.customerPhone).toBe('+351 912 345 678');
    });

    test('should handle optional order specifications', () => {
      const orderData: CreateOrderData = {
        fileId: 1,
        format: 'A4',
        paperType: 'premium',
        finish: 'binding',
        copies: 5,
        pages: 20,
        hasColor: true,
        complexity: 'high',
        notes: 'Urgent order - needed by tomorrow'
      };

      expect(orderData.copies).toBe(5);
      expect(orderData.pages).toBe(20);
      expect(orderData.hasColor).toBe(true);
      expect(orderData.complexity).toBe('high');
      expect(orderData.notes).toBe('Urgent order - needed by tomorrow');
    });
  });

  describe('Order Status Validation', () => {
    test('should validate order status values', () => {
      const validStatuses: OrderStatus[] = [
        OrderStatus.PENDING,
        OrderStatus.CONFIRMED,
        OrderStatus.PROCESSING,
        OrderStatus.READY,
        OrderStatus.COMPLETED,
        OrderStatus.CANCELLED
      ];

      validStatuses.forEach(status => {
        expect(['pending', 'confirmed', 'processing', 'ready', 'completed', 'cancelled']).toContain(status);
      });
    });

    test('should handle status transitions logically', () => {
      // This would be tested with actual database operations
      // For now, we test the logic structure
      const validTransitions = {
        'pending': ['confirmed', 'cancelled'],
        'confirmed': ['processing', 'cancelled'],
        'processing': ['ready', 'cancelled'],
        'ready': ['completed', 'processing'],
        'completed': [],
        'cancelled': ['pending']
      };

      expect(validTransitions.pending).toContain('confirmed');
      expect(validTransitions.confirmed).toContain('processing');
      expect(validTransitions.processing).toContain('ready');
      expect(validTransitions.ready).toContain('completed');
      expect(validTransitions.cancelled).toContain('pending');
    });
  });

  describe('Order Number Generation', () => {
    test('should generate order numbers with correct format', () => {
      // Test the format pattern (WP + 8 digits + 3 digits)
      const orderNumberPattern = /^WP\d{8}\d{3}$/;
      
      // Since generateOrderNumber is private, we test the pattern
      const mockOrderNumber = 'WP12345678123';
      expect(orderNumberPattern.test(mockOrderNumber)).toBe(true);
    });

    test('should generate unique order numbers', () => {
      // This would require database access to test properly
      // For now, we test that the format includes timestamp and random components
      const timestamp = Date.now().toString().slice(-8);
      const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
      const orderNumber = `WP${timestamp}${random}`;
      
      expect(orderNumber).toMatch(/^WP\d{11}$/);
      expect(orderNumber.length).toBe(13);
    });
  });
});
