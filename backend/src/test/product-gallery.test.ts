import { ProductCategoryModel } from '../models/ProductCategory';
import { MaterialModel } from '../models/Material';
import { ProductSizeModel } from '../models/ProductSize';
import { ProductModel } from '../models/Product';

describe('Product Gallery System', () => {
  describe('ProductCategory Model', () => {
    test('should fetch all categories', async () => {
      const categories = await ProductCategoryModel.findAll();
      expect(Array.isArray(categories)).toBe(true);
      expect(categories.length).toBeGreaterThan(0);

      // Check if default categories exist
      const categoryNames = categories.map((c: any) => c.name);
      expect(categoryNames).toContain('Arte Abstrata');
      expect(categoryNames).toContain('Paisagens');
    });

    test('should find category by slug', async () => {
      const category = await ProductCategoryModel.findBySlug('arte-abstrata');
      expect(category).toBeDefined();
      expect(category?.name).toBe('Arte Abstrata');
      expect(category?.slug).toBe('arte-abstrata');
    });

    test('should create new category', async () => {
      const newCategory = await ProductCategoryModel.create({
        name: 'Test Category',
        slug: 'test-category',
        description: 'A test category',
        sortOrder: 999,
        isActive: true
      });

      expect(newCategory).toBeDefined();
      expect(newCategory.name).toBe('Test Category');
      expect(newCategory.slug).toBe('test-category');

      // Clean up
      await ProductCategoryModel.delete(newCategory.id);
    });
  });

  describe('Material Model', () => {
    test('should fetch all materials', async () => {
      const materials = await MaterialModel.findAll();
      expect(Array.isArray(materials)).toBe(true);
      expect(materials.length).toBeGreaterThan(0);

      // Check if default materials exist
      const materialNames = materials.map((m: any) => m.name);
      expect(materialNames).toContain('Canvas');
      expect(materialNames).toContain('MDF');
    });

    test('should find material by slug', async () => {
      const material = await MaterialModel.findBySlug('canvas');
      expect(material).toBeDefined();
      expect(material?.name).toBe('Canvas');
      expect(material?.slug).toBe('canvas');
    });

    test('should get active materials only', async () => {
      const allMaterials = await MaterialModel.findAll();
      const activeMaterials = allMaterials.filter((material: any) => material.isActive);
      expect(Array.isArray(activeMaterials)).toBe(true);
      activeMaterials.forEach((material: any) => {
        expect(material.isActive).toBe(true);
      });
    });
  });

  describe('ProductSize Model', () => {
    test('should fetch all sizes', async () => {
      const sizes = await ProductSizeModel.findAll();
      expect(Array.isArray(sizes)).toBe(true);
    });

    test('should find sizes by dimensions', async () => {
      // This will depend on what sizes were inserted in the migration
      const sizes = await ProductSizeModel.findByDimensions(30, 40);
      expect(Array.isArray(sizes)).toBe(true);
    });

    test('should get standard sizes only', async () => {
      const allSizes = await ProductSizeModel.findAll();
      const standardSizes = allSizes.filter((size: any) => !size.isCustom);
      expect(Array.isArray(standardSizes)).toBe(true);
      standardSizes.forEach((size: any) => {
        expect(size.isCustom).toBe(false);
      });
    });
  });

  describe('Product Model', () => {
    test('should fetch all products', async () => {
      const result = await ProductModel.findAll();
      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
    });

    test('should search products by category', async () => {
      // First get a category
      const categories = await ProductCategoryModel.findAll();
      if (categories.length > 0 && categories[0]?.id) {
        const result = await ProductModel.findAll({
          categoryId: categories[0].id,
          limit: 10,
          page: 1
        });

        expect(result).toBeDefined();
        expect(Array.isArray(result)).toBe(true);
      }
    });

    test('should get all products (basic test)', async () => {
      const allProducts = await ProductModel.findAll();
      expect(Array.isArray(allProducts)).toBe(true);
      // Since we haven't created any products yet, this might be empty
      // but the query should work
    });
  });

  describe('Integration Tests', () => {
    test('should handle category hierarchy', async () => {
      // Create parent category
      const parentCategory = await ProductCategoryModel.create({
        name: 'Parent Test',
        slug: 'parent-test',
        description: 'Parent category for testing',
        sortOrder: 1000,
        isActive: true
      });

      // Create child category
      const childCategory = await ProductCategoryModel.create({
        name: 'Child Test',
        slug: 'child-test',
        description: 'Child category for testing',
        parentId: parentCategory.id,
        sortOrder: 1001,
        isActive: true
      });

      expect(childCategory.parentId).toBe(parentCategory.id);

      // Clean up
      await ProductCategoryModel.delete(childCategory.id);
      await ProductCategoryModel.delete(parentCategory.id);
    });

    test('should validate slug uniqueness', async () => {
      const existingCategory = await ProductCategoryModel.findAll();
      if (existingCategory.length > 0) {
        const existingSlug = existingCategory[0]?.slug;

        if (existingSlug) {
          try {
            await ProductCategoryModel.create({
              name: 'Duplicate Slug Test',
              slug: existingSlug,
              description: 'This should fail',
              sortOrder: 1002,
              isActive: true
            });

            // If we reach here, the test should fail
            expect(true).toBe(false);
          } catch (error) {
            // This is expected - duplicate slug should fail
            expect(error).toBeDefined();
          }
        }
      }
    });
  });
});
