// ============================================================================
// WePrint AI Backend - Performance Tests
// ============================================================================

import request from 'supertest';
import app from '../app';

describe('Performance Tests', () => {
  let adminToken: string;
  const testFiles: number[] = [];
  const testOrders: number[] = [];

  beforeAll(async () => {
    // Get admin token for authenticated tests
    const loginResponse = await request(app)
      .post('/api/admin/auth/login')
      .send({
        email: '<EMAIL>',
        password: 'admin123'
      });
    
    adminToken = loginResponse.body.data.token;
  });

  describe('File Upload Performance', () => {
    it('should handle multiple concurrent file uploads', async () => {
      const startTime = Date.now();
      const uploadPromises = [];

      // Create 10 concurrent file uploads
      for (let i = 0; i < 10; i++) {
        const fileContent = Buffer.from(`Test file content ${i}`);
        const uploadPromise = request(app)
          .post('/api/files/upload')
          .attach('file', fileContent, `performance-test-${i}.txt`)
          .expect(200);
        
        uploadPromises.push(uploadPromise);
      }

      const responses = await Promise.all(uploadPromises);
      const endTime = Date.now();
      const duration = endTime - startTime;

      // Verify all uploads succeeded
      responses.forEach((response, index) => {
        expect(response.body.success).toBe(true);
        expect(response.body.data.file.originalName).toBe(`performance-test-${index}.txt`);
        testFiles.push(response.body.data.file.id);
      });

      console.log(`✅ 10 concurrent uploads completed in ${duration}ms (avg: ${duration/10}ms per upload)`);
      expect(duration).toBeLessThan(10000); // Should complete within 10 seconds
    });

    it('should handle large file upload efficiently', async () => {
      const startTime = Date.now();
      
      // Create a 5MB test file
      const largeFileContent = Buffer.alloc(5 * 1024 * 1024, 'A');
      
      const response = await request(app)
        .post('/api/files/upload')
        .attach('file', largeFileContent, 'large-performance-test.txt')
        .expect(200);

      const endTime = Date.now();
      const duration = endTime - startTime;

      expect(response.body.success).toBe(true);
      expect(response.body.data.file.size).toBe(5 * 1024 * 1024);
      
      testFiles.push(response.body.data.file.id);
      
      console.log(`✅ 5MB file upload completed in ${duration}ms`);
      expect(duration).toBeLessThan(15000); // Should complete within 15 seconds
    });
  });

  describe('Order Creation Performance', () => {
    it('should handle multiple concurrent order creations', async () => {
      const startTime = Date.now();
      const orderPromises = [];

      // Create 20 concurrent orders using uploaded files
      for (let i = 0; i < 20; i++) {
        const fileId = testFiles[i % testFiles.length]; // Cycle through available files
        
        const orderData = {
          fileId: fileId,
          customerName: `Performance Test Customer ${i}`,
          customerEmail: `perf-test-${i}@example.com`,
          customerPhone: `+24492345678${i % 10}`,
          format: 'A4',
          paperType: i % 2 === 0 ? 'standard' : 'premium',
          finish: 'none',
          copies: Math.floor(Math.random() * 5) + 1,
          notes: `Performance test order ${i}`
        };

        const orderPromise = request(app)
          .post('/api/orders')
          .send(orderData)
          .expect(200);
        
        orderPromises.push(orderPromise);
      }

      const responses = await Promise.all(orderPromises);
      const endTime = Date.now();
      const duration = endTime - startTime;

      // Verify all orders were created successfully
      responses.forEach((response, index) => {
        expect(response.body.success).toBe(true);
        expect(response.body.data.order.customerName).toBe(`Performance Test Customer ${index}`);
        testOrders.push(response.body.data.order.id);
      });

      console.log(`✅ 20 concurrent orders created in ${duration}ms (avg: ${duration/20}ms per order)`);
      expect(duration).toBeLessThan(15000); // Should complete within 15 seconds
    });

    it('should handle rapid price calculations', async () => {
      const startTime = Date.now();
      const calculationPromises = [];

      // Create 50 concurrent price calculations
      for (let i = 0; i < 50; i++) {
        const calculationData = {
          pages: Math.floor(Math.random() * 20) + 1,
          copies: Math.floor(Math.random() * 10) + 1,
          format: ['A4', 'A3', 'A5'][Math.floor(Math.random() * 3)],
          paperType: ['standard', 'premium', 'photo'][Math.floor(Math.random() * 3)],
          finish: ['none', 'glossy', 'matte', 'laminated'][Math.floor(Math.random() * 4)],
          hasColor: Math.random() > 0.5,
          complexity: ['simple', 'medium', 'complex'][Math.floor(Math.random() * 3)]
        };

        const calculationPromise = request(app)
          .post('/api/orders/calculate-price')
          .send(calculationData)
          .expect(200);
        
        calculationPromises.push(calculationPromise);
      }

      const responses = await Promise.all(calculationPromises);
      const endTime = Date.now();
      const duration = endTime - startTime;

      // Verify all calculations succeeded
      responses.forEach((response) => {
        expect(response.body.success).toBe(true);
        expect(response.body.data.price.total).toBeGreaterThan(0);
      });

      console.log(`✅ 50 concurrent price calculations completed in ${duration}ms (avg: ${duration/50}ms per calculation)`);
      expect(duration).toBeLessThan(5000); // Should complete within 5 seconds
    });
  });

  describe('Database Query Performance', () => {
    it('should efficiently retrieve large order lists', async () => {
      const startTime = Date.now();

      const response = await request(app)
        .get('/api/admin/orders?limit=100')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      const endTime = Date.now();
      const duration = endTime - startTime;

      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data.orders)).toBe(true);

      console.log(`✅ Retrieved ${response.body.data.orders.length} orders in ${duration}ms`);
      expect(duration).toBeLessThan(2000); // Should complete within 2 seconds
    });

    it('should efficiently search customer orders', async () => {
      const startTime = Date.now();
      const searchPromises = [];

      // Search for multiple customers concurrently
      for (let i = 0; i < 10; i++) {
        const searchPromise = request(app)
          .get(`/api/orders/customer/perf-test-${i}@example.com`)
          .expect(200);
        
        searchPromises.push(searchPromise);
      }

      const responses = await Promise.all(searchPromises);
      const endTime = Date.now();
      const duration = endTime - startTime;

      // Verify all searches succeeded
      responses.forEach((response) => {
        expect(response.body.success).toBe(true);
        expect(Array.isArray(response.body.data.orders)).toBe(true);
      });

      console.log(`✅ 10 concurrent customer searches completed in ${duration}ms`);
      expect(duration).toBeLessThan(3000); // Should complete within 3 seconds
    });
  });

  describe('API Response Time Tests', () => {
    it('should respond to health checks quickly', async () => {
      const startTime = Date.now();

      const response = await request(app)
        .get('/health')
        .expect(200);

      const endTime = Date.now();
      const duration = endTime - startTime;

      expect(response.body.status).toBe('healthy');
      console.log(`✅ Health check responded in ${duration}ms`);
      expect(duration).toBeLessThan(100); // Should respond within 100ms
    });

    it('should handle dashboard stats efficiently', async () => {
      const startTime = Date.now();

      const response = await request(app)
        .get('/api/admin/dashboard/stats')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      const endTime = Date.now();
      const duration = endTime - startTime;

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('overview');

      console.log(`✅ Dashboard stats generated in ${duration}ms`);
      expect(duration).toBeLessThan(1500); // Should complete within 1.5 seconds
    });
  });

  describe('Memory and Resource Usage', () => {
    it('should handle stress test without memory leaks', async () => {
      const initialMemory = process.memoryUsage();
      console.log(`📊 Initial memory usage: ${Math.round(initialMemory.heapUsed / 1024 / 1024)}MB`);

      // Perform 100 rapid API calls
      const promises = [];
      for (let i = 0; i < 100; i++) {
        promises.push(
          request(app)
            .get('/health')
            .expect(200)
        );
      }

      await Promise.all(promises);

      // Force garbage collection if available
      if (global.gc) {
        global.gc();
      }

      const finalMemory = process.memoryUsage();
      const memoryIncrease = finalMemory.heapUsed - initialMemory.heapUsed;
      
      console.log(`📊 Final memory usage: ${Math.round(finalMemory.heapUsed / 1024 / 1024)}MB`);
      console.log(`📊 Memory increase: ${Math.round(memoryIncrease / 1024 / 1024)}MB`);

      // Memory increase should be reasonable (less than 50MB for 100 requests)
      expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024);
    });
  });

  afterAll(() => {
    console.log('\n🚀 Performance Test Summary:');
    console.log(`📁 Created ${testFiles.length} test files`);
    console.log(`📋 Created ${testOrders.length} test orders`);
    console.log('✅ All performance benchmarks passed');
  });
});
