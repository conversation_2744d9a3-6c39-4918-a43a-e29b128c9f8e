import request from 'supertest';
import app from '../index';
import { AdminModel } from '../models/Admin';
import { OrderModel } from '../models/Order';
import { FileModel } from '../models/File';
import { AdminActivityLogModel } from '../models/AdminActivityLog';
import { AdminRole, AdminStatus, OrderStatus } from '../types';
import { initializeDatabase, query } from '../database';

describe('Admin System Tests', () => {
  let adminToken: string;
  let superAdminToken: string;
  let testAdminId: number;
  let testSuperAdminId: number;
  let testOrderId: number;

  beforeAll(async () => {
    // Initialize database connection for tests
    await initializeDatabase();
  });

  afterAll(async () => {
    // Test cleanup is handled by Jest configuration
  });

  beforeEach(async () => {
    // Reset test data before each test
    await query('DELETE FROM admin_activity_logs');
    await query('DELETE FROM admins');
    await query('DELETE FROM orders');
    await query('DELETE FROM files');

    // Create test admin users
    const adminData = {
      name: 'Test Admin',
      email: '<EMAIL>',
      password: 'admin123',
      role: AdminRole.ADMIN,
      status: AdminStatus.ACTIVE
    };

    const superAdminData = {
      name: 'Test Super Admin',
      email: '<EMAIL>',
      password: 'superadmin123',
      role: AdminRole.SUPER_ADMIN,
      status: AdminStatus.ACTIVE
    };

    const admin = await AdminModel.create(adminData);
    const superAdmin = await AdminModel.create(superAdminData);
    
    testAdminId = admin.id;
    testSuperAdminId = superAdmin.id;

    // Login to get tokens
    const adminLoginResponse = await request(app)
      .post('/api/admin/auth/login')
      .send({ email: '<EMAIL>', password: 'admin123' });
    
    const superAdminLoginResponse = await request(app)
      .post('/api/admin/auth/login')
      .send({ email: '<EMAIL>', password: 'superadmin123' });

    // Check response structure and extract tokens
    console.log('Admin login response:', JSON.stringify(adminLoginResponse.body, null, 2));
    adminToken = adminLoginResponse.body.token || adminLoginResponse.body.data?.token;
    superAdminToken = superAdminLoginResponse.body.token || superAdminLoginResponse.body.data?.token;

    // Create test file first
    const fileResult = await query(`
      INSERT INTO files (filename, original_name, path, size, mimetype)
      VALUES ('test-file.pdf', 'test-document.pdf', '/uploads/test-file.pdf', 1024, 'application/pdf')
      RETURNING id
    `);
    const testFileId = fileResult.rows[0].id;

    // Create test order
    const orderData = {
      fileId: testFileId,
      customerName: 'Test Customer',
      customerEmail: '<EMAIL>',
      customerPhone: '+244900000000',
      format: 'A4',
      paperType: 'standard',
      finish: 'none',
      copies: 1,
      pages: 1,
      price: 1000,
      status: OrderStatus.PENDING
    };

    const order = await OrderModel.create(orderData);
    testOrderId = order.id;
  });

  describe('Admin Authentication', () => {
    it('should login admin successfully', async () => {
      const response = await request(app)
        .post('/api/admin/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'admin123'
        });

      if (response.status !== 200) {
        console.log('Login failed with status:', response.status);
        console.log('Response body:', JSON.stringify(response.body, null, 2));
      }
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.admin).toMatchObject({
        email: '<EMAIL>',
        name: 'Test Admin',
        role: AdminRole.ADMIN,
        status: AdminStatus.ACTIVE
      });
      expect(response.body.data.token).toBeDefined();
    });

    it('should reject invalid credentials', async () => {
      const response = await request(app)
        .post('/api/admin/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'wrongpassword'
        });

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
    });

    it('should get admin profile', async () => {
      const response = await request(app)
        .get('/api/admin/auth/me')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.data).toMatchObject({
        email: '<EMAIL>',
        name: 'Test Admin',
        role: AdminRole.ADMIN
      });
    });

    it('should refresh admin token', async () => {
      const response = await request(app)
        .post('/api/admin/auth/refresh')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.data.token).toBeDefined();
      expect(response.body.data.admin).toBeDefined();
    });

    it('should logout admin', async () => {
      const response = await request(app)
        .post('/api/admin/auth/logout')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
    });

    it('should change admin password', async () => {
      const response = await request(app)
        .post('/api/admin/auth/change-password')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          currentPassword: 'admin123',
          newPassword: 'newpassword123'
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);

      // Verify new password works
      const loginResponse = await request(app)
        .post('/api/admin/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'newpassword123'
        });

      expect(loginResponse.status).toBe(200);
    });
  });

  describe('Admin Dashboard', () => {
    it('should get dashboard statistics', async () => {
      const response = await request(app)
        .get('/api/admin/dashboard/stats')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.data).toHaveProperty('overview');
      expect(response.body.data).toHaveProperty('orderStats');
      expect(response.body.data).toHaveProperty('systemHealth');
      expect(response.body.data.overview).toHaveProperty('totalOrders');
      expect(response.body.data.overview).toHaveProperty('totalRevenue');
    });

    it('should get dashboard overview', async () => {
      const response = await request(app)
        .get('/api/admin/dashboard/overview')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.data).toHaveProperty('overview');
      expect(response.body.data).toHaveProperty('systemHealth');
    });

    it('should get recent activity', async () => {
      const response = await request(app)
        .get('/api/admin/dashboard/recent-activity')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.data).toBeInstanceOf(Array);
    });

    it('should get admin activity logs', async () => {
      const response = await request(app)
        .get('/api/admin/dashboard/admin-activity')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.data).toHaveProperty('logs');
      expect(response.body.data).toHaveProperty('total');
    });

    it('should get system health', async () => {
      const response = await request(app)
        .get('/api/admin/dashboard/system-health')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.data).toHaveProperty('uptime');
      expect(response.body.data).toHaveProperty('memoryUsage');
      expect(response.body.data).toHaveProperty('databaseConnections');
    });

    it('should get performance metrics', async () => {
      const response = await request(app)
        .get('/api/admin/dashboard/performance')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.data).toHaveProperty('responseTime');
      expect(response.body.data).toHaveProperty('throughput');
    });
  });

  describe('Admin Order Management', () => {
    it('should list all orders', async () => {
      const response = await request(app)
        .get('/api/admin/orders')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.data).toHaveProperty('orders');
      expect(response.body.data).toHaveProperty('total');
      expect(response.body.data).toHaveProperty('page');
      expect(response.body.data.orders).toBeInstanceOf(Array);
    });

    it('should get order statistics', async () => {
      const response = await request(app)
        .get('/api/admin/orders/stats')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.data).toHaveProperty('total');
      expect(response.body.data).toHaveProperty('byStatus');
    });

    it('should get specific order details', async () => {
      const response = await request(app)
        .get(`/api/admin/orders/${testOrderId}`)
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.data).toMatchObject({
        id: testOrderId,
        customerName: 'Test Customer',
        customerEmail: '<EMAIL>'
      });
      expect(response.body.data).toHaveProperty('files');
    });

    it('should update order status', async () => {
      const response = await request(app)
        .put(`/api/admin/orders/${testOrderId}/status`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          status: OrderStatus.PROCESSING,
          notes: 'Order is being processed'
        });

      expect(response.status).toBe(200);
      expect(response.body.data.status).toBe(OrderStatus.PROCESSING);
      expect(response.body.data.notes).toBe('Order is being processed');
    });

    it('should update order details', async () => {
      const updateData = {
        customerName: 'Updated Customer Name',
        deliveryAddress: 'Updated Address'
      };

      const response = await request(app)
        .put(`/api/admin/orders/${testOrderId}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send(updateData);

      expect(response.status).toBe(200);
      expect(response.body.data.customerName).toBe('Updated Customer Name');
      expect(response.body.data.deliveryAddress).toBe('Updated Address');
    });

    it('should filter orders by status', async () => {
      const response = await request(app)
        .get('/api/admin/orders')
        .query({ status: OrderStatus.PENDING })
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.data.orders).toBeInstanceOf(Array);
      // All returned orders should have PENDING status
      response.body.data.orders.forEach((order: any) => {
        expect(order.status).toBe(OrderStatus.PENDING);
      });
    });

    it('should search orders by customer email', async () => {
      const response = await request(app)
        .get('/api/admin/orders')
        .query({ customerEmail: '<EMAIL>' })
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.data.orders).toBeInstanceOf(Array);
      // All returned orders should match the customer email
      response.body.data.orders.forEach((order: any) => {
        expect(order.customerEmail).toBe('<EMAIL>');
      });
    });
  });

  describe('Admin User Management', () => {
    it('should list all admins (super admin only)', async () => {
      const response = await request(app)
        .get('/api/admin/admins')
        .set('Authorization', `Bearer ${superAdminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.data).toHaveProperty('admins');
      expect(response.body.data.admins).toBeInstanceOf(Array);
      expect(response.body.data.admins.length).toBeGreaterThanOrEqual(2);
    });

    it('should deny admin list access to regular admin', async () => {
      const response = await request(app)
        .get('/api/admin/admins')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(403);
    });

    it('should create new admin (super admin only)', async () => {
      const newAdminData = {
        name: 'New Admin',
        email: '<EMAIL>',
        password: 'newadmin123',
        role: AdminRole.ADMIN
      };

      const response = await request(app)
        .post('/api/admin/admins')
        .set('Authorization', `Bearer ${superAdminToken}`)
        .send(newAdminData);

      expect(response.status).toBe(201);
      expect(response.body.data).toMatchObject({
        name: 'New Admin',
        email: '<EMAIL>',
        role: AdminRole.ADMIN,
        status: AdminStatus.ACTIVE
      });
    });

    it('should update admin status (super admin only)', async () => {
      const response = await request(app)
        .put(`/api/admin/admins/${testAdminId}/status`)
        .set('Authorization', `Bearer ${superAdminToken}`)
        .send({ status: AdminStatus.INACTIVE });

      expect(response.status).toBe(200);
      expect(response.body.data.status).toBe(AdminStatus.INACTIVE);
    });

    it('should update admin role (super admin only)', async () => {
      const response = await request(app)
        .put(`/api/admin/admins/${testAdminId}/role`)
        .set('Authorization', `Bearer ${superAdminToken}`)
        .send({ role: AdminRole.SUPER_ADMIN });

      expect(response.status).toBe(200);
      expect(response.body.data.role).toBe(AdminRole.SUPER_ADMIN);
    });

    it('should reset admin password (super admin only)', async () => {
      const response = await request(app)
        .post(`/api/admin/admins/${testAdminId}/reset-password`)
        .set('Authorization', `Bearer ${superAdminToken}`)
        .send({ newPassword: 'resetpassword123' });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);

      // Verify new password works
      const loginResponse = await request(app)
        .post('/api/admin/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'resetpassword123'
        });

      expect(loginResponse.status).toBe(200);
    });
  });

  describe('Admin Activity Logging', () => {
    it('should log admin activities', async () => {
      // Perform some admin actions
      await request(app)
        .get('/api/admin/dashboard/stats')
        .set('Authorization', `Bearer ${adminToken}`);

      await request(app)
        .get(`/api/admin/orders/${testOrderId}`)
        .set('Authorization', `Bearer ${adminToken}`);

      // Check activity logs
      const response = await request(app)
        .get('/api/admin/auth/activity')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.data).toHaveProperty('logs');
      expect(response.body.data.logs).toBeInstanceOf(Array);
      expect(response.body.data.logs.length).toBeGreaterThan(0);

      // Verify log entries contain expected actions
      const actions = response.body.data.logs.map((log: any) => log.action);
      expect(actions).toContain('view_dashboard_stats');
      expect(actions).toContain('view_order');
    });

    it('should get admin activity statistics', async () => {
      const response = await request(app)
        .get('/api/admin/dashboard/admin-activity/stats')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.data).toHaveProperty('totalActions');
      expect(response.body.data).toHaveProperty('actionsByType');
      expect(response.body.data).toHaveProperty('adminsByActivity');
    });
  });

  describe('Admin Security and Permissions', () => {
    it('should require authentication for admin endpoints', async () => {
      const response = await request(app)
        .get('/api/admin/dashboard/stats');

      expect(response.status).toBe(401);
    });

    it('should reject invalid tokens', async () => {
      const response = await request(app)
        .get('/api/admin/dashboard/stats')
        .set('Authorization', 'Bearer invalid-token');

      expect(response.status).toBe(401);
    });

    it('should enforce role-based access control', async () => {
      // Regular admin should not access super admin endpoints
      const response = await request(app)
        .post('/api/admin/admins')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          name: 'Test Admin',
          email: '<EMAIL>',
          password: 'test123',
          role: AdminRole.ADMIN
        });

      expect(response.status).toBe(403);
    });

    it('should prevent self-modification', async () => {
      // Admin should not be able to modify their own status
      const response = await request(app)
        .put(`/api/admin/admins/${testAdminId}/status`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send({ status: AdminStatus.INACTIVE });

      expect(response.status).toBe(403);
    });

    it('should apply rate limiting', async () => {
      // Make multiple rapid requests to test rate limiting
      const promises = Array(10).fill(null).map(() =>
        request(app)
          .get('/api/admin/dashboard/stats')
          .set('Authorization', `Bearer ${adminToken}`)
      );

      const responses = await Promise.all(promises);

      // All requests should succeed (within rate limit)
      responses.forEach(response => {
        expect(response.status).toBe(200);
      });
    });
  });

  describe('Admin API Health and Status', () => {
    it('should get admin API health status', async () => {
      const response = await request(app)
        .get('/api/admin/health');

      expect(response.status).toBe(200);
      expect(response.body.data).toHaveProperty('status', 'healthy');
      expect(response.body.data).toHaveProperty('uptime');
      expect(response.body.data).toHaveProperty('services');
      expect(response.body.data.services).toHaveProperty('database', 'connected');
    });

    it('should get admin session status', async () => {
      const response = await request(app)
        .get('/api/admin/status')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.data).toHaveProperty('authenticated', true);
      expect(response.body.data).toHaveProperty('admin');
      expect(response.body.data).toHaveProperty('session');
      expect(response.body.data.admin).toMatchObject({
        id: testAdminId,
        email: '<EMAIL>',
        role: AdminRole.ADMIN
      });
    });

    it('should get admin API information', async () => {
      const response = await request(app)
        .get('/api/admin');

      expect(response.status).toBe(200);
      expect(response.body.data).toHaveProperty('name', 'WePrint AI Admin API');
      expect(response.body.data).toHaveProperty('endpoints');
      expect(response.body.data).toHaveProperty('authentication');
      expect(response.body.data).toHaveProperty('permissions');
      expect(response.body.data.endpoints).toHaveProperty('auth');
      expect(response.body.data.endpoints).toHaveProperty('dashboard');
      expect(response.body.data.endpoints).toHaveProperty('orders');
      expect(response.body.data.endpoints).toHaveProperty('admins');
    });
  });
});
