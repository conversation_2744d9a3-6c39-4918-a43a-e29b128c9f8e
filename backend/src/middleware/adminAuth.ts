import { Request, Response, NextFunction } from 'express';
import { AdminModel } from '../models/Admin';
import { AdminActivityLogModel } from '../models/AdminActivityLog';
import { AdminRole, AdminStatus } from '../types';

// Extend Request interface to include admin
declare global {
  namespace Express {
    interface Request {
      admin?: {
        id: number;
        email: string;
        name: string;
        role: AdminRole;
        status: AdminStatus;
      };
    }
  }
}

/**
 * Middleware to authenticate admin users
 */
export const authenticateAdmin = async (
  req: Request, 
  res: Response, 
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      res.error('Token de acesso requerido', 401);
      return;
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix
    
    // Verify token
    const decoded = AdminModel.verifyToken(token);
    if (!decoded) {
      res.error('Token inválido', 401);
      return;
    }

    // Get admin from database
    const admin = await AdminModel.findById(decoded.adminId);
    if (!admin) {
      res.error('Administrador não encontrado', 401);
      return;
    }

    // Check if admin is active
    if (admin.status !== AdminStatus.ACTIVE) {
      res.error('Conta de administrador inativa', 403);
      return;
    }

    // Add admin to request
    req.admin = {
      id: admin.id,
      email: admin.email,
      name: admin.name,
      role: admin.role,
      status: admin.status
    };

    next();
  } catch (error) {
    console.error('Admin authentication error:', error);
    res.error('Erro de autenticação', 500);
  }
};

/**
 * Middleware to require specific admin role
 */
export const requireAdminRole = (requiredRole: AdminRole) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.admin) {
      res.error('Autenticação requerida', 401);
      return;
    }

    // Super admin has access to everything
    if (req.admin.role === AdminRole.SUPER_ADMIN) {
      next();
      return;
    }

    // Check if admin has required role
    if (req.admin.role !== requiredRole) {
      res.error('Permissões insuficientes', 403);
      return;
    }

    next();
  };
};

/**
 * Middleware to require super admin role
 */
export const requireSuperAdmin = requireAdminRole(AdminRole.SUPER_ADMIN);

/**
 * Middleware to log admin activities
 */
export const logAdminActivity = (action: string, resource: string) => {
  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    // Store original res.json to intercept response
    const originalJson = res.json;
    
    res.json = function(body: any) {
      // Log activity after successful response
      if (req.admin && res.statusCode < 400) {
        const resourceId = req.params.id ? parseInt(req.params.id) : undefined;
        
        AdminActivityLogModel.logAction(
          req.admin.id,
          req.admin.email,
          action,
          resource,
          resourceId,
          {
            method: req.method,
            url: req.originalUrl,
            body: req.method !== 'GET' ? req.body : undefined,
            query: Object.keys(req.query).length > 0 ? req.query : undefined
          },
          req
        ).catch(error => {
          console.error('Failed to log admin activity:', error);
        });
      }
      
      // Call original json method
      return originalJson.call(this, body);
    };

    next();
  };
};

/**
 * Middleware to validate admin exists and is active
 */
export const validateAdminAccess = async (
  req: Request, 
  res: Response, 
  next: NextFunction
): Promise<void> => {
  try {
    if (!req.admin) {
      res.error('Autenticação requerida', 401);
      return;
    }

    // Re-validate admin status from database
    const admin = await AdminModel.findById(req.admin.id);
    if (!admin || admin.status !== AdminStatus.ACTIVE) {
      res.error('Acesso negado - conta inativa', 403);
      return;
    }

    next();
  } catch (error) {
    console.error('Admin validation error:', error);
    res.error('Erro de validação', 500);
  }
};

/**
 * Middleware to check if admin can modify resource
 */
export const canModifyResource = (resourceType: string) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.admin) {
      res.error('Autenticação requerida', 401);
      return;
    }

    // Super admin can modify everything
    if (req.admin.role === AdminRole.SUPER_ADMIN) {
      next();
      return;
    }

    // Regular admin restrictions
    const restrictedResources = ['admins', 'system-config'];
    
    if (restrictedResources.includes(resourceType)) {
      res.error('Permissões insuficientes para modificar este recurso', 403);
      return;
    }

    next();
  };
};

/**
 * Middleware to prevent self-modification of critical admin data
 */
export const preventSelfModification = (req: Request, res: Response, next: NextFunction): void => {
  if (!req.admin) {
    res.error('Autenticação requerida', 401);
    return;
  }

  const targetAdminId = parseInt(req.params.id);
  
  // Prevent admin from modifying their own role or status
  if (req.admin.id === targetAdminId) {
    const { role, status } = req.body;
    
    if (role !== undefined || status !== undefined) {
      res.error('Não é possível modificar o próprio papel ou status', 403);
      return;
    }
  }

  next();
};

/**
 * Rate limiting for admin actions
 */
export const adminRateLimit = (maxRequests: number = 100, windowMs: number = 15 * 60 * 1000) => {
  const requests = new Map<string, { count: number; resetTime: number }>();

  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.admin) {
      next();
      return;
    }

    const key = `admin_${req.admin.id}`;
    const now = Date.now();
    const windowStart = now - windowMs;

    // Clean old entries
    for (const [k, v] of requests.entries()) {
      if (v.resetTime < windowStart) {
        requests.delete(k);
      }
    }

    const current = requests.get(key);
    
    if (!current) {
      requests.set(key, { count: 1, resetTime: now + windowMs });
      next();
      return;
    }

    if (current.resetTime < now) {
      // Reset window
      requests.set(key, { count: 1, resetTime: now + windowMs });
      next();
      return;
    }

    if (current.count >= maxRequests) {
      res.error('Muitas requisições - tente novamente mais tarde', 429);
      return;
    }

    current.count++;
    next();
  };
};
