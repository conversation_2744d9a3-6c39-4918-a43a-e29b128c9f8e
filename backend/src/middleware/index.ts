// ============================================================================
// WePrint AI Backend - Middleware
// ============================================================================

import { Request, Response, NextFunction } from 'express';
import { ApiResponse, ValidationError } from '../types';
import { logConfig } from '../config';

// ============================================================================
// Error Handling Middleware
// ============================================================================

export class AppError extends Error {
  public statusCode: number;
  public isOperational: boolean;

  constructor(message: string, statusCode: number = 500) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = true;

    Error.captureStackTrace(this, this.constructor);
  }
}

export const errorHandler = (
  error: Error,
  req: Request,
  res: Response,
  _next: NextFunction
): void => {
  let statusCode = 500;
  let message = 'Internal Server Error';

  if (error instanceof AppError) {
    statusCode = error.statusCode;
    message = error.message;
  }

  // Log error in development
  if (!logConfig.silent) {
    console.error('Error:', {
      message: error.message,
      stack: error.stack,
      url: req.url,
      method: req.method,
      ip: req.ip,
      userAgent: req.get('User-Agent')
    });
  }

  const response: ApiResponse = {
    success: false,
    error: message
  };

  res.status(statusCode).json(response);
};

// ============================================================================
// Not Found Middleware
// ============================================================================

export const notFoundHandler = (
  req: Request,
  res: Response,
  _next: NextFunction
): void => {
  const response: ApiResponse = {
    success: false,
    error: `Route ${req.method} ${req.originalUrl} not found`
  };

  res.status(404).json(response);
};

// ============================================================================
// Request Logging Middleware
// ============================================================================

export const requestLogger = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  const start = Date.now();

  res.on('finish', () => {
    const duration = Date.now() - start;
    
    if (!logConfig.silent) {
      console.log(`${req.method} ${req.originalUrl} - ${res.statusCode} - ${duration}ms`);
    }
  });

  next();
};

// ============================================================================
// Validation Middleware
// ============================================================================

export const validateRequest = (schema: any, property: 'body' | 'query' | 'params' = 'body') => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const { error } = schema.validate(req[property], { abortEarly: false });

    if (error) {
      const errors: ValidationError[] = error.details.map((detail: any) => ({
        field: detail.path.join('.'),
        message: detail.message
      }));

      const response: ApiResponse = {
        success: false,
        error: 'Validation failed',
        errors
      };

      res.status(400).json(response);
      return;
    }

    next();
  };
};

// ============================================================================
// Async Error Handler
// ============================================================================

export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

// ============================================================================
// Response Helper Middleware
// ============================================================================

declare global {
  namespace Express {
    interface Response {
      success: (data?: any, message?: string) => void;
      error: (message: string, statusCode?: number, data?: any) => void;
      paginated: (data: any[], pagination: any) => void;
    }
  }
}

export const responseHelpers = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  res.success = (data?: any, message?: string) => {
    const response: ApiResponse = {
      success: true,
      data,
      ...(message && { message })
    };
    res.json(response);
  };

  res.error = (message: string, statusCode: number = 400, data?: any) => {
    const response: ApiResponse = {
      success: false,
      error: message,
      ...(data && { data })
    };
    res.status(statusCode).json(response);
  };

  res.paginated = (data: any[], pagination: any) => {
    const response = {
      success: true,
      data,
      pagination
    };
    res.json(response);
  };

  next();
};

// ============================================================================
// CORS Configuration
// ============================================================================

export const corsOptions = {
  origin: (origin: string | undefined, callback: Function) => {
    // Allow requests with no origin (mobile apps, Postman, etc.)
    if (!origin) return callback(null, true);
    
    const allowedOrigins = process.env.CORS_ORIGIN?.split(',') || ['http://localhost:3000'];
    
    if (allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  optionsSuccessStatus: 200
};

// ============================================================================
// Security Headers
// ============================================================================

export const securityHeaders = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');

  next();
};

// ============================================================================
// Upload Middleware Exports
// ============================================================================

export {
  uploadSingle,
  uploadMultiple,
  uploadFields,
  handleUploadError,
  uploadConfig
} from './upload';
