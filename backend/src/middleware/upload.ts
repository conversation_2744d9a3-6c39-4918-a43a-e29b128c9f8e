/**
 * File upload middleware using multer
 * Handles file uploads with validation and security checks
 */

import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { Request, Response, NextFunction } from 'express';
import config from '../config';

// ============================================================================
// Configuration
// ============================================================================

// Allowed file types for printing
const ALLOWED_MIMETYPES = [
  'application/pdf',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'application/vnd.ms-excel',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'application/vnd.ms-powerpoint',
  'application/vnd.openxmlformats-officedocument.presentationml.presentation',
  'text/plain',
  'image/jpeg',
  'image/png',
  'image/gif',
  'image/bmp',
  'image/tiff'
];

// File extensions mapping
const ALLOWED_EXTENSIONS = [
  '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx',
  '.txt', '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.tif'
];

// Maximum file size (50MB)
const MAX_FILE_SIZE = 50 * 1024 * 1024;

// Maximum number of files in batch upload
const MAX_FILES_COUNT = 10;

// ============================================================================
// Storage Configuration
// ============================================================================

// Create uploads directory if it doesn't exist
const uploadsDir = path.join(__dirname, '../../uploads');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

// Create subdirectories by date
const createDateDirectory = (): string => {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  
  const dateDir = path.join(uploadsDir, `${year}`, `${month}`, `${day}`);
  
  if (!fs.existsSync(dateDir)) {
    fs.mkdirSync(dateDir, { recursive: true });
  }
  
  return dateDir;
};

// Generate unique filename
const generateFilename = (originalname: string): string => {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 15);
  const ext = path.extname(originalname).toLowerCase();
  const baseName = path.basename(originalname, ext)
    .replace(/[^a-zA-Z0-9]/g, '_')
    .substring(0, 50);
  
  return `${timestamp}_${random}_${baseName}${ext}`;
};

// ============================================================================
// Multer Storage Configuration
// ============================================================================

const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    try {
      const dateDir = createDateDirectory();
      cb(null, dateDir);
    } catch (error) {
      cb(error as Error, '');
    }
  },

  filename: (req, file, cb) => {
    try {
      const filename = generateFilename(file.originalname);
      cb(null, filename);
    } catch (error) {
      cb(error as Error, '');
    }
  }
});

// ============================================================================
// File Filter
// ============================================================================

const fileFilter = (req: Request, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  // Check mimetype
  if (!ALLOWED_MIMETYPES.includes(file.mimetype)) {
    const error = new Error(`Tipo de arquivo não permitido: ${file.mimetype}`);
    error.name = 'INVALID_FILE_TYPE';
    return cb(error);
  }
  
  // Check file extension
  const ext = path.extname(file.originalname).toLowerCase();
  if (!ALLOWED_EXTENSIONS.includes(ext)) {
    const error = new Error(`Extensão de arquivo não permitida: ${ext}`);
    error.name = 'INVALID_FILE_EXTENSION';
    return cb(error);
  }
  
  // Additional security checks
  if (file.originalname.includes('..') || file.originalname.includes('/')) {
    const error = new Error('Nome de arquivo inválido');
    error.name = 'INVALID_FILENAME';
    return cb(error);
  }
  
  cb(null, true);
};

// ============================================================================
// Multer Configuration
// ============================================================================

const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: MAX_FILE_SIZE,
    files: MAX_FILES_COUNT,
    fieldNameSize: 100,
    fieldSize: 1024 * 1024, // 1MB for text fields
  }
});

// ============================================================================
// Upload Middleware Functions
// ============================================================================

/**
 * Single file upload middleware
 */
export const uploadSingle = upload.single('file');

/**
 * Multiple files upload middleware
 */
export const uploadMultiple = upload.array('files', MAX_FILES_COUNT);

/**
 * Fields upload middleware for mixed content
 */
export const uploadFields = upload.fields([
  { name: 'file', maxCount: 1 },
  { name: 'files', maxCount: MAX_FILES_COUNT }
]);

// ============================================================================
// Error Handling Middleware
// ============================================================================

export const handleUploadError = (error: any, req: Request, res: Response, next: NextFunction) => {
  if (error instanceof multer.MulterError) {
    switch (error.code) {
      case 'LIMIT_FILE_SIZE':
        return res.error('Arquivo muito grande. Tamanho máximo: 50MB', 400);
      
      case 'LIMIT_FILE_COUNT':
        return res.error(`Muitos arquivos. Máximo permitido: ${MAX_FILES_COUNT}`, 400);
      
      case 'LIMIT_UNEXPECTED_FILE':
        return res.error('Campo de arquivo inesperado', 400);
      
      case 'LIMIT_FIELD_KEY':
        return res.error('Nome do campo muito longo', 400);
      
      case 'LIMIT_FIELD_VALUE':
        return res.error('Valor do campo muito longo', 400);
      
      case 'LIMIT_FIELD_COUNT':
        return res.error('Muitos campos', 400);
      
      case 'LIMIT_PART_COUNT':
        return res.error('Muitas partes no upload', 400);
      
      default:
        return res.error(`Erro no upload: ${error.message}`, 400);
    }
  }
  
  // Custom validation errors
  if (error.name === 'INVALID_FILE_TYPE') {
    return res.error(error.message, 400, {
      allowedTypes: ALLOWED_MIMETYPES
    });
  }
  
  if (error.name === 'INVALID_FILE_EXTENSION') {
    return res.error(error.message, 400, {
      allowedExtensions: ALLOWED_EXTENSIONS
    });
  }
  
  if (error.name === 'INVALID_FILENAME') {
    return res.error(error.message, 400);
  }
  
  // Generic upload error
  return res.error('Erro interno no upload de arquivo', 500);
};

// ============================================================================
// Utility Functions
// ============================================================================

/**
 * Get file path relative to uploads directory
 */
export const getRelativeFilePath = (absolutePath: string): string => {
  return path.relative(uploadsDir, absolutePath);
};

/**
 * Get absolute file path from relative path
 */
export const getAbsoluteFilePath = (relativePath: string): string => {
  return path.join(uploadsDir, relativePath);
};

/**
 * Check if file exists
 */
export const fileExists = (filePath: string): boolean => {
  try {
    return fs.existsSync(filePath);
  } catch {
    return false;
  }
};

/**
 * Delete file safely
 */
export const deleteFile = async (filePath: string): Promise<boolean> => {
  try {
    if (fileExists(filePath)) {
      await fs.promises.unlink(filePath);
      return true;
    }
    return false;
  } catch (error) {
    console.error('Error deleting file:', error);
    return false;
  }
};

/**
 * Get file stats
 */
export const getFileStats = async (filePath: string): Promise<fs.Stats | null> => {
  try {
    return await fs.promises.stat(filePath);
  } catch {
    return null;
  }
};

// ============================================================================
// Export Configuration
// ============================================================================

export const uploadConfig = {
  ALLOWED_MIMETYPES,
  ALLOWED_EXTENSIONS,
  MAX_FILE_SIZE,
  MAX_FILES_COUNT,
  uploadsDir
};

export default {
  uploadSingle,
  uploadMultiple,
  uploadFields,
  handleUploadError,
  uploadConfig,
  getRelativeFilePath,
  getAbsoluteFilePath,
  fileExists,
  deleteFile,
  getFileStats
};
