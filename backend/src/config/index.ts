// ============================================================================
// WePrint AI Backend - Configuration
// ============================================================================

import dotenv from 'dotenv';
import { DatabaseConfig, ServerConfig, JwtConfig, UploadConfig } from '../types';

// Load environment variables
dotenv.config();

// ============================================================================
// Environment Variables Validation
// ============================================================================

const requiredEnvVars = [
  'NODE_ENV',
  'PORT',
  'DATABASE_URL',
  'JWT_SECRET'
];

const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);

if (missingEnvVars.length > 0) {
  throw new Error(`Missing required environment variables: ${missingEnvVars.join(', ')}`);
}

// ============================================================================
// Configuration Objects
// ============================================================================

export const serverConfig: ServerConfig = {
  port: parseInt(process.env.PORT || '8000', 10),
  host: process.env.HOST || 'localhost',
  nodeEnv: process.env.NODE_ENV || 'development',
  corsOrigin: process.env.CORS_ORIGIN || 'http://localhost:3000'
};

export const databaseConfig: DatabaseConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '5432', 10),
  database: process.env.DB_NAME || 'weprint',
  username: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'postgres',
  ssl: process.env.DB_SSL === 'true'
};

export const jwtConfig: JwtConfig = {
  secret: process.env.JWT_SECRET!,
  expiresIn: process.env.JWT_EXPIRES_IN || '7d'
};

export const uploadConfig: UploadConfig = {
  uploadDir: process.env.UPLOAD_DIR || 'uploads',
  maxFileSize: parseInt(process.env.MAX_FILE_SIZE || '10485760', 10), // 10MB
  allowedFileTypes: (process.env.ALLOWED_FILE_TYPES || 'pdf,doc,docx,txt,jpg,jpeg,png').split(',')
};

// ============================================================================
// Application Constants
// ============================================================================

export const APP_CONSTANTS = {
  API_PREFIX: '/api',
  VERSION: '1.0.0',
  NAME: 'WePrint AI Backend',
  DESCRIPTION: 'Professional printing service platform API',
  
  // Rate limiting
  RATE_LIMIT: {
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000', 10), // 15 minutes
    maxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100', 10)
  },
  
  // Security
  BCRYPT_ROUNDS: parseInt(process.env.BCRYPT_ROUNDS || '12', 10),
  
  // Pagination
  DEFAULT_PAGE_SIZE: 20,
  MAX_PAGE_SIZE: 100,
  
  // File upload
  SUPPORTED_MIME_TYPES: {
    'application/pdf': 'pdf',
    'application/msword': 'doc',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'docx',
    'text/plain': 'txt',
    'image/jpeg': 'jpg',
    'image/jpg': 'jpg',
    'image/png': 'png'
  },
  
  // Print pricing (base prices in cents)
  PRICING: {
    BASE_PRICE_PER_PAGE: 50, // 0.50 AOA per page
    FORMAT_MULTIPLIERS: {
      A4: 1.0,
      A3: 2.0,
      A5: 0.7,
      letter: 1.0,
      legal: 1.2,
      custom: 1.5
    },
    PAPER_MULTIPLIERS: {
      standard: 1.0,
      premium: 1.5,
      photo: 2.0,
      cardstock: 1.8
    },
    FINISH_MULTIPLIERS: {
      none: 1.0,
      laminated: 1.3,
      spiral_bound: 1.4,
      stapled: 1.1
    }
  }
};

// ============================================================================
// Environment Helpers
// ============================================================================

export const isDevelopment = () => serverConfig.nodeEnv === 'development';
export const isProduction = () => serverConfig.nodeEnv === 'production';
export const isTest = () => serverConfig.nodeEnv === 'test';

// ============================================================================
// Logging Configuration
// ============================================================================

export const logConfig = {
  level: process.env.LOG_LEVEL || (isDevelopment() ? 'debug' : 'info'),
  format: isDevelopment() ? 'dev' : 'combined',
  silent: isTest()
};

// ============================================================================
// Export all configurations
// ============================================================================

export default {
  server: serverConfig,
  database: databaseConfig,
  jwt: jwtConfig,
  upload: uploadConfig,
  app: APP_CONSTANTS,
  log: logConfig
};
