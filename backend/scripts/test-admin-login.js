const bcrypt = require('bcryptjs');
const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'weprint',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'P@ssw0rd@1988',
});

async function testAdminLogin() {
  try {
    console.log('🔍 Testando login do admin...');
    
    // Get admin from database
    const result = await pool.query(
      'SELECT id, email, password, role, status FROM admins WHERE email = $1',
      ['<EMAIL>']
    );
    
    if (result.rows.length === 0) {
      console.log('❌ Admin não encontrado na base de dados');
      return;
    }
    
    const admin = result.rows[0];
    console.log('👤 Admin encontrado:', {
      id: admin.id,
      email: admin.email,
      role: admin.role,
      status: admin.status
    });
    
    // Test password
    const testPassword = 'admin123';
    const isValidPassword = await bcrypt.compare(testPassword, admin.password);
    
    console.log('🔑 Teste de password:');
    console.log('   Password testada:', testPassword);
    console.log('   Hash na BD:', admin.password.substring(0, 20) + '...');
    console.log('   Password válida:', isValidPassword ? '✅ SIM' : '❌ NÃO');
    
    if (!isValidPassword) {
      console.log('\n🔧 Vou resetar a password para "admin123"...');
      
      const newHash = await bcrypt.hash('admin123', 12);
      await pool.query(
        'UPDATE admins SET password = $1 WHERE email = $2',
        [newHash, '<EMAIL>']
      );
      
      console.log('✅ Password resetada com sucesso!');
      
      // Test again
      const testAgain = await bcrypt.compare('admin123', newHash);
      console.log('🔄 Teste após reset:', testAgain ? '✅ SIM' : '❌ NÃO');
    }
    
  } catch (error) {
    console.error('❌ Erro no teste:', error);
  } finally {
    await pool.end();
  }
}

testAdminLogin();
