{"name": "@types/express-rate-limit", "version": "5.1.3", "description": "TypeScript definitions for express-rate-limit", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express-rate-limit", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/cyrilschumacher", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "makepost", "url": "https://github.com/makepost", "githubUsername": "makepost"}, {"name": "<PERSON>", "url": "https://github.com/jdforsythe", "githubUsername": "jdforsythe"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/express-rate-limit"}, "scripts": {}, "dependencies": {"@types/express": "*"}, "typesPublisherContentHash": "1344e3fcf5d81f6333f591a90f3963c0095ab7b956b18efb34debb43ed0ad881", "typeScriptVersion": "3.6"}