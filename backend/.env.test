# ============================================================================
# WePrint AI Backend - Test Environment Configuration
# ============================================================================

# Node Environment
NODE_ENV=test

# Server Configuration
PORT=3001
HOST=localhost
CORS_ORIGIN=http://localhost:3000

# Database Configuration (Test Database)
DB_HOST=localhost
DB_PORT=5432
DB_NAME=weprint_test
DB_USER=postgres
DB_PASSWORD=P@ssw0rd@1988

# JWT Configuration
JWT_SECRET=test-secret-key-for-weprint-ai-backend-testing
JWT_EXPIRES_IN=24h

# Upload Configuration
UPLOAD_MAX_SIZE=10485760
UPLOAD_ALLOWED_TYPES=pdf,doc,docx,txt,jpg,jpeg,png,gif
UPLOAD_DIR=uploads/test

# Logging
LOG_LEVEL=silent

# App Constants
APP_NAME=WePrint AI Backend Test
APP_VERSION=1.0.0
APP_DESCRIPTION=Professional printing service platform API - Test Environment
