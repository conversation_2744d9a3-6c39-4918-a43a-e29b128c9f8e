# Production Environment Configuration
NODE_ENV=production
PORT=8000

# Database Configuration
DATABASE_URL=*******************************************/weprint

# JWT Configuration
JWT_SECRET=weprint-jwt-secret-key-2025
JWT_EXPIRES_IN=7d

# CORS Configuration
CORS_ORIGIN=http://localhost:3000

# File Upload Configuration
UPLOAD_DIR=/app/uploads
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=pdf,jpg,jpeg,png,txt

# Email Configuration (for notifications)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=
EMAIL_PASS=
EMAIL_FROM=<EMAIL>

# Payment Configuration
MULTICAIXA_API_URL=https://api.multicaixa.ao
MULTICAIXA_API_KEY=
MULTICAIXA_MERCHANT_ID=

# Application Configuration
APP_NAME=WePrint AI
APP_URL=http://localhost:3000
API_URL=http://localhost:8000

# Security Configuration
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=/app/logs/app.log

# Health Check Configuration
HEALTH_CHECK_TIMEOUT=5000
