"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const morgan_1 = __importDefault(require("morgan"));
const path_1 = __importDefault(require("path"));
const http_1 = require("http");
const config_1 = __importDefault(require("./config"));
const database_1 = __importDefault(require("./database"));
const migrations_1 = require("./database/migrations");
const middleware_1 = require("./middleware");
const app = (0, express_1.default)();
app.use((0, helmet_1.default)({
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'"],
            scriptSrc: ["'self'"],
            imgSrc: ["'self'", "data:", "https:"],
        },
    },
    crossOriginEmbedderPolicy: false
}));
app.use(middleware_1.securityHeaders);
app.use((0, cors_1.default)(middleware_1.corsOptions));
app.use(express_1.default.json({ limit: '10mb' }));
app.use(express_1.default.urlencoded({ extended: true, limit: '10mb' }));
if (!config_1.default.log.silent) {
    app.use((0, morgan_1.default)(config_1.default.log.format));
}
app.use(middleware_1.requestLogger);
app.use(middleware_1.responseHelpers);
app.use('/uploads', express_1.default.static(path_1.default.join(__dirname, '../uploads')));
app.get('/health', async (req, res) => {
    try {
        const dbHealth = await database_1.default.health();
        res.success({
            status: 'OK',
            timestamp: new Date().toISOString(),
            version: config_1.default.app.VERSION,
            environment: config_1.default.server.nodeEnv,
            uptime: process.uptime(),
            database: dbHealth
        }, 'Service is healthy');
    }
    catch (error) {
        res.error('Service unhealthy', 503, {
            status: 'ERROR',
            timestamp: new Date().toISOString(),
            version: config_1.default.app.VERSION,
            environment: config_1.default.server.nodeEnv,
            uptime: process.uptime(),
            database: { status: 'unhealthy' }
        });
    }
});
app.get('/api', (req, res) => {
    res.success({
        name: config_1.default.app.NAME,
        description: config_1.default.app.DESCRIPTION,
        version: config_1.default.app.VERSION,
        environment: config_1.default.server.nodeEnv,
        endpoints: {
            health: '/health',
            api: '/api',
            files: '/api/files',
            orders: '/api/orders',
            notifications: '/api/notifications',
            admin: '/api/admin'
        },
        documentation: 'https://github.com/weprint-ai/backend/blob/main/README.md'
    }, 'WePrint AI Backend API');
});
const files_1 = __importDefault(require("./routes/files"));
const orders_1 = __importStar(require("./routes/orders"));
const notifications_1 = __importStar(require("./routes/notifications"));
const admin_1 = __importDefault(require("./routes/admin"));
const payments_1 = __importDefault(require("./routes/payments"));
const EmailService_1 = require("./services/EmailService");
const NotificationService_1 = require("./services/NotificationService");
const WebSocketService_1 = require("./services/WebSocketService");
app.use('/api/files', files_1.default);
app.use('/api/orders', orders_1.default);
app.use('/api/notifications', notifications_1.default);
app.use('/api/admin', admin_1.default);
app.use('/api/payments', payments_1.default);
app.use(middleware_1.notFoundHandler);
app.use(middleware_1.errorHandler);
let notificationService;
let webSocketService;
const startServer = async () => {
    try {
        console.log('🔄 Initializing WePrint AI Backend...');
        try {
            await database_1.default.initialize();
            console.log('✅ Database connected successfully');
            try {
                await (0, migrations_1.runPendingMigrations)();
                console.log('✅ Database migrations completed');
            }
            catch (migrationError) {
                console.error('❌ Database migration failed:', migrationError);
            }
        }
        catch (error) {
            console.log('⚠️  Database connection failed, continuing without database...');
            console.log('   Make sure PostgreSQL is running for full functionality');
        }
        const httpServer = (0, http_1.createServer)(app);
        try {
            console.log('🔔 Initializing notification services...');
            const emailConfig = {
                host: process.env.SMTP_HOST || 'localhost',
                port: parseInt(process.env.SMTP_PORT || '587'),
                secure: process.env.SMTP_SECURE === 'true',
                auth: {
                    user: process.env.SMTP_USER || '',
                    pass: process.env.SMTP_PASS || ''
                },
                from: {
                    name: process.env.SMTP_FROM_NAME || 'WePrint AI',
                    email: process.env.SMTP_FROM_EMAIL || '<EMAIL>'
                }
            };
            const emailService = new EmailService_1.EmailService(emailConfig);
            const emailTest = await emailService.testConnection();
            if (emailTest.success) {
                console.log('✅ Email service configured successfully');
            }
            else {
                console.log('⚠️  Email service configuration issue:', emailTest.error);
            }
            notificationService = new NotificationService_1.NotificationService(emailService);
            webSocketService = new WebSocketService_1.WebSocketService(httpServer);
            (0, notifications_1.setNotificationService)(notificationService);
            (0, orders_1.setOrderNotificationService)(notificationService);
            console.log('✅ Notification services initialized');
        }
        catch (error) {
            console.log('⚠️  Notification services failed to initialize:', error);
        }
        const server = httpServer.listen(config_1.default.server.port, config_1.default.server.host, () => {
            console.log(`🚀 WePrint AI Backend started successfully!`);
            console.log(`📍 Server running on: http://${config_1.default.server.host}:${config_1.default.server.port}`);
            console.log(`🌍 Environment: ${config_1.default.server.nodeEnv}`);
            console.log(`📚 API Documentation: http://${config_1.default.server.host}:${config_1.default.server.port}/api`);
            console.log(`❤️  Health Check: http://${config_1.default.server.host}:${config_1.default.server.port}/health`);
            console.log(`🔔 Notifications: http://${config_1.default.server.host}:${config_1.default.server.port}/api/notifications`);
            console.log(`⚡ WebSocket: ws://${config_1.default.server.host}:${config_1.default.server.port}`);
        });
        const gracefulShutdown = (signal) => {
            console.log(`\n🛑 Received ${signal}. Starting graceful shutdown...`);
            server.close(async () => {
                console.log('✅ HTTP server closed.');
                if (webSocketService) {
                    webSocketService.close();
                    console.log('✅ WebSocket service closed.');
                }
                await database_1.default.close();
                process.exit(0);
            });
            setTimeout(() => {
                console.log('⚠️  Forcing shutdown after timeout');
                process.exit(1);
            }, 10000);
        };
        process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
        process.on('SIGINT', () => gracefulShutdown('SIGINT'));
    }
    catch (error) {
        console.error('❌ Failed to start server:', error);
        process.exit(1);
    }
};
if (require.main === module) {
    startServer().catch((error) => {
        console.error('❌ Unhandled error during startup:', error);
        process.exit(1);
    });
}
exports.default = app;
//# sourceMappingURL=index.js.map