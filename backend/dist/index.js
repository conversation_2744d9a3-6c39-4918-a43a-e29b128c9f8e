"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const morgan_1 = __importDefault(require("morgan"));
const path_1 = __importDefault(require("path"));
const config_1 = __importDefault(require("./config"));
const database_1 = __importDefault(require("./database"));
const middleware_1 = require("./middleware");
const app = (0, express_1.default)();
app.use((0, helmet_1.default)({
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'"],
            scriptSrc: ["'self'"],
            imgSrc: ["'self'", "data:", "https:"],
        },
    },
    crossOriginEmbedderPolicy: false
}));
app.use(middleware_1.securityHeaders);
app.use((0, cors_1.default)(middleware_1.corsOptions));
app.use(express_1.default.json({ limit: '10mb' }));
app.use(express_1.default.urlencoded({ extended: true, limit: '10mb' }));
if (!config_1.default.log.silent) {
    app.use((0, morgan_1.default)(config_1.default.log.format));
}
app.use(middleware_1.requestLogger);
app.use(middleware_1.responseHelpers);
app.use('/uploads', express_1.default.static(path_1.default.join(__dirname, '../uploads')));
app.get('/health', async (req, res) => {
    try {
        const dbHealth = await database_1.default.health();
        res.success({
            status: 'OK',
            timestamp: new Date().toISOString(),
            version: config_1.default.app.VERSION,
            environment: config_1.default.server.nodeEnv,
            uptime: process.uptime(),
            database: dbHealth
        }, 'Service is healthy');
    }
    catch (error) {
        res.error('Service unhealthy', 503, {
            status: 'ERROR',
            timestamp: new Date().toISOString(),
            version: config_1.default.app.VERSION,
            environment: config_1.default.server.nodeEnv,
            uptime: process.uptime(),
            database: { status: 'unhealthy' }
        });
    }
});
app.get('/api', (req, res) => {
    res.success({
        name: config_1.default.app.NAME,
        description: config_1.default.app.DESCRIPTION,
        version: config_1.default.app.VERSION,
        environment: config_1.default.server.nodeEnv,
        endpoints: {
            health: '/health',
            api: '/api',
            files: '/api/files',
            orders: '/api/orders',
            admin: '/api/admin'
        },
        documentation: 'https://github.com/weprint-ai/backend/blob/main/README.md'
    }, 'WePrint AI Backend API');
});
const files_1 = __importDefault(require("./routes/files"));
const orders_1 = __importDefault(require("./routes/orders"));
app.use('/api/files', files_1.default);
app.use('/api/orders', orders_1.default);
app.use(middleware_1.notFoundHandler);
app.use(middleware_1.errorHandler);
const startServer = async () => {
    try {
        console.log('🔄 Initializing WePrint AI Backend...');
        try {
            await database_1.default.initialize();
        }
        catch (error) {
            console.log('⚠️  Database connection failed, continuing without database...');
            console.log('   Make sure PostgreSQL is running for full functionality');
        }
        const server = app.listen(config_1.default.server.port, config_1.default.server.host, () => {
            console.log(`🚀 WePrint AI Backend started successfully!`);
            console.log(`📍 Server running on: http://${config_1.default.server.host}:${config_1.default.server.port}`);
            console.log(`🌍 Environment: ${config_1.default.server.nodeEnv}`);
            console.log(`📚 API Documentation: http://${config_1.default.server.host}:${config_1.default.server.port}/api`);
            console.log(`❤️  Health Check: http://${config_1.default.server.host}:${config_1.default.server.port}/health`);
        });
        const gracefulShutdown = (signal) => {
            console.log(`\n🛑 Received ${signal}. Starting graceful shutdown...`);
            server.close(async () => {
                console.log('✅ HTTP server closed.');
                await database_1.default.close();
                process.exit(0);
            });
            setTimeout(() => {
                console.log('⚠️  Forcing shutdown after timeout');
                process.exit(1);
            }, 10000);
        };
        process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
        process.on('SIGINT', () => gracefulShutdown('SIGINT'));
    }
    catch (error) {
        console.error('❌ Failed to start server:', error);
        process.exit(1);
    }
};
if (require.main === module) {
    startServer().catch((error) => {
        console.error('❌ Unhandled error during startup:', error);
        process.exit(1);
    });
}
exports.default = app;
//# sourceMappingURL=index.js.map