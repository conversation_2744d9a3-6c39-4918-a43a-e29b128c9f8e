import { EmailConfig, NotificationType } from '../types';
export declare class EmailService {
    private transporter;
    private config;
    constructor(config: EmailConfig);
    sendEmail(options: {
        to: string;
        toName?: string;
        subject: string;
        html: string;
        text?: string;
    }): Promise<{
        success: boolean;
        messageId?: string;
        error?: string;
    }>;
    sendOrderNotification(type: NotificationType, recipientEmail: string, recipientName: string, orderData: any): Promise<{
        success: boolean;
        messageId?: string;
        error?: string;
    }>;
    private getOrderTemplate;
    private getOrderCreatedHtml;
    private getOrderCreatedText;
    private getOrderReadyHtml;
    private getOrderReadyText;
    private getOrderConfirmedHtml;
    private getOrderConfirmedText;
    private getOrderProcessingHtml;
    private getOrderProcessingText;
    private getOrderCompletedHtml;
    private getOrderCompletedText;
    private getOrderCancelledHtml;
    private getOrderCancelledText;
    private getGenericOrderHtml;
    private getGenericOrderText;
    private stripHtml;
    testConnection(): Promise<{
        success: boolean;
        error?: string;
    }>;
}
//# sourceMappingURL=EmailService.d.ts.map