import Stripe from 'stripe';
export interface CreatePaymentIntentData {
    amount: number;
    currency?: string;
    orderId: number;
    customerEmail: string;
    description?: string;
    metadata?: Record<string, string>;
}
export interface CreateCustomerData {
    email: string;
    name?: string;
    phone?: string;
    address?: {
        line1: string;
        line2?: string;
        city: string;
        postal_code: string;
        country: string;
    };
}
export interface RefundData {
    paymentIntentId: string;
    amount?: number;
    reason?: 'duplicate' | 'fraudulent' | 'requested_by_customer';
    metadata?: Record<string, string>;
}
export declare class StripeService {
    static createPaymentIntent(data: CreatePaymentIntentData): Promise<Stripe.PaymentIntent>;
    static getPaymentIntent(paymentIntentId: string): Promise<Stripe.PaymentIntent>;
    static confirmPaymentIntent(paymentIntentId: string, paymentMethodId?: string): Promise<Stripe.PaymentIntent>;
    static cancelPaymentIntent(paymentIntentId: string): Promise<Stripe.PaymentIntent>;
    static createCustomer(data: CreateCustomerData): Promise<Stripe.Customer>;
    static getCustomerByEmail(email: string): Promise<Stripe.Customer | null>;
    static createRefund(data: RefundData): Promise<Stripe.Refund>;
    static getRefund(refundId: string): Promise<Stripe.Refund>;
    static listPaymentMethods(customerId: string): Promise<Stripe.PaymentMethod[]>;
    static attachPaymentMethod(paymentMethodId: string, customerId: string): Promise<Stripe.PaymentMethod>;
    static detachPaymentMethod(paymentMethodId: string): Promise<Stripe.PaymentMethod>;
    static constructWebhookEvent(payload: string | Buffer, signature: string): Stripe.Event;
    static getStripeInstance(): Stripe;
    static eurosToCents(euros: number): number;
    static centsToEuros(cents: number): number;
    static formatAmount(amount: number, currency?: string): string;
    static validateWebhookSignature(payload: string | Buffer, signature: string): boolean;
}
export default StripeService;
//# sourceMappingURL=StripeService.d.ts.map