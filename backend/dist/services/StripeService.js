"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StripeService = void 0;
const stripe_1 = __importDefault(require("stripe"));
const stripe = new stripe_1.default(process.env.STRIPE_SECRET_KEY, {
    apiVersion: '2024-12-18.acacia',
    typescript: true,
});
class StripeService {
    static async createPaymentIntent(data) {
        try {
            const paymentIntent = await stripe.paymentIntents.create({
                amount: Math.round(data.amount * 100),
                currency: data.currency || process.env.STRIPE_CURRENCY || 'eur',
                description: data.description || `WePrint AI - Pedido #${data.orderId}`,
                metadata: {
                    order_id: data.orderId.toString(),
                    customer_email: data.customerEmail,
                    ...data.metadata,
                },
                automatic_payment_methods: {
                    enabled: true,
                },
                receipt_email: data.customerEmail,
            });
            return paymentIntent;
        }
        catch (error) {
            console.error('Stripe createPaymentIntent error:', error);
            throw new Error(`Failed to create payment intent: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    static async getPaymentIntent(paymentIntentId) {
        try {
            return await stripe.paymentIntents.retrieve(paymentIntentId);
        }
        catch (error) {
            console.error('Stripe getPaymentIntent error:', error);
            throw new Error(`Failed to retrieve payment intent: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    static async confirmPaymentIntent(paymentIntentId, paymentMethodId) {
        try {
            const confirmData = {};
            if (paymentMethodId) {
                confirmData.payment_method = paymentMethodId;
            }
            return await stripe.paymentIntents.confirm(paymentIntentId, confirmData);
        }
        catch (error) {
            console.error('Stripe confirmPaymentIntent error:', error);
            throw new Error(`Failed to confirm payment intent: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    static async cancelPaymentIntent(paymentIntentId) {
        try {
            return await stripe.paymentIntents.cancel(paymentIntentId);
        }
        catch (error) {
            console.error('Stripe cancelPaymentIntent error:', error);
            throw new Error(`Failed to cancel payment intent: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    static async createCustomer(data) {
        try {
            return await stripe.customers.create({
                email: data.email,
                name: data.name,
                phone: data.phone,
                address: data.address,
                metadata: {
                    source: 'weprint_ai',
                },
            });
        }
        catch (error) {
            console.error('Stripe createCustomer error:', error);
            throw new Error(`Failed to create customer: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    static async getCustomerByEmail(email) {
        try {
            const customers = await stripe.customers.list({
                email: email,
                limit: 1,
            });
            return customers.data.length > 0 ? customers.data[0] : null;
        }
        catch (error) {
            console.error('Stripe getCustomerByEmail error:', error);
            throw new Error(`Failed to get customer: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    static async createRefund(data) {
        try {
            const refundData = {
                payment_intent: data.paymentIntentId,
                reason: data.reason,
                metadata: data.metadata,
            };
            if (data.amount) {
                refundData.amount = Math.round(data.amount * 100);
            }
            return await stripe.refunds.create(refundData);
        }
        catch (error) {
            console.error('Stripe createRefund error:', error);
            throw new Error(`Failed to create refund: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    static async getRefund(refundId) {
        try {
            return await stripe.refunds.retrieve(refundId);
        }
        catch (error) {
            console.error('Stripe getRefund error:', error);
            throw new Error(`Failed to retrieve refund: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    static async listPaymentMethods(customerId) {
        try {
            const paymentMethods = await stripe.paymentMethods.list({
                customer: customerId,
                type: 'card',
            });
            return paymentMethods.data;
        }
        catch (error) {
            console.error('Stripe listPaymentMethods error:', error);
            throw new Error(`Failed to list payment methods: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    static async attachPaymentMethod(paymentMethodId, customerId) {
        try {
            return await stripe.paymentMethods.attach(paymentMethodId, {
                customer: customerId,
            });
        }
        catch (error) {
            console.error('Stripe attachPaymentMethod error:', error);
            throw new Error(`Failed to attach payment method: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    static async detachPaymentMethod(paymentMethodId) {
        try {
            return await stripe.paymentMethods.detach(paymentMethodId);
        }
        catch (error) {
            console.error('Stripe detachPaymentMethod error:', error);
            throw new Error(`Failed to detach payment method: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    static constructWebhookEvent(payload, signature) {
        try {
            return stripe.webhooks.constructEvent(payload, signature, process.env.STRIPE_WEBHOOK_SECRET);
        }
        catch (error) {
            console.error('Stripe webhook verification error:', error);
            throw new Error(`Webhook signature verification failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    static getStripeInstance() {
        return stripe;
    }
    static eurosToCents(euros) {
        return Math.round(euros * 100);
    }
    static centsToEuros(cents) {
        return Math.round(cents) / 100;
    }
    static formatAmount(amount, currency = 'EUR') {
        return new Intl.NumberFormat('pt-PT', {
            style: 'currency',
            currency: currency,
        }).format(amount);
    }
    static validateWebhookSignature(payload, signature) {
        try {
            this.constructWebhookEvent(payload, signature);
            return true;
        }
        catch (error) {
            return false;
        }
    }
}
exports.StripeService = StripeService;
exports.default = StripeService;
//# sourceMappingURL=StripeService.js.map