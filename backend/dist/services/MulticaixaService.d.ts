export interface CreateMulticaixaPaymentData {
    amount: number;
    currency?: string;
    orderId: number;
    customerEmail: string;
    customerPhone?: string;
    description?: string;
    metadata?: Record<string, string>;
}
export interface MulticaixaPaymentResponse {
    transactionId: string;
    paymentUrl: string;
    qrCode?: string;
    status: MulticaixaPaymentStatus;
    amount: number;
    currency: string;
    expiresAt: Date;
}
export interface MulticaixaWebhookPayload {
    transactionId: string;
    status: MulticaixaPaymentStatus;
    amount: number;
    currency: string;
    orderId: string;
    customerEmail: string;
    paidAt?: string;
    failureReason?: string;
    signature: string;
}
export declare enum MulticaixaPaymentStatus {
    PENDING = "pending",
    PROCESSING = "processing",
    COMPLETED = "completed",
    FAILED = "failed",
    CANCELLED = "cancelled",
    EXPIRED = "expired"
}
export declare class MulticaixaService {
    private static readonly baseUrl;
    private static readonly merchantId;
    private static readonly secretKey;
    private static readonly webhookSecret;
    private static readonly returnUrl;
    private static readonly cancelUrl;
    static createPayment(data: CreateMulticaixaPaymentData): Promise<MulticaixaPaymentResponse>;
    static getPaymentStatus(transactionId: string): Promise<MulticaixaPaymentStatus>;
    static cancelPayment(transactionId: string): Promise<boolean>;
    static verifyWebhookSignature(payload: MulticaixaWebhookPayload): boolean;
    static processWebhook(payload: MulticaixaWebhookPayload): {
        transactionId: string;
        status: MulticaixaPaymentStatus;
        orderId: number;
        amount: number;
        paidAt?: Date;
        failureReason?: string;
    };
    private static generateSignature;
    static eurosToKwanza(euros: number): number;
    static kwanzaToEuros(kwanza: number): number;
    static formatAmount(amount: number, currency?: string): string;
    static validateConfig(): boolean;
}
export default MulticaixaService;
//# sourceMappingURL=MulticaixaService.d.ts.map