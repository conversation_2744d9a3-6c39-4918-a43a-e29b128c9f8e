import { EventEmitter } from 'events';
import { CreateNotificationData, OrderStatus } from '../types';
import { EmailService } from './EmailService';
export declare class NotificationService extends EventEmitter {
    private static instance;
    private emailService;
    private isProcessing;
    constructor(emailService: EmailService);
    static getInstance(): NotificationService;
    private setupEventListeners;
    createNotification(data: CreateNotificationData): Promise<void>;
    sendImmediateNotification(data: CreateNotificationData): Promise<boolean>;
    private startProcessingQueue;
    private processQueue;
    private processRetries;
    private processNotification;
    private handleOrderCreated;
    private handleOrderConfirmed;
    private handleOrderProcessing;
    private handleOrderReady;
    private handleOrderCompleted;
    private handleOrderCancelled;
    private handleFileUploaded;
    private handleSystemAlert;
    private generateOrderEmailContent;
    emitOrderStatusChange(orderData: any, newStatus: OrderStatus): void;
    emitFileUploaded(fileData: any): void;
    emitSystemAlert(title: string, message: string, severity?: 'info' | 'warning' | 'error'): void;
    getStatistics(days?: number): Promise<import("../types").NotificationStats>;
    cleanupOldNotifications(daysOld?: number): Promise<number>;
    sendOrderStatusNotification(orderId: number, status: string, customerEmail?: string): Promise<void>;
}
//# sourceMappingURL=NotificationService.d.ts.map