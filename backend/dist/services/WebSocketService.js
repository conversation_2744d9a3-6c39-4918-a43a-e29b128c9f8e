"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebSocketService = void 0;
const socket_io_1 = require("socket.io");
const types_1 = require("../types");
class WebSocketService {
    constructor(httpServer) {
        this.connectedClients = new Map();
        this.io = new socket_io_1.Server(httpServer, {
            cors: {
                origin: process.env.FRONTEND_URL || "http://localhost:3000",
                methods: ["GET", "POST"],
                credentials: true
            },
            transports: ['websocket', 'polling']
        });
        this.setupEventHandlers();
    }
    setupEventHandlers() {
        this.io.on('connection', (socket) => {
            console.log(`Client connected: ${socket.id}`);
            socket.on('authenticate', (data) => {
                this.handleAuthentication(socket, data);
            });
            socket.on('join-order', (orderId) => {
                socket.join(`order-${orderId}`);
                console.log(`Client ${socket.id} joined order room: ${orderId}`);
            });
            socket.on('leave-order', (orderId) => {
                socket.leave(`order-${orderId}`);
                console.log(`Client ${socket.id} left order room: ${orderId}`);
            });
            socket.on('notification-read', (notificationId) => {
                this.handleNotificationRead(socket, notificationId);
            });
            socket.on('disconnect', () => {
                this.handleDisconnection(socket);
            });
            socket.emit('connected', {
                message: 'Conectado ao sistema de notificações WePrint AI',
                timestamp: new Date()
            });
        });
    }
    handleAuthentication(socket, data) {
        const { userId, userEmail, userType } = data;
        if (userId && userEmail) {
            this.connectedClients.set(socket.id, {
                userId,
                userEmail,
                userType: userType || 'customer',
                connectedAt: new Date()
            });
            socket.join(`user-${userId}`);
            if (userType === 'admin') {
                socket.join('admins');
            }
            else {
                socket.join('customers');
            }
            socket.emit('authenticated', {
                success: true,
                message: 'Autenticado com sucesso'
            });
            console.log(`Client authenticated: ${userEmail} (${userType})`);
        }
        else {
            socket.emit('authentication-error', {
                message: 'Dados de autenticação inválidos'
            });
        }
    }
    handleNotificationRead(socket, notificationId) {
        const client = this.connectedClients.get(socket.id);
        if (client) {
            console.log(`Notification ${notificationId} marked as read by ${client.userEmail}`);
            socket.to(`user-${client.userId}`).emit('notification-read', {
                notificationId,
                readBy: client.userEmail,
                timestamp: new Date()
            });
        }
    }
    handleDisconnection(socket) {
        const client = this.connectedClients.get(socket.id);
        if (client) {
            console.log(`Client disconnected: ${client.userEmail}`);
            this.connectedClients.delete(socket.id);
        }
    }
    sendToUser(userId, notification) {
        this.io.to(`user-${userId}`).emit('notification', notification);
        console.log(`Notification sent to user ${userId}: ${notification.type}`);
    }
    sendToEmail(email, notification) {
        for (const [socketId, client] of this.connectedClients.entries()) {
            if (client.userEmail === email) {
                this.io.to(socketId).emit('notification', notification);
                console.log(`Notification sent to ${email}: ${notification.type}`);
            }
        }
    }
    sendOrderUpdate(orderId, notification) {
        this.io.to(`order-${orderId}`).emit('order-update', {
            orderId,
            ...notification
        });
        console.log(`Order update sent for order ${orderId}: ${notification.type}`);
    }
    sendToAdmins(notification) {
        this.io.to('admins').emit('admin-notification', notification);
        console.log(`Admin notification sent: ${notification.type}`);
    }
    sendToCustomers(notification) {
        this.io.to('customers').emit('customer-notification', notification);
        console.log(`Customer notification sent: ${notification.type}`);
    }
    broadcast(notification) {
        this.io.emit('broadcast', notification);
        console.log(`Broadcast notification sent: ${notification.type}`);
    }
    sendSystemStatus(status) {
        this.io.emit('system-status', status);
        console.log(`System status update: ${status.message}`);
    }
    getConnectedClientsCount() {
        return this.connectedClients.size;
    }
    getConnectedClients() {
        return Array.from(this.connectedClients.entries()).map(([socketId, client]) => ({
            socketId,
            ...client
        }));
    }
    createNotification(type, title, message, data) {
        return {
            id: `ws-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
            type,
            title,
            message,
            data,
            timestamp: new Date(),
            read: false
        };
    }
    sendOrderStatusChange(orderData, newStatus) {
        const statusMessages = {
            'confirmed': {
                title: 'Pedido Confirmado',
                message: `O seu pedido ${orderData.orderNumber} foi confirmado e está na fila de produção.`
            },
            'processing': {
                title: 'Pedido em Produção',
                message: `O seu pedido ${orderData.orderNumber} está sendo processado.`
            },
            'ready': {
                title: 'Pedido Pronto',
                message: `O seu pedido ${orderData.orderNumber} está pronto para levantamento!`
            },
            'completed': {
                title: 'Pedido Concluído',
                message: `O seu pedido ${orderData.orderNumber} foi concluído com sucesso.`
            },
            'cancelled': {
                title: 'Pedido Cancelado',
                message: `O seu pedido ${orderData.orderNumber} foi cancelado.`
            }
        };
        const statusInfo = statusMessages[newStatus] || {
            title: 'Atualização do Pedido',
            message: `O status do seu pedido ${orderData.orderNumber} foi atualizado.`
        };
        const notification = this.createNotification(types_1.NotificationType.ORDER_CONFIRMED, statusInfo.title, statusInfo.message, {
            orderId: orderData.id,
            orderNumber: orderData.orderNumber,
            status: newStatus,
            customerEmail: orderData.customerEmail
        });
        if (orderData.customerEmail) {
            this.sendToEmail(orderData.customerEmail, notification);
        }
        this.sendOrderUpdate(orderData.id, notification);
        this.sendToAdmins({
            ...notification,
            title: `Pedido ${orderData.orderNumber} - ${statusInfo.title}`,
            message: `Cliente: ${orderData.customerEmail} - ${statusInfo.message}`
        });
    }
    sendFileUploadNotification(fileData) {
        const notification = this.createNotification(types_1.NotificationType.FILE_UPLOADED, 'Arquivo Carregado', `O arquivo "${fileData.originalName}" foi carregado com sucesso.`, {
            fileId: fileData.id,
            fileName: fileData.originalName,
            orderId: fileData.orderId
        });
        if (fileData.customerEmail) {
            this.sendToEmail(fileData.customerEmail, notification);
        }
        if (fileData.orderId) {
            this.sendOrderUpdate(fileData.orderId, notification);
        }
    }
    sendSystemAlert(title, message, severity = 'info') {
        const notification = this.createNotification(types_1.NotificationType.SYSTEM_ALERT, title, message, { severity });
        this.sendToAdmins(notification);
        if (severity === 'error') {
            this.broadcast(notification);
        }
    }
    close() {
        this.io.close();
        console.log('WebSocket server closed');
    }
}
exports.WebSocketService = WebSocketService;
//# sourceMappingURL=WebSocketService.js.map