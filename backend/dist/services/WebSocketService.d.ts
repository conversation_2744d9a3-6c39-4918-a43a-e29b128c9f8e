import { Server as HttpServer } from 'http';
import { NotificationType } from '../types';
export interface WebSocketNotification {
    id: string;
    type: NotificationType;
    title: string;
    message: string;
    data?: any;
    timestamp: Date;
    read: boolean;
}
export declare class WebSocketService {
    private io;
    private connectedClients;
    constructor(httpServer: HttpServer);
    private setupEventHandlers;
    private handleAuthentication;
    private handleNotificationRead;
    private handleDisconnection;
    sendToUser(userId: string, notification: WebSocketNotification): void;
    sendToEmail(email: string, notification: WebSocketNotification): void;
    sendOrderUpdate(orderId: number, notification: WebSocketNotification): void;
    sendToAdmins(notification: WebSocketNotification): void;
    sendToCustomers(notification: WebSocketNotification): void;
    broadcast(notification: WebSocketNotification): void;
    sendSystemStatus(status: {
        online: boolean;
        message: string;
        timestamp: Date;
    }): void;
    getConnectedClientsCount(): number;
    getConnectedClients(): Array<{
        socketId: string;
        userId: string;
        userEmail: string;
        userType: string;
        connectedAt: Date;
    }>;
    createNotification(type: NotificationType, title: string, message: string, data?: any): WebSocketNotification;
    sendOrderStatusChange(orderData: any, newStatus: string): void;
    sendFileUploadNotification(fileData: any): void;
    sendSystemAlert(title: string, message: string, severity?: 'info' | 'warning' | 'error'): void;
    close(): void;
}
//# sourceMappingURL=WebSocketService.d.ts.map