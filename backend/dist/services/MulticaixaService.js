"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MulticaixaService = exports.MulticaixaPaymentStatus = void 0;
const axios_1 = __importDefault(require("axios"));
const crypto_1 = __importDefault(require("crypto"));
var MulticaixaPaymentStatus;
(function (MulticaixaPaymentStatus) {
    MulticaixaPaymentStatus["PENDING"] = "pending";
    MulticaixaPaymentStatus["PROCESSING"] = "processing";
    MulticaixaPaymentStatus["COMPLETED"] = "completed";
    MulticaixaPaymentStatus["FAILED"] = "failed";
    MulticaixaPaymentStatus["CANCELLED"] = "cancelled";
    MulticaixaPaymentStatus["EXPIRED"] = "expired";
})(MulticaixaPaymentStatus || (exports.MulticaixaPaymentStatus = MulticaixaPaymentStatus = {}));
class MulticaixaService {
    static async createPayment(data) {
        try {
            const paymentData = {
                merchant_id: this.merchantId,
                amount: data.amount,
                currency: data.currency || 'AOA',
                order_id: data.orderId.toString(),
                customer_email: data.customerEmail,
                customer_phone: data.customerPhone,
                description: data.description || `WePrint AI - Pedido #${data.orderId}`,
                return_url: this.returnUrl,
                cancel_url: this.cancelUrl,
                webhook_url: `${process.env.API_BASE_URL}/api/payments/multicaixa/webhook`,
                metadata: data.metadata || {},
                timestamp: new Date().toISOString()
            };
            const signature = this.generateSignature(paymentData);
            const response = await axios_1.default.post(`${this.baseUrl}/payments`, {
                ...paymentData,
                signature
            }, {
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.secretKey}`
                },
                timeout: 30000
            });
            const responseData = response.data;
            return {
                transactionId: responseData.transaction_id,
                paymentUrl: responseData.payment_url,
                qrCode: responseData.qr_code,
                status: responseData.status,
                amount: responseData.amount,
                currency: responseData.currency,
                expiresAt: new Date(responseData.expires_at)
            };
        }
        catch (error) {
            console.error('Multicaixa createPayment error:', error);
            if (error && typeof error === 'object' && 'response' in error) {
                const axiosError = error;
                const message = axiosError.response?.data?.message || axiosError.message;
                throw new Error(`Failed to create Multicaixa payment: ${message}`);
            }
            throw new Error(`Failed to create Multicaixa payment: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    static async getPaymentStatus(transactionId) {
        try {
            const response = await axios_1.default.get(`${this.baseUrl}/payments/${transactionId}`, {
                headers: {
                    'Authorization': `Bearer ${this.secretKey}`
                },
                timeout: 15000
            });
            return response.data.status;
        }
        catch (error) {
            console.error('Multicaixa getPaymentStatus error:', error);
            throw new Error(`Failed to get payment status: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    static async cancelPayment(transactionId) {
        try {
            const response = await axios_1.default.post(`${this.baseUrl}/payments/${transactionId}/cancel`, {}, {
                headers: {
                    'Authorization': `Bearer ${this.secretKey}`
                },
                timeout: 15000
            });
            return response.data.success === true;
        }
        catch (error) {
            console.error('Multicaixa cancelPayment error:', error);
            throw new Error(`Failed to cancel payment: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    static verifyWebhookSignature(payload) {
        try {
            const { signature, ...dataToSign } = payload;
            const expectedSignature = this.generateSignature(dataToSign);
            return crypto_1.default.timingSafeEqual(Buffer.from(signature, 'hex'), Buffer.from(expectedSignature, 'hex'));
        }
        catch (error) {
            console.error('Multicaixa webhook signature verification error:', error);
            return false;
        }
    }
    static processWebhook(payload) {
        const result = {
            transactionId: payload.transactionId,
            status: payload.status,
            orderId: parseInt(payload.orderId),
            amount: payload.amount
        };
        if (payload.paidAt) {
            result.paidAt = new Date(payload.paidAt);
        }
        if (payload.failureReason) {
            result.failureReason = payload.failureReason;
        }
        return result;
    }
    static generateSignature(data) {
        const sortedKeys = Object.keys(data).sort();
        const queryString = sortedKeys
            .map(key => `${key}=${encodeURIComponent(data[key])}`)
            .join('&');
        const hmac = crypto_1.default.createHmac('sha256', this.webhookSecret);
        hmac.update(queryString);
        return hmac.digest('hex');
    }
    static eurosToKwanza(euros) {
        const exchangeRate = parseFloat(process.env.EUR_TO_AOA_RATE || '850');
        return Math.round(euros * exchangeRate);
    }
    static kwanzaToEuros(kwanza) {
        const exchangeRate = parseFloat(process.env.EUR_TO_AOA_RATE || '850');
        return Math.round((kwanza / exchangeRate) * 100) / 100;
    }
    static formatAmount(amount, currency = 'AOA') {
        return new Intl.NumberFormat('pt-AO', {
            style: 'currency',
            currency: currency,
            minimumFractionDigits: 2
        }).format(amount);
    }
    static validateConfig() {
        const requiredEnvVars = [
            'MULTICAIXA_MERCHANT_ID',
            'MULTICAIXA_SECRET_KEY',
            'MULTICAIXA_WEBHOOK_SECRET'
        ];
        for (const envVar of requiredEnvVars) {
            if (!process.env[envVar]) {
                console.error(`Missing required environment variable: ${envVar}`);
                return false;
            }
        }
        return true;
    }
}
exports.MulticaixaService = MulticaixaService;
MulticaixaService.baseUrl = process.env.MULTICAIXA_GPO_ENDPOINT || 'https://gpo.multicaixa.ao/api/v1';
MulticaixaService.merchantId = process.env.MULTICAIXA_MERCHANT_ID;
MulticaixaService.secretKey = process.env.MULTICAIXA_SECRET_KEY;
MulticaixaService.webhookSecret = process.env.MULTICAIXA_WEBHOOK_SECRET;
MulticaixaService.returnUrl = process.env.MULTICAIXA_RETURN_URL || 'http://localhost:3001/payment/return';
MulticaixaService.cancelUrl = process.env.MULTICAIXA_CANCEL_URL || 'http://localhost:3001/payment/cancel';
exports.default = MulticaixaService;
//# sourceMappingURL=MulticaixaService.js.map