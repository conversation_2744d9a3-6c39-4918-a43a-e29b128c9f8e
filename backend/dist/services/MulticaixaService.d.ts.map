{"version": 3, "file": "MulticaixaService.d.ts", "sourceRoot": "", "sources": ["../../src/services/MulticaixaService.ts"], "names": [], "mappings": "AAQA,MAAM,WAAW,2BAA2B;IAC1C,MAAM,EAAE,MAAM,CAAC;IACf,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,OAAO,EAAE,MAAM,CAAC;IAChB,aAAa,EAAE,MAAM,CAAC;IACtB,aAAa,CAAC,EAAE,MAAM,CAAC;IACvB,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,QAAQ,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;CACnC;AAED,MAAM,WAAW,yBAAyB;IACxC,aAAa,EAAE,MAAM,CAAC;IACtB,UAAU,EAAE,MAAM,CAAC;IACnB,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,MAAM,EAAE,uBAAuB,CAAC;IAChC,MAAM,EAAE,MAAM,CAAC;IACf,QAAQ,EAAE,MAAM,CAAC;IACjB,SAAS,EAAE,IAAI,CAAC;CACjB;AAED,MAAM,WAAW,wBAAwB;IACvC,aAAa,EAAE,MAAM,CAAC;IACtB,MAAM,EAAE,uBAAuB,CAAC;IAChC,MAAM,EAAE,MAAM,CAAC;IACf,QAAQ,EAAE,MAAM,CAAC;IACjB,OAAO,EAAE,MAAM,CAAC;IAChB,aAAa,EAAE,MAAM,CAAC;IACtB,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,aAAa,CAAC,EAAE,MAAM,CAAC;IACvB,SAAS,EAAE,MAAM,CAAC;CACnB;AAED,oBAAY,uBAAuB;IACjC,OAAO,YAAY;IACnB,UAAU,eAAe;IACzB,SAAS,cAAc;IACvB,MAAM,WAAW;IACjB,SAAS,cAAc;IACvB,OAAO,YAAY;CACpB;AAED,qBAAa,iBAAiB;IAC5B,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAA6E;IAC5G,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAuC;IACzE,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAsC;IACvE,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,aAAa,CAA0C;IAC/E,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAA+E;IAChH,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAA+E;WAKnG,aAAa,CAAC,IAAI,EAAE,2BAA2B,GAAG,OAAO,CAAC,yBAAyB,CAAC;WA6DpF,gBAAgB,CAAC,aAAa,EAAE,MAAM,GAAG,OAAO,CAAC,uBAAuB,CAAC;WAuBzE,aAAa,CAAC,aAAa,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC;IAwBnE,MAAM,CAAC,sBAAsB,CAAC,OAAO,EAAE,wBAAwB,GAAG,OAAO;IAkBzE,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE,wBAAwB,GAAG;QACxD,aAAa,EAAE,MAAM,CAAC;QACtB,MAAM,EAAE,uBAAuB,CAAC;QAChC,OAAO,EAAE,MAAM,CAAC;QAChB,MAAM,EAAE,MAAM,CAAC;QACf,MAAM,CAAC,EAAE,IAAI,CAAC;QACd,aAAa,CAAC,EAAE,MAAM,CAAC;KACxB;IA6BD,OAAO,CAAC,MAAM,CAAC,iBAAiB;IAiBhC,MAAM,CAAC,aAAa,CAAC,KAAK,EAAE,MAAM,GAAG,MAAM;IAQ3C,MAAM,CAAC,aAAa,CAAC,MAAM,EAAE,MAAM,GAAG,MAAM;IAQ5C,MAAM,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,EAAE,QAAQ,GAAE,MAAc,GAAG,MAAM;IAWrE,MAAM,CAAC,cAAc,IAAI,OAAO;CAgBjC;AAED,eAAe,iBAAiB,CAAC"}