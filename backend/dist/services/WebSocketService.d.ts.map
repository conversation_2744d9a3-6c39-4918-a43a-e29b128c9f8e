{"version": 3, "file": "WebSocketService.d.ts", "sourceRoot": "", "sources": ["../../src/services/WebSocketService.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,MAAM,IAAI,UAAU,EAAE,MAAM,MAAM,CAAC;AAC5C,OAAO,EAAE,gBAAgB,EAAuB,MAAM,UAAU,CAAC;AAEjE,MAAM,WAAW,qBAAqB;IACpC,EAAE,EAAE,MAAM,CAAC;IACX,IAAI,EAAE,gBAAgB,CAAC;IACvB,KAAK,EAAE,MAAM,CAAC;IACd,OAAO,EAAE,MAAM,CAAC;IAChB,IAAI,CAAC,EAAE,GAAG,CAAC;IACX,SAAS,EAAE,IAAI,CAAC;IAChB,IAAI,EAAE,OAAO,CAAC;CACf;AAED,qBAAa,gBAAgB;IAC3B,OAAO,CAAC,EAAE,CAAiB;IAC3B,OAAO,CAAC,gBAAgB,CAA+B;gBAE3C,UAAU,EAAE,UAAU;IAgBlC,OAAO,CAAC,kBAAkB;IAyC1B,OAAO,CAAC,oBAAoB;IAsC5B,OAAO,CAAC,sBAAsB;IAiB9B,OAAO,CAAC,mBAAmB;IAW3B,UAAU,CAAC,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,qBAAqB,GAAG,IAAI;IAQrE,WAAW,CAAC,KAAK,EAAE,MAAM,EAAE,YAAY,EAAE,qBAAqB,GAAG,IAAI;IAarE,eAAe,CAAC,OAAO,EAAE,MAAM,EAAE,YAAY,EAAE,qBAAqB,GAAG,IAAI;IAW3E,YAAY,CAAC,YAAY,EAAE,qBAAqB,GAAG,IAAI;IAQvD,eAAe,CAAC,YAAY,EAAE,qBAAqB,GAAG,IAAI;IAQ1D,SAAS,CAAC,YAAY,EAAE,qBAAqB,GAAG,IAAI;IAQpD,gBAAgB,CAAC,MAAM,EAAE;QACvB,MAAM,EAAE,OAAO,CAAC;QAChB,OAAO,EAAE,MAAM,CAAC;QAChB,SAAS,EAAE,IAAI,CAAC;KACjB,GAAG,IAAI;IAQR,wBAAwB,IAAI,MAAM;IAOlC,mBAAmB,IAAI,KAAK,CAAC;QAC3B,QAAQ,EAAE,MAAM,CAAC;QACjB,MAAM,EAAE,MAAM,CAAC;QACf,SAAS,EAAE,MAAM,CAAC;QAClB,QAAQ,EAAE,MAAM,CAAC;QACjB,WAAW,EAAE,IAAI,CAAC;KACnB,CAAC;IAUF,kBAAkB,CAChB,IAAI,EAAE,gBAAgB,EACtB,KAAK,EAAE,MAAM,EACb,OAAO,EAAE,MAAM,EACf,IAAI,CAAC,EAAE,GAAG,GACT,qBAAqB;IAexB,qBAAqB,CAAC,SAAS,EAAE,GAAG,EAAE,SAAS,EAAE,MAAM,GAAG,IAAI;IA4D9D,0BAA0B,CAAC,QAAQ,EAAE,GAAG,GAAG,IAAI;IAwB/C,eAAe,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,GAAE,MAAM,GAAG,SAAS,GAAG,OAAgB,GAAG,IAAI;IAoBtG,KAAK,IAAI,IAAI;CAId"}