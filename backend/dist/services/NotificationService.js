"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotificationService = void 0;
const events_1 = require("events");
const types_1 = require("../types");
const Notification_1 = require("../models/Notification");
class NotificationService extends events_1.EventEmitter {
    constructor(emailService) {
        super();
        this.isProcessing = false;
        this.emailService = emailService;
        this.setupEventListeners();
        this.startProcessingQueue();
    }
    setupEventListeners() {
        this.on('order:created', this.handleOrderCreated.bind(this));
        this.on('order:confirmed', this.handleOrderConfirmed.bind(this));
        this.on('order:processing', this.handleOrderProcessing.bind(this));
        this.on('order:ready', this.handleOrderReady.bind(this));
        this.on('order:completed', this.handleOrderCompleted.bind(this));
        this.on('order:cancelled', this.handleOrderCancelled.bind(this));
        this.on('file:uploaded', this.handleFileUploaded.bind(this));
        this.on('system:alert', this.handleSystemAlert.bind(this));
    }
    async createNotification(data) {
        try {
            await Notification_1.NotificationModel.create(data);
            console.log(`Notification queued: ${data.type} to ${data.recipientEmail}`);
        }
        catch (error) {
            console.error('Failed to create notification:', error);
        }
    }
    async sendImmediateNotification(data) {
        try {
            const notification = await Notification_1.NotificationModel.create(data);
            if (data.channel === types_1.NotificationChannel.EMAIL) {
                const result = await this.emailService.sendEmail({
                    to: data.recipientEmail,
                    toName: data.recipientName,
                    subject: data.subject,
                    html: data.content
                });
                if (result.success) {
                    await Notification_1.NotificationModel.update(notification.id, {
                        status: 'sent',
                        sentAt: new Date()
                    });
                    return true;
                }
                else {
                    await Notification_1.NotificationModel.update(notification.id, {
                        status: 'failed',
                        errorMessage: result.error
                    });
                    return false;
                }
            }
            return true;
        }
        catch (error) {
            console.error('Failed to send immediate notification:', error);
            return false;
        }
    }
    async startProcessingQueue() {
        setInterval(async () => {
            if (!this.isProcessing) {
                await this.processQueue();
            }
        }, 30000);
        setInterval(async () => {
            if (!this.isProcessing) {
                await this.processRetries();
            }
        }, 300000);
    }
    async processQueue() {
        if (this.isProcessing)
            return;
        this.isProcessing = true;
        try {
            const pendingNotifications = await Notification_1.NotificationModel.getPendingNotifications(10);
            for (const notification of pendingNotifications) {
                await this.processNotification(notification);
            }
        }
        catch (error) {
            console.error('Error processing notification queue:', error);
        }
        finally {
            this.isProcessing = false;
        }
    }
    async processRetries() {
        try {
            const retryableNotifications = await Notification_1.NotificationModel.getRetryableNotifications(5);
            for (const notification of retryableNotifications) {
                await this.processNotification(notification);
            }
        }
        catch (error) {
            console.error('Error processing notification retries:', error);
        }
    }
    async processNotification(notification) {
        try {
            let success = false;
            let errorMessage = '';
            if (notification.channel === types_1.NotificationChannel.EMAIL) {
                const result = await this.emailService.sendEmail({
                    to: notification.recipientEmail,
                    toName: notification.recipientName,
                    subject: notification.subject,
                    html: notification.content
                });
                success = result.success;
                errorMessage = result.error || '';
            }
            if (success) {
                await Notification_1.NotificationModel.update(notification.id, {
                    status: 'sent',
                    sentAt: new Date()
                });
                console.log(`Notification sent: ${notification.id}`);
            }
            else {
                await Notification_1.NotificationModel.update(notification.id, {
                    status: 'failed',
                    errorMessage,
                    retryCount: notification.retryCount + 1
                });
                console.log(`Notification failed: ${notification.id} - ${errorMessage}`);
            }
        }
        catch (error) {
            console.error(`Error processing notification ${notification.id}:`, error);
            await Notification_1.NotificationModel.update(notification.id, {
                status: 'failed',
                errorMessage: error instanceof Error ? error.message : 'Unknown error',
                retryCount: notification.retryCount + 1
            });
        }
    }
    async handleOrderCreated(orderData) {
        await this.createNotification({
            type: types_1.NotificationType.ORDER_CREATED,
            channel: types_1.NotificationChannel.EMAIL,
            recipientEmail: orderData.customerEmail,
            recipientName: orderData.customerName,
            subject: `Pedido ${orderData.orderNumber} - Recebido com Sucesso`,
            content: await this.generateOrderEmailContent(types_1.NotificationType.ORDER_CREATED, orderData),
            templateData: orderData,
            orderId: orderData.id
        });
    }
    async handleOrderConfirmed(orderData) {
        await this.createNotification({
            type: types_1.NotificationType.ORDER_CONFIRMED,
            channel: types_1.NotificationChannel.EMAIL,
            recipientEmail: orderData.customerEmail,
            recipientName: orderData.customerName,
            subject: `Pedido ${orderData.orderNumber} - Confirmado`,
            content: await this.generateOrderEmailContent(types_1.NotificationType.ORDER_CONFIRMED, orderData),
            templateData: orderData,
            orderId: orderData.id
        });
    }
    async handleOrderProcessing(orderData) {
        await this.createNotification({
            type: types_1.NotificationType.ORDER_PROCESSING,
            channel: types_1.NotificationChannel.EMAIL,
            recipientEmail: orderData.customerEmail,
            recipientName: orderData.customerName,
            subject: `Pedido ${orderData.orderNumber} - Em Produção`,
            content: await this.generateOrderEmailContent(types_1.NotificationType.ORDER_PROCESSING, orderData),
            templateData: orderData,
            orderId: orderData.id
        });
    }
    async handleOrderReady(orderData) {
        await this.createNotification({
            type: types_1.NotificationType.ORDER_READY,
            channel: types_1.NotificationChannel.EMAIL,
            recipientEmail: orderData.customerEmail,
            recipientName: orderData.customerName,
            subject: `Pedido ${orderData.orderNumber} - Pronto para Levantamento`,
            content: await this.generateOrderEmailContent(types_1.NotificationType.ORDER_READY, orderData),
            templateData: orderData,
            orderId: orderData.id
        });
    }
    async handleOrderCompleted(orderData) {
        await this.createNotification({
            type: types_1.NotificationType.ORDER_COMPLETED,
            channel: types_1.NotificationChannel.EMAIL,
            recipientEmail: orderData.customerEmail,
            recipientName: orderData.customerName,
            subject: `Pedido ${orderData.orderNumber} - Concluído`,
            content: await this.generateOrderEmailContent(types_1.NotificationType.ORDER_COMPLETED, orderData),
            templateData: orderData,
            orderId: orderData.id
        });
    }
    async handleOrderCancelled(orderData) {
        await this.createNotification({
            type: types_1.NotificationType.ORDER_CANCELLED,
            channel: types_1.NotificationChannel.EMAIL,
            recipientEmail: orderData.customerEmail,
            recipientName: orderData.customerName,
            subject: `Pedido ${orderData.orderNumber} - Cancelado`,
            content: await this.generateOrderEmailContent(types_1.NotificationType.ORDER_CANCELLED, orderData),
            templateData: orderData,
            orderId: orderData.id
        });
    }
    async handleFileUploaded(fileData) {
        if (fileData.orderId && fileData.customerEmail) {
            await this.createNotification({
                type: types_1.NotificationType.FILE_UPLOADED,
                channel: types_1.NotificationChannel.EMAIL,
                recipientEmail: fileData.customerEmail,
                recipientName: fileData.customerName,
                subject: `Arquivo Carregado - ${fileData.originalName}`,
                content: `O arquivo "${fileData.originalName}" foi carregado com sucesso.`,
                templateData: fileData,
                fileId: fileData.id,
                orderId: fileData.orderId
            });
        }
    }
    async handleSystemAlert(alertData) {
        const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';
        await this.createNotification({
            type: types_1.NotificationType.SYSTEM_ALERT,
            channel: types_1.NotificationChannel.EMAIL,
            recipientEmail: adminEmail,
            recipientName: 'Administrador',
            subject: `Alerta do Sistema - ${alertData.title}`,
            content: alertData.message,
            templateData: alertData
        });
    }
    async generateOrderEmailContent(type, orderData) {
        const template = this.emailService.getOrderTemplate(type, orderData);
        return template.html;
    }
    emitOrderStatusChange(orderData, newStatus) {
        const eventMap = {
            [types_1.OrderStatus.PENDING]: 'order:created',
            [types_1.OrderStatus.CONFIRMED]: 'order:confirmed',
            [types_1.OrderStatus.PROCESSING]: 'order:processing',
            [types_1.OrderStatus.READY]: 'order:ready',
            [types_1.OrderStatus.COMPLETED]: 'order:completed',
            [types_1.OrderStatus.CANCELLED]: 'order:cancelled'
        };
        const eventName = eventMap[newStatus];
        if (eventName) {
            this.emit(eventName, orderData);
        }
    }
    emitFileUploaded(fileData) {
        this.emit('file:uploaded', fileData);
    }
    emitSystemAlert(title, message, severity = 'info') {
        this.emit('system:alert', { title, message, severity, timestamp: new Date() });
    }
    async getStatistics(days = 30) {
        return await Notification_1.NotificationModel.getStatistics(days);
    }
    async cleanupOldNotifications(daysOld = 90) {
        return await Notification_1.NotificationModel.deleteOldNotifications(daysOld);
    }
}
exports.NotificationService = NotificationService;
//# sourceMappingURL=NotificationService.js.map