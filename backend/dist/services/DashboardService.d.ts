import { DashboardStats } from '../types';
export declare class DashboardService {
    static getDashboardStats(): Promise<DashboardStats>;
    private static getOverviewStats;
    private static getOrderStats;
    private static getFileStats;
    private static getNotificationStats;
    private static getSystemHealth;
    static getRecentActivity(limit?: number): Promise<Array<{
        type: string;
        description: string;
        timestamp: Date;
        details?: any;
    }>>;
}
//# sourceMappingURL=DashboardService.d.ts.map