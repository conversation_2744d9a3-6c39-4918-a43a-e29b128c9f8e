import { Invoice, CreateInvoiceData, UpdateInvoiceData, InvoiceStatus } from '../types';
export declare class InvoiceModel {
    static create(data: CreateInvoiceData): Promise<Invoice>;
    static findById(id: number): Promise<Invoice | null>;
    static findByInvoiceNumber(invoiceNumber: string): Promise<Invoice | null>;
    static findByOrderId(orderId: number): Promise<Invoice[]>;
    static findByPaymentId(paymentId: number): Promise<Invoice[]>;
    static update(id: number, data: UpdateInvoiceData): Promise<Invoice | null>;
    static findAll(offset?: number, limit?: number, status?: InvoiceStatus): Promise<{
        invoices: Invoice[];
        total: number;
    }>;
    static getStatistics(): Promise<{
        totalInvoices: number;
        totalAmount: number;
        paidInvoices: number;
        overdueInvoices: number;
        draftInvoices: number;
    }>;
    static delete(id: number): Promise<boolean>;
    private static mapRowToInvoice;
}
export default InvoiceModel;
//# sourceMappingURL=Invoice.d.ts.map