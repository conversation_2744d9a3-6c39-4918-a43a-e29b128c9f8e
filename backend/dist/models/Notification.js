"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotificationModel = void 0;
const types_1 = require("../types");
const database_1 = require("../database");
class NotificationModel {
    static async create(data) {
        const sql = `
      INSERT INTO notifications (
        type, channel, recipient_email, recipient_name, subject, content,
        template_data, order_id, file_id, max_retries, status, retry_count
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
      RETURNING *
    `;
        const values = [
            data.type,
            data.channel,
            data.recipientEmail,
            data.recipientName || null,
            data.subject,
            data.content,
            data.templateData ? JSON.stringify(data.templateData) : null,
            data.orderId || null,
            data.fileId || null,
            data.maxRetries || 3,
            types_1.NotificationStatus.PENDING,
            0
        ];
        const result = await (0, database_1.queryOne)(sql, values);
        return this.mapRowToNotification(result);
    }
    static async findById(id) {
        const sql = `
      SELECT * FROM notifications 
      WHERE id = $1
    `;
        try {
            const result = await (0, database_1.queryOne)(sql, [id]);
            return this.mapRowToNotification(result);
        }
        catch (error) {
            return null;
        }
    }
    static async findMany(options = {}) {
        const { page = 1, limit = 20, type, channel, status, recipientEmail, orderId } = options;
        const offset = (page - 1) * limit;
        const conditions = [];
        const values = [];
        let paramCount = 0;
        if (type) {
            conditions.push(`type = $${++paramCount}`);
            values.push(type);
        }
        if (channel) {
            conditions.push(`channel = $${++paramCount}`);
            values.push(channel);
        }
        if (status) {
            conditions.push(`status = $${++paramCount}`);
            values.push(status);
        }
        if (recipientEmail) {
            conditions.push(`recipient_email ILIKE $${++paramCount}`);
            values.push(`%${recipientEmail}%`);
        }
        if (orderId) {
            conditions.push(`order_id = $${++paramCount}`);
            values.push(orderId);
        }
        const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';
        const countSql = `
      SELECT COUNT(*) as total 
      FROM notifications 
      ${whereClause}
    `;
        const countResult = await (0, database_1.queryOne)(countSql, values);
        const total = parseInt(countResult.total);
        const sql = `
      SELECT * FROM notifications 
      ${whereClause}
      ORDER BY created_at DESC
      LIMIT $${++paramCount} OFFSET $${++paramCount}
    `;
        values.push(limit, offset);
        const result = await (0, database_1.query)(sql, values);
        const notifications = result.rows.map(row => this.mapRowToNotification(row));
        return { notifications, total };
    }
    static async update(id, data) {
        const fields = [];
        const values = [];
        let paramCount = 0;
        if (data.status !== undefined) {
            fields.push(`status = $${++paramCount}`);
            values.push(data.status);
        }
        if (data.sentAt !== undefined) {
            fields.push(`sent_at = $${++paramCount}`);
            values.push(data.sentAt);
        }
        if (data.deliveredAt !== undefined) {
            fields.push(`delivered_at = $${++paramCount}`);
            values.push(data.deliveredAt);
        }
        if (data.readAt !== undefined) {
            fields.push(`read_at = $${++paramCount}`);
            values.push(data.readAt);
        }
        if (data.errorMessage !== undefined) {
            fields.push(`error_message = $${++paramCount}`);
            values.push(data.errorMessage);
        }
        if (data.retryCount !== undefined) {
            fields.push(`retry_count = $${++paramCount}`);
            values.push(data.retryCount);
        }
        if (fields.length === 0) {
            return this.findById(id);
        }
        fields.push(`updated_at = NOW()`);
        const sql = `
      UPDATE notifications 
      SET ${fields.join(', ')}
      WHERE id = $${++paramCount}
      RETURNING *
    `;
        values.push(id);
        try {
            const result = await (0, database_1.queryOne)(sql, values);
            return this.mapRowToNotification(result);
        }
        catch (error) {
            return null;
        }
    }
    static async getPendingNotifications(limit = 50) {
        const sql = `
      SELECT * FROM notifications 
      WHERE status = $1 
        AND retry_count < max_retries
      ORDER BY created_at ASC
      LIMIT $2
    `;
        const result = await (0, database_1.query)(sql, [types_1.NotificationStatus.PENDING, limit]);
        return result.rows.map(row => this.mapRowToNotification(row));
    }
    static async getRetryableNotifications(limit = 20) {
        const sql = `
      SELECT * FROM notifications 
      WHERE status = $1 
        AND retry_count < max_retries
        AND updated_at < NOW() - INTERVAL '5 minutes'
      ORDER BY updated_at ASC
      LIMIT $2
    `;
        const result = await (0, database_1.query)(sql, [types_1.NotificationStatus.FAILED, limit]);
        return result.rows.map(row => this.mapRowToNotification(row));
    }
    static async getStatistics(days = 30) {
        const basicStatsSql = `
      SELECT 
        COUNT(*) FILTER (WHERE status = 'sent') as total_sent,
        COUNT(*) FILTER (WHERE status = 'delivered') as total_delivered,
        COUNT(*) FILTER (WHERE status = 'failed') as total_failed,
        COUNT(*) FILTER (WHERE status = 'read') as total_read
      FROM notifications
      WHERE created_at >= NOW() - INTERVAL '${days} days'
    `;
        const basicStats = await (0, database_1.queryOne)(basicStatsSql);
        const typeStatsSql = `
      SELECT type, COUNT(*) as count
      FROM notifications
      WHERE created_at >= NOW() - INTERVAL '${days} days'
        AND status = 'sent'
      GROUP BY type
    `;
        const typeStats = await (0, database_1.query)(typeStatsSql);
        const byType = {};
        typeStats.rows.forEach(row => {
            byType[row.type] = parseInt(row.count);
        });
        const channelStatsSql = `
      SELECT channel, COUNT(*) as count
      FROM notifications
      WHERE created_at >= NOW() - INTERVAL '${days} days'
        AND status = 'sent'
      GROUP BY channel
    `;
        const channelStats = await (0, database_1.query)(channelStatsSql);
        const byChannel = {};
        channelStats.rows.forEach(row => {
            byChannel[row.channel] = parseInt(row.count);
        });
        const statusStatsSql = `
      SELECT status, COUNT(*) as count
      FROM notifications
      WHERE created_at >= NOW() - INTERVAL '${days} days'
      GROUP BY status
    `;
        const statusStats = await (0, database_1.query)(statusStatsSql);
        const byStatus = {};
        statusStats.rows.forEach(row => {
            byStatus[row.status] = parseInt(row.count);
        });
        const activitySql = `
      SELECT 
        DATE(created_at) as date,
        COUNT(*) FILTER (WHERE status = 'sent') as sent,
        COUNT(*) FILTER (WHERE status = 'delivered') as delivered,
        COUNT(*) FILTER (WHERE status = 'failed') as failed
      FROM notifications
      WHERE created_at >= NOW() - INTERVAL '7 days'
      GROUP BY DATE(created_at)
      ORDER BY date DESC
    `;
        const activityStats = await (0, database_1.query)(activitySql);
        const recentActivity = activityStats.rows.map(row => ({
            date: row.date,
            sent: parseInt(row.sent),
            delivered: parseInt(row.delivered),
            failed: parseInt(row.failed)
        }));
        const totalSent = parseInt(basicStats.total_sent);
        const totalDelivered = parseInt(basicStats.total_delivered);
        const totalFailed = parseInt(basicStats.total_failed);
        const totalRead = parseInt(basicStats.total_read);
        return {
            totalSent,
            totalDelivered,
            totalFailed,
            totalRead,
            deliveryRate: totalSent > 0 ? (totalDelivered / totalSent) * 100 : 0,
            readRate: totalDelivered > 0 ? (totalRead / totalDelivered) * 100 : 0,
            byType,
            byChannel,
            byStatus,
            recentActivity
        };
    }
    static async deleteOldNotifications(daysOld = 90) {
        const sql = `
      DELETE FROM notifications 
      WHERE created_at < NOW() - INTERVAL '${daysOld} days'
        AND status IN ('delivered', 'read', 'failed')
    `;
        const result = await (0, database_1.query)(sql);
        return result.rowCount || 0;
    }
    static mapRowToNotification(row) {
        return {
            id: row.id,
            type: row.type,
            channel: row.channel,
            status: row.status,
            recipientEmail: row.recipient_email,
            recipientName: row.recipient_name,
            subject: row.subject,
            content: row.content,
            templateData: row.template_data ? JSON.parse(row.template_data) : undefined,
            orderId: row.order_id,
            fileId: row.file_id,
            sentAt: row.sent_at,
            deliveredAt: row.delivered_at,
            readAt: row.read_at,
            errorMessage: row.error_message,
            retryCount: row.retry_count,
            maxRetries: row.max_retries,
            createdAt: row.created_at,
            updatedAt: row.updated_at
        };
    }
}
exports.NotificationModel = NotificationModel;
//# sourceMappingURL=Notification.js.map