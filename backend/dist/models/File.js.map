{"version": 3, "file": "File.js", "sourceRoot": "", "sources": ["../../src/models/File.ts"], "names": [], "mappings": ";;;AAKA,0CAA2D;AAG3D,MAAa,SAAS;IAIpB,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,IAAoB;QACtC,MAAM,GAAG,GAAG;;;;KAIX,CAAC;QAEF,MAAM,MAAM,GAAG;YACb,IAAI,CAAC,QAAQ;YACb,IAAI,CAAC,YAAY;YACjB,IAAI,CAAC,QAAQ;YACb,IAAI,CAAC,IAAI;YACT,IAAI,CAAC,GAAG;SACT,CAAC;QAEF,MAAM,MAAM,GAAG,MAAM,IAAA,mBAAQ,EAAC,GAAG,EAAE,MAAM,CAAC,CAAC;QAC3C,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;IACnC,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAU;QAC9B,MAAM,GAAG,GAAG;;;KAGX,CAAC;QAEF,MAAM,MAAM,GAAG,MAAM,IAAA,mBAAQ,EAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACzC,OAAO,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IACnD,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,cAAc,CAAC,QAAgB;QAC1C,MAAM,GAAG,GAAG;;;KAGX,CAAC;QAEF,MAAM,MAAM,GAAG,MAAM,IAAA,mBAAQ,EAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;QAC/C,OAAO,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IACnD,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,QAAgB,EAAE,EAAE,SAAiB,CAAC;QACzD,MAAM,QAAQ,GAAG;;;KAGhB,CAAC;QAEF,MAAM,QAAQ,GAAG;;;;;KAKhB,CAAC;QAEF,MAAM,CAAC,WAAW,EAAE,WAAW,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;YACnD,IAAA,mBAAQ,EAAC,QAAQ,CAAC;YAClB,IAAA,gBAAK,EAAC,QAAQ,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;SACjC,CAAC,CAAC;QAEH,OAAO;YACL,KAAK,EAAE,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;YAC1D,KAAK,EAAE,QAAQ,CAAC,WAAW,CAAC,KAAK,CAAC;SACnC,CAAC;IACJ,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,EAAU,EAAE,IAAoB;QAClD,MAAM,MAAM,GAAG,EAAE,CAAC;QAClB,MAAM,MAAM,GAAG,EAAE,CAAC;QAClB,IAAI,UAAU,GAAG,CAAC,CAAC;QAEnB,IAAI,IAAI,CAAC,YAAY,KAAK,SAAS,EAAE,CAAC;YACpC,MAAM,CAAC,IAAI,CAAC,oBAAoB,UAAU,EAAE,EAAE,CAAC,CAAC;YAChD,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACjC,CAAC;QAED,IAAI,IAAI,CAAC,GAAG,KAAK,SAAS,EAAE,CAAC;YAC3B,MAAM,CAAC,IAAI,CAAC,UAAU,UAAU,EAAE,EAAE,CAAC,CAAC;YACtC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACxB,CAAC;QAED,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACxB,OAAO,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;QAC3B,CAAC;QAED,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAEhB,MAAM,GAAG,GAAG;;YAEJ,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;oBACT,UAAU;;KAEzB,CAAC;QAEF,MAAM,MAAM,GAAG,MAAM,IAAA,mBAAQ,EAAC,GAAG,EAAE,MAAM,CAAC,CAAC;QAC3C,OAAO,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IACnD,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,EAAU;QAC5B,MAAM,GAAG,GAAG;;;;;KAKX,CAAC;QAEF,MAAM,MAAM,GAAG,MAAM,IAAA,mBAAQ,EAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACzC,OAAO,CAAC,CAAC,MAAM,CAAC;IAClB,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,cAAc,CAAC,EAAU;QACpC,MAAM,GAAG,GAAG;;;KAGX,CAAC;QAEF,MAAM,MAAM,GAAG,MAAM,IAAA,mBAAQ,EAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACzC,OAAO,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IACpC,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,cAAc,CAAC,QAAgB,EAAE,QAAgB,EAAE;QAC9D,MAAM,GAAG,GAAG;;;;;KAKX,CAAC;QAEF,MAAM,MAAM,GAAG,MAAM,IAAA,gBAAK,EAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC;QACnD,OAAO,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC;IACxD,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,eAAe;QAM1B,MAAM,QAAQ,GAAG;;;;;;;KAOhB,CAAC;QAEF,MAAM,WAAW,GAAG;;;;;;;;;KASnB,CAAC;QAEF,MAAM,CAAC,WAAW,EAAE,cAAc,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;YACtD,IAAA,mBAAQ,EAAC,QAAQ,CAAC;YAClB,IAAA,gBAAK,EAAC,WAAW,CAAC;SACnB,CAAC,CAAC;QAEH,OAAO;YACL,UAAU,EAAE,QAAQ,CAAC,WAAW,CAAC,WAAW,CAAC;YAC7C,SAAS,EAAE,QAAQ,CAAC,WAAW,CAAC,UAAU,CAAC;YAC3C,WAAW,EAAE,UAAU,CAAC,WAAW,CAAC,YAAY,CAAC;YACjD,aAAa,EAAE,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;gBAC7C,QAAQ,EAAE,GAAG,CAAC,QAAQ;gBACtB,KAAK,EAAE,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC;gBAC1B,SAAS,EAAE,QAAQ,CAAC,GAAG,CAAC,UAAU,CAAC;aACpC,CAAC,CAAC;SACJ,CAAC;IACJ,CAAC;IAKO,MAAM,CAAC,YAAY,CAAC,GAAQ;QAClC,OAAO;YACL,EAAE,EAAE,GAAG,CAAC,EAAE;YACV,QAAQ,EAAE,GAAG,CAAC,QAAQ;YACtB,YAAY,EAAE,GAAG,CAAC,aAAa;YAC/B,QAAQ,EAAE,GAAG,CAAC,QAAQ;YACtB,IAAI,EAAE,GAAG,CAAC,IAAI;YACd,GAAG,EAAE,GAAG,CAAC,GAAG;YACZ,UAAU,EAAE,GAAG,CAAC,WAAW;YAC3B,SAAS,EAAE,GAAG,CAAC,UAAU;SAC1B,CAAC;IACJ,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,aAAa,CAAC,OAAe;QACxC,MAAM,GAAG,GAAG;;;;KAIX,CAAC;QAEF,MAAM,MAAM,GAAG,MAAM,IAAA,gBAAK,EAAC,GAAG,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC;QAC3C,OAAO,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC;IACxD,CAAC;CACF;AArOD,8BAqOC"}