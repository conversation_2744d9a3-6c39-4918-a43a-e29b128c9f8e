import { Payment, CreatePaymentData, UpdatePaymentData, PaymentStatus } from '../types';
export declare class PaymentModel {
    static create(data: CreatePaymentData): Promise<Payment>;
    static findById(id: number): Promise<Payment | null>;
    static findByStripePaymentIntentId(stripePaymentIntentId: string): Promise<Payment | null>;
    static findByOrderId(orderId: number): Promise<Payment[]>;
    static update(id: number, data: UpdatePaymentData): Promise<Payment | null>;
    static findAll(offset?: number, limit?: number, status?: PaymentStatus): Promise<{
        payments: Payment[];
        total: number;
    }>;
    static getStatistics(): Promise<{
        totalPayments: number;
        totalAmount: number;
        successfulPayments: number;
        failedPayments: number;
        pendingPayments: number;
    }>;
    static delete(id: number): Promise<boolean>;
    private static mapRowToPayment;
}
export default PaymentModel;
//# sourceMappingURL=Payment.d.ts.map