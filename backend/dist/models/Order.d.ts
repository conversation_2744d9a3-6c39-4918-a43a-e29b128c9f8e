import { Order, CreateOrderData, UpdateOrderData, OrderStatus } from '../types';
interface OrderWithFile extends Order {
    file: {
        id: number;
        filename: string;
        originalName: string;
        mimetype: string;
        size: number;
        url: string;
        uploadedAt: Date;
    };
}
export declare class OrderModel {
    static calculatePrice(specs: {
        pages: number;
        copies: number;
        format: string;
        paperType: string;
        finish: string;
        hasColor?: boolean;
        complexity?: 'low' | 'medium' | 'high';
    }): {
        basePrice: number;
        paperCost: number;
        finishCost: number;
        complexityCost: number;
        totalPrice: number;
        breakdown: {
            baseCostPerPage: number;
            paperMultiplier: number;
            finishCost: number;
            complexityMultiplier: number;
        };
    };
    static create(data: CreateOrderData): Promise<Order>;
    static findById(id: number): Promise<Order | null>;
    static findByOrderNumber(orderNumber: string): Promise<Order | null>;
    static findByIdWithFile(id: number): Promise<OrderWithFile | null>;
    static findByStatus(status: OrderStatus, limit?: number, options?: {
        customerEmail?: string;
        dateFrom?: Date;
        dateTo?: Date;
        minPrice?: number;
        maxPrice?: number;
    }): Promise<Order[]>;
    static getStatistics(period?: 'day' | 'week' | 'month' | 'year'): Promise<{
        totalOrders: number;
        totalRevenue: number;
        averageOrderValue: number;
        statusBreakdown: Record<OrderStatus, number>;
        revenueByPeriod: Array<{
            period: string;
            revenue: number;
            orders: number;
        }>;
    }>;
    static findAll(options?: {
        limit?: number;
        offset?: number;
        status?: OrderStatus;
        customerEmail?: string;
        orderBy?: 'created_at' | 'updated_at' | 'price';
        orderDirection?: 'ASC' | 'DESC';
    }): Promise<{
        orders: Order[];
        total: number;
    }>;
    static update(id: number, data: UpdateOrderData): Promise<Order | null>;
    static updateStatus(id: number, status: OrderStatus, notes?: string): Promise<Order | null>;
    static getReadyOrders(limit?: number): Promise<Order[]>;
    static getOverdueOrders(): Promise<Order[]>;
    static findByCustomerEmail(email: string, limit?: number): Promise<Order[]>;
    static calculateEstimatedCompletion(complexity?: 'low' | 'medium' | 'high'): Promise<Date>;
    static delete(id: number): Promise<boolean>;
    static getStats(): Promise<{
        totalOrders: number;
        totalRevenue: number;
        averageOrderValue: number;
        statusCounts: Array<{
            status: string;
            count: number;
        }>;
    }>;
    private static generateOrderNumber;
    private static camelToSnake;
    private static mapRowToOrder;
    private static mapRowToOrderWithFile;
}
export {};
//# sourceMappingURL=Order.d.ts.map