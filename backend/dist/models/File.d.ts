import { File, CreateFileData, UpdateFileData } from '../types';
export declare class FileModel {
    static create(data: CreateFileData): Promise<File>;
    static findById(id: number): Promise<File | null>;
    static findByFilename(filename: string): Promise<File | null>;
    static findAll(limit?: number, offset?: number): Promise<{
        files: File[];
        total: number;
    }>;
    static update(id: number, data: UpdateFileData): Promise<File | null>;
    static delete(id: number): Promise<boolean>;
    static isUsedInOrders(id: number): Promise<boolean>;
    static findByMimetype(mimetype: string, limit?: number): Promise<File[]>;
    static getStorageStats(): Promise<{
        totalFiles: number;
        totalSize: number;
        averageSize: number;
        mimetypeStats: Array<{
            mimetype: string;
            count: number;
            totalSize: number;
        }>;
    }>;
    private static mapRowToFile;
}
//# sourceMappingURL=File.d.ts.map