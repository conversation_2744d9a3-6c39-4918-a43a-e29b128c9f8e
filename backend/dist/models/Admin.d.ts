import { Admin, CreateAdminData, UpdateAdminData, AdminRole, AdminStatus, AdminLoginData, AdminAuthResponse } from '../types';
export declare class AdminModel {
    static create(data: CreateAdminData): Promise<Admin>;
    static findById(id: number): Promise<Admin | null>;
    static findByEmail(email: string): Promise<Admin | null>;
    static findByEmailWithPassword(email: string): Promise<(Admin & {
        password: string;
    }) | null>;
    static authenticate(data: AdminLoginData): Promise<AdminAuthResponse | null>;
    static update(id: number, data: UpdateAdminData): Promise<Admin | null>;
    static updateLastLogin(id: number): Promise<void>;
    static findMany(options?: {
        page?: number;
        limit?: number;
        role?: AdminRole;
        status?: AdminStatus;
    }): Promise<{
        admins: Admin[];
        total: number;
        page: number;
        limit: number;
        totalPages: number;
    }>;
    static delete(id: number): Promise<boolean>;
    static verifyToken(token: string): {
        adminId: number;
        email: string;
        role: AdminRole;
    } | null;
    static existsByEmail(email: string): Promise<boolean>;
    private static mapRowToAdmin;
}
//# sourceMappingURL=Admin.d.ts.map