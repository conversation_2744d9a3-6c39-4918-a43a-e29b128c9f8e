import { Refund, CreateRefundData, UpdateRefundData, RefundStatus } from '../types';
export declare class RefundModel {
    static create(data: CreateRefundData): Promise<Refund>;
    static findById(id: number): Promise<Refund | null>;
    static findByStripeRefundId(stripeRefundId: string): Promise<Refund | null>;
    static findByPaymentId(paymentId: number): Promise<Refund[]>;
    static update(id: number, data: UpdateRefundData): Promise<Refund | null>;
    static findAll(offset?: number, limit?: number, status?: RefundStatus): Promise<{
        refunds: Refund[];
        total: number;
    }>;
    static getStatistics(): Promise<{
        totalRefunds: number;
        totalAmount: number;
        successfulRefunds: number;
        failedRefunds: number;
        pendingRefunds: number;
    }>;
    static getTotalRefundedForPayment(paymentId: number): Promise<number>;
    static delete(id: number): Promise<boolean>;
    private static mapRowToRefund;
}
export default RefundModel;
//# sourceMappingURL=Refund.d.ts.map