import { AdminActivityLog, CreateAdminActivityData } from '../types';
export declare class AdminActivityLogModel {
    static create(data: CreateAdminActivityData): Promise<AdminActivityLog>;
    static findMany(options?: {
        page?: number;
        limit?: number;
        adminId?: number;
        action?: string;
        resource?: string;
        dateFrom?: Date;
        dateTo?: Date;
    }): Promise<{
        logs: AdminActivityLog[];
        total: number;
        page: number;
        limit: number;
        totalPages: number;
    }>;
    static findByAdminId(adminId: number, options?: {
        page?: number;
        limit?: number;
    }): Promise<{
        logs: AdminActivityLog[];
        total: number;
        page: number;
        limit: number;
        totalPages: number;
    }>;
    static getRecentActivity(limit?: number): Promise<AdminActivityLog[]>;
    static getStatistics(days?: number): Promise<{
        totalActions: number;
        actionsByType: Record<string, number>;
        actionsByResource: Record<string, number>;
        actionsByAdmin: Record<string, number>;
        actionsPerDay: Array<{
            date: string;
            count: number;
        }>;
    }>;
    static cleanup(daysOld?: number): Promise<number>;
    static logAction(adminId: number, adminEmail: string, action: string, resource: string, resourceId?: number, details?: any, req?: any): Promise<AdminActivityLog>;
    private static mapRowToActivityLog;
}
//# sourceMappingURL=AdminActivityLog.d.ts.map