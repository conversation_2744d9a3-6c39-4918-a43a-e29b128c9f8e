"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminActivityLogModel = void 0;
const database_1 = __importDefault(require("../database"));
class AdminActivityLogModel {
    static async create(data) {
        const query = `
      INSERT INTO admin_activity_logs (
        admin_id, admin_email, action, resource, resource_id, 
        details, ip_address, user_agent
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      RETURNING id, admin_id, admin_email, action, resource, resource_id, 
                details, ip_address, user_agent, created_at
    `;
        const values = [
            data.adminId,
            data.adminEmail,
            data.action,
            data.resource,
            data.resourceId || null,
            data.details ? JSON.stringify(data.details) : null,
            data.ipAddress || null,
            data.userAgent || null
        ];
        const result = await database_1.default.query(query, values);
        return this.mapRowToActivityLog(result.rows[0]);
    }
    static async findMany(options = {}) {
        const page = options.page || 1;
        const limit = options.limit || 50;
        const offset = (page - 1) * limit;
        let whereConditions = [];
        const values = [];
        let paramCount = 1;
        if (options.adminId) {
            whereConditions.push(`admin_id = $${paramCount++}`);
            values.push(options.adminId);
        }
        if (options.action) {
            whereConditions.push(`action ILIKE $${paramCount++}`);
            values.push(`%${options.action}%`);
        }
        if (options.resource) {
            whereConditions.push(`resource = $${paramCount++}`);
            values.push(options.resource);
        }
        if (options.dateFrom) {
            whereConditions.push(`created_at >= $${paramCount++}`);
            values.push(options.dateFrom);
        }
        if (options.dateTo) {
            whereConditions.push(`created_at <= $${paramCount++}`);
            values.push(options.dateTo);
        }
        const whereClause = whereConditions.length > 0
            ? `WHERE ${whereConditions.join(' AND ')}`
            : '';
        const countQuery = `SELECT COUNT(*) FROM admin_activity_logs ${whereClause}`;
        const countResult = await database_1.default.query(countQuery, values);
        const total = parseInt(countResult.rows[0].count);
        const query = `
      SELECT id, admin_id, admin_email, action, resource, resource_id, 
             details, ip_address, user_agent, created_at
      FROM admin_activity_logs 
      ${whereClause}
      ORDER BY created_at DESC
      LIMIT $${paramCount++} OFFSET $${paramCount++}
    `;
        values.push(limit, offset);
        const result = await database_1.default.query(query, values);
        return {
            logs: result.rows.map(row => this.mapRowToActivityLog(row)),
            total,
            page,
            limit,
            totalPages: Math.ceil(total / limit)
        };
    }
    static async findByAdminId(adminId, options = {}) {
        return this.findMany({ ...options, adminId });
    }
    static async getRecentActivity(limit = 20) {
        const query = `
      SELECT id, admin_id, admin_email, action, resource, resource_id, 
             details, ip_address, user_agent, created_at
      FROM admin_activity_logs 
      ORDER BY created_at DESC
      LIMIT $1
    `;
        const result = await database_1.default.query(query, [limit]);
        return result.rows.map(row => this.mapRowToActivityLog(row));
    }
    static async getStatistics(days = 30) {
        const dateFrom = new Date();
        dateFrom.setDate(dateFrom.getDate() - days);
        const totalQuery = `
      SELECT COUNT(*) as total
      FROM admin_activity_logs 
      WHERE created_at >= $1
    `;
        const totalResult = await database_1.default.query(totalQuery, [dateFrom]);
        const totalActions = parseInt(totalResult.rows[0].total);
        const actionTypeQuery = `
      SELECT action, COUNT(*) as count
      FROM admin_activity_logs 
      WHERE created_at >= $1
      GROUP BY action
      ORDER BY count DESC
    `;
        const actionTypeResult = await database_1.default.query(actionTypeQuery, [dateFrom]);
        const actionsByType = actionTypeResult.rows.reduce((acc, row) => {
            acc[row.action] = parseInt(row.count);
            return acc;
        }, {});
        const resourceQuery = `
      SELECT resource, COUNT(*) as count
      FROM admin_activity_logs 
      WHERE created_at >= $1
      GROUP BY resource
      ORDER BY count DESC
    `;
        const resourceResult = await database_1.default.query(resourceQuery, [dateFrom]);
        const actionsByResource = resourceResult.rows.reduce((acc, row) => {
            acc[row.resource] = parseInt(row.count);
            return acc;
        }, {});
        const adminQuery = `
      SELECT admin_email, COUNT(*) as count
      FROM admin_activity_logs 
      WHERE created_at >= $1
      GROUP BY admin_email
      ORDER BY count DESC
    `;
        const adminResult = await database_1.default.query(adminQuery, [dateFrom]);
        const actionsByAdmin = adminResult.rows.reduce((acc, row) => {
            acc[row.admin_email] = parseInt(row.count);
            return acc;
        }, {});
        const dailyQuery = `
      SELECT DATE(created_at) as date, COUNT(*) as count
      FROM admin_activity_logs 
      WHERE created_at >= $1
      GROUP BY DATE(created_at)
      ORDER BY date DESC
    `;
        const dailyResult = await database_1.default.query(dailyQuery, [dateFrom]);
        const actionsPerDay = dailyResult.rows.map(row => ({
            date: row.date,
            count: parseInt(row.count)
        }));
        return {
            totalActions,
            actionsByType,
            actionsByResource,
            actionsByAdmin,
            actionsPerDay
        };
    }
    static async cleanup(daysOld = 365) {
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - daysOld);
        const query = `
      DELETE FROM admin_activity_logs 
      WHERE created_at < $1
    `;
        const result = await database_1.default.query(query, [cutoffDate]);
        return result.rowCount || 0;
    }
    static async logAction(adminId, adminEmail, action, resource, resourceId, details, req) {
        const data = {
            adminId,
            adminEmail,
            action,
            resource,
            details,
            ipAddress: req?.ip || req?.connection?.remoteAddress,
            userAgent: req?.get('User-Agent')
        };
        if (resourceId !== undefined) {
            data.resourceId = resourceId;
        }
        return this.create(data);
    }
    static mapRowToActivityLog(row) {
        return {
            id: row.id,
            adminId: row.admin_id,
            adminEmail: row.admin_email,
            action: row.action,
            resource: row.resource,
            resourceId: row.resource_id,
            details: row.details || null,
            ipAddress: row.ip_address,
            userAgent: row.user_agent,
            createdAt: new Date(row.created_at)
        };
    }
}
exports.AdminActivityLogModel = AdminActivityLogModel;
//# sourceMappingURL=AdminActivityLog.js.map