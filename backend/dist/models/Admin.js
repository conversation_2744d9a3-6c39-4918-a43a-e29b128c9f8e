"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminModel = void 0;
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const database_1 = __importDefault(require("../database"));
const types_1 = require("../types");
class AdminModel {
    static async create(data) {
        const hashedPassword = await bcryptjs_1.default.hash(data.password, 12);
        const query = `
      INSERT INTO admins (email, name, password, role, status)
      VALUES ($1, $2, $3, $4, $5)
      RETURNING id, email, name, role, status, last_login, created_at, updated_at
    `;
        const values = [
            data.email,
            data.name,
            hashedPassword,
            data.role,
            data.status || types_1.AdminStatus.ACTIVE
        ];
        const result = await database_1.default.query(query, values);
        return this.mapRowToAdmin(result.rows[0]);
    }
    static async findById(id) {
        const query = `
      SELECT id, email, name, role, status, last_login, created_at, updated_at
      FROM admins 
      WHERE id = $1 AND deleted_at IS NULL
    `;
        const result = await database_1.default.query(query, [id]);
        return result.rows[0] ? this.mapRowToAdmin(result.rows[0]) : null;
    }
    static async findByEmail(email) {
        const query = `
      SELECT id, email, name, role, status, last_login, created_at, updated_at
      FROM admins 
      WHERE email = $1 AND deleted_at IS NULL
    `;
        const result = await database_1.default.query(query, [email]);
        return result.rows[0] ? this.mapRowToAdmin(result.rows[0]) : null;
    }
    static async findByEmailWithPassword(email) {
        const query = `
      SELECT id, email, name, password, role, status, last_login, created_at, updated_at
      FROM admins 
      WHERE email = $1 AND deleted_at IS NULL
    `;
        const result = await database_1.default.query(query, [email]);
        if (!result.rows[0])
            return null;
        const row = result.rows[0];
        return {
            ...this.mapRowToAdmin(row),
            password: row.password
        };
    }
    static async authenticate(data) {
        const admin = await this.findByEmailWithPassword(data.email);
        if (!admin || admin.status !== types_1.AdminStatus.ACTIVE) {
            return null;
        }
        const isPasswordValid = await bcryptjs_1.default.compare(data.password, admin.password);
        if (!isPasswordValid) {
            return null;
        }
        await this.updateLastLogin(admin.id);
        const jwtSecret = process.env.JWT_SECRET;
        if (!jwtSecret) {
            throw new Error('JWT_SECRET não configurado');
        }
        const token = jsonwebtoken_1.default.sign({
            adminId: admin.id,
            email: admin.email,
            role: admin.role
        }, jwtSecret, { expiresIn: process.env.JWT_EXPIRES_IN || '7d' });
        const { password, ...adminWithoutPassword } = admin;
        return {
            admin: adminWithoutPassword,
            token,
            expiresIn: process.env.JWT_EXPIRES_IN || '7d'
        };
    }
    static async update(id, data) {
        const updates = [];
        const values = [];
        let paramCount = 1;
        if (data.email !== undefined) {
            updates.push(`email = $${paramCount++}`);
            values.push(data.email);
        }
        if (data.name !== undefined) {
            updates.push(`name = $${paramCount++}`);
            values.push(data.name);
        }
        if (data.password !== undefined) {
            const hashedPassword = await bcryptjs_1.default.hash(data.password, 12);
            updates.push(`password = $${paramCount++}`);
            values.push(hashedPassword);
        }
        if (data.role !== undefined) {
            updates.push(`role = $${paramCount++}`);
            values.push(data.role);
        }
        if (data.status !== undefined) {
            updates.push(`status = $${paramCount++}`);
            values.push(data.status);
        }
        if (data.lastLogin !== undefined) {
            updates.push(`last_login = $${paramCount++}`);
            values.push(data.lastLogin);
        }
        if (updates.length === 0) {
            return this.findById(id);
        }
        updates.push(`updated_at = NOW()`);
        values.push(id);
        const query = `
      UPDATE admins 
      SET ${updates.join(', ')}
      WHERE id = $${paramCount} AND deleted_at IS NULL
      RETURNING id, email, name, role, status, last_login, created_at, updated_at
    `;
        const result = await database_1.default.query(query, values);
        return result.rows[0] ? this.mapRowToAdmin(result.rows[0]) : null;
    }
    static async updateLastLogin(id) {
        const query = `
      UPDATE admins 
      SET last_login = NOW(), updated_at = NOW()
      WHERE id = $1 AND deleted_at IS NULL
    `;
        await database_1.default.query(query, [id]);
    }
    static async findMany(options = {}) {
        const page = options.page || 1;
        const limit = options.limit || 20;
        const offset = (page - 1) * limit;
        let whereConditions = ['deleted_at IS NULL'];
        const values = [];
        let paramCount = 1;
        if (options.role) {
            whereConditions.push(`role = $${paramCount++}`);
            values.push(options.role);
        }
        if (options.status) {
            whereConditions.push(`status = $${paramCount++}`);
            values.push(options.status);
        }
        const whereClause = whereConditions.join(' AND ');
        const countQuery = `SELECT COUNT(*) FROM admins WHERE ${whereClause}`;
        const countResult = await database_1.default.query(countQuery, values);
        const total = parseInt(countResult.rows[0].count);
        const query = `
      SELECT id, email, name, role, status, last_login, created_at, updated_at
      FROM admins 
      WHERE ${whereClause}
      ORDER BY created_at DESC
      LIMIT $${paramCount++} OFFSET $${paramCount++}
    `;
        values.push(limit, offset);
        const result = await database_1.default.query(query, values);
        return {
            admins: result.rows.map(row => this.mapRowToAdmin(row)),
            total,
            page,
            limit,
            totalPages: Math.ceil(total / limit)
        };
    }
    static async delete(id) {
        const query = `
      UPDATE admins 
      SET deleted_at = NOW(), updated_at = NOW()
      WHERE id = $1 AND deleted_at IS NULL
    `;
        const result = await database_1.default.query(query, [id]);
        return (result.rowCount || 0) > 0;
    }
    static verifyToken(token) {
        try {
            const decoded = jsonwebtoken_1.default.verify(token, process.env.JWT_SECRET || 'default-secret');
            return {
                adminId: decoded.adminId,
                email: decoded.email,
                role: decoded.role
            };
        }
        catch (error) {
            return null;
        }
    }
    static async existsByEmail(email) {
        const query = `
      SELECT 1 FROM admins 
      WHERE email = $1 AND deleted_at IS NULL
    `;
        const result = await database_1.default.query(query, [email]);
        return result.rows.length > 0;
    }
    static mapRowToAdmin(row) {
        const admin = {
            id: row.id,
            email: row.email,
            name: row.name,
            role: row.role,
            status: row.status,
            createdAt: new Date(row.created_at),
            updatedAt: new Date(row.updated_at)
        };
        if (row.last_login) {
            admin.lastLogin = new Date(row.last_login);
        }
        return admin;
    }
}
exports.AdminModel = AdminModel;
//# sourceMappingURL=Admin.js.map