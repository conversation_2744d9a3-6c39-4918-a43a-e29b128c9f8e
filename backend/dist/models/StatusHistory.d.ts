import { StatusHistory, CreateStatusHistoryData, OrderStatus } from '../types';
export declare class StatusHistoryModel {
    static create(data: CreateStatusHistoryData): Promise<StatusHistory>;
    static findByOrderId(orderId: number): Promise<StatusHistory[]>;
    static getLatestStatus(orderId: number): Promise<StatusHistory | null>;
    static findAll(limit?: number, offset?: number, orderId?: number): Promise<{
        history: StatusHistory[];
        total: number;
    }>;
    static findByStatus(status: OrderStatus, limit?: number): Promise<StatusHistory[]>;
    static findByUpdatedBy(updatedBy: string, limit?: number): Promise<StatusHistory[]>;
    static getStatusTransitionStats(): Promise<Array<{
        fromStatus: string | null;
        toStatus: string;
        count: number;
        averageTimeInStatus?: number;
    }>>;
    static getStaleOrders(status: OrderStatus, hoursThreshold?: number): Promise<Array<{
        orderId: number;
        status: string;
        hoursInStatus: number;
        lastUpdated: Date;
    }>>;
    static deleteByOrderId(orderId: number): Promise<number>;
    static findWithOrderInfo(limit?: number, offset?: number): Promise<Array<StatusHistory & {
        orderNumber: string;
        customerEmail?: string;
    }>>;
    private static mapRowToStatusHistory;
}
//# sourceMappingURL=StatusHistory.d.ts.map