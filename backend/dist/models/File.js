"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FileModel = void 0;
const database_1 = require("../database");
class FileModel {
    static async create(data) {
        const sql = `
      INSERT INTO files (filename, original_name, mimetype, size, url)
      VALUES ($1, $2, $3, $4, $5)
      RETURNING *
    `;
        const values = [
            data.filename,
            data.originalName,
            data.mimetype,
            data.size,
            data.url
        ];
        const result = await (0, database_1.queryOne)(sql, values);
        return this.mapRowToFile(result);
    }
    static async findById(id) {
        const sql = `
      SELECT * FROM files 
      WHERE id = $1 AND deleted_at IS NULL
    `;
        const result = await (0, database_1.queryOne)(sql, [id]);
        return result ? this.mapRowToFile(result) : null;
    }
    static async findByFilename(filename) {
        const sql = `
      SELECT * FROM files 
      WHERE filename = $1 AND deleted_at IS NULL
    `;
        const result = await (0, database_1.queryOne)(sql, [filename]);
        return result ? this.mapRowToFile(result) : null;
    }
    static async findAll(limit = 50, offset = 0) {
        const countSql = `
      SELECT COUNT(*) as total FROM files 
      WHERE deleted_at IS NULL
    `;
        const filesSql = `
      SELECT * FROM files 
      WHERE deleted_at IS NULL
      ORDER BY uploaded_at DESC
      LIMIT $1 OFFSET $2
    `;
        const [countResult, filesResult] = await Promise.all([
            (0, database_1.queryOne)(countSql),
            (0, database_1.query)(filesSql, [limit, offset])
        ]);
        return {
            files: filesResult.rows.map(row => this.mapRowToFile(row)),
            total: parseInt(countResult.total)
        };
    }
    static async update(id, data) {
        const fields = [];
        const values = [];
        let paramCount = 1;
        if (data.originalName !== undefined) {
            fields.push(`original_name = $${paramCount++}`);
            values.push(data.originalName);
        }
        if (data.url !== undefined) {
            fields.push(`url = $${paramCount++}`);
            values.push(data.url);
        }
        if (fields.length === 0) {
            return this.findById(id);
        }
        values.push(id);
        const sql = `
      UPDATE files 
      SET ${fields.join(', ')}
      WHERE id = $${paramCount} AND deleted_at IS NULL
      RETURNING *
    `;
        const result = await (0, database_1.queryOne)(sql, values);
        return result ? this.mapRowToFile(result) : null;
    }
    static async delete(id) {
        const sql = `
      UPDATE files 
      SET deleted_at = CURRENT_TIMESTAMP
      WHERE id = $1 AND deleted_at IS NULL
      RETURNING id
    `;
        const result = await (0, database_1.queryOne)(sql, [id]);
        return !!result;
    }
    static async isUsedInOrders(id) {
        const sql = `
      SELECT COUNT(*) as count FROM orders 
      WHERE file_id = $1
    `;
        const result = await (0, database_1.queryOne)(sql, [id]);
        return parseInt(result.count) > 0;
    }
    static async findByMimetype(mimetype, limit = 50) {
        const sql = `
      SELECT * FROM files 
      WHERE mimetype = $1 AND deleted_at IS NULL
      ORDER BY uploaded_at DESC
      LIMIT $2
    `;
        const result = await (0, database_1.query)(sql, [mimetype, limit]);
        return result.rows.map(row => this.mapRowToFile(row));
    }
    static async getStorageStats() {
        const statsSql = `
      SELECT 
        COUNT(*) as total_files,
        COALESCE(SUM(size), 0) as total_size,
        COALESCE(AVG(size), 0) as average_size
      FROM files 
      WHERE deleted_at IS NULL
    `;
        const mimetypeSql = `
      SELECT 
        mimetype,
        COUNT(*) as count,
        COALESCE(SUM(size), 0) as total_size
      FROM files 
      WHERE deleted_at IS NULL
      GROUP BY mimetype
      ORDER BY count DESC
    `;
        const [statsResult, mimetypeResult] = await Promise.all([
            (0, database_1.queryOne)(statsSql),
            (0, database_1.query)(mimetypeSql)
        ]);
        return {
            totalFiles: parseInt(statsResult.total_files),
            totalSize: parseInt(statsResult.total_size),
            averageSize: parseFloat(statsResult.average_size),
            mimetypeStats: mimetypeResult.rows.map(row => ({
                mimetype: row.mimetype,
                count: parseInt(row.count),
                totalSize: parseInt(row.total_size)
            }))
        };
    }
    static mapRowToFile(row) {
        return {
            id: row.id,
            filename: row.filename,
            originalName: row.original_name,
            mimetype: row.mimetype,
            size: row.size,
            url: row.url,
            uploadedAt: row.uploaded_at,
            deletedAt: row.deleted_at
        };
    }
    static async findByOrderId(orderId) {
        const sql = `
      SELECT * FROM files
      WHERE order_id = $1 AND deleted_at IS NULL
      ORDER BY uploaded_at DESC
    `;
        const result = await (0, database_1.query)(sql, [orderId]);
        return result.rows.map(row => this.mapRowToFile(row));
    }
}
exports.FileModel = FileModel;
//# sourceMappingURL=File.js.map