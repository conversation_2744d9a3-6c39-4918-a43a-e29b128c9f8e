"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrderModel = void 0;
const database_1 = require("../database");
const types_1 = require("../types");
class OrderModel {
    static calculatePrice(specs) {
        const basePrices = {
            'A4': 0.50,
            'A3': 1.00,
            'A5': 0.30,
            'Letter': 0.50,
            'Legal': 0.60
        };
        const paperMultipliers = {
            'standard': 1.0,
            'premium': 1.5,
            'photo': 2.0,
            'cardstock': 1.8
        };
        const finishCosts = {
            'none': 0,
            'binding': 5.00,
            'lamination': 3.00,
            'stapling': 1.00,
            'hole-punch': 0.50
        };
        const complexityMultipliers = {
            'low': 1.0,
            'medium': 1.2,
            'high': 1.5
        };
        const baseCostPerPage = basePrices[specs.format] || basePrices['A4'] || 0.50;
        const paperMultiplier = paperMultipliers[specs.paperType] || 1.0;
        const finishCost = finishCosts[specs.finish] || 0;
        const complexityMultiplier = complexityMultipliers[specs.complexity || 'low'] || 1.0;
        const colorMultiplier = specs.hasColor ? 1.5 : 1.0;
        const basePrice = specs.pages * specs.copies * baseCostPerPage * colorMultiplier;
        const paperCost = basePrice * (paperMultiplier - 1);
        const complexityCost = basePrice * (complexityMultiplier - 1);
        const totalPrice = basePrice + paperCost + finishCost + complexityCost;
        return {
            basePrice,
            paperCost,
            finishCost,
            complexityCost,
            totalPrice: Math.round(totalPrice * 100) / 100,
            breakdown: {
                baseCostPerPage,
                paperMultiplier,
                finishCost,
                complexityMultiplier
            }
        };
    }
    static async create(data) {
        const orderNumber = await this.generateOrderNumber();
        let finalPrice = data.price;
        if (!finalPrice && data.pages) {
            const priceCalc = this.calculatePrice({
                pages: data.pages,
                copies: data.copies || 1,
                format: data.format,
                paperType: data.paperType,
                finish: data.finish,
                ...(data.hasColor !== undefined && { hasColor: data.hasColor }),
                ...(data.complexity !== undefined && { complexity: data.complexity })
            });
            finalPrice = priceCalc.totalPrice;
        }
        const sql = `
      INSERT INTO orders (
        order_number, file_id, customer_name, customer_email, customer_phone,
        format, paper_type, finish, copies, pages, price, status, notes,
        has_color, complexity, estimated_completion
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16)
      RETURNING *
    `;
        const estimatedDays = data.complexity === 'high' ? 5 : data.complexity === 'medium' ? 3 : 2;
        const estimatedCompletion = new Date();
        estimatedCompletion.setDate(estimatedCompletion.getDate() + estimatedDays);
        const values = [
            orderNumber,
            data.fileId,
            data.customerName || null,
            data.customerEmail || null,
            data.customerPhone || null,
            data.format,
            data.paperType,
            data.finish,
            data.copies || 1,
            data.pages || null,
            finalPrice,
            data.status || 'pending',
            data.notes || null,
            data.hasColor || false,
            data.complexity || 'low',
            estimatedCompletion
        ];
        const result = await (0, database_1.queryOne)(sql, values);
        return this.mapRowToOrder(result);
    }
    static async findById(id) {
        const sql = `SELECT * FROM orders WHERE id = $1`;
        const result = await (0, database_1.queryOne)(sql, [id]);
        return result ? this.mapRowToOrder(result) : null;
    }
    static async findByOrderNumber(orderNumber) {
        const sql = `SELECT * FROM orders WHERE order_number = $1`;
        const result = await (0, database_1.queryOne)(sql, [orderNumber]);
        return result ? this.mapRowToOrder(result) : null;
    }
    static async findByIdWithFile(id) {
        const sql = `
      SELECT 
        o.*,
        f.filename,
        f.original_name,
        f.mimetype,
        f.size as file_size,
        f.uploaded_at as file_uploaded_at
      FROM orders o
      JOIN files f ON o.file_id = f.id
      WHERE o.id = $1 AND f.deleted_at IS NULL
    `;
        const result = await (0, database_1.queryOne)(sql, [id]);
        return result ? this.mapRowToOrderWithFile(result) : null;
    }
    static async findByStatus(status, limit = 50, options = {}) {
        const conditions = ['status = $1'];
        const values = [status];
        let paramCount = 2;
        if (options.customerEmail) {
            conditions.push(`customer_email ILIKE $${paramCount++}`);
            values.push(`%${options.customerEmail}%`);
        }
        if (options.dateFrom) {
            conditions.push(`created_at >= $${paramCount++}`);
            values.push(options.dateFrom);
        }
        if (options.dateTo) {
            conditions.push(`created_at <= $${paramCount++}`);
            values.push(options.dateTo);
        }
        if (options.minPrice) {
            conditions.push(`price >= $${paramCount++}`);
            values.push(options.minPrice);
        }
        if (options.maxPrice) {
            conditions.push(`price <= $${paramCount++}`);
            values.push(options.maxPrice);
        }
        const sql = `
      SELECT * FROM orders
      WHERE ${conditions.join(' AND ')}
      ORDER BY created_at DESC
      LIMIT $${paramCount}
    `;
        values.push(limit);
        const result = await (0, database_1.query)(sql, values);
        return result.rows.map(row => this.mapRowToOrder(row));
    }
    static async getStatistics(period = 'month') {
        const totalSql = `
      SELECT
        COUNT(*) as total_orders,
        COALESCE(SUM(price), 0) as total_revenue,
        COALESCE(AVG(price), 0) as average_order_value
      FROM orders
      WHERE deleted_at IS NULL
    `;
        const totalResult = await (0, database_1.queryOne)(totalSql);
        const statusSql = `
      SELECT status, COUNT(*) as count
      FROM orders
      WHERE deleted_at IS NULL
      GROUP BY status
    `;
        const statusResult = await (0, database_1.query)(statusSql);
        const statusBreakdown = {
            'pending': 0,
            'confirmed': 0,
            'processing': 0,
            'ready': 0,
            'completed': 0,
            'cancelled': 0
        };
        statusResult.rows.forEach(row => {
            statusBreakdown[row.status] = parseInt(row.count);
        });
        const periodFormat = {
            'day': 'YYYY-MM-DD',
            'week': 'YYYY-"W"WW',
            'month': 'YYYY-MM',
            'year': 'YYYY'
        }[period];
        const revenueSql = `
      SELECT
        TO_CHAR(created_at, '${periodFormat}') as period,
        COALESCE(SUM(price), 0) as revenue,
        COUNT(*) as orders
      FROM orders
      WHERE deleted_at IS NULL
        AND created_at >= NOW() - INTERVAL '30 days'
      GROUP BY TO_CHAR(created_at, '${periodFormat}')
      ORDER BY period DESC
      LIMIT 10
    `;
        const revenueResult = await (0, database_1.query)(revenueSql);
        return {
            totalOrders: parseInt(totalResult.total_orders),
            totalRevenue: parseFloat(totalResult.total_revenue),
            averageOrderValue: parseFloat(totalResult.average_order_value),
            statusBreakdown,
            revenueByPeriod: revenueResult.rows.map(row => ({
                period: row.period,
                revenue: parseFloat(row.revenue),
                orders: parseInt(row.orders)
            }))
        };
    }
    static async findAll(options = {}) {
        const { limit = 50, offset = 0, status, customerEmail, orderBy = 'created_at', orderDirection = 'DESC' } = options;
        let whereConditions = [];
        let values = [];
        let paramCount = 1;
        if (status) {
            whereConditions.push(`status = $${paramCount++}`);
            values.push(status);
        }
        if (customerEmail) {
            whereConditions.push(`customer_email ILIKE $${paramCount++}`);
            values.push(`%${customerEmail}%`);
        }
        const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';
        const countSql = `
      SELECT COUNT(*) as total FROM orders ${whereClause}
    `;
        const ordersSql = `
      SELECT * FROM orders 
      ${whereClause}
      ORDER BY ${orderBy} ${orderDirection}
      LIMIT $${paramCount++} OFFSET $${paramCount++}
    `;
        values.push(limit, offset);
        const [countResult, ordersResult] = await Promise.all([
            (0, database_1.queryOne)(countSql, values.slice(0, -2)),
            (0, database_1.query)(ordersSql, values)
        ]);
        return {
            orders: ordersResult.rows.map(row => this.mapRowToOrder(row)),
            total: parseInt(countResult.total)
        };
    }
    static async update(id, data) {
        const fields = [];
        const values = [];
        let paramCount = 1;
        Object.entries(data).forEach(([key, value]) => {
            if (value !== undefined) {
                const dbField = this.camelToSnake(key);
                fields.push(`${dbField} = $${paramCount++}`);
                values.push(value);
            }
        });
        if (fields.length === 0) {
            return this.findById(id);
        }
        values.push(id);
        const sql = `
      UPDATE orders 
      SET ${fields.join(', ')}
      WHERE id = $${paramCount}
      RETURNING *
    `;
        const result = await (0, database_1.queryOne)(sql, values);
        return result ? this.mapRowToOrder(result) : null;
    }
    static async updateStatus(id, status, notes) {
        const currentOrder = await this.findById(id);
        if (!currentOrder) {
            throw new Error('Order not found');
        }
        const validTransitions = {
            [types_1.OrderStatus.PENDING]: [types_1.OrderStatus.CONFIRMED, types_1.OrderStatus.CANCELLED],
            [types_1.OrderStatus.CONFIRMED]: [types_1.OrderStatus.PROCESSING, types_1.OrderStatus.CANCELLED],
            [types_1.OrderStatus.PROCESSING]: [types_1.OrderStatus.READY, types_1.OrderStatus.CANCELLED],
            [types_1.OrderStatus.READY]: [types_1.OrderStatus.COMPLETED, types_1.OrderStatus.PROCESSING],
            [types_1.OrderStatus.COMPLETED]: [],
            [types_1.OrderStatus.CANCELLED]: [types_1.OrderStatus.PENDING]
        };
        const allowedStatuses = validTransitions[currentOrder.status];
        if (!allowedStatuses.includes(status)) {
            throw new Error(`Invalid status transition from ${currentOrder.status} to ${status}`);
        }
        const result = await (0, database_1.transaction)(async (client) => {
            const updateSql = `
        UPDATE orders
        SET status = $1, notes = $2, updated_at = NOW()
        WHERE id = $3
        RETURNING *
      `;
            const orderResult = await client.query(updateSql, [status, notes || currentOrder.notes, id]);
            const historySql = `
        INSERT INTO status_history (order_id, from_status, to_status, notes, updated_by)
        VALUES ($1, $2, $3, $4, $5)
      `;
            await client.query(historySql, [
                id,
                currentOrder.status,
                status,
                notes || `Status changed from ${currentOrder.status} to ${status}`,
                'system'
            ]);
            return orderResult.rows[0];
        });
        return this.mapRowToOrder(result);
    }
    static async getReadyOrders(limit = 20) {
        const sql = `
      SELECT * FROM orders
      WHERE status = 'ready'
        AND deleted_at IS NULL
      ORDER BY updated_at ASC
      LIMIT $1
    `;
        const result = await (0, database_1.query)(sql, [limit]);
        return result.rows.map(row => this.mapRowToOrder(row));
    }
    static async getOverdueOrders() {
        const sql = `
      SELECT * FROM orders
      WHERE estimated_completion < NOW()
        AND status NOT IN ('completed', 'cancelled')
        AND deleted_at IS NULL
      ORDER BY estimated_completion ASC
    `;
        const result = await (0, database_1.query)(sql);
        return result.rows.map(row => this.mapRowToOrder(row));
    }
    static async findByCustomerEmail(email, limit = 50) {
        const sql = `
      SELECT * FROM orders
      WHERE customer_email = $1
        AND deleted_at IS NULL
      ORDER BY created_at DESC
      LIMIT $2
    `;
        const result = await (0, database_1.query)(sql, [email, limit]);
        return result.rows.map(row => this.mapRowToOrder(row));
    }
    static async calculateEstimatedCompletion(complexity = 'low') {
        const workloadSql = `
      SELECT COUNT(*) as pending_orders
      FROM orders
      WHERE status IN ('pending', 'confirmed', 'processing')
        AND deleted_at IS NULL
    `;
        const workloadResult = await (0, database_1.queryOne)(workloadSql);
        const pendingOrders = parseInt(workloadResult.pending_orders);
        const baseDays = {
            'low': 1,
            'medium': 2,
            'high': 3
        }[complexity];
        const workloadDays = Math.ceil(pendingOrders / 10);
        const totalDays = baseDays + workloadDays;
        const estimatedDate = new Date();
        estimatedDate.setDate(estimatedDate.getDate() + totalDays);
        return estimatedDate;
    }
    static async delete(id) {
        const sql = `DELETE FROM orders WHERE id = $1 RETURNING id`;
        const result = await (0, database_1.queryOne)(sql, [id]);
        return !!result;
    }
    static async getStats() {
        const statsSql = `
      SELECT 
        COUNT(*) as total_orders,
        COALESCE(SUM(price), 0) as total_revenue,
        COALESCE(AVG(price), 0) as average_order_value
      FROM orders
    `;
        const statusSql = `
      SELECT status, COUNT(*) as count
      FROM orders
      GROUP BY status
      ORDER BY count DESC
    `;
        const [statsResult, statusResult] = await Promise.all([
            (0, database_1.queryOne)(statsSql),
            (0, database_1.query)(statusSql)
        ]);
        return {
            totalOrders: parseInt(statsResult.total_orders),
            totalRevenue: parseFloat(statsResult.total_revenue),
            averageOrderValue: parseFloat(statsResult.average_order_value),
            statusCounts: statusResult.rows.map(row => ({
                status: row.status,
                count: parseInt(row.count)
            }))
        };
    }
    static async generateOrderNumber() {
        const prefix = 'WP';
        const timestamp = Date.now().toString().slice(-8);
        const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        let orderNumber = `${prefix}${timestamp}${random}`;
        let attempts = 0;
        while (attempts < 10) {
            const existing = await this.findByOrderNumber(orderNumber);
            if (!existing)
                break;
            attempts++;
            const newRandom = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
            orderNumber = `${prefix}${timestamp}${newRandom}`;
        }
        return orderNumber;
    }
    static camelToSnake(str) {
        return str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
    }
    static mapRowToOrder(row) {
        return {
            id: row.id,
            orderNumber: row.order_number,
            fileId: row.file_id,
            customerName: row.customer_name,
            customerEmail: row.customer_email,
            customerPhone: row.customer_phone,
            format: row.format,
            paperType: row.paper_type,
            finish: row.finish,
            copies: row.copies,
            pages: row.pages,
            price: parseFloat(row.price),
            status: row.status,
            notes: row.notes,
            createdAt: row.created_at,
            updatedAt: row.updated_at
        };
    }
    static mapRowToOrderWithFile(row) {
        return {
            ...this.mapRowToOrder(row),
            file: {
                id: row.file_id,
                filename: row.filename,
                originalName: row.original_name,
                mimetype: row.mimetype,
                size: row.file_size,
                url: row.file_url,
                uploadedAt: row.file_uploaded_at
            }
        };
    }
}
exports.OrderModel = OrderModel;
//# sourceMappingURL=Order.js.map