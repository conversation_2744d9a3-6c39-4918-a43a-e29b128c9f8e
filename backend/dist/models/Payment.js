"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentModel = void 0;
const database_1 = require("../database");
const types_1 = require("../types");
class PaymentModel {
    static async create(data) {
        const sql = `
      INSERT INTO payments (
        order_id, customer_email, stripe_payment_intent_id, stripe_payment_method_id,
        amount, currency, status, payment_method_type, description, metadata
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
      RETURNING *
    `;
        const values = [
            data.orderId,
            data.customerEmail,
            data.stripePaymentIntentId,
            data.stripePaymentMethodId || null,
            data.amount,
            data.currency || 'EUR',
            data.status || types_1.PaymentStatus.PENDING,
            data.paymentMethodType || null,
            data.description || null,
            data.metadata ? JSON.stringify(data.metadata) : null
        ];
        const result = await (0, database_1.queryOne)(sql, values);
        return this.mapRowToPayment(result);
    }
    static async findById(id) {
        const sql = `
      SELECT * FROM payments 
      WHERE id = $1 AND deleted_at IS NULL
    `;
        const result = await (0, database_1.queryOne)(sql, [id]);
        return result ? this.mapRowToPayment(result) : null;
    }
    static async findByStripePaymentIntentId(stripePaymentIntentId) {
        const sql = `
      SELECT * FROM payments 
      WHERE stripe_payment_intent_id = $1 AND deleted_at IS NULL
    `;
        const result = await (0, database_1.queryOne)(sql, [stripePaymentIntentId]);
        return result ? this.mapRowToPayment(result) : null;
    }
    static async findByOrderId(orderId) {
        const sql = `
      SELECT * FROM payments 
      WHERE order_id = $1 AND deleted_at IS NULL
      ORDER BY created_at DESC
    `;
        const result = await (0, database_1.query)(sql, [orderId]);
        return result.rows.map(row => this.mapRowToPayment(row));
    }
    static async update(id, data) {
        const updateFields = [];
        const values = [];
        let paramCount = 1;
        if (data.status !== undefined) {
            updateFields.push(`status = $${paramCount++}`);
            values.push(data.status);
        }
        if (data.stripePaymentMethodId !== undefined) {
            updateFields.push(`stripe_payment_method_id = $${paramCount++}`);
            values.push(data.stripePaymentMethodId);
        }
        if (data.paymentMethodType !== undefined) {
            updateFields.push(`payment_method_type = $${paramCount++}`);
            values.push(data.paymentMethodType);
        }
        if (data.paidAt !== undefined) {
            updateFields.push(`paid_at = $${paramCount++}`);
            values.push(data.paidAt);
        }
        if (data.failedAt !== undefined) {
            updateFields.push(`failed_at = $${paramCount++}`);
            values.push(data.failedAt);
        }
        if (data.failureReason !== undefined) {
            updateFields.push(`failure_reason = $${paramCount++}`);
            values.push(data.failureReason);
        }
        if (data.failureCode !== undefined) {
            updateFields.push(`failure_code = $${paramCount++}`);
            values.push(data.failureCode);
        }
        if (data.receiptUrl !== undefined) {
            updateFields.push(`receipt_url = $${paramCount++}`);
            values.push(data.receiptUrl);
        }
        if (data.metadata !== undefined) {
            updateFields.push(`metadata = $${paramCount++}`);
            values.push(data.metadata ? JSON.stringify(data.metadata) : null);
        }
        if (updateFields.length === 0) {
            return this.findById(id);
        }
        updateFields.push(`updated_at = CURRENT_TIMESTAMP`);
        values.push(id);
        const sql = `
      UPDATE payments 
      SET ${updateFields.join(', ')}
      WHERE id = $${paramCount} AND deleted_at IS NULL
      RETURNING *
    `;
        const result = await (0, database_1.queryOne)(sql, values);
        return result ? this.mapRowToPayment(result) : null;
    }
    static async findAll(offset = 0, limit = 50, status) {
        let whereClause = 'WHERE deleted_at IS NULL';
        const values = [];
        let paramCount = 1;
        if (status) {
            whereClause += ` AND status = $${paramCount++}`;
            values.push(status);
        }
        const countSql = `SELECT COUNT(*) FROM payments ${whereClause}`;
        const countResult = await (0, database_1.queryOne)(countSql, values);
        const total = parseInt(countResult.count);
        values.push(limit, offset);
        const sql = `
      SELECT * FROM payments 
      ${whereClause}
      ORDER BY created_at DESC
      LIMIT $${paramCount++} OFFSET $${paramCount++}
    `;
        const result = await (0, database_1.query)(sql, values);
        const payments = result.rows.map(row => this.mapRowToPayment(row));
        return { payments, total };
    }
    static async getStatistics() {
        const sql = `
      SELECT 
        COUNT(*) as total_payments,
        COALESCE(SUM(amount), 0) as total_amount,
        COUNT(CASE WHEN status = 'succeeded' THEN 1 END) as successful_payments,
        COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_payments,
        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_payments
      FROM payments 
      WHERE deleted_at IS NULL
    `;
        const result = await (0, database_1.queryOne)(sql);
        return {
            totalPayments: parseInt(result.total_payments),
            totalAmount: parseFloat(result.total_amount),
            successfulPayments: parseInt(result.successful_payments),
            failedPayments: parseInt(result.failed_payments),
            pendingPayments: parseInt(result.pending_payments)
        };
    }
    static async delete(id) {
        const sql = `
      UPDATE payments 
      SET deleted_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
      WHERE id = $1 AND deleted_at IS NULL
    `;
        const result = await (0, database_1.query)(sql, [id]);
        return (result.rowCount || 0) > 0;
    }
    static mapRowToPayment(row) {
        const payment = {
            id: row.id,
            orderId: row.order_id,
            customerEmail: row.customer_email,
            stripePaymentIntentId: row.stripe_payment_intent_id,
            amount: parseFloat(row.amount),
            currency: row.currency,
            status: row.status,
            stripeFee: parseFloat(row.stripe_fee || '0'),
            createdAt: new Date(row.created_at),
            updatedAt: new Date(row.updated_at)
        };
        if (row.stripe_payment_method_id)
            payment.stripePaymentMethodId = row.stripe_payment_method_id;
        if (row.payment_method_type)
            payment.paymentMethodType = row.payment_method_type;
        if (row.description)
            payment.description = row.description;
        if (row.metadata)
            payment.metadata = JSON.parse(row.metadata);
        if (row.net_amount)
            payment.netAmount = parseFloat(row.net_amount);
        if (row.failure_reason)
            payment.failureReason = row.failure_reason;
        if (row.failure_code)
            payment.failureCode = row.failure_code;
        if (row.receipt_url)
            payment.receiptUrl = row.receipt_url;
        if (row.paid_at)
            payment.paidAt = new Date(row.paid_at);
        if (row.failed_at)
            payment.failedAt = new Date(row.failed_at);
        if (row.deleted_at)
            payment.deletedAt = new Date(row.deleted_at);
        return payment;
    }
}
exports.PaymentModel = PaymentModel;
exports.default = PaymentModel;
//# sourceMappingURL=Payment.js.map