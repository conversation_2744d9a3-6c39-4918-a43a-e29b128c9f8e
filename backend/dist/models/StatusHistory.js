"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.StatusHistoryModel = void 0;
const database_1 = require("../database");
class StatusHistoryModel {
    static async create(data) {
        const sql = `
      INSERT INTO status_history (order_id, status, notes, updated_by)
      VALUES ($1, $2, $3, $4)
      RETURNING *
    `;
        const values = [
            data.orderId,
            data.status,
            data.notes || null,
            data.updatedBy || 'system'
        ];
        const result = await (0, database_1.queryOne)(sql, values);
        return this.mapRowToStatusHistory(result);
    }
    static async findByOrderId(orderId) {
        const sql = `
      SELECT * FROM status_history 
      WHERE order_id = $1 
      ORDER BY updated_at ASC
    `;
        const result = await (0, database_1.query)(sql, [orderId]);
        return result.rows.map(row => this.mapRowToStatusHistory(row));
    }
    static async getLatestStatus(orderId) {
        const sql = `
      SELECT * FROM status_history 
      WHERE order_id = $1 
      ORDER BY updated_at DESC 
      LIMIT 1
    `;
        const result = await (0, database_1.queryOne)(sql, [orderId]);
        return result ? this.mapRowToStatusHistory(result) : null;
    }
    static async findAll(limit = 50, offset = 0, orderId) {
        let whereClause = '';
        let values = [];
        if (orderId) {
            whereClause = 'WHERE order_id = $1';
            values.push(orderId);
        }
        const countSql = `
      SELECT COUNT(*) as total FROM status_history ${whereClause}
    `;
        const historySql = `
      SELECT * FROM status_history 
      ${whereClause}
      ORDER BY updated_at DESC
      LIMIT $${values.length + 1} OFFSET $${values.length + 2}
    `;
        values.push(limit, offset);
        const [countResult, historyResult] = await Promise.all([
            (0, database_1.queryOne)(countSql, orderId ? [orderId] : []),
            (0, database_1.query)(historySql, values)
        ]);
        return {
            history: historyResult.rows.map(row => this.mapRowToStatusHistory(row)),
            total: parseInt(countResult.total)
        };
    }
    static async findByStatus(status, limit = 50) {
        const sql = `
      SELECT * FROM status_history 
      WHERE status = $1 
      ORDER BY updated_at DESC 
      LIMIT $2
    `;
        const result = await (0, database_1.query)(sql, [status, limit]);
        return result.rows.map(row => this.mapRowToStatusHistory(row));
    }
    static async findByUpdatedBy(updatedBy, limit = 50) {
        const sql = `
      SELECT * FROM status_history 
      WHERE updated_by = $1 
      ORDER BY updated_at DESC 
      LIMIT $2
    `;
        const result = await (0, database_1.query)(sql, [updatedBy, limit]);
        return result.rows.map(row => this.mapRowToStatusHistory(row));
    }
    static async getStatusTransitionStats() {
        const sql = `
      WITH status_transitions AS (
        SELECT 
          sh1.order_id,
          LAG(sh1.status) OVER (PARTITION BY sh1.order_id ORDER BY sh1.updated_at) as from_status,
          sh1.status as to_status,
          sh1.updated_at,
          LAG(sh1.updated_at) OVER (PARTITION BY sh1.order_id ORDER BY sh1.updated_at) as prev_updated_at
        FROM status_history sh1
      )
      SELECT 
        from_status,
        to_status,
        COUNT(*) as count,
        AVG(EXTRACT(EPOCH FROM (updated_at - prev_updated_at))) as avg_time_seconds
      FROM status_transitions
      GROUP BY from_status, to_status
      ORDER BY count DESC
    `;
        const result = await (0, database_1.query)(sql);
        return result.rows.map(row => ({
            fromStatus: row.from_status,
            toStatus: row.to_status,
            count: parseInt(row.count),
            ...(row.avg_time_seconds && { averageTimeInStatus: parseFloat(row.avg_time_seconds) })
        }));
    }
    static async getStaleOrders(status, hoursThreshold = 24) {
        const sql = `
      SELECT DISTINCT ON (order_id)
        order_id,
        status,
        EXTRACT(EPOCH FROM (NOW() - updated_at)) / 3600 as hours_in_status,
        updated_at as last_updated
      FROM status_history
      WHERE status = $1
      ORDER BY order_id, updated_at DESC
      HAVING EXTRACT(EPOCH FROM (NOW() - updated_at)) / 3600 > $2
    `;
        const result = await (0, database_1.query)(sql, [status, hoursThreshold]);
        return result.rows.map(row => ({
            orderId: row.order_id,
            status: row.status,
            hoursInStatus: parseFloat(row.hours_in_status),
            lastUpdated: row.last_updated
        }));
    }
    static async deleteByOrderId(orderId) {
        const sql = `
      DELETE FROM status_history 
      WHERE order_id = $1 
      RETURNING id
    `;
        const result = await (0, database_1.query)(sql, [orderId]);
        return result.rowCount || 0;
    }
    static async findWithOrderInfo(limit = 50, offset = 0) {
        const sql = `
      SELECT 
        sh.*,
        o.order_number,
        o.customer_email
      FROM status_history sh
      JOIN orders o ON sh.order_id = o.id
      ORDER BY sh.updated_at DESC
      LIMIT $1 OFFSET $2
    `;
        const result = await (0, database_1.query)(sql, [limit, offset]);
        return result.rows.map(row => ({
            ...this.mapRowToStatusHistory(row),
            orderNumber: row.order_number,
            customerEmail: row.customer_email
        }));
    }
    static mapRowToStatusHistory(row) {
        return {
            id: row.id,
            orderId: row.order_id,
            status: row.status,
            notes: row.notes,
            updatedBy: row.updated_by,
            updatedAt: row.updated_at
        };
    }
}
exports.StatusHistoryModel = StatusHistoryModel;
//# sourceMappingURL=StatusHistory.js.map