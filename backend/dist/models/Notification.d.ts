import { Notification, CreateNotificationData, UpdateNotificationData, NotificationType, NotificationChannel, NotificationStatus, NotificationStats } from '../types';
export declare class NotificationModel {
    static create(data: CreateNotificationData): Promise<Notification>;
    static findById(id: number): Promise<Notification | null>;
    static findMany(options?: {
        page?: number;
        limit?: number;
        type?: NotificationType;
        channel?: NotificationChannel;
        status?: NotificationStatus;
        recipientEmail?: string;
        orderId?: number;
    }): Promise<{
        notifications: Notification[];
        total: number;
    }>;
    static update(id: number, data: UpdateNotificationData): Promise<Notification | null>;
    static getPendingNotifications(limit?: number): Promise<Notification[]>;
    static getRetryableNotifications(limit?: number): Promise<Notification[]>;
    static getStatistics(days?: number): Promise<NotificationStats>;
    static deleteOldNotifications(daysOld?: number): Promise<number>;
    private static mapRowToNotification;
}
//# sourceMappingURL=Notification.d.ts.map