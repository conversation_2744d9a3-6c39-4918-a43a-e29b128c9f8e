{"version": 3, "file": "fileValidation.js", "sourceRoot": "", "sources": ["../../src/utils/fileValidation.ts"], "names": [], "mappings": ";;;;;;AAKA,4CAAoB;AACpB,gDAAwB;AACxB,iDAAoD;AAgCpD,MAAM,eAAe,GAA+D;IAElF,KAAK,EAAE,EAAE,SAAS,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,EAAE,QAAQ,EAAE,iBAAiB,EAAE;IAGxF,MAAM,EAAE,EAAE,SAAS,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,EAAE,QAAQ,EAAE,yEAAyE,EAAE;IACjJ,MAAM,EAAE,EAAE,SAAS,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,EAAE,QAAQ,EAAE,mEAAmE,EAAE;IAC3I,MAAM,EAAE,EAAE,SAAS,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,EAAE,QAAQ,EAAE,2EAA2E,EAAE;IAGnJ,KAAK,EAAE,EAAE,SAAS,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,EAAE,QAAQ,EAAE,oBAAoB,EAAE;IACnH,KAAK,EAAE,EAAE,SAAS,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,EAAE,QAAQ,EAAE,0BAA0B,EAAE;IACzH,KAAK,EAAE,EAAE,SAAS,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,EAAE,QAAQ,EAAE,+BAA+B,EAAE;IAG9H,MAAM,EAAE,EAAE,SAAS,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,EAAE,QAAQ,EAAE,YAAY,EAAE;IAC9E,KAAK,EAAE,EAAE,SAAS,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,EAAE,QAAQ,EAAE,WAAW,EAAE;IAC1G,OAAO,EAAE,EAAE,SAAS,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,EAAE,QAAQ,EAAE,WAAW,EAAE;IAChG,OAAO,EAAE,EAAE,SAAS,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,EAAE,QAAQ,EAAE,WAAW,EAAE;IAChG,KAAK,EAAE,EAAE,SAAS,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,EAAE,QAAQ,EAAE,WAAW,EAAE;IACtE,SAAS,EAAE,EAAE,SAAS,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,EAAE,QAAQ,EAAE,YAAY,EAAE;IACvF,SAAS,EAAE,EAAE,SAAS,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,EAAE,QAAQ,EAAE,YAAY,EAAE;CACxF,CAAC;AAGF,MAAM,qBAAqB,GAAG;IAC5B,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IACzB,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;IACrC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;IACrC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;IACrC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;CACtC,CAAC;AASK,MAAM,mBAAmB,GAAG,KAAK,EAAE,QAAgB,EAAsD,EAAE;IAChH,IAAI,CAAC;QACH,MAAM,EAAE,GAAG,MAAM,YAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;QACjD,MAAM,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QAChC,MAAM,EAAE,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;QAChC,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC;QAGjB,KAAK,MAAM,CAAC,IAAI,EAAE,EAAE,SAAS,EAAE,QAAQ,EAAE,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,EAAE,CAAC;YAC9E,IAAI,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC3D,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC;YAC5B,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;QACxD,OAAO,IAAI,CAAC;IACd,CAAC;AACH,CAAC,CAAC;AAnBW,QAAA,mBAAmB,uBAmB9B;AAKK,MAAM,gBAAgB,GAAG,KAAK,EAAE,QAAgB,EAAoB,EAAE;IAC3E,IAAI,CAAC;QACH,MAAM,EAAE,GAAG,MAAM,YAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;QACjD,MAAM,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAC/B,MAAM,EAAE,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC/B,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC;QAGjB,KAAK,MAAM,SAAS,IAAI,qBAAqB,EAAE,CAAC;YAC9C,IAAI,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC3D,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;QACxD,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC,CAAC;AAnBW,QAAA,gBAAgB,oBAmB3B;AAKK,MAAM,gBAAgB,GAAG,CAAC,IAAY,EAAwC,EAAE;IACrF,IAAI,IAAI,IAAI,CAAC,EAAE,CAAC;QACd,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,eAAe,EAAE,CAAC;IACpD,CAAC;IAED,IAAI,IAAI,GAAG,qBAAY,CAAC,aAAa,EAAE,CAAC;QACtC,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,qBAAY,CAAC,aAAa,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC;QACzE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,yCAAyC,SAAS,IAAI,EAAE,CAAC;IAC3F,CAAC;IAED,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;AAC3B,CAAC,CAAC;AAXW,QAAA,gBAAgB,oBAW3B;AAKK,MAAM,qBAAqB,GAAG,CAAC,QAAgB,EAAwC,EAAE;IAC9F,MAAM,GAAG,GAAG,cAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;IAEjD,IAAI,CAAC,GAAG,EAAE,CAAC;QACT,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,sBAAsB,EAAE,CAAC;IAC3D,CAAC;IAED,IAAI,CAAC,qBAAY,CAAC,kBAAkB,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;QACnD,OAAO;YACL,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,2BAA2B,GAAG,2BAA2B,qBAAY,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;SAC7G,CAAC;IACJ,CAAC;IAED,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;AAC3B,CAAC,CAAC;AAfW,QAAA,qBAAqB,yBAehC;AAKK,MAAM,gBAAgB,GAAG,CAAC,QAAgB,EAAwC,EAAE;IACzF,IAAI,CAAC,QAAQ,EAAE,CAAC;QACd,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,4BAA4B,EAAE,CAAC;IACjE,CAAC;IAED,IAAI,CAAC,qBAAY,CAAC,iBAAiB,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;QACvD,OAAO;YACL,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,kCAAkC,QAAQ,uBAAuB,qBAAY,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;SACpH,CAAC;IACJ,CAAC;IAED,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;AAC3B,CAAC,CAAC;AAbW,QAAA,gBAAgB,oBAa3B;AAKK,MAAM,gBAAgB,GAAG,CAAC,QAAgB,EAAwC,EAAE;IAEzF,IAAI,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;QACjF,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,6CAA6C,EAAE,CAAC;IAClF,CAAC;IAGD,IAAI,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;QAC5B,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,yCAAyC,EAAE,CAAC;IAC9E,CAAC;IAGD,IAAI,QAAQ,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC;QAC1B,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,qDAAqD,EAAE,CAAC;IAC1F,CAAC;IAGD,MAAM,aAAa,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;IACnM,MAAM,QAAQ,GAAG,cAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,cAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;IAE/E,IAAI,aAAa,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;QACrC,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,wCAAwC,EAAE,CAAC;IAC7E,CAAC;IAED,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;AAC3B,CAAC,CAAC;AAzBW,QAAA,gBAAgB,oBAyB3B;AAKK,MAAM,YAAY,GAAG,KAAK,EAAE,QAAkB,EAAiC,EAAE;IACtF,MAAM,MAAM,GAAyB;QACnC,OAAO,EAAE,IAAI;QACb,MAAM,EAAE,EAAE;QACV,QAAQ,EAAE,EAAE;QACZ,QAAQ,EAAE,EAAE;KACb,CAAC;IAGF,MAAM,cAAc,GAAG,IAAA,wBAAgB,EAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IACvD,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;QAC5B,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,KAAM,CAAC,CAAC;IAC5C,CAAC;IAED,MAAM,mBAAmB,GAAG,IAAA,6BAAqB,EAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;IACzE,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,CAAC;QACjC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,KAAM,CAAC,CAAC;IACjD,CAAC;IAED,MAAM,cAAc,GAAG,IAAA,wBAAgB,EAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;IAC3D,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;QAC5B,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,KAAM,CAAC,CAAC;IAC5C,CAAC;IAED,MAAM,kBAAkB,GAAG,IAAA,wBAAgB,EAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;IACnE,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,CAAC;QAChC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,KAAM,CAAC,CAAC;IAChD,CAAC;IAGD,IAAI,YAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;QAEjC,MAAM,SAAS,GAAG,MAAM,IAAA,2BAAmB,EAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAC3D,IAAI,SAAS,EAAE,CAAC;YACd,MAAM,CAAC,QAAS,CAAC,cAAc,GAAG,SAAS,CAAC,QAAQ,CAAC;YACrD,MAAM,CAAC,QAAS,CAAC,aAAa,GAAG,SAAS,CAAC,IAAI,CAAC;YAGhD,IAAI,SAAS,CAAC,QAAQ,KAAK,QAAQ,CAAC,QAAQ,EAAE,CAAC;gBAC7C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,wBAAwB,QAAQ,CAAC,QAAQ,mCAAmC,SAAS,CAAC,QAAQ,GAAG,CAAC,CAAC;YAC1H,CAAC;QACH,CAAC;QAGD,MAAM,YAAY,GAAG,MAAM,IAAA,wBAAgB,EAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAC3D,MAAM,CAAC,QAAS,CAAC,YAAY,GAAG,YAAY,CAAC;QAE7C,IAAI,YAAY,EAAE,CAAC;YACjB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;QACzD,CAAC;IACH,CAAC;IAGD,MAAM,CAAC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,CAAC;IAE5C,OAAO,MAAM,CAAC;AAChB,CAAC,CAAC;AAxDW,QAAA,YAAY,gBAwDvB;AAKK,MAAM,aAAa,GAAG,KAAK,EAAE,SAAqB,EAAyD,EAAE;IAClH,MAAM,OAAO,GAAiD,EAAE,CAAC;IAEjE,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;QACjC,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,GAAG,MAAM,IAAA,oBAAY,EAAC,QAAQ,CAAC,CAAC;IAChE,CAAC;IAED,OAAO,OAAO,CAAC;AACjB,CAAC,CAAC;AARW,QAAA,aAAa,iBAQxB;AAKK,MAAM,eAAe,GAAG,CAAC,QAAgB,EAAU,EAAE;IAC1D,IAAI,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC;QAAE,OAAO,OAAO,CAAC;IAClD,IAAI,QAAQ,KAAK,iBAAiB;QAAE,OAAO,KAAK,CAAC;IACjD,IAAI,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC;QAAE,OAAO,UAAU,CAAC;IAClF,IAAI,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC;QAAE,OAAO,aAAa,CAAC;IACnF,IAAI,QAAQ,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,YAAY,CAAC;QAAE,OAAO,cAAc,CAAC;IAChG,IAAI,QAAQ,KAAK,YAAY;QAAE,OAAO,MAAM,CAAC;IAC7C,OAAO,OAAO,CAAC;AACjB,CAAC,CAAC;AARW,QAAA,eAAe,mBAQ1B;AAKK,MAAM,cAAc,GAAG,CAAC,KAAa,EAAU,EAAE;IACtD,IAAI,KAAK,KAAK,CAAC;QAAE,OAAO,SAAS,CAAC;IAElC,MAAM,CAAC,GAAG,IAAI,CAAC;IACf,MAAM,KAAK,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;IAC1C,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IAEpD,OAAO,UAAU,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;AAC1E,CAAC,CAAC;AARW,QAAA,cAAc,kBAQzB;AAEF,kBAAe;IACb,YAAY,EAAZ,oBAAY;IACZ,aAAa,EAAb,qBAAa;IACb,mBAAmB,EAAnB,2BAAmB;IACnB,gBAAgB,EAAhB,wBAAgB;IAChB,gBAAgB,EAAhB,wBAAgB;IAChB,qBAAqB,EAArB,6BAAqB;IACrB,gBAAgB,EAAhB,wBAAgB;IAChB,gBAAgB,EAAhB,wBAAgB;IAChB,eAAe,EAAf,uBAAe;IACf,cAAc,EAAd,sBAAc;CACf,CAAC"}