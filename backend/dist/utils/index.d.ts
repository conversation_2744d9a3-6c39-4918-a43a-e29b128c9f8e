import { PrintFormat, PaperType, FinishType } from '../types';
export declare const generateUniqueFilename: (originalName: string) => string;
export declare const getFileExtension: (filename: string) => string;
export declare const isValidFileType: (mimetype: string) => boolean;
export declare const getMimeTypeExtension: (mimetype: string) => string | null;
export declare const ensureDirectoryExists: (dirPath: string) => Promise<void>;
export declare const formatFileSize: (bytes: number) => string;
export declare const isValidEmail: (email: string) => boolean;
export declare const isValidPhone: (phone: string) => boolean;
export declare const sanitizeString: (str: string) => string;
export declare const calculatePrintPrice: (pages: number, copies: number, format: PrintFormat, paperType: PaperType, finish: FinishType) => number;
export declare const formatPrice: (priceInCents: number, currency?: string) => string;
export declare const formatDate: (date: Date) => string;
export declare const formatDateTime: (date: Date) => string;
export declare const addDays: (date: Date, days: number) => Date;
export declare const isDateInRange: (date: Date, startDate: Date, endDate: Date) => boolean;
export declare const generateOrderNumber: () => string;
export declare const slugify: (text: string) => string;
export declare const truncateString: (str: string, length: number) => string;
export interface PaginationParams {
    page: number;
    limit: number;
}
export interface PaginationResult {
    offset: number;
    limit: number;
    page: number;
    totalPages: number;
    total: number;
}
export declare const calculatePagination: (page: number | undefined, limit: number | undefined, total: number) => PaginationResult;
export declare const createError: (message: string, statusCode?: number) => Error;
export declare const getEnvVar: (key: string, defaultValue?: string) => string;
export declare const getEnvVarAsNumber: (key: string, defaultValue?: number) => number;
export declare const getEnvVarAsBoolean: (key: string, defaultValue?: boolean) => boolean;
//# sourceMappingURL=index.d.ts.map