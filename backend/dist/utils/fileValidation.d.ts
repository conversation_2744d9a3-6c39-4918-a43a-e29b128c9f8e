export interface FileValidationResult {
    isValid: boolean;
    errors: string[];
    warnings: string[];
    metadata?: {
        actualMimeType?: string;
        fileSignature?: string;
        isExecutable?: boolean;
        hasMetadata?: boolean;
    };
}
export interface FileInfo {
    filename: string;
    originalName: string;
    mimetype: string;
    size: number;
    path: string;
    extension: string;
}
export declare const detectFileSignature: (filePath: string) => Promise<{
    type: string;
    mimetype: string;
} | null>;
export declare const isExecutableFile: (filePath: string) => Promise<boolean>;
export declare const validateFileSize: (size: number) => {
    isValid: boolean;
    error?: string;
};
export declare const validateFileExtension: (filename: string) => {
    isValid: boolean;
    error?: string;
};
export declare const validateMimeType: (mimetype: string) => {
    isValid: boolean;
    error?: string;
};
export declare const validateFilename: (filename: string) => {
    isValid: boolean;
    error?: string;
};
export declare const validateFile: (fileInfo: FileInfo) => Promise<FileValidationResult>;
export declare const validateFiles: (filesInfo: FileInfo[]) => Promise<{
    [filename: string]: FileValidationResult;
}>;
export declare const getFileCategory: (mimetype: string) => string;
export declare const formatFileSize: (bytes: number) => string;
declare const _default: {
    validateFile: (fileInfo: FileInfo) => Promise<FileValidationResult>;
    validateFiles: (filesInfo: FileInfo[]) => Promise<{
        [filename: string]: FileValidationResult;
    }>;
    detectFileSignature: (filePath: string) => Promise<{
        type: string;
        mimetype: string;
    } | null>;
    isExecutableFile: (filePath: string) => Promise<boolean>;
    validateFileSize: (size: number) => {
        isValid: boolean;
        error?: string;
    };
    validateFileExtension: (filename: string) => {
        isValid: boolean;
        error?: string;
    };
    validateMimeType: (mimetype: string) => {
        isValid: boolean;
        error?: string;
    };
    validateFilename: (filename: string) => {
        isValid: boolean;
        error?: string;
    };
    getFileCategory: (mimetype: string) => string;
    formatFileSize: (bytes: number) => string;
};
export default _default;
//# sourceMappingURL=fileValidation.d.ts.map