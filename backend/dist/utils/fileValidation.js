"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.formatFileSize = exports.getFileCategory = exports.validateFiles = exports.validateFile = exports.validateFilename = exports.validateMimeType = exports.validateFileExtension = exports.validateFileSize = exports.isExecutableFile = exports.detectFileSignature = void 0;
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const upload_1 = require("../middleware/upload");
const FILE_SIGNATURES = {
    'PDF': { signature: Buffer.from([0x25, 0x50, 0x44, 0x46]), mimetype: 'application/pdf' },
    'DOCX': { signature: Buffer.from([0x50, 0x4B, 0x03, 0x04]), mimetype: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' },
    'XLSX': { signature: Buffer.from([0x50, 0x4B, 0x03, 0x04]), mimetype: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' },
    'PPTX': { signature: Buffer.from([0x50, 0x4B, 0x03, 0x04]), mimetype: 'application/vnd.openxmlformats-officedocument.presentationml.presentation' },
    'DOC': { signature: Buffer.from([0xD0, 0xCF, 0x11, 0xE0, 0xA1, 0xB1, 0x1A, 0xE1]), mimetype: 'application/msword' },
    'XLS': { signature: Buffer.from([0xD0, 0xCF, 0x11, 0xE0, 0xA1, 0xB1, 0x1A, 0xE1]), mimetype: 'application/vnd.ms-excel' },
    'PPT': { signature: Buffer.from([0xD0, 0xCF, 0x11, 0xE0, 0xA1, 0xB1, 0x1A, 0xE1]), mimetype: 'application/vnd.ms-powerpoint' },
    'JPEG': { signature: Buffer.from([0xFF, 0xD8, 0xFF]), mimetype: 'image/jpeg' },
    'PNG': { signature: Buffer.from([0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A]), mimetype: 'image/png' },
    'GIF87': { signature: Buffer.from([0x47, 0x49, 0x46, 0x38, 0x37, 0x61]), mimetype: 'image/gif' },
    'GIF89': { signature: Buffer.from([0x47, 0x49, 0x46, 0x38, 0x39, 0x61]), mimetype: 'image/gif' },
    'BMP': { signature: Buffer.from([0x42, 0x4D]), mimetype: 'image/bmp' },
    'TIFF_LE': { signature: Buffer.from([0x49, 0x49, 0x2A, 0x00]), mimetype: 'image/tiff' },
    'TIFF_BE': { signature: Buffer.from([0x4D, 0x4D, 0x00, 0x2A]), mimetype: 'image/tiff' },
};
const EXECUTABLE_SIGNATURES = [
    Buffer.from([0x4D, 0x5A]),
    Buffer.from([0x7F, 0x45, 0x4C, 0x46]),
    Buffer.from([0xFE, 0xED, 0xFA, 0xCE]),
    Buffer.from([0xFE, 0xED, 0xFA, 0xCF]),
    Buffer.from([0xCA, 0xFE, 0xBA, 0xBE]),
];
const detectFileSignature = async (filePath) => {
    try {
        const fd = await fs_1.default.promises.open(filePath, 'r');
        const buffer = Buffer.alloc(16);
        await fd.read(buffer, 0, 16, 0);
        await fd.close();
        for (const [type, { signature, mimetype }] of Object.entries(FILE_SIGNATURES)) {
            if (buffer.subarray(0, signature.length).equals(signature)) {
                return { type, mimetype };
            }
        }
        return null;
    }
    catch (error) {
        console.error('Error detecting file signature:', error);
        return null;
    }
};
exports.detectFileSignature = detectFileSignature;
const isExecutableFile = async (filePath) => {
    try {
        const fd = await fs_1.default.promises.open(filePath, 'r');
        const buffer = Buffer.alloc(8);
        await fd.read(buffer, 0, 8, 0);
        await fd.close();
        for (const signature of EXECUTABLE_SIGNATURES) {
            if (buffer.subarray(0, signature.length).equals(signature)) {
                return true;
            }
        }
        return false;
    }
    catch (error) {
        console.error('Error checking executable file:', error);
        return false;
    }
};
exports.isExecutableFile = isExecutableFile;
const validateFileSize = (size) => {
    if (size <= 0) {
        return { isValid: false, error: 'Arquivo vazio' };
    }
    if (size > upload_1.uploadConfig.MAX_FILE_SIZE) {
        const maxSizeMB = Math.round(upload_1.uploadConfig.MAX_FILE_SIZE / (1024 * 1024));
        return { isValid: false, error: `Arquivo muito grande. Tamanho máximo: ${maxSizeMB}MB` };
    }
    return { isValid: true };
};
exports.validateFileSize = validateFileSize;
const validateFileExtension = (filename) => {
    const ext = path_1.default.extname(filename).toLowerCase();
    if (!ext) {
        return { isValid: false, error: 'Arquivo sem extensão' };
    }
    if (!upload_1.uploadConfig.ALLOWED_EXTENSIONS.includes(ext)) {
        return {
            isValid: false,
            error: `Extensão não permitida: ${ext}. Extensões permitidas: ${upload_1.uploadConfig.ALLOWED_EXTENSIONS.join(', ')}`
        };
    }
    return { isValid: true };
};
exports.validateFileExtension = validateFileExtension;
const validateMimeType = (mimetype) => {
    if (!mimetype) {
        return { isValid: false, error: 'Tipo MIME não especificado' };
    }
    if (!upload_1.uploadConfig.ALLOWED_MIMETYPES.includes(mimetype)) {
        return {
            isValid: false,
            error: `Tipo de arquivo não permitido: ${mimetype}. Tipos permitidos: ${upload_1.uploadConfig.ALLOWED_MIMETYPES.join(', ')}`
        };
    }
    return { isValid: true };
};
exports.validateMimeType = validateMimeType;
const validateFilename = (filename) => {
    if (filename.includes('..') || filename.includes('/') || filename.includes('\\')) {
        return { isValid: false, error: 'Nome de arquivo contém caracteres inválidos' };
    }
    if (filename.includes('\0')) {
        return { isValid: false, error: 'Nome de arquivo contém caracteres nulos' };
    }
    if (filename.length > 255) {
        return { isValid: false, error: 'Nome de arquivo muito longo (máximo 255 caracteres)' };
    }
    const reservedNames = ['CON', 'PRN', 'AUX', 'NUL', 'COM1', 'COM2', 'COM3', 'COM4', 'COM5', 'COM6', 'COM7', 'COM8', 'COM9', 'LPT1', 'LPT2', 'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9'];
    const baseName = path_1.default.basename(filename, path_1.default.extname(filename)).toUpperCase();
    if (reservedNames.includes(baseName)) {
        return { isValid: false, error: 'Nome de arquivo reservado pelo sistema' };
    }
    return { isValid: true };
};
exports.validateFilename = validateFilename;
const validateFile = async (fileInfo) => {
    const result = {
        isValid: true,
        errors: [],
        warnings: [],
        metadata: {}
    };
    const sizeValidation = (0, exports.validateFileSize)(fileInfo.size);
    if (!sizeValidation.isValid) {
        result.errors.push(sizeValidation.error);
    }
    const extensionValidation = (0, exports.validateFileExtension)(fileInfo.originalName);
    if (!extensionValidation.isValid) {
        result.errors.push(extensionValidation.error);
    }
    const mimeValidation = (0, exports.validateMimeType)(fileInfo.mimetype);
    if (!mimeValidation.isValid) {
        result.errors.push(mimeValidation.error);
    }
    const filenameValidation = (0, exports.validateFilename)(fileInfo.originalName);
    if (!filenameValidation.isValid) {
        result.errors.push(filenameValidation.error);
    }
    if (fs_1.default.existsSync(fileInfo.path)) {
        const signature = await (0, exports.detectFileSignature)(fileInfo.path);
        if (signature) {
            result.metadata.actualMimeType = signature.mimetype;
            result.metadata.fileSignature = signature.type;
            if (signature.mimetype !== fileInfo.mimetype) {
                result.warnings.push(`Tipo MIME declarado (${fileInfo.mimetype}) não corresponde ao tipo real (${signature.mimetype})`);
            }
        }
        const isExecutable = await (0, exports.isExecutableFile)(fileInfo.path);
        result.metadata.isExecutable = isExecutable;
        if (isExecutable) {
            result.errors.push('Arquivo executável não permitido');
        }
    }
    result.isValid = result.errors.length === 0;
    return result;
};
exports.validateFile = validateFile;
const validateFiles = async (filesInfo) => {
    const results = {};
    for (const fileInfo of filesInfo) {
        results[fileInfo.originalName] = await (0, exports.validateFile)(fileInfo);
    }
    return results;
};
exports.validateFiles = validateFiles;
const getFileCategory = (mimetype) => {
    if (mimetype.startsWith('image/'))
        return 'image';
    if (mimetype === 'application/pdf')
        return 'pdf';
    if (mimetype.includes('word') || mimetype.includes('document'))
        return 'document';
    if (mimetype.includes('sheet') || mimetype.includes('excel'))
        return 'spreadsheet';
    if (mimetype.includes('presentation') || mimetype.includes('powerpoint'))
        return 'presentation';
    if (mimetype === 'text/plain')
        return 'text';
    return 'other';
};
exports.getFileCategory = getFileCategory;
const formatFileSize = (bytes) => {
    if (bytes === 0)
        return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};
exports.formatFileSize = formatFileSize;
exports.default = {
    validateFile: exports.validateFile,
    validateFiles: exports.validateFiles,
    detectFileSignature: exports.detectFileSignature,
    isExecutableFile: exports.isExecutableFile,
    validateFileSize: exports.validateFileSize,
    validateFileExtension: exports.validateFileExtension,
    validateMimeType: exports.validateMimeType,
    validateFilename: exports.validateFilename,
    getFileCategory: exports.getFileCategory,
    formatFileSize: exports.formatFileSize
};
//# sourceMappingURL=fileValidation.js.map