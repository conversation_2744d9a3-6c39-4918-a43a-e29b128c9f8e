"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.calculatePrintEstimate = exports.validatePDF = exports.getPageCount = exports.extractPDFMetadata = void 0;
const fs_1 = __importDefault(require("fs"));
const pdf_parse_1 = __importDefault(require("pdf-parse"));
const extractPDFMetadata = async (filePath) => {
    try {
        if (!fs_1.default.existsSync(filePath)) {
            return {
                success: false,
                error: 'Arquivo PDF não encontrado'
            };
        }
        const dataBuffer = await fs_1.default.promises.readFile(filePath);
        const pdfData = await (0, pdf_parse_1.default)(dataBuffer, {
            max: 0,
            version: 'v1.10.100'
        });
        const stats = await fs_1.default.promises.stat(filePath);
        const metadata = {
            pages: pdfData.numpages,
            fileSize: stats.size,
            ...(pdfData.text && { textContent: pdfData.text.substring(0, 1000) }),
            hasText: Boolean(pdfData.text && pdfData.text.trim().length > 0),
            hasImages: false,
            printable: true,
            copyable: true,
        };
        if (pdfData.info) {
            const info = pdfData.info;
            metadata.title = info.Title || undefined;
            metadata.author = info.Author || undefined;
            metadata.subject = info.Subject || undefined;
            metadata.creator = info.Creator || undefined;
            metadata.producer = info.Producer || undefined;
            metadata.keywords = info.Keywords || undefined;
            if (info.CreationDate) {
                try {
                    metadata.creationDate = new Date(info.CreationDate);
                }
                catch {
                }
            }
            if (info.ModDate) {
                try {
                    metadata.modificationDate = new Date(info.ModDate);
                }
                catch {
                }
            }
        }
        metadata.hasImages = await analyzeForImages(dataBuffer);
        const restrictions = await checkPDFRestrictions(dataBuffer);
        metadata.encrypted = restrictions.encrypted;
        metadata.printable = restrictions.printable;
        metadata.copyable = restrictions.copyable;
        const version = await getPDFVersion(dataBuffer);
        if (version) {
            metadata.version = version;
        }
        const warnings = [];
        if (metadata.encrypted) {
            warnings.push('PDF está criptografado');
        }
        if (!metadata.printable) {
            warnings.push('PDF tem restrições de impressão');
        }
        if (!metadata.hasText && !metadata.hasImages) {
            warnings.push('PDF parece estar vazio');
        }
        if (metadata.pages > 100) {
            warnings.push('PDF tem muitas páginas (>100), pode demorar para processar');
        }
        return {
            success: true,
            metadata,
            ...(warnings.length > 0 && { warnings })
        };
    }
    catch (error) {
        console.error('Error processing PDF:', error);
        let errorMessage = 'Erro ao processar PDF';
        if (error instanceof Error) {
            if (error.message.includes('Invalid PDF')) {
                errorMessage = 'Arquivo PDF inválido ou corrompido';
            }
            else if (error.message.includes('encrypted')) {
                errorMessage = 'PDF criptografado não suportado';
            }
            else if (error.message.includes('password')) {
                errorMessage = 'PDF protegido por senha';
            }
        }
        return {
            success: false,
            error: errorMessage
        };
    }
};
exports.extractPDFMetadata = extractPDFMetadata;
const getPageCount = async (filePath) => {
    try {
        const dataBuffer = await fs_1.default.promises.readFile(filePath);
        const pdfData = await (0, pdf_parse_1.default)(dataBuffer, {
            max: 0,
            pagerender: () => '',
        });
        return pdfData.numpages;
    }
    catch (error) {
        console.error('Error counting PDF pages:', error);
        return 0;
    }
};
exports.getPageCount = getPageCount;
const analyzeForImages = async (dataBuffer) => {
    try {
        const pdfString = dataBuffer.toString('binary');
        const imageMarkers = ['/Image', '/DCTDecode', '/JPXDecode', '/JBIG2Decode', '/CCITTFaxDecode'];
        return imageMarkers.some(marker => pdfString.includes(marker));
    }
    catch {
        return false;
    }
};
const checkPDFRestrictions = async (dataBuffer) => {
    try {
        const pdfString = dataBuffer.toString('binary');
        const encrypted = pdfString.includes('/Encrypt') || pdfString.includes('/Filter');
        if (!encrypted) {
            return {
                encrypted: false,
                printable: true,
                copyable: true
            };
        }
        return {
            encrypted: true,
            printable: false,
            copyable: false
        };
    }
    catch {
        return {
            encrypted: false,
            printable: true,
            copyable: true
        };
    }
};
const getPDFVersion = async (dataBuffer) => {
    try {
        const firstLine = dataBuffer.subarray(0, 20).toString('ascii');
        const versionMatch = firstLine.match(/%PDF-(\d+\.\d+)/);
        return versionMatch ? versionMatch[1] : undefined;
    }
    catch {
        return undefined;
    }
};
const validatePDF = async (filePath) => {
    try {
        const result = await (0, exports.extractPDFMetadata)(filePath);
        if (!result.success) {
            return {
                isValid: false,
                ...(result.error && { error: result.error })
            };
        }
        const warnings = [];
        if (result.metadata.pages === 0) {
            return {
                isValid: false,
                error: 'PDF não contém páginas'
            };
        }
        if (result.metadata.encrypted) {
            warnings.push('PDF está criptografado');
        }
        if (!result.metadata.hasText && !result.metadata.hasImages) {
            warnings.push('PDF parece estar vazio');
        }
        return {
            isValid: true,
            ...(warnings.length > 0 && { warnings })
        };
    }
    catch (error) {
        return {
            isValid: false,
            error: 'Erro ao validar PDF'
        };
    }
};
exports.validatePDF = validatePDF;
const calculatePrintEstimate = (metadata, pricePerPage = 0.50) => {
    const pages = metadata.pages;
    let complexity = 'low';
    if (metadata.hasImages && metadata.hasText) {
        complexity = 'high';
    }
    else if (metadata.hasImages || (metadata.textContent && metadata.textContent.length > 500)) {
        complexity = 'medium';
    }
    let adjustedPrice = pricePerPage;
    if (complexity === 'medium') {
        adjustedPrice *= 1.2;
    }
    else if (complexity === 'high') {
        adjustedPrice *= 1.5;
    }
    return {
        pages,
        estimatedCost: Math.round(pages * adjustedPrice * 100) / 100,
        hasColor: metadata.hasImages || false,
        complexity
    };
};
exports.calculatePrintEstimate = calculatePrintEstimate;
exports.default = {
    extractPDFMetadata: exports.extractPDFMetadata,
    getPageCount: exports.getPageCount,
    validatePDF: exports.validatePDF,
    calculatePrintEstimate: exports.calculatePrintEstimate
};
//# sourceMappingURL=pdfProcessor.js.map