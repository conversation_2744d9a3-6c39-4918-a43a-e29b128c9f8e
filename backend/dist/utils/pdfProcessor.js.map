{"version": 3, "file": "pdfProcessor.js", "sourceRoot": "", "sources": ["../../src/utils/pdfProcessor.ts"], "names": [], "mappings": ";;;;;;AAKA,4CAAoB;AACpB,0DAAiC;AAyC1B,MAAM,kBAAkB,GAAG,KAAK,EAAE,QAAgB,EAAgC,EAAE;IACzF,IAAI,CAAC;QAEH,IAAI,CAAC,YAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC7B,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,4BAA4B;aACpC,CAAC;QACJ,CAAC;QAGD,MAAM,UAAU,GAAG,MAAM,YAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAGxD,MAAM,OAAO,GAAG,MAAM,IAAA,mBAAQ,EAAC,UAAU,EAAE;YAEzC,GAAG,EAAE,CAAC;YACN,OAAO,EAAE,WAAW;SACrB,CAAC,CAAC;QAGH,MAAM,KAAK,GAAG,MAAM,YAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAG/C,MAAM,QAAQ,GAAgB;YAC5B,KAAK,EAAE,OAAO,CAAC,QAAQ;YACvB,QAAQ,EAAE,KAAK,CAAC,IAAI;YACpB,GAAG,CAAC,OAAO,CAAC,IAAI,IAAI,EAAE,WAAW,EAAE,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC;YACrE,OAAO,EAAE,OAAO,CAAC,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC;YAChE,SAAS,EAAE,KAAK;YAChB,SAAS,EAAE,IAAI;YACf,QAAQ,EAAE,IAAI;SACf,CAAC;QAGF,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;YACjB,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;YAE1B,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI,SAAS,CAAC;YACzC,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,IAAI,SAAS,CAAC;YAC3C,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,IAAI,SAAS,CAAC;YAC7C,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,IAAI,SAAS,CAAC;YAC7C,QAAQ,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,IAAI,SAAS,CAAC;YAC/C,QAAQ,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,IAAI,SAAS,CAAC;YAG/C,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;gBACtB,IAAI,CAAC;oBACH,QAAQ,CAAC,YAAY,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBACtD,CAAC;gBAAC,MAAM,CAAC;gBAET,CAAC;YACH,CAAC;YAED,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;gBACjB,IAAI,CAAC;oBACH,QAAQ,CAAC,gBAAgB,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBACrD,CAAC;gBAAC,MAAM,CAAC;gBAET,CAAC;YACH,CAAC;QACH,CAAC;QAGD,QAAQ,CAAC,SAAS,GAAG,MAAM,gBAAgB,CAAC,UAAU,CAAC,CAAC;QAGxD,MAAM,YAAY,GAAG,MAAM,oBAAoB,CAAC,UAAU,CAAC,CAAC;QAC5D,QAAQ,CAAC,SAAS,GAAG,YAAY,CAAC,SAAS,CAAC;QAC5C,QAAQ,CAAC,SAAS,GAAG,YAAY,CAAC,SAAS,CAAC;QAC5C,QAAQ,CAAC,QAAQ,GAAG,YAAY,CAAC,QAAQ,CAAC;QAG1C,MAAM,OAAO,GAAG,MAAM,aAAa,CAAC,UAAU,CAAC,CAAC;QAChD,IAAI,OAAO,EAAE,CAAC;YACZ,QAAQ,CAAC,OAAO,GAAG,OAAO,CAAC;QAC7B,CAAC;QAED,MAAM,QAAQ,GAAa,EAAE,CAAC;QAG9B,IAAI,QAAQ,CAAC,SAAS,EAAE,CAAC;YACvB,QAAQ,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;QAC1C,CAAC;QAED,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,CAAC;YACxB,QAAQ,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;QACnD,CAAC;QAED,IAAI,CAAC,QAAQ,CAAC,OAAO,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,CAAC;YAC7C,QAAQ,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;QAC1C,CAAC;QAED,IAAI,QAAQ,CAAC,KAAK,GAAG,GAAG,EAAE,CAAC;YACzB,QAAQ,CAAC,IAAI,CAAC,4DAA4D,CAAC,CAAC;QAC9E,CAAC;QAED,OAAO;YACL,OAAO,EAAE,IAAI;YACb,QAAQ;YACR,GAAG,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,IAAI,EAAE,QAAQ,EAAE,CAAC;SACzC,CAAC;IAEJ,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;QAE9C,IAAI,YAAY,GAAG,uBAAuB,CAAC;QAE3C,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;YAC3B,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE,CAAC;gBAC1C,YAAY,GAAG,oCAAoC,CAAC;YACtD,CAAC;iBAAM,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;gBAC/C,YAAY,GAAG,iCAAiC,CAAC;YACnD,CAAC;iBAAM,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;gBAC9C,YAAY,GAAG,yBAAyB,CAAC;YAC3C,CAAC;QACH,CAAC;QAED,OAAO;YACL,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,YAAY;SACpB,CAAC;IACJ,CAAC;AACH,CAAC,CAAC;AA3HW,QAAA,kBAAkB,sBA2H7B;AAKK,MAAM,YAAY,GAAG,KAAK,EAAE,QAAgB,EAAmB,EAAE;IACtE,IAAI,CAAC;QACH,MAAM,UAAU,GAAG,MAAM,YAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QACxD,MAAM,OAAO,GAAG,MAAM,IAAA,mBAAQ,EAAC,UAAU,EAAE;YACzC,GAAG,EAAE,CAAC;YACN,UAAU,EAAE,GAAG,EAAE,CAAC,EAAE;SACrB,CAAC,CAAC;QAEH,OAAO,OAAO,CAAC,QAAQ,CAAC;IAC1B,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;QAClD,OAAO,CAAC,CAAC;IACX,CAAC;AACH,CAAC,CAAC;AAbW,QAAA,YAAY,gBAavB;AAKF,MAAM,gBAAgB,GAAG,KAAK,EAAE,UAAkB,EAAoB,EAAE;IACtE,IAAI,CAAC;QAEH,MAAM,SAAS,GAAG,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAGhD,MAAM,YAAY,GAAG,CAAC,QAAQ,EAAE,YAAY,EAAE,YAAY,EAAE,cAAc,EAAE,iBAAiB,CAAC,CAAC;QAE/F,OAAO,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;IACjE,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC,CAAC;AAKF,MAAM,oBAAoB,GAAG,KAAK,EAAE,UAAkB,EAInD,EAAE;IACH,IAAI,CAAC;QACH,MAAM,SAAS,GAAG,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAGhD,MAAM,SAAS,GAAG,SAAS,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;QAGlF,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,OAAO;gBACL,SAAS,EAAE,KAAK;gBAChB,SAAS,EAAE,IAAI;gBACf,QAAQ,EAAE,IAAI;aACf,CAAC;QACJ,CAAC;QAID,OAAO;YACL,SAAS,EAAE,IAAI;YACf,SAAS,EAAE,KAAK;YAChB,QAAQ,EAAE,KAAK;SAChB,CAAC;IAEJ,CAAC;IAAC,MAAM,CAAC;QACP,OAAO;YACL,SAAS,EAAE,KAAK;YAChB,SAAS,EAAE,IAAI;YACf,QAAQ,EAAE,IAAI;SACf,CAAC;IACJ,CAAC;AACH,CAAC,CAAC;AAKF,MAAM,aAAa,GAAG,KAAK,EAAE,UAAkB,EAA+B,EAAE;IAC9E,IAAI,CAAC;QAEH,MAAM,SAAS,GAAG,UAAU,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QAC/D,MAAM,YAAY,GAAG,SAAS,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC;QAExD,OAAO,YAAY,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;IACpD,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,SAAS,CAAC;IACnB,CAAC;AACH,CAAC,CAAC;AAKK,MAAM,WAAW,GAAG,KAAK,EAAE,QAAgB,EAI/C,EAAE;IACH,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,MAAM,IAAA,0BAAkB,EAAC,QAAQ,CAAC,CAAC;QAElD,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;YACpB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,GAAG,CAAC,MAAM,CAAC,KAAK,IAAI,EAAE,KAAK,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC;aAC7C,CAAC;QACJ,CAAC;QAED,MAAM,QAAQ,GAAa,EAAE,CAAC;QAG9B,IAAI,MAAM,CAAC,QAAS,CAAC,KAAK,KAAK,CAAC,EAAE,CAAC;YACjC,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,wBAAwB;aAChC,CAAC;QACJ,CAAC;QAED,IAAI,MAAM,CAAC,QAAS,CAAC,SAAS,EAAE,CAAC;YAC/B,QAAQ,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;QAC1C,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,QAAS,CAAC,OAAO,IAAI,CAAC,MAAM,CAAC,QAAS,CAAC,SAAS,EAAE,CAAC;YAC7D,QAAQ,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;QAC1C,CAAC;QAED,OAAO;YACL,OAAO,EAAE,IAAI;YACb,GAAG,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,IAAI,EAAE,QAAQ,EAAE,CAAC;SACzC,CAAC;IAEJ,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO;YACL,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,qBAAqB;SAC7B,CAAC;IACJ,CAAC;AACH,CAAC,CAAC;AA5CW,QAAA,WAAW,eA4CtB;AAKK,MAAM,sBAAsB,GAAG,CAAC,QAAqB,EAAE,eAAuB,IAAI,EAKvF,EAAE;IACF,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;IAC7B,IAAI,UAAU,GAA8B,KAAK,CAAC;IAGlD,IAAI,QAAQ,CAAC,SAAS,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;QAC3C,UAAU,GAAG,MAAM,CAAC;IACtB,CAAC;SAAM,IAAI,QAAQ,CAAC,SAAS,IAAI,CAAC,QAAQ,CAAC,WAAW,IAAI,QAAQ,CAAC,WAAW,CAAC,MAAM,GAAG,GAAG,CAAC,EAAE,CAAC;QAC7F,UAAU,GAAG,QAAQ,CAAC;IACxB,CAAC;IAGD,IAAI,aAAa,GAAG,YAAY,CAAC;IACjC,IAAI,UAAU,KAAK,QAAQ,EAAE,CAAC;QAC5B,aAAa,IAAI,GAAG,CAAC;IACvB,CAAC;SAAM,IAAI,UAAU,KAAK,MAAM,EAAE,CAAC;QACjC,aAAa,IAAI,GAAG,CAAC;IACvB,CAAC;IAED,OAAO;QACL,KAAK;QACL,aAAa,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,aAAa,GAAG,GAAG,CAAC,GAAG,GAAG;QAC5D,QAAQ,EAAE,QAAQ,CAAC,SAAS,IAAI,KAAK;QACrC,UAAU;KACX,CAAC;AACJ,CAAC,CAAC;AA9BW,QAAA,sBAAsB,0BA8BjC;AAEF,kBAAe;IACb,kBAAkB,EAAlB,0BAAkB;IAClB,YAAY,EAAZ,oBAAY;IACZ,WAAW,EAAX,mBAAW;IACX,sBAAsB,EAAtB,8BAAsB;CACvB,CAAC"}