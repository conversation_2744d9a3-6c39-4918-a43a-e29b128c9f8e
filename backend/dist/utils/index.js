"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getEnvVarAsBoolean = exports.getEnvVarAsNumber = exports.getEnvVar = exports.createError = exports.calculatePagination = exports.truncateString = exports.slugify = exports.generateOrderNumber = exports.isDateInRange = exports.addDays = exports.formatDateTime = exports.formatDate = exports.formatPrice = exports.calculatePrintPrice = exports.sanitizeString = exports.isValidPhone = exports.isValidEmail = exports.formatFileSize = exports.ensureDirectoryExists = exports.getMimeTypeExtension = exports.isValidFileType = exports.getFileExtension = exports.generateUniqueFilename = void 0;
const uuid_1 = require("uuid");
const path_1 = __importDefault(require("path"));
const promises_1 = __importDefault(require("fs/promises"));
const config_1 = require("../config");
const generateUniqueFilename = (originalName) => {
    const ext = path_1.default.extname(originalName);
    const name = path_1.default.basename(originalName, ext);
    const timestamp = Date.now();
    const uuid = (0, uuid_1.v4)().slice(0, 8);
    return `${name}-${timestamp}-${uuid}${ext}`;
};
exports.generateUniqueFilename = generateUniqueFilename;
const getFileExtension = (filename) => {
    return path_1.default.extname(filename).toLowerCase().slice(1);
};
exports.getFileExtension = getFileExtension;
const isValidFileType = (mimetype) => {
    return Object.keys(config_1.APP_CONSTANTS.SUPPORTED_MIME_TYPES).includes(mimetype);
};
exports.isValidFileType = isValidFileType;
const getMimeTypeExtension = (mimetype) => {
    return config_1.APP_CONSTANTS.SUPPORTED_MIME_TYPES[mimetype] || null;
};
exports.getMimeTypeExtension = getMimeTypeExtension;
const ensureDirectoryExists = async (dirPath) => {
    try {
        await promises_1.default.access(dirPath);
    }
    catch {
        await promises_1.default.mkdir(dirPath, { recursive: true });
    }
};
exports.ensureDirectoryExists = ensureDirectoryExists;
const formatFileSize = (bytes) => {
    if (bytes === 0)
        return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};
exports.formatFileSize = formatFileSize;
const isValidEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
};
exports.isValidEmail = isValidEmail;
const isValidPhone = (phone) => {
    const phoneRegex = /^(\+244\s?)?9\d{2}\s?\d{3}\s?\d{3}$/;
    return phoneRegex.test(phone.replace(/\s/g, ''));
};
exports.isValidPhone = isValidPhone;
const sanitizeString = (str) => {
    return str.trim().replace(/[<>]/g, '');
};
exports.sanitizeString = sanitizeString;
const calculatePrintPrice = (pages, copies, format, paperType, finish) => {
    const basePrice = config_1.APP_CONSTANTS.PRICING.BASE_PRICE_PER_PAGE;
    const formatMultiplier = config_1.APP_CONSTANTS.PRICING.FORMAT_MULTIPLIERS[format] || 1.0;
    const paperMultiplier = config_1.APP_CONSTANTS.PRICING.PAPER_MULTIPLIERS[paperType] || 1.0;
    const finishMultiplier = config_1.APP_CONSTANTS.PRICING.FINISH_MULTIPLIERS[finish] || 1.0;
    const pricePerPage = basePrice * formatMultiplier * paperMultiplier * finishMultiplier;
    const totalPrice = pricePerPage * pages * copies;
    return Math.round(totalPrice);
};
exports.calculatePrintPrice = calculatePrintPrice;
const formatPrice = (priceInCents, currency = 'AOA') => {
    const price = priceInCents / 100;
    return `${price.toFixed(2)} ${currency}`;
};
exports.formatPrice = formatPrice;
const formatDate = (date) => {
    const isoString = date.toISOString();
    const datePart = isoString.split('T')[0];
    if (!datePart) {
        throw new Error('Invalid date format');
    }
    return datePart;
};
exports.formatDate = formatDate;
const formatDateTime = (date) => {
    return date.toISOString().replace('T', ' ').slice(0, 19);
};
exports.formatDateTime = formatDateTime;
const addDays = (date, days) => {
    const result = new Date(date);
    result.setDate(result.getDate() + days);
    return result;
};
exports.addDays = addDays;
const isDateInRange = (date, startDate, endDate) => {
    return date >= startDate && date <= endDate;
};
exports.isDateInRange = isDateInRange;
const generateOrderNumber = () => {
    const timestamp = Date.now().toString().slice(-6);
    const random = Math.random().toString(36).substring(2, 6).toUpperCase();
    return `WP${timestamp}${random}`;
};
exports.generateOrderNumber = generateOrderNumber;
const slugify = (text) => {
    return text
        .toLowerCase()
        .replace(/[^\w\s-]/g, '')
        .replace(/[\s_-]+/g, '-')
        .replace(/^-+|-+$/g, '');
};
exports.slugify = slugify;
const truncateString = (str, length) => {
    if (str.length <= length)
        return str;
    return str.slice(0, length) + '...';
};
exports.truncateString = truncateString;
const calculatePagination = (page = 1, limit = config_1.APP_CONSTANTS.DEFAULT_PAGE_SIZE, total) => {
    const normalizedPage = Math.max(1, page);
    const normalizedLimit = Math.min(Math.max(1, limit), config_1.APP_CONSTANTS.MAX_PAGE_SIZE);
    const offset = (normalizedPage - 1) * normalizedLimit;
    const totalPages = Math.ceil(total / normalizedLimit);
    return {
        offset,
        limit: normalizedLimit,
        page: normalizedPage,
        totalPages,
        total
    };
};
exports.calculatePagination = calculatePagination;
const createError = (message, statusCode = 400) => {
    const error = new Error(message);
    error.statusCode = statusCode;
    return error;
};
exports.createError = createError;
const getEnvVar = (key, defaultValue) => {
    const value = process.env[key];
    if (!value && !defaultValue) {
        throw new Error(`Environment variable ${key} is required`);
    }
    return value || defaultValue;
};
exports.getEnvVar = getEnvVar;
const getEnvVarAsNumber = (key, defaultValue) => {
    const value = process.env[key];
    if (!value && defaultValue === undefined) {
        throw new Error(`Environment variable ${key} is required`);
    }
    return value ? parseInt(value, 10) : defaultValue;
};
exports.getEnvVarAsNumber = getEnvVarAsNumber;
const getEnvVarAsBoolean = (key, defaultValue) => {
    const value = process.env[key];
    if (!value && defaultValue === undefined) {
        throw new Error(`Environment variable ${key} is required`);
    }
    return value ? value.toLowerCase() === 'true' : defaultValue;
};
exports.getEnvVarAsBoolean = getEnvVarAsBoolean;
//# sourceMappingURL=index.js.map