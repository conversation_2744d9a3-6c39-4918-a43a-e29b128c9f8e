export interface PDFMetadata {
    pages: number;
    title?: string;
    author?: string;
    subject?: string;
    creator?: string;
    producer?: string;
    creationDate?: Date;
    modificationDate?: Date;
    keywords?: string;
    version?: string;
    encrypted?: boolean;
    fileSize: number;
    textContent?: string;
    hasImages?: boolean;
    hasText?: boolean;
    printable?: boolean;
    copyable?: boolean;
}
export interface PDFProcessingResult {
    success: boolean;
    metadata?: PDFMetadata;
    error?: string;
    warnings?: string[];
}
export declare const extractPDFMetadata: (filePath: string) => Promise<PDFProcessingResult>;
export declare const getPageCount: (filePath: string) => Promise<number>;
export declare const validatePDF: (filePath: string) => Promise<{
    isValid: boolean;
    error?: string;
    warnings?: string[];
}>;
export declare const calculatePrintEstimate: (metadata: PDFMetadata, pricePerPage?: number) => {
    pages: number;
    estimatedCost: number;
    hasColor: boolean;
    complexity: "low" | "medium" | "high";
};
declare const _default: {
    extractPDFMetadata: (filePath: string) => Promise<PDFProcessingResult>;
    getPageCount: (filePath: string) => Promise<number>;
    validatePDF: (filePath: string) => Promise<{
        isValid: boolean;
        error?: string;
        warnings?: string[];
    }>;
    calculatePrintEstimate: (metadata: PDFMetadata, pricePerPage?: number) => {
        pages: number;
        estimatedCost: number;
        hasColor: boolean;
        complexity: "low" | "medium" | "high";
    };
};
export default _default;
//# sourceMappingURL=pdfProcessor.d.ts.map