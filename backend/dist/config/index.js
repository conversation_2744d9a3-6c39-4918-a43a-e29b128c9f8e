"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.logConfig = exports.isTest = exports.isProduction = exports.isDevelopment = exports.APP_CONSTANTS = exports.uploadConfig = exports.jwtConfig = exports.databaseConfig = exports.serverConfig = void 0;
const dotenv_1 = __importDefault(require("dotenv"));
dotenv_1.default.config();
const requiredEnvVars = [
    'NODE_ENV',
    'PORT',
    'DATABASE_URL',
    'JWT_SECRET'
];
const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);
if (missingEnvVars.length > 0) {
    throw new Error(`Missing required environment variables: ${missingEnvVars.join(', ')}`);
}
exports.serverConfig = {
    port: parseInt(process.env.PORT || '8000', 10),
    host: process.env.HOST || 'localhost',
    nodeEnv: process.env.NODE_ENV || 'development',
    corsOrigin: process.env.CORS_ORIGIN || 'http://localhost:3000'
};
exports.databaseConfig = {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '5432', 10),
    database: process.env.DB_NAME || 'weprint',
    username: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || 'postgres',
    ssl: process.env.DB_SSL === 'true'
};
exports.jwtConfig = {
    secret: process.env.JWT_SECRET,
    expiresIn: process.env.JWT_EXPIRES_IN || '7d'
};
exports.uploadConfig = {
    uploadDir: process.env.UPLOAD_DIR || 'uploads',
    maxFileSize: parseInt(process.env.MAX_FILE_SIZE || '10485760', 10),
    allowedFileTypes: (process.env.ALLOWED_FILE_TYPES || 'pdf,doc,docx,txt,jpg,jpeg,png').split(',')
};
exports.APP_CONSTANTS = {
    API_PREFIX: '/api',
    VERSION: '1.0.0',
    NAME: 'WePrint AI Backend',
    DESCRIPTION: 'Professional printing service platform API',
    RATE_LIMIT: {
        windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000', 10),
        maxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100', 10)
    },
    BCRYPT_ROUNDS: parseInt(process.env.BCRYPT_ROUNDS || '12', 10),
    DEFAULT_PAGE_SIZE: 20,
    MAX_PAGE_SIZE: 100,
    SUPPORTED_MIME_TYPES: {
        'application/pdf': 'pdf',
        'application/msword': 'doc',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'docx',
        'text/plain': 'txt',
        'image/jpeg': 'jpg',
        'image/jpg': 'jpg',
        'image/png': 'png'
    },
    PRICING: {
        BASE_PRICE_PER_PAGE: 50,
        FORMAT_MULTIPLIERS: {
            A4: 1.0,
            A3: 2.0,
            A5: 0.7,
            letter: 1.0,
            legal: 1.2,
            custom: 1.5
        },
        PAPER_MULTIPLIERS: {
            standard: 1.0,
            premium: 1.5,
            photo: 2.0,
            cardstock: 1.8
        },
        FINISH_MULTIPLIERS: {
            none: 1.0,
            laminated: 1.3,
            spiral_bound: 1.4,
            stapled: 1.1
        }
    }
};
const isDevelopment = () => exports.serverConfig.nodeEnv === 'development';
exports.isDevelopment = isDevelopment;
const isProduction = () => exports.serverConfig.nodeEnv === 'production';
exports.isProduction = isProduction;
const isTest = () => exports.serverConfig.nodeEnv === 'test';
exports.isTest = isTest;
exports.logConfig = {
    level: process.env.LOG_LEVEL || ((0, exports.isDevelopment)() ? 'debug' : 'info'),
    format: (0, exports.isDevelopment)() ? 'dev' : 'combined',
    silent: (0, exports.isTest)()
};
exports.default = {
    server: exports.serverConfig,
    database: exports.databaseConfig,
    jwt: exports.jwtConfig,
    upload: exports.uploadConfig,
    app: exports.APP_CONSTANTS,
    log: exports.logConfig
};
//# sourceMappingURL=index.js.map