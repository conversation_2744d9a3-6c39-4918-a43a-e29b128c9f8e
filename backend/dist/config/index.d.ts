import { DatabaseConfig, ServerConfig, JwtConfig, UploadConfig } from '../types';
export declare const serverConfig: ServerConfig;
export declare const databaseConfig: DatabaseConfig;
export declare const jwtConfig: JwtConfig;
export declare const uploadConfig: UploadConfig;
export declare const APP_CONSTANTS: {
    API_PREFIX: string;
    VERSION: string;
    NAME: string;
    DESCRIPTION: string;
    RATE_LIMIT: {
        windowMs: number;
        maxRequests: number;
    };
    BCRYPT_ROUNDS: number;
    DEFAULT_PAGE_SIZE: number;
    MAX_PAGE_SIZE: number;
    SUPPORTED_MIME_TYPES: {
        'application/pdf': string;
        'application/msword': string;
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document': string;
        'text/plain': string;
        'image/jpeg': string;
        'image/jpg': string;
        'image/png': string;
    };
    PRICING: {
        BASE_PRICE_PER_PAGE: number;
        FORMAT_MULTIPLIERS: {
            A4: number;
            A3: number;
            A5: number;
            letter: number;
            legal: number;
            custom: number;
        };
        PAPER_MULTIPLIERS: {
            standard: number;
            premium: number;
            photo: number;
            cardstock: number;
        };
        FINISH_MULTIPLIERS: {
            none: number;
            laminated: number;
            spiral_bound: number;
            stapled: number;
        };
    };
};
export declare const isDevelopment: () => boolean;
export declare const isProduction: () => boolean;
export declare const isTest: () => boolean;
export declare const logConfig: {
    level: string;
    format: string;
    silent: boolean;
};
declare const _default: {
    server: ServerConfig;
    database: DatabaseConfig;
    jwt: JwtConfig;
    upload: UploadConfig;
    app: {
        API_PREFIX: string;
        VERSION: string;
        NAME: string;
        DESCRIPTION: string;
        RATE_LIMIT: {
            windowMs: number;
            maxRequests: number;
        };
        BCRYPT_ROUNDS: number;
        DEFAULT_PAGE_SIZE: number;
        MAX_PAGE_SIZE: number;
        SUPPORTED_MIME_TYPES: {
            'application/pdf': string;
            'application/msword': string;
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': string;
            'text/plain': string;
            'image/jpeg': string;
            'image/jpg': string;
            'image/png': string;
        };
        PRICING: {
            BASE_PRICE_PER_PAGE: number;
            FORMAT_MULTIPLIERS: {
                A4: number;
                A3: number;
                A5: number;
                letter: number;
                legal: number;
                custom: number;
            };
            PAPER_MULTIPLIERS: {
                standard: number;
                premium: number;
                photo: number;
                cardstock: number;
            };
            FINISH_MULTIPLIERS: {
                none: number;
                laminated: number;
                spiral_bound: number;
                stapled: number;
            };
        };
    };
    log: {
        level: string;
        format: string;
        silent: boolean;
    };
};
export default _default;
//# sourceMappingURL=index.d.ts.map