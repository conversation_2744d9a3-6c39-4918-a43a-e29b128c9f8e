"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.runAdminSystemMigration = runAdminSystemMigration;
exports.checkAdminSystemMigration = checkAdminSystemMigration;
exports.runPendingMigrations = runPendingMigrations;
const index_1 = __importDefault(require("./index"));
const bcryptjs_1 = __importDefault(require("bcryptjs"));
async function runAdminSystemMigration() {
    const client = await index_1.default.connect();
    try {
        console.log('🔄 Executando migration do sistema administrativo...');
        await client.query('BEGIN');
        await client.query(`
      DO $$ BEGIN
        CREATE TYPE admin_role AS ENUM ('admin', 'super_admin');
      EXCEPTION
        WHEN duplicate_object THEN null;
      END $$;
    `);
        await client.query(`
      DO $$ BEGIN
        CREATE TYPE admin_status AS ENUM ('active', 'inactive', 'suspended');
      EXCEPTION
        WHEN duplicate_object THEN null;
      END $$;
    `);
        await client.query(`
      CREATE TABLE IF NOT EXISTS admins (
        id SERIAL PRIMARY KEY,
        email VARCHAR(255) UNIQUE NOT NULL,
        name VARCHAR(255) NOT NULL,
        password VARCHAR(255) NOT NULL,
        role admin_role NOT NULL DEFAULT 'admin',
        status admin_status NOT NULL DEFAULT 'active',
        last_login TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        deleted_at TIMESTAMP NULL
      )
    `);
        await client.query(`
      CREATE TABLE IF NOT EXISTS admin_activity_logs (
        id SERIAL PRIMARY KEY,
        admin_id INTEGER NOT NULL,
        admin_email VARCHAR(255) NOT NULL,
        action VARCHAR(100) NOT NULL,
        resource VARCHAR(100) NOT NULL,
        resource_id INTEGER NULL,
        details JSONB NULL,
        ip_address INET NULL,
        user_agent TEXT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);
        const indexes = [
            'CREATE INDEX IF NOT EXISTS idx_admins_email ON admins(email) WHERE deleted_at IS NULL',
            'CREATE INDEX IF NOT EXISTS idx_admins_role ON admins(role) WHERE deleted_at IS NULL',
            'CREATE INDEX IF NOT EXISTS idx_admins_status ON admins(status) WHERE deleted_at IS NULL',
            'CREATE INDEX IF NOT EXISTS idx_admin_activity_logs_admin_id ON admin_activity_logs(admin_id)',
            'CREATE INDEX IF NOT EXISTS idx_admin_activity_logs_action ON admin_activity_logs(action)',
            'CREATE INDEX IF NOT EXISTS idx_admin_activity_logs_resource ON admin_activity_logs(resource)',
            'CREATE INDEX IF NOT EXISTS idx_admin_activity_logs_created_at ON admin_activity_logs(created_at)'
        ];
        for (const indexQuery of indexes) {
            await client.query(indexQuery);
        }
        await client.query(`
      CREATE OR REPLACE FUNCTION update_updated_at_column()
      RETURNS TRIGGER AS $$
      BEGIN
        NEW.updated_at = CURRENT_TIMESTAMP;
        RETURN NEW;
      END;
      $$ language 'plpgsql';
    `);
        await client.query(`
      DROP TRIGGER IF EXISTS update_admins_updated_at ON admins;
      CREATE TRIGGER update_admins_updated_at 
        BEFORE UPDATE ON admins 
        FOR EACH ROW 
        EXECUTE FUNCTION update_updated_at_column();
    `);
        await client.query(`
      DO $$ BEGIN
        ALTER TABLE admins ADD CONSTRAINT check_email_format 
          CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$');
      EXCEPTION
        WHEN duplicate_object THEN null;
      END $$;
    `);
        await client.query(`
      DO $$ BEGIN
        ALTER TABLE admins ADD CONSTRAINT check_name_not_empty 
          CHECK (LENGTH(TRIM(name)) > 0);
      EXCEPTION
        WHEN duplicate_object THEN null;
      END $$;
    `);
        const existingAdmin = await client.query('SELECT id FROM admins WHERE email = $1', ['<EMAIL>']);
        if (existingAdmin.rows.length === 0) {
            const hashedPassword = await bcryptjs_1.default.hash('admin123', 12);
            await client.query(`
        INSERT INTO admins (email, name, password, role, status) 
        VALUES ($1, $2, $3, $4, $5)
      `, [
                '<EMAIL>',
                'Super Administrator',
                hashedPassword,
                'super_admin',
                'active'
            ]);
            console.log('👤 Administrador padrão criado: <EMAIL>');
            console.log('🔑 Senha padrão: admin123');
            console.log('⚠️  IMPORTANTE: Altere a senha após o primeiro login!');
        }
        await client.query(`
      INSERT INTO admin_activity_logs (admin_id, admin_email, action, resource, details) 
      VALUES ($1, $2, $3, $4, $5)
    `, [
            0,
            'system',
            'migration_executed',
            'database',
            JSON.stringify({
                migration: 'admin_system',
                timestamp: new Date().toISOString()
            })
        ]);
        await client.query('COMMIT');
        console.log('✅ Migration do sistema administrativo concluída com sucesso!');
    }
    catch (error) {
        await client.query('ROLLBACK');
        console.error('❌ Erro na migration do sistema administrativo:', error);
        throw error;
    }
    finally {
        client.release();
    }
}
async function checkAdminSystemMigration() {
    try {
        const result = await index_1.default.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'admins'
      ) as table_exists
    `);
        return !result.rows[0].table_exists;
    }
    catch (error) {
        console.error('Erro ao verificar migration:', error);
        return true;
    }
}
async function runPendingMigrations() {
    try {
        const needsAdminMigration = await checkAdminSystemMigration();
        if (needsAdminMigration) {
            await runAdminSystemMigration();
        }
        else {
            console.log('✅ Sistema administrativo já está configurado');
        }
    }
    catch (error) {
        console.error('❌ Erro ao executar migrations:', error);
        throw error;
    }
}
//# sourceMappingURL=migrations.js.map