import { Pool, PoolClient, QueryResult } from 'pg';
export declare const initializeDatabase: () => Promise<void>;
export declare const getPool: () => Pool;
export declare const query: (text: string, params?: any[]) => Promise<QueryResult>;
export declare const queryOne: (text: string, params?: any[]) => Promise<any>;
export declare const transaction: (callback: (client: PoolClient) => Promise<any>) => Promise<any>;
export declare const closeDatabase: () => Promise<void>;
export declare const checkDatabaseHealth: () => Promise<{
    status: string;
    timestamp: Date;
    version?: string;
}>;
declare const _default: {
    initialize: () => Promise<void>;
    getPool: () => Pool;
    query: (text: string, params?: any[]) => Promise<QueryResult>;
    queryOne: (text: string, params?: any[]) => Promise<any>;
    transaction: (callback: (client: PoolClient) => Promise<any>) => Promise<any>;
    close: () => Promise<void>;
    health: () => Promise<{
        status: string;
        timestamp: Date;
        version?: string;
    }>;
};
export default _default;
//# sourceMappingURL=index.d.ts.map