"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.checkDatabaseHealth = exports.closeDatabase = exports.transaction = exports.queryOne = exports.query = exports.getPool = exports.initializeDatabase = void 0;
const pg_1 = require("pg");
const config_1 = __importDefault(require("../config"));
let pool = null;
const initializeDatabase = async () => {
    try {
        pool = new pg_1.Pool({
            host: config_1.default.database.host,
            port: config_1.default.database.port,
            database: config_1.default.database.database,
            user: config_1.default.database.username,
            password: config_1.default.database.password,
            max: 20,
            idleTimeoutMillis: 30000,
            connectionTimeoutMillis: 2000,
        });
        const client = await pool.connect();
        await client.query('SELECT NOW()');
        client.release();
        console.log('✅ Database connected successfully');
    }
    catch (error) {
        console.error('❌ Database connection failed:', error);
        throw error;
    }
};
exports.initializeDatabase = initializeDatabase;
const getPool = () => {
    if (!pool) {
        throw new Error('Database not initialized. Call initializeDatabase() first.');
    }
    return pool;
};
exports.getPool = getPool;
const query = async (text, params) => {
    const client = await (0, exports.getPool)().connect();
    try {
        const result = await client.query(text, params);
        return result;
    }
    finally {
        client.release();
    }
};
exports.query = query;
const queryOne = async (text, params) => {
    const result = await (0, exports.query)(text, params);
    return result.rows[0] || null;
};
exports.queryOne = queryOne;
const transaction = async (callback) => {
    const client = await (0, exports.getPool)().connect();
    try {
        await client.query('BEGIN');
        const result = await callback(client);
        await client.query('COMMIT');
        return result;
    }
    catch (error) {
        await client.query('ROLLBACK');
        throw error;
    }
    finally {
        client.release();
    }
};
exports.transaction = transaction;
const closeDatabase = async () => {
    if (pool) {
        await pool.end();
        pool = null;
        console.log('✅ Database connection closed');
    }
};
exports.closeDatabase = closeDatabase;
const checkDatabaseHealth = async () => {
    try {
        const result = await (0, exports.query)('SELECT version(), NOW() as timestamp');
        return {
            status: 'healthy',
            timestamp: result.rows[0].timestamp,
            version: result.rows[0].version
        };
    }
    catch (error) {
        return {
            status: 'unhealthy',
            timestamp: new Date()
        };
    }
};
exports.checkDatabaseHealth = checkDatabaseHealth;
exports.default = {
    initialize: exports.initializeDatabase,
    getPool: exports.getPool,
    query: exports.query,
    queryOne: exports.queryOne,
    transaction: exports.transaction,
    close: exports.closeDatabase,
    health: exports.checkDatabaseHealth
};
//# sourceMappingURL=index.js.map