"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RefundStatus = exports.InvoiceStatus = exports.PaymentStatus = exports.NotificationStatus = exports.NotificationChannel = exports.NotificationType = exports.AdminStatus = exports.AdminRole = exports.FinishType = exports.PaperType = exports.PrintFormat = exports.OrderStatus = void 0;
var OrderStatus;
(function (OrderStatus) {
    OrderStatus["PENDING"] = "pending";
    OrderStatus["CONFIRMED"] = "confirmed";
    OrderStatus["PROCESSING"] = "processing";
    OrderStatus["READY"] = "ready";
    OrderStatus["COMPLETED"] = "completed";
    OrderStatus["CANCELLED"] = "cancelled";
})(OrderStatus || (exports.OrderStatus = OrderStatus = {}));
var PrintFormat;
(function (PrintFormat) {
    PrintFormat["A4"] = "A4";
    PrintFormat["A3"] = "A3";
    PrintFormat["A5"] = "A5";
    PrintFormat["LETTER"] = "letter";
    PrintFormat["LEGAL"] = "legal";
    PrintFormat["CUSTOM"] = "custom";
})(PrintFormat || (exports.PrintFormat = PrintFormat = {}));
var PaperType;
(function (PaperType) {
    PaperType["STANDARD"] = "standard";
    PaperType["PREMIUM"] = "premium";
    PaperType["PHOTO"] = "photo";
    PaperType["CARDSTOCK"] = "cardstock";
})(PaperType || (exports.PaperType = PaperType = {}));
var FinishType;
(function (FinishType) {
    FinishType["NONE"] = "none";
    FinishType["LAMINATED"] = "laminated";
    FinishType["SPIRAL_BOUND"] = "spiral_bound";
    FinishType["STAPLED"] = "stapled";
})(FinishType || (exports.FinishType = FinishType = {}));
var AdminRole;
(function (AdminRole) {
    AdminRole["ADMIN"] = "admin";
    AdminRole["SUPER_ADMIN"] = "super_admin";
})(AdminRole || (exports.AdminRole = AdminRole = {}));
var AdminStatus;
(function (AdminStatus) {
    AdminStatus["ACTIVE"] = "active";
    AdminStatus["INACTIVE"] = "inactive";
    AdminStatus["SUSPENDED"] = "suspended";
})(AdminStatus || (exports.AdminStatus = AdminStatus = {}));
var NotificationType;
(function (NotificationType) {
    NotificationType["ORDER_CREATED"] = "order_created";
    NotificationType["ORDER_CONFIRMED"] = "order_confirmed";
    NotificationType["ORDER_PROCESSING"] = "order_processing";
    NotificationType["ORDER_READY"] = "order_ready";
    NotificationType["ORDER_COMPLETED"] = "order_completed";
    NotificationType["ORDER_CANCELLED"] = "order_cancelled";
    NotificationType["FILE_UPLOADED"] = "file_uploaded";
    NotificationType["PAYMENT_RECEIVED"] = "payment_received";
    NotificationType["SYSTEM_ALERT"] = "system_alert";
})(NotificationType || (exports.NotificationType = NotificationType = {}));
var NotificationChannel;
(function (NotificationChannel) {
    NotificationChannel["EMAIL"] = "email";
    NotificationChannel["SMS"] = "sms";
    NotificationChannel["PUSH"] = "push";
    NotificationChannel["IN_APP"] = "in_app";
})(NotificationChannel || (exports.NotificationChannel = NotificationChannel = {}));
var NotificationStatus;
(function (NotificationStatus) {
    NotificationStatus["PENDING"] = "pending";
    NotificationStatus["SENT"] = "sent";
    NotificationStatus["DELIVERED"] = "delivered";
    NotificationStatus["FAILED"] = "failed";
    NotificationStatus["READ"] = "read";
})(NotificationStatus || (exports.NotificationStatus = NotificationStatus = {}));
var PaymentStatus;
(function (PaymentStatus) {
    PaymentStatus["PENDING"] = "pending";
    PaymentStatus["PROCESSING"] = "processing";
    PaymentStatus["SUCCEEDED"] = "succeeded";
    PaymentStatus["FAILED"] = "failed";
    PaymentStatus["CANCELED"] = "canceled";
    PaymentStatus["REFUNDED"] = "refunded";
})(PaymentStatus || (exports.PaymentStatus = PaymentStatus = {}));
var InvoiceStatus;
(function (InvoiceStatus) {
    InvoiceStatus["DRAFT"] = "draft";
    InvoiceStatus["SENT"] = "sent";
    InvoiceStatus["PAID"] = "paid";
    InvoiceStatus["OVERDUE"] = "overdue";
    InvoiceStatus["CANCELLED"] = "cancelled";
})(InvoiceStatus || (exports.InvoiceStatus = InvoiceStatus = {}));
var RefundStatus;
(function (RefundStatus) {
    RefundStatus["PENDING"] = "pending";
    RefundStatus["SUCCEEDED"] = "succeeded";
    RefundStatus["FAILED"] = "failed";
    RefundStatus["CANCELED"] = "canceled";
})(RefundStatus || (exports.RefundStatus = RefundStatus = {}));
//# sourceMappingURL=index.js.map