"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FinishType = exports.PaperType = exports.PrintFormat = exports.OrderStatus = void 0;
var OrderStatus;
(function (OrderStatus) {
    OrderStatus["PENDING"] = "pending";
    OrderStatus["CONFIRMED"] = "confirmed";
    OrderStatus["PROCESSING"] = "processing";
    OrderStatus["READY"] = "ready";
    OrderStatus["COMPLETED"] = "completed";
    OrderStatus["CANCELLED"] = "cancelled";
})(OrderStatus || (exports.OrderStatus = OrderStatus = {}));
var PrintFormat;
(function (PrintFormat) {
    PrintFormat["A4"] = "A4";
    PrintFormat["A3"] = "A3";
    PrintFormat["A5"] = "A5";
    PrintFormat["LETTER"] = "letter";
    PrintFormat["LEGAL"] = "legal";
    PrintFormat["CUSTOM"] = "custom";
})(PrintFormat || (exports.PrintFormat = PrintFormat = {}));
var PaperType;
(function (PaperType) {
    PaperType["STANDARD"] = "standard";
    PaperType["PREMIUM"] = "premium";
    PaperType["PHOTO"] = "photo";
    PaperType["CARDSTOCK"] = "cardstock";
})(PaperType || (exports.PaperType = PaperType = {}));
var FinishType;
(function (FinishType) {
    FinishType["NONE"] = "none";
    FinishType["LAMINATED"] = "laminated";
    FinishType["SPIRAL_BOUND"] = "spiral_bound";
    FinishType["STAPLED"] = "stapled";
})(FinishType || (exports.FinishType = FinishType = {}));
//# sourceMappingURL=index.js.map