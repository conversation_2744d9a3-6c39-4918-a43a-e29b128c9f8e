import { Request } from 'express';
export interface File {
    id: number;
    filename: string;
    originalName: string;
    mimetype: string;
    size: number;
    url: string;
    uploadedAt: Date;
    deletedAt?: Date;
}
export interface Order {
    id: number;
    orderNumber: string;
    fileId: number;
    customerName?: string;
    customerEmail?: string;
    customerPhone?: string;
    format: PrintFormat;
    paperType: PaperType;
    finish: FinishType;
    copies: number;
    pages?: number;
    price: number;
    status: OrderStatus;
    notes?: string;
    hasColor?: boolean;
    complexity?: 'low' | 'medium' | 'high';
    estimatedCompletion?: Date;
    createdAt: Date;
    updatedAt: Date;
    deletedAt?: Date;
    file?: File;
    statusHistory?: StatusHistory[];
}
export interface StatusHistory {
    id: number;
    orderId: number;
    status: OrderStatus;
    notes?: string;
    updatedAt: Date;
    updatedBy?: string;
}
export declare enum OrderStatus {
    PENDING = "pending",
    CONFIRMED = "confirmed",
    PROCESSING = "processing",
    READY = "ready",
    COMPLETED = "completed",
    CANCELLED = "cancelled"
}
export declare enum PrintFormat {
    A4 = "A4",
    A3 = "A3",
    A5 = "A5",
    LETTER = "letter",
    LEGAL = "legal",
    CUSTOM = "custom"
}
export declare enum PaperType {
    STANDARD = "standard",
    PREMIUM = "premium",
    PHOTO = "photo",
    CARDSTOCK = "cardstock"
}
export declare enum FinishType {
    NONE = "none",
    LAMINATED = "laminated",
    SPIRAL_BOUND = "spiral_bound",
    STAPLED = "stapled"
}
export interface ApiResponse<T = any> {
    success: boolean;
    data?: T;
    message?: string;
    error?: string;
    errors?: ValidationError[];
}
export interface PaginatedResponse<T> extends ApiResponse<T[]> {
    pagination: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
    };
}
export interface ValidationError {
    field: string;
    message: string;
}
export interface CreateOrderRequest {
    fileId: number;
    customerName?: string;
    customerEmail?: string;
    customerPhone?: string;
    format: PrintFormat;
    paperType: PaperType;
    finish: FinishType;
    copies: number;
    notes?: string;
}
export interface UpdateOrderRequest {
    status?: OrderStatus;
    notes?: string;
}
export interface FileUploadRequest extends Request {
    file?: Express.Multer.File;
}
export interface CreateFileData {
    filename: string;
    originalName: string;
    mimetype: string;
    size: number;
    url: string;
}
export interface UpdateFileData {
    filename?: string;
    originalName?: string;
    mimetype?: string;
    size?: number;
    url?: string;
}
export interface CreateOrderData {
    fileId: number;
    customerName?: string;
    customerEmail?: string;
    customerPhone?: string;
    format: string;
    paperType: string;
    finish: string;
    copies?: number;
    pages?: number;
    price?: number;
    status?: string;
    notes?: string;
    hasColor?: boolean;
    complexity?: 'low' | 'medium' | 'high';
}
export interface UpdateOrderData {
    fileId?: number;
    customerName?: string;
    customerEmail?: string;
    customerPhone?: string;
    format?: string;
    paperType?: string;
    finish?: string;
    copies?: number;
    pages?: number;
    price?: number;
    status?: string;
    notes?: string | undefined;
    hasColor?: boolean;
    complexity?: 'low' | 'medium' | 'high';
    estimatedCompletion?: Date;
}
export interface CreateStatusHistoryData {
    orderId: number;
    status: string;
    notes?: string;
    updatedBy?: string;
}
export interface DatabaseConfig {
    host: string;
    port: number;
    database: string;
    username: string;
    password: string;
    ssl: boolean;
}
export interface ServerConfig {
    port: number;
    host: string;
    nodeEnv: string;
    corsOrigin: string;
}
export interface JwtConfig {
    secret: string;
    expiresIn: string;
}
export interface UploadConfig {
    uploadDir: string;
    maxFileSize: number;
    allowedFileTypes: string[];
}
export type Omit<T, K extends keyof T> = Pick<T, Exclude<keyof T, K>>;
export type Partial<T> = {
    [P in keyof T]?: T[P];
};
export interface QueryParams {
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
    search?: string;
    status?: OrderStatus;
    format?: PrintFormat;
    dateFrom?: string;
    dateTo?: string;
}
//# sourceMappingURL=index.d.ts.map