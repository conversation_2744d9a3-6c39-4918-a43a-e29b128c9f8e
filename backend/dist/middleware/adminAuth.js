"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.adminAuth = exports.adminRateLimit = exports.preventSelfModification = exports.canModifyResource = exports.validateAdminAccess = exports.logAdminActivity = exports.requireSuperAdmin = exports.requireAdminRole = exports.authenticateAdmin = void 0;
const Admin_1 = require("../models/Admin");
const AdminActivityLog_1 = require("../models/AdminActivityLog");
const types_1 = require("../types");
const authenticateAdmin = async (req, res, next) => {
    try {
        const authHeader = req.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            res.error('Token de acesso requerido', 401);
            return;
        }
        const token = authHeader.substring(7);
        const decoded = Admin_1.AdminModel.verifyToken(token);
        if (!decoded) {
            res.error('Token inválido', 401);
            return;
        }
        const admin = await Admin_1.AdminModel.findById(decoded.adminId);
        if (!admin) {
            res.error('Administrador não encontrado', 401);
            return;
        }
        if (admin.status !== types_1.AdminStatus.ACTIVE) {
            res.error('Conta de administrador inativa', 403);
            return;
        }
        req.admin = {
            id: admin.id,
            email: admin.email,
            name: admin.name,
            role: admin.role,
            status: admin.status
        };
        next();
    }
    catch (error) {
        console.error('Admin authentication error:', error);
        res.error('Erro de autenticação', 500);
    }
};
exports.authenticateAdmin = authenticateAdmin;
const requireAdminRole = (requiredRole) => {
    return (req, res, next) => {
        if (!req.admin) {
            res.error('Autenticação requerida', 401);
            return;
        }
        if (req.admin.role === types_1.AdminRole.SUPER_ADMIN) {
            next();
            return;
        }
        if (req.admin.role !== requiredRole) {
            res.error('Permissões insuficientes', 403);
            return;
        }
        next();
    };
};
exports.requireAdminRole = requireAdminRole;
exports.requireSuperAdmin = (0, exports.requireAdminRole)(types_1.AdminRole.SUPER_ADMIN);
const logAdminActivity = (action, resource) => {
    return async (req, res, next) => {
        const originalJson = res.json;
        res.json = function (body) {
            if (req.admin && res.statusCode < 400) {
                const resourceId = req.params.id ? parseInt(req.params.id) : undefined;
                AdminActivityLog_1.AdminActivityLogModel.logAction(req.admin.id, req.admin.email, action, resource, resourceId, {
                    method: req.method,
                    url: req.originalUrl,
                    body: req.method !== 'GET' ? req.body : undefined,
                    query: Object.keys(req.query).length > 0 ? req.query : undefined
                }, req).catch(error => {
                    console.error('Failed to log admin activity:', error);
                });
            }
            return originalJson.call(this, body);
        };
        next();
    };
};
exports.logAdminActivity = logAdminActivity;
const validateAdminAccess = async (req, res, next) => {
    try {
        if (!req.admin) {
            res.error('Autenticação requerida', 401);
            return;
        }
        const admin = await Admin_1.AdminModel.findById(req.admin.id);
        if (!admin || admin.status !== types_1.AdminStatus.ACTIVE) {
            res.error('Acesso negado - conta inativa', 403);
            return;
        }
        next();
    }
    catch (error) {
        console.error('Admin validation error:', error);
        res.error('Erro de validação', 500);
    }
};
exports.validateAdminAccess = validateAdminAccess;
const canModifyResource = (resourceType) => {
    return (req, res, next) => {
        if (!req.admin) {
            res.error('Autenticação requerida', 401);
            return;
        }
        if (req.admin.role === types_1.AdminRole.SUPER_ADMIN) {
            next();
            return;
        }
        const restrictedResources = ['admins', 'system-config'];
        if (restrictedResources.includes(resourceType)) {
            res.error('Permissões insuficientes para modificar este recurso', 403);
            return;
        }
        next();
    };
};
exports.canModifyResource = canModifyResource;
const preventSelfModification = (req, res, next) => {
    if (!req.admin) {
        res.error('Autenticação requerida', 401);
        return;
    }
    const targetAdminId = parseInt(req.params.id || '0');
    if (req.admin.id === targetAdminId) {
        const { role, status } = req.body;
        if (role !== undefined || status !== undefined) {
            res.error('Não é possível modificar o próprio papel ou status', 403);
            return;
        }
    }
    next();
};
exports.preventSelfModification = preventSelfModification;
const adminRateLimit = (maxRequests = 100, windowMs = 15 * 60 * 1000) => {
    const requests = new Map();
    return (req, res, next) => {
        if (!req.admin) {
            next();
            return;
        }
        const key = `admin_${req.admin.id}`;
        const now = Date.now();
        const windowStart = now - windowMs;
        for (const [k, v] of requests.entries()) {
            if (v.resetTime < windowStart) {
                requests.delete(k);
            }
        }
        const current = requests.get(key);
        if (!current) {
            requests.set(key, { count: 1, resetTime: now + windowMs });
            next();
            return;
        }
        if (current.resetTime < now) {
            requests.set(key, { count: 1, resetTime: now + windowMs });
            next();
            return;
        }
        if (current.count >= maxRequests) {
            res.error('Muitas requisições - tente novamente mais tarde', 429);
            return;
        }
        current.count++;
        next();
    };
};
exports.adminRateLimit = adminRateLimit;
exports.adminAuth = exports.authenticateAdmin;
//# sourceMappingURL=adminAuth.js.map