"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.uploadConfig = exports.handleUploadError = exports.uploadFields = exports.uploadMultiple = exports.uploadSingle = exports.securityHeaders = exports.corsOptions = exports.responseHelpers = exports.asyncHandler = exports.validateRequest = exports.requestLogger = exports.notFoundHandler = exports.errorHandler = exports.AppError = void 0;
const config_1 = require("../config");
class AppError extends Error {
    constructor(message, statusCode = 500) {
        super(message);
        this.statusCode = statusCode;
        this.isOperational = true;
        Error.captureStackTrace(this, this.constructor);
    }
}
exports.AppError = AppError;
const errorHandler = (error, req, res, _next) => {
    let statusCode = 500;
    let message = 'Internal Server Error';
    if (error instanceof AppError) {
        statusCode = error.statusCode;
        message = error.message;
    }
    if (!config_1.logConfig.silent) {
        console.error('Error:', {
            message: error.message,
            stack: error.stack,
            url: req.url,
            method: req.method,
            ip: req.ip,
            userAgent: req.get('User-Agent')
        });
    }
    const response = {
        success: false,
        error: message
    };
    res.status(statusCode).json(response);
};
exports.errorHandler = errorHandler;
const notFoundHandler = (req, res, _next) => {
    const response = {
        success: false,
        error: `Route ${req.method} ${req.originalUrl} not found`
    };
    res.status(404).json(response);
};
exports.notFoundHandler = notFoundHandler;
const requestLogger = (req, res, next) => {
    const start = Date.now();
    res.on('finish', () => {
        const duration = Date.now() - start;
        if (!config_1.logConfig.silent) {
            console.log(`${req.method} ${req.originalUrl} - ${res.statusCode} - ${duration}ms`);
        }
    });
    next();
};
exports.requestLogger = requestLogger;
const validateRequest = (schema, property = 'body') => {
    return (req, res, next) => {
        const { error } = schema.validate(req[property], { abortEarly: false });
        if (error) {
            const errors = error.details.map((detail) => ({
                field: detail.path.join('.'),
                message: detail.message
            }));
            const response = {
                success: false,
                error: 'Validation failed',
                errors
            };
            res.status(400).json(response);
            return;
        }
        next();
    };
};
exports.validateRequest = validateRequest;
const asyncHandler = (fn) => {
    return (req, res, next) => {
        Promise.resolve(fn(req, res, next)).catch(next);
    };
};
exports.asyncHandler = asyncHandler;
const responseHelpers = (req, res, next) => {
    res.success = (data, message) => {
        const response = {
            success: true,
            data,
            ...(message && { message })
        };
        res.json(response);
    };
    res.error = (message, statusCode = 400, data) => {
        const response = {
            success: false,
            error: message,
            ...(data && { data })
        };
        res.status(statusCode).json(response);
    };
    res.paginated = (data, pagination) => {
        const response = {
            success: true,
            data,
            pagination
        };
        res.json(response);
    };
    next();
};
exports.responseHelpers = responseHelpers;
exports.corsOptions = {
    origin: (origin, callback) => {
        if (!origin)
            return callback(null, true);
        const allowedOrigins = process.env.CORS_ORIGIN?.split(',') || ['http://localhost:3000'];
        if (allowedOrigins.includes(origin)) {
            callback(null, true);
        }
        else {
            callback(new Error('Not allowed by CORS'));
        }
    },
    credentials: true,
    optionsSuccessStatus: 200
};
const securityHeaders = (req, res, next) => {
    res.setHeader('X-Content-Type-Options', 'nosniff');
    res.setHeader('X-Frame-Options', 'DENY');
    res.setHeader('X-XSS-Protection', '1; mode=block');
    res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
    next();
};
exports.securityHeaders = securityHeaders;
var upload_1 = require("./upload");
Object.defineProperty(exports, "uploadSingle", { enumerable: true, get: function () { return upload_1.uploadSingle; } });
Object.defineProperty(exports, "uploadMultiple", { enumerable: true, get: function () { return upload_1.uploadMultiple; } });
Object.defineProperty(exports, "uploadFields", { enumerable: true, get: function () { return upload_1.uploadFields; } });
Object.defineProperty(exports, "handleUploadError", { enumerable: true, get: function () { return upload_1.handleUploadError; } });
Object.defineProperty(exports, "uploadConfig", { enumerable: true, get: function () { return upload_1.uploadConfig; } });
//# sourceMappingURL=index.js.map