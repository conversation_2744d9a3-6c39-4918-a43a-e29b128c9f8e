import { Request, Response, NextFunction } from 'express';
export declare class AppError extends Error {
    statusCode: number;
    isOperational: boolean;
    constructor(message: string, statusCode?: number);
}
export declare const errorHandler: (error: Error, req: Request, res: Response, _next: NextFunction) => void;
export declare const notFoundHandler: (req: Request, res: Response, _next: NextFunction) => void;
export declare const requestLogger: (req: Request, res: Response, next: NextFunction) => void;
export declare const validateRequest: (schema: any, property?: "body" | "query" | "params") => (req: Request, res: Response, next: NextFunction) => void;
export declare const asyncHandler: (fn: Function) => (req: Request, res: Response, next: NextFunction) => void;
declare global {
    namespace Express {
        interface Response {
            success: (data?: any, message?: string) => void;
            error: (message: string, statusCode?: number, data?: any) => void;
            paginated: (data: any[], pagination: any) => void;
        }
    }
}
export declare const responseHelpers: (req: Request, res: Response, next: NextFunction) => void;
export declare const corsOptions: {
    origin: (origin: string | undefined, callback: Function) => any;
    credentials: boolean;
    optionsSuccessStatus: number;
};
export declare const securityHeaders: (req: Request, res: Response, next: NextFunction) => void;
export { uploadSingle, uploadMultiple, uploadFields, handleUploadError, uploadConfig } from './upload';
//# sourceMappingURL=index.d.ts.map