import fs from 'fs';
import { Request, Response, NextFunction } from 'express';
export declare const uploadSingle: import("express").RequestHandler<import("express-serve-static-core").ParamsDictionary, any, any, import("qs").ParsedQs, Record<string, any>>;
export declare const uploadMultiple: import("express").RequestHandler<import("express-serve-static-core").ParamsDictionary, any, any, import("qs").ParsedQs, Record<string, any>>;
export declare const uploadFields: import("express").RequestHandler<import("express-serve-static-core").ParamsDictionary, any, any, import("qs").ParsedQs, Record<string, any>>;
export declare const handleUploadError: (error: any, req: Request, res: Response, next: NextFunction) => void;
export declare const getRelativeFilePath: (absolutePath: string) => string;
export declare const getAbsoluteFilePath: (relativePath: string) => string;
export declare const fileExists: (filePath: string) => boolean;
export declare const deleteFile: (filePath: string) => Promise<boolean>;
export declare const getFileStats: (filePath: string) => Promise<fs.Stats | null>;
export declare const uploadConfig: {
    ALLOWED_MIMETYPES: string[];
    ALLOWED_EXTENSIONS: string[];
    MAX_FILE_SIZE: number;
    MAX_FILES_COUNT: number;
    uploadsDir: string;
};
declare const _default: {
    uploadSingle: import("express").RequestHandler<import("express-serve-static-core").ParamsDictionary, any, any, import("qs").ParsedQs, Record<string, any>>;
    uploadMultiple: import("express").RequestHandler<import("express-serve-static-core").ParamsDictionary, any, any, import("qs").ParsedQs, Record<string, any>>;
    uploadFields: import("express").RequestHandler<import("express-serve-static-core").ParamsDictionary, any, any, import("qs").ParsedQs, Record<string, any>>;
    handleUploadError: (error: any, req: Request, res: Response, next: NextFunction) => void;
    uploadConfig: {
        ALLOWED_MIMETYPES: string[];
        ALLOWED_EXTENSIONS: string[];
        MAX_FILE_SIZE: number;
        MAX_FILES_COUNT: number;
        uploadsDir: string;
    };
    getRelativeFilePath: (absolutePath: string) => string;
    getAbsoluteFilePath: (relativePath: string) => string;
    fileExists: (filePath: string) => boolean;
    deleteFile: (filePath: string) => Promise<boolean>;
    getFileStats: (filePath: string) => Promise<fs.Stats | null>;
};
export default _default;
//# sourceMappingURL=upload.d.ts.map