import { Request, Response, NextFunction } from 'express';
import { AdminRole, AdminStatus } from '../types';
declare global {
    namespace Express {
        interface Request {
            admin?: {
                id: number;
                email: string;
                name: string;
                role: AdminRole;
                status: AdminStatus;
            };
        }
    }
}
export declare const authenticateAdmin: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const requireAdminRole: (requiredRole: AdminRole) => (req: Request, res: Response, next: NextFunction) => void;
export declare const requireSuperAdmin: (req: Request, res: Response, next: NextFunction) => void;
export declare const logAdminActivity: (action: string, resource: string) => (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const validateAdminAccess: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const canModifyResource: (resourceType: string) => (req: Request, res: Response, next: NextFunction) => void;
export declare const preventSelfModification: (req: Request, res: Response, next: NextFunction) => void;
export declare const adminRateLimit: (maxRequests?: number, windowMs?: number) => (req: Request, res: Response, next: NextFunction) => void;
export declare const adminAuth: (req: Request, res: Response, next: NextFunction) => Promise<void>;
//# sourceMappingURL=adminAuth.d.ts.map