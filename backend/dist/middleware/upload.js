"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.uploadConfig = exports.getFileStats = exports.deleteFile = exports.fileExists = exports.getAbsoluteFilePath = exports.getRelativeFilePath = exports.handleUploadError = exports.uploadFields = exports.uploadMultiple = exports.uploadSingle = void 0;
const multer_1 = __importDefault(require("multer"));
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
const ALLOWED_MIMETYPES = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-powerpoint',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'text/plain',
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/bmp',
    'image/tiff'
];
const ALLOWED_EXTENSIONS = [
    '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx',
    '.txt', '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.tif'
];
const MAX_FILE_SIZE = 50 * 1024 * 1024;
const MAX_FILES_COUNT = 10;
const uploadsDir = path_1.default.join(__dirname, '../../uploads');
if (!fs_1.default.existsSync(uploadsDir)) {
    fs_1.default.mkdirSync(uploadsDir, { recursive: true });
}
const createDateDirectory = () => {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const dateDir = path_1.default.join(uploadsDir, `${year}`, `${month}`, `${day}`);
    if (!fs_1.default.existsSync(dateDir)) {
        fs_1.default.mkdirSync(dateDir, { recursive: true });
    }
    return dateDir;
};
const generateFilename = (originalname) => {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 15);
    const ext = path_1.default.extname(originalname).toLowerCase();
    const baseName = path_1.default.basename(originalname, ext)
        .replace(/[^a-zA-Z0-9]/g, '_')
        .substring(0, 50);
    return `${timestamp}_${random}_${baseName}${ext}`;
};
const storage = multer_1.default.diskStorage({
    destination: (req, file, cb) => {
        try {
            const dateDir = createDateDirectory();
            cb(null, dateDir);
        }
        catch (error) {
            cb(error, '');
        }
    },
    filename: (req, file, cb) => {
        try {
            const filename = generateFilename(file.originalname);
            cb(null, filename);
        }
        catch (error) {
            cb(error, '');
        }
    }
});
const fileFilter = (req, file, cb) => {
    if (!ALLOWED_MIMETYPES.includes(file.mimetype)) {
        const error = new Error(`Tipo de arquivo não permitido: ${file.mimetype}`);
        error.name = 'INVALID_FILE_TYPE';
        return cb(error);
    }
    const ext = path_1.default.extname(file.originalname).toLowerCase();
    if (!ALLOWED_EXTENSIONS.includes(ext)) {
        const error = new Error(`Extensão de arquivo não permitida: ${ext}`);
        error.name = 'INVALID_FILE_EXTENSION';
        return cb(error);
    }
    if (file.originalname.includes('..') || file.originalname.includes('/')) {
        const error = new Error('Nome de arquivo inválido');
        error.name = 'INVALID_FILENAME';
        return cb(error);
    }
    cb(null, true);
};
const upload = (0, multer_1.default)({
    storage,
    fileFilter,
    limits: {
        fileSize: MAX_FILE_SIZE,
        files: MAX_FILES_COUNT,
        fieldNameSize: 100,
        fieldSize: 1024 * 1024,
    }
});
exports.uploadSingle = upload.single('file');
exports.uploadMultiple = upload.array('files', MAX_FILES_COUNT);
exports.uploadFields = upload.fields([
    { name: 'file', maxCount: 1 },
    { name: 'files', maxCount: MAX_FILES_COUNT }
]);
const handleUploadError = (error, req, res, next) => {
    if (error instanceof multer_1.default.MulterError) {
        switch (error.code) {
            case 'LIMIT_FILE_SIZE':
                return res.error('Arquivo muito grande. Tamanho máximo: 50MB', 400);
            case 'LIMIT_FILE_COUNT':
                return res.error(`Muitos arquivos. Máximo permitido: ${MAX_FILES_COUNT}`, 400);
            case 'LIMIT_UNEXPECTED_FILE':
                return res.error('Campo de arquivo inesperado', 400);
            case 'LIMIT_FIELD_KEY':
                return res.error('Nome do campo muito longo', 400);
            case 'LIMIT_FIELD_VALUE':
                return res.error('Valor do campo muito longo', 400);
            case 'LIMIT_FIELD_COUNT':
                return res.error('Muitos campos', 400);
            case 'LIMIT_PART_COUNT':
                return res.error('Muitas partes no upload', 400);
            default:
                return res.error(`Erro no upload: ${error.message}`, 400);
        }
    }
    if (error.name === 'INVALID_FILE_TYPE') {
        return res.error(error.message, 400, {
            allowedTypes: ALLOWED_MIMETYPES
        });
    }
    if (error.name === 'INVALID_FILE_EXTENSION') {
        return res.error(error.message, 400, {
            allowedExtensions: ALLOWED_EXTENSIONS
        });
    }
    if (error.name === 'INVALID_FILENAME') {
        return res.error(error.message, 400);
    }
    return res.error('Erro interno no upload de arquivo', 500);
};
exports.handleUploadError = handleUploadError;
const getRelativeFilePath = (absolutePath) => {
    return path_1.default.relative(uploadsDir, absolutePath);
};
exports.getRelativeFilePath = getRelativeFilePath;
const getAbsoluteFilePath = (relativePath) => {
    return path_1.default.join(uploadsDir, relativePath);
};
exports.getAbsoluteFilePath = getAbsoluteFilePath;
const fileExists = (filePath) => {
    try {
        return fs_1.default.existsSync(filePath);
    }
    catch {
        return false;
    }
};
exports.fileExists = fileExists;
const deleteFile = async (filePath) => {
    try {
        if ((0, exports.fileExists)(filePath)) {
            await fs_1.default.promises.unlink(filePath);
            return true;
        }
        return false;
    }
    catch (error) {
        console.error('Error deleting file:', error);
        return false;
    }
};
exports.deleteFile = deleteFile;
const getFileStats = async (filePath) => {
    try {
        return await fs_1.default.promises.stat(filePath);
    }
    catch {
        return null;
    }
};
exports.getFileStats = getFileStats;
exports.uploadConfig = {
    ALLOWED_MIMETYPES,
    ALLOWED_EXTENSIONS,
    MAX_FILE_SIZE,
    MAX_FILES_COUNT,
    uploadsDir
};
exports.default = {
    uploadSingle: exports.uploadSingle,
    uploadMultiple: exports.uploadMultiple,
    uploadFields: exports.uploadFields,
    handleUploadError: exports.handleUploadError,
    uploadConfig: exports.uploadConfig,
    getRelativeFilePath: exports.getRelativeFilePath,
    getAbsoluteFilePath: exports.getAbsoluteFilePath,
    fileExists: exports.fileExists,
    deleteFile: exports.deleteFile,
    getFileStats: exports.getFileStats
};
//# sourceMappingURL=upload.js.map