{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/middleware/index.ts"], "names": [], "mappings": ";;;AAMA,sCAAsC;AAMtC,MAAa,QAAS,SAAQ,KAAK;IAIjC,YAAY,OAAe,EAAE,aAAqB,GAAG;QACnD,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAE1B,KAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;IAClD,CAAC;CACF;AAXD,4BAWC;AAEM,MAAM,YAAY,GAAG,CAC1B,KAAY,EACZ,GAAY,EACZ,GAAa,EACb,KAAmB,EACb,EAAE;IACR,IAAI,UAAU,GAAG,GAAG,CAAC;IACrB,IAAI,OAAO,GAAG,uBAAuB,CAAC;IAEtC,IAAI,KAAK,YAAY,QAAQ,EAAE,CAAC;QAC9B,UAAU,GAAG,KAAK,CAAC,UAAU,CAAC;QAC9B,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;IAC1B,CAAC;IAGD,IAAI,CAAC,kBAAS,CAAC,MAAM,EAAE,CAAC;QACtB,OAAO,CAAC,KAAK,CAAC,QAAQ,EAAE;YACtB,OAAO,EAAE,KAAK,CAAC,OAAO;YACtB,KAAK,EAAE,KAAK,CAAC,KAAK;YAClB,GAAG,EAAE,GAAG,CAAC,GAAG;YACZ,MAAM,EAAE,GAAG,CAAC,MAAM;YAClB,EAAE,EAAE,GAAG,CAAC,EAAE;YACV,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;SACjC,CAAC,CAAC;IACL,CAAC;IAED,MAAM,QAAQ,GAAgB;QAC5B,OAAO,EAAE,KAAK;QACd,KAAK,EAAE,OAAO;KACf,CAAC;IAEF,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AACxC,CAAC,CAAC;AAhCW,QAAA,YAAY,gBAgCvB;AAMK,MAAM,eAAe,GAAG,CAC7B,GAAY,EACZ,GAAa,EACb,KAAmB,EACb,EAAE;IACR,MAAM,QAAQ,GAAgB;QAC5B,OAAO,EAAE,KAAK;QACd,KAAK,EAAE,SAAS,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,WAAW,YAAY;KAC1D,CAAC;IAEF,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AACjC,CAAC,CAAC;AAXW,QAAA,eAAe,mBAW1B;AAMK,MAAM,aAAa,GAAG,CAC3B,GAAY,EACZ,GAAa,EACb,IAAkB,EACZ,EAAE;IACR,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IAEzB,GAAG,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE;QACpB,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC;QAEpC,IAAI,CAAC,kBAAS,CAAC,MAAM,EAAE,CAAC;YACtB,OAAO,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,WAAW,MAAM,GAAG,CAAC,UAAU,MAAM,QAAQ,IAAI,CAAC,CAAC;QACtF,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,IAAI,EAAE,CAAC;AACT,CAAC,CAAC;AAhBW,QAAA,aAAa,iBAgBxB;AAMK,MAAM,eAAe,GAAG,CAAC,MAAW,EAAE,WAAwC,MAAM,EAAE,EAAE;IAC7F,OAAO,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAQ,EAAE;QAC/D,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,EAAE,UAAU,EAAE,KAAK,EAAE,CAAC,CAAC;QAExE,IAAI,KAAK,EAAE,CAAC;YACV,MAAM,MAAM,GAAsB,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,MAAW,EAAE,EAAE,CAAC,CAAC;gBACpE,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;gBAC5B,OAAO,EAAE,MAAM,CAAC,OAAO;aACxB,CAAC,CAAC,CAAC;YAEJ,MAAM,QAAQ,GAAgB;gBAC5B,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,mBAAmB;gBAC1B,MAAM;aACP,CAAC;YAEF,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC/B,OAAO;QACT,CAAC;QAED,IAAI,EAAE,CAAC;IACT,CAAC,CAAC;AACJ,CAAC,CAAC;AAtBW,QAAA,eAAe,mBAsB1B;AAMK,MAAM,YAAY,GAAG,CAAC,EAAY,EAAE,EAAE;IAC3C,OAAO,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;QACzD,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAClD,CAAC,CAAC;AACJ,CAAC,CAAC;AAJW,QAAA,YAAY,gBAIvB;AAgBK,MAAM,eAAe,GAAG,CAC7B,GAAY,EACZ,GAAa,EACb,IAAkB,EACZ,EAAE;IACR,GAAG,CAAC,OAAO,GAAG,CAAC,IAAU,EAAE,OAAgB,EAAE,EAAE;QAC7C,MAAM,QAAQ,GAAgB;YAC5B,OAAO,EAAE,IAAI;YACb,IAAI;YACJ,GAAG,CAAC,OAAO,IAAI,EAAE,OAAO,EAAE,CAAC;SAC5B,CAAC;QACF,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACrB,CAAC,CAAC;IAEF,GAAG,CAAC,KAAK,GAAG,CAAC,OAAe,EAAE,aAAqB,GAAG,EAAE,IAAU,EAAE,EAAE;QACpE,MAAM,QAAQ,GAAgB;YAC5B,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,OAAO;YACd,GAAG,CAAC,IAAI,IAAI,EAAE,IAAI,EAAE,CAAC;SACtB,CAAC;QACF,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACxC,CAAC,CAAC;IAEF,GAAG,CAAC,SAAS,GAAG,CAAC,IAAW,EAAE,UAAe,EAAE,EAAE;QAC/C,MAAM,QAAQ,GAAG;YACf,OAAO,EAAE,IAAI;YACb,IAAI;YACJ,UAAU;SACX,CAAC;QACF,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACrB,CAAC,CAAC;IAEF,IAAI,EAAE,CAAC;AACT,CAAC,CAAC;AAjCW,QAAA,eAAe,mBAiC1B;AAMW,QAAA,WAAW,GAAG;IACzB,MAAM,EAAE,CAAC,MAA0B,EAAE,QAAkB,EAAE,EAAE;QAEzD,IAAI,CAAC,MAAM;YAAE,OAAO,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAEzC,MAAM,cAAc,GAAG,OAAO,CAAC,GAAG,CAAC,WAAW,EAAE,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;QAExF,IAAI,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YACpC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QACvB,CAAC;aAAM,CAAC;YACN,QAAQ,CAAC,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC,CAAC;QAC7C,CAAC;IACH,CAAC;IACD,WAAW,EAAE,IAAI;IACjB,oBAAoB,EAAE,GAAG;CAC1B,CAAC;AAMK,MAAM,eAAe,GAAG,CAC7B,GAAY,EACZ,GAAa,EACb,IAAkB,EACZ,EAAE;IACR,GAAG,CAAC,SAAS,CAAC,wBAAwB,EAAE,SAAS,CAAC,CAAC;IACnD,GAAG,CAAC,SAAS,CAAC,iBAAiB,EAAE,MAAM,CAAC,CAAC;IACzC,GAAG,CAAC,SAAS,CAAC,kBAAkB,EAAE,eAAe,CAAC,CAAC;IACnD,GAAG,CAAC,SAAS,CAAC,iBAAiB,EAAE,iCAAiC,CAAC,CAAC;IAEpE,IAAI,EAAE,CAAC;AACT,CAAC,CAAC;AAXW,QAAA,eAAe,mBAW1B;AAMF,mCAMkB;AALhB,sGAAA,YAAY,OAAA;AACZ,wGAAA,cAAc,OAAA;AACd,sGAAA,YAAY,OAAA;AACZ,2GAAA,iBAAiB,OAAA;AACjB,sGAAA,YAAY,OAAA"}