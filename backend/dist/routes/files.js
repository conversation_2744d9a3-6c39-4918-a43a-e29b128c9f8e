"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const path_1 = __importDefault(require("path"));
const models_1 = require("../models");
const upload_1 = require("../middleware/upload");
const fileValidation_1 = require("../utils/fileValidation");
const pdfProcessor_1 = require("../utils/pdfProcessor");
const router = express_1.default.Router();
router.post('/upload', upload_1.uploadSingle, upload_1.handleUploadError, async (req, res) => {
    try {
        if (!req.file) {
            return res.error('Nenhum arquivo foi enviado', 400);
        }
        const file = req.file;
        const relativePath = (0, upload_1.getRelativeFilePath)(file.path);
        const fileInfo = {
            filename: file.filename,
            originalName: file.originalname,
            mimetype: file.mimetype,
            size: file.size,
            path: file.path,
            extension: path_1.default.extname(file.originalname).toLowerCase()
        };
        const validation = await (0, fileValidation_1.validateFile)(fileInfo);
        if (!validation.isValid) {
            await (0, upload_1.deleteFile)(file.path);
            return res.error('Arquivo inválido', 400, {
                errors: validation.errors,
                warnings: validation.warnings
            });
        }
        let pdfMetadata = null;
        let estimatedCost = null;
        if (file.mimetype === 'application/pdf') {
            const pdfResult = await (0, pdfProcessor_1.extractPDFMetadata)(file.path);
            if (pdfResult.success && pdfResult.metadata) {
                pdfMetadata = pdfResult.metadata;
                estimatedCost = (0, pdfProcessor_1.calculatePrintEstimate)(pdfResult.metadata);
            }
        }
        const fileData = {
            filename: file.filename,
            originalName: file.originalname,
            mimetype: file.mimetype,
            size: file.size,
            url: `/uploads/${relativePath}`
        };
        const savedFile = await models_1.FileModel.create(fileData);
        const response = {
            file: {
                id: savedFile.id,
                filename: savedFile.filename,
                originalName: savedFile.originalName,
                mimetype: savedFile.mimetype,
                size: savedFile.size,
                formattedSize: (0, fileValidation_1.formatFileSize)(savedFile.size),
                url: savedFile.url,
                category: (0, fileValidation_1.getFileCategory)(savedFile.mimetype),
                uploadedAt: savedFile.uploadedAt
            },
            validation: {
                warnings: validation.warnings,
                metadata: validation.metadata
            }
        };
        if (pdfMetadata) {
            response.pdf = {
                pages: pdfMetadata.pages,
                hasText: pdfMetadata.hasText,
                hasImages: pdfMetadata.hasImages,
                title: pdfMetadata.title,
                author: pdfMetadata.author,
                estimatedCost
            };
        }
        res.success(response, 'Arquivo enviado com sucesso');
    }
    catch (error) {
        console.error('Error uploading file:', error);
        if (req.file) {
            await (0, upload_1.deleteFile)(req.file.path);
        }
        res.error('Erro interno no upload do arquivo', 500);
    }
});
router.post('/upload-multiple', upload_1.uploadMultiple, upload_1.handleUploadError, async (req, res) => {
    try {
        if (!req.files || !Array.isArray(req.files) || req.files.length === 0) {
            return res.error('Nenhum arquivo foi enviado', 400);
        }
        const files = req.files;
        const results = [];
        const errors = [];
        for (const file of files) {
            try {
                const relativePath = (0, upload_1.getRelativeFilePath)(file.path);
                const fileInfo = {
                    filename: file.filename,
                    originalName: file.originalname,
                    mimetype: file.mimetype,
                    size: file.size,
                    path: file.path,
                    extension: path_1.default.extname(file.originalname).toLowerCase()
                };
                const validation = await (0, fileValidation_1.validateFile)(fileInfo);
                if (!validation.isValid) {
                    await (0, upload_1.deleteFile)(file.path);
                    errors.push({
                        filename: file.originalname,
                        errors: validation.errors
                    });
                    continue;
                }
                let pdfMetadata = null;
                if (file.mimetype === 'application/pdf') {
                    const pdfResult = await (0, pdfProcessor_1.extractPDFMetadata)(file.path);
                    if (pdfResult.success && pdfResult.metadata) {
                        pdfMetadata = pdfResult.metadata;
                    }
                }
                const fileData = {
                    filename: file.filename,
                    originalName: file.originalname,
                    mimetype: file.mimetype,
                    size: file.size,
                    url: `/uploads/${relativePath}`
                };
                const savedFile = await models_1.FileModel.create(fileData);
                const result = {
                    file: {
                        id: savedFile.id,
                        filename: savedFile.filename,
                        originalName: savedFile.originalName,
                        mimetype: savedFile.mimetype,
                        size: savedFile.size,
                        formattedSize: (0, fileValidation_1.formatFileSize)(savedFile.size),
                        url: savedFile.url,
                        category: (0, fileValidation_1.getFileCategory)(savedFile.mimetype),
                        uploadedAt: savedFile.uploadedAt
                    },
                    validation: {
                        warnings: validation.warnings
                    }
                };
                if (pdfMetadata) {
                    result.pdf = {
                        pages: pdfMetadata.pages,
                        hasText: pdfMetadata.hasText,
                        hasImages: pdfMetadata.hasImages
                    };
                }
                results.push(result);
            }
            catch (error) {
                console.error(`Error processing file ${file.originalname}:`, error);
                await (0, upload_1.deleteFile)(file.path);
                errors.push({
                    filename: file.originalname,
                    errors: ['Erro interno no processamento do arquivo']
                });
            }
        }
        const response = {
            uploaded: results,
            errors: errors.length > 0 ? errors : undefined,
            summary: {
                total: files.length,
                successful: results.length,
                failed: errors.length
            }
        };
        if (results.length > 0) {
            res.success(response, `${results.length} arquivo(s) enviado(s) com sucesso`);
        }
        else {
            res.error('Nenhum arquivo foi processado com sucesso', 400, response);
        }
    }
    catch (error) {
        console.error('Error uploading multiple files:', error);
        if (req.files && Array.isArray(req.files)) {
            for (const file of req.files) {
                await (0, upload_1.deleteFile)(file.path);
            }
        }
        res.error('Erro interno no upload dos arquivos', 500);
    }
});
router.post('/validate', upload_1.uploadSingle, upload_1.handleUploadError, async (req, res) => {
    try {
        if (!req.file) {
            return res.error('Nenhum arquivo foi enviado para validação', 400);
        }
        const file = req.file;
        const fileInfo = {
            filename: file.filename,
            originalName: file.originalname,
            mimetype: file.mimetype,
            size: file.size,
            path: file.path,
            extension: path_1.default.extname(file.originalname).toLowerCase()
        };
        const validation = await (0, fileValidation_1.validateFile)(fileInfo);
        let pdfMetadata = null;
        let estimatedCost = null;
        if (validation.isValid && file.mimetype === 'application/pdf') {
            const pdfResult = await (0, pdfProcessor_1.extractPDFMetadata)(file.path);
            if (pdfResult.success && pdfResult.metadata) {
                pdfMetadata = pdfResult.metadata;
                estimatedCost = (0, pdfProcessor_1.calculatePrintEstimate)(pdfResult.metadata);
            }
        }
        await (0, upload_1.deleteFile)(file.path);
        const response = {
            validation: {
                isValid: validation.isValid,
                errors: validation.errors,
                warnings: validation.warnings,
                metadata: validation.metadata
            },
            file: {
                originalName: file.originalname,
                mimetype: file.mimetype,
                size: file.size,
                formattedSize: (0, fileValidation_1.formatFileSize)(file.size),
                category: (0, fileValidation_1.getFileCategory)(file.mimetype)
            }
        };
        if (pdfMetadata) {
            response.pdf = {
                pages: pdfMetadata.pages,
                hasText: pdfMetadata.hasText,
                hasImages: pdfMetadata.hasImages,
                title: pdfMetadata.title,
                author: pdfMetadata.author,
                estimatedCost
            };
        }
        if (validation.isValid) {
            res.success(response, 'Arquivo válido para upload');
        }
        else {
            res.error('Arquivo inválido', 400, response);
        }
    }
    catch (error) {
        console.error('Error validating file:', error);
        if (req.file) {
            await (0, upload_1.deleteFile)(req.file.path);
        }
        res.error('Erro interno na validação do arquivo', 500);
    }
});
router.get('/', async (req, res) => {
    try {
        const limit = parseInt(req.query.limit) || 20;
        const offset = parseInt(req.query.offset) || 0;
        const mimetype = req.query.mimetype;
        const category = req.query.category;
        const result = await models_1.FileModel.findAll(limit, offset);
        let filteredFiles = result.files;
        if (category) {
            filteredFiles = result.files.filter(file => (0, fileValidation_1.getFileCategory)(file.mimetype) === category);
        }
        const response = {
            files: filteredFiles.map(file => ({
                id: file.id,
                filename: file.filename,
                originalName: file.originalName,
                mimetype: file.mimetype,
                size: file.size,
                formattedSize: (0, fileValidation_1.formatFileSize)(file.size),
                url: file.url,
                category: (0, fileValidation_1.getFileCategory)(file.mimetype),
                uploadedAt: file.uploadedAt
            })),
            pagination: {
                total: result.total,
                limit,
                offset,
                hasMore: offset + limit < result.total
            }
        };
        res.success(response, 'Arquivos recuperados com sucesso');
    }
    catch (error) {
        console.error('Error getting files:', error);
        res.error('Erro ao recuperar arquivos', 500);
    }
});
router.get('/:id', async (req, res) => {
    try {
        const fileId = parseInt(req.params.id || '0');
        if (isNaN(fileId)) {
            return res.error('ID de arquivo inválido', 400);
        }
        const file = await models_1.FileModel.findById(fileId);
        if (!file) {
            return res.error('Arquivo não encontrado', 404);
        }
        const absolutePath = (0, upload_1.getAbsoluteFilePath)(file.url.replace('/uploads/', ''));
        const exists = (0, upload_1.fileExists)(absolutePath);
        const response = {
            file: {
                id: file.id,
                filename: file.filename,
                originalName: file.originalName,
                mimetype: file.mimetype,
                size: file.size,
                formattedSize: (0, fileValidation_1.formatFileSize)(file.size),
                url: file.url,
                category: (0, fileValidation_1.getFileCategory)(file.mimetype),
                uploadedAt: file.uploadedAt,
                physicalFileExists: exists
            }
        };
        if (file.mimetype === 'application/pdf' && exists) {
            try {
                const pdfResult = await (0, pdfProcessor_1.extractPDFMetadata)(absolutePath);
                if (pdfResult.success && pdfResult.metadata) {
                    response.pdf = {
                        pages: pdfResult.metadata.pages,
                        hasText: pdfResult.metadata.hasText,
                        hasImages: pdfResult.metadata.hasImages,
                        title: pdfResult.metadata.title,
                        author: pdfResult.metadata.author,
                        estimatedCost: (0, pdfProcessor_1.calculatePrintEstimate)(pdfResult.metadata)
                    };
                }
            }
            catch (error) {
                console.error('Error extracting PDF metadata:', error);
            }
        }
        res.success(response, 'Arquivo recuperado com sucesso');
    }
    catch (error) {
        console.error('Error getting file:', error);
        res.error('Erro ao recuperar arquivo', 500);
    }
});
router.get('/:id/download', async (req, res) => {
    try {
        const fileId = parseInt(req.params.id || '0');
        if (isNaN(fileId)) {
            return res.error('ID de arquivo inválido', 400);
        }
        const file = await models_1.FileModel.findById(fileId);
        if (!file) {
            return res.error('Arquivo não encontrado', 404);
        }
        const absolutePath = (0, upload_1.getAbsoluteFilePath)(file.url.replace('/uploads/', ''));
        if (!(0, upload_1.fileExists)(absolutePath)) {
            return res.error('Arquivo físico não encontrado', 404);
        }
        res.setHeader('Content-Disposition', `attachment; filename="${file.originalName}"`);
        res.setHeader('Content-Type', file.mimetype);
        res.sendFile(absolutePath);
    }
    catch (error) {
        console.error('Error downloading file:', error);
        res.error('Erro ao baixar arquivo', 500);
    }
});
router.delete('/:id', async (req, res) => {
    try {
        const fileId = parseInt(req.params.id || '0');
        if (isNaN(fileId)) {
            return res.error('ID de arquivo inválido', 400);
        }
        const file = await models_1.FileModel.findById(fileId);
        if (!file) {
            return res.error('Arquivo não encontrado', 404);
        }
        const isUsed = await models_1.FileModel.isUsedInOrders(fileId);
        if (isUsed) {
            return res.error('Arquivo não pode ser deletado pois está sendo usado em pedidos', 400);
        }
        const deleted = await models_1.FileModel.delete(fileId);
        if (!deleted) {
            return res.error('Erro ao deletar arquivo do banco de dados', 500);
        }
        const absolutePath = (0, upload_1.getAbsoluteFilePath)(file.url.replace('/uploads/', ''));
        if ((0, upload_1.fileExists)(absolutePath)) {
            await (0, upload_1.deleteFile)(absolutePath);
        }
        res.success({ fileId }, 'Arquivo deletado com sucesso');
    }
    catch (error) {
        console.error('Error deleting file:', error);
        res.error('Erro ao deletar arquivo', 500);
    }
});
router.get('/stats', async (req, res) => {
    try {
        const stats = await models_1.FileModel.getStorageStats();
        const response = {
            storage: {
                totalFiles: stats.totalFiles,
                totalSize: stats.totalSize,
                formattedTotalSize: (0, fileValidation_1.formatFileSize)(stats.totalSize),
                averageSize: stats.averageSize,
                formattedAverageSize: (0, fileValidation_1.formatFileSize)(stats.averageSize)
            },
            mimetypes: stats.mimetypeStats.map(stat => ({
                mimetype: stat.mimetype,
                category: (0, fileValidation_1.getFileCategory)(stat.mimetype),
                count: stat.count,
                totalSize: stat.totalSize,
                formattedTotalSize: (0, fileValidation_1.formatFileSize)(stat.totalSize),
                percentage: Math.round((stat.count / stats.totalFiles) * 100)
            }))
        };
        res.success(response, 'Estatísticas recuperadas com sucesso');
    }
    catch (error) {
        console.error('Error getting storage stats:', error);
        res.error('Erro ao recuperar estatísticas', 500);
    }
});
exports.default = router;
//# sourceMappingURL=files.js.map