"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.setNotificationService = void 0;
const express_1 = require("express");
const Notification_1 = require("../models/Notification");
const types_1 = require("../types");
const router = (0, express_1.Router)();
let notificationService;
const setNotificationService = (service) => {
    notificationService = service;
};
exports.setNotificationService = setNotificationService;
router.get('/', async (req, res) => {
    try {
        const { page = 1, limit = 20, type, channel, status, recipientEmail, orderId } = req.query;
        const options = {
            page: parseInt(page),
            limit: parseInt(limit),
            type: type,
            channel: channel,
            status: status,
            recipientEmail: recipientEmail
        };
        if (orderId) {
            options.orderId = parseInt(orderId);
        }
        const result = await Notification_1.NotificationModel.findMany(options);
        res.paginated(result.notifications, {
            total: result.total,
            page: options.page,
            limit: options.limit,
            message: 'Notificações recuperadas com sucesso'
        });
    }
    catch (error) {
        console.error('Error fetching notifications:', error);
        res.error('Erro ao buscar notificações', 500);
    }
});
router.get('/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const notificationId = parseInt(id);
        if (isNaN(notificationId)) {
            return res.error('ID de notificação inválido', 400);
        }
        const notification = await Notification_1.NotificationModel.findById(notificationId);
        if (!notification) {
            return res.error('Notificação não encontrada', 404);
        }
        res.success(notification, 'Notificação encontrada');
    }
    catch (error) {
        console.error('Error fetching notification:', error);
        res.error('Erro ao buscar notificação', 500);
    }
});
router.post('/', async (req, res) => {
    try {
        const { type, channel, recipientEmail, recipientName, subject, content, templateData, orderId, fileId, maxRetries } = req.body;
        if (!type || !channel || !recipientEmail || !subject || !content) {
            return res.error('Campos obrigatórios: type, channel, recipientEmail, subject, content', 400);
        }
        if (!Object.values(types_1.NotificationType).includes(type)) {
            return res.error('Tipo de notificação inválido', 400);
        }
        if (!Object.values(types_1.NotificationChannel).includes(channel)) {
            return res.error('Canal de notificação inválido', 400);
        }
        const notificationData = {
            type,
            channel,
            recipientEmail,
            recipientName,
            subject,
            content,
            templateData,
            orderId,
            fileId,
            maxRetries
        };
        const notification = await Notification_1.NotificationModel.create(notificationData);
        res.status(201);
        res.success(notification, 'Notificação criada com sucesso');
    }
    catch (error) {
        console.error('Error creating notification:', error);
        res.error('Erro ao criar notificação', 500);
    }
});
router.post('/send', async (req, res) => {
    try {
        if (!notificationService) {
            return res.error('Serviço de notificações não disponível', 503);
        }
        const { type, channel, recipientEmail, recipientName, subject, content, templateData, orderId, fileId } = req.body;
        if (!type || !channel || !recipientEmail || !subject || !content) {
            return res.error('Campos obrigatórios: type, channel, recipientEmail, subject, content', 400);
        }
        const notificationData = {
            type,
            channel,
            recipientEmail,
            recipientName,
            subject,
            content,
            templateData,
            orderId,
            fileId
        };
        const success = await notificationService.sendImmediateNotification(notificationData);
        if (success) {
            res.success({ sent: true }, 'Notificação enviada com sucesso');
        }
        else {
            res.error('Falha ao enviar notificação', 500);
        }
    }
    catch (error) {
        console.error('Error sending immediate notification:', error);
        res.error('Erro ao enviar notificação', 500);
    }
});
router.put('/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const notificationId = parseInt(id);
        if (isNaN(notificationId)) {
            return res.error('ID de notificação inválido', 400);
        }
        const { status, sentAt, deliveredAt, readAt, errorMessage, retryCount } = req.body;
        if (status && !Object.values(types_1.NotificationStatus).includes(status)) {
            return res.error('Status de notificação inválido', 400);
        }
        const updateData = {
            status,
            sentAt: sentAt ? new Date(sentAt) : undefined,
            deliveredAt: deliveredAt ? new Date(deliveredAt) : undefined,
            readAt: readAt ? new Date(readAt) : undefined,
            errorMessage,
            retryCount
        };
        const notification = await Notification_1.NotificationModel.update(notificationId, updateData);
        if (!notification) {
            return res.error('Notificação não encontrada', 404);
        }
        res.success(notification, 'Notificação atualizada com sucesso');
    }
    catch (error) {
        console.error('Error updating notification:', error);
        res.error('Erro ao atualizar notificação', 500);
    }
});
router.post('/:id/mark-read', async (req, res) => {
    try {
        const { id } = req.params;
        const notificationId = parseInt(id);
        if (isNaN(notificationId)) {
            return res.error('ID de notificação inválido', 400);
        }
        const notification = await Notification_1.NotificationModel.update(notificationId, {
            status: types_1.NotificationStatus.READ,
            readAt: new Date()
        });
        if (!notification) {
            return res.error('Notificação não encontrada', 404);
        }
        res.success(notification, 'Notificação marcada como lida');
    }
    catch (error) {
        console.error('Error marking notification as read:', error);
        res.error('Erro ao marcar notificação como lida', 500);
    }
});
router.get('/stats/overview', async (req, res) => {
    try {
        const { days = 30 } = req.query;
        const daysNumber = parseInt(days);
        if (!notificationService) {
            return res.error('Serviço de notificações não disponível', 503);
        }
        const stats = await notificationService.getStatistics(daysNumber);
        res.success(stats, 'Estatísticas de notificações recuperadas');
    }
    catch (error) {
        console.error('Error fetching notification stats:', error);
        res.error('Erro ao buscar estatísticas de notificações', 500);
    }
});
router.get('/queue/pending', async (req, res) => {
    try {
        const { limit = 50 } = req.query;
        const limitNumber = parseInt(limit);
        const notifications = await Notification_1.NotificationModel.getPendingNotifications(limitNumber);
        res.success(notifications, 'Notificações pendentes recuperadas');
    }
    catch (error) {
        console.error('Error fetching pending notifications:', error);
        res.error('Erro ao buscar notificações pendentes', 500);
    }
});
router.get('/queue/failed', async (req, res) => {
    try {
        const { limit = 20 } = req.query;
        const limitNumber = parseInt(limit);
        const notifications = await Notification_1.NotificationModel.getRetryableNotifications(limitNumber);
        res.success(notifications, 'Notificações falhadas recuperadas');
    }
    catch (error) {
        console.error('Error fetching failed notifications:', error);
        res.error('Erro ao buscar notificações falhadas', 500);
    }
});
router.post('/cleanup', async (req, res) => {
    try {
        const { daysOld = 90 } = req.body;
        if (!notificationService) {
            return res.error('Serviço de notificações não disponível', 503);
        }
        const deletedCount = await notificationService.cleanupOldNotifications(daysOld);
        res.success({ deletedCount }, `${deletedCount} notificações antigas foram removidas`);
    }
    catch (error) {
        console.error('Error cleaning up notifications:', error);
        res.error('Erro ao limpar notificações antigas', 500);
    }
});
router.post('/test-email', async (req, res) => {
    try {
        const { email, name = 'Teste' } = req.body;
        if (!email) {
            return res.error('Email é obrigatório', 400);
        }
        if (!notificationService) {
            return res.error('Serviço de notificações não disponível', 503);
        }
        const success = await notificationService.sendImmediateNotification({
            type: types_1.NotificationType.SYSTEM_ALERT,
            channel: types_1.NotificationChannel.EMAIL,
            recipientEmail: email,
            recipientName: name,
            subject: 'Teste de Email - WePrint AI',
            content: `
        <h2>Teste de Email</h2>
        <p>Este é um email de teste do sistema WePrint AI.</p>
        <p>Se você recebeu este email, a configuração está funcionando corretamente!</p>
        <p><strong>Data/Hora:</strong> ${new Date().toLocaleString('pt-PT')}</p>
      `
        });
        if (success) {
            res.success({ sent: true }, 'Email de teste enviado com sucesso');
        }
        else {
            res.error('Falha ao enviar email de teste', 500);
        }
    }
    catch (error) {
        console.error('Error sending test email:', error);
        res.error('Erro ao enviar email de teste', 500);
    }
});
exports.default = router;
//# sourceMappingURL=notifications.js.map