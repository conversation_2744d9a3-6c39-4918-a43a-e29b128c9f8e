"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const models_1 = require("../models");
const StripeService_1 = require("../services/StripeService");
const adminAuth_1 = require("../middleware/adminAuth");
const types_1 = require("../types");
const router = express_1.default.Router();
const paymentRateLimit = (0, express_rate_limit_1.default)({
    windowMs: 15 * 60 * 1000,
    max: 10,
    message: {
        error: 'Too many payment requests from this IP, please try again later.'
    }
});
const refundRateLimit = (0, express_rate_limit_1.default)({
    windowMs: 60 * 60 * 1000,
    max: 5,
    message: {
        error: 'Too many refund requests from this IP, please try again later.'
    }
});
router.post('/create-intent', paymentRateLimit, async (req, res) => {
    try {
        const { orderId, amount, currency = 'EUR', customerEmail, description } = req.body;
        if (!orderId || !amount || !customerEmail) {
            return res.status(400).json({
                error: 'Missing required fields: orderId, amount, customerEmail'
            });
        }
        if (amount < 50) {
            return res.status(400).json({
                error: 'Amount must be at least 50 cents'
            });
        }
        const existingPayments = await models_1.PaymentModel.findByOrderId(orderId);
        const pendingPayment = existingPayments.find(p => p.status === types_1.PaymentStatus.PENDING || p.status === types_1.PaymentStatus.PROCESSING);
        if (pendingPayment) {
            return res.status(400).json({
                error: 'A payment is already pending for this order',
                paymentId: pendingPayment.id
            });
        }
        const paymentIntent = await StripeService_1.StripeService.createPaymentIntent({
            amount,
            currency,
            metadata: {
                orderId: orderId.toString(),
                customerEmail
            }
        });
        const payment = await models_1.PaymentModel.create({
            orderId,
            customerEmail,
            stripePaymentIntentId: paymentIntent.id,
            amount,
            currency,
            status: types_1.PaymentStatus.PENDING,
            description: description || `Payment for order #${orderId}`
        });
        res.status(201).json({
            success: true,
            data: {
                paymentId: payment.id,
                clientSecret: paymentIntent.client_secret,
                amount: payment.amount,
                currency: payment.currency
            }
        });
    }
    catch (error) {
        console.error('Error creating payment intent:', error);
        res.status(500).json({
            error: 'Failed to create payment intent',
            details: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
});
router.post('/confirm', paymentRateLimit, async (req, res) => {
    try {
        const { paymentIntentId, paymentMethodId } = req.body;
        if (!paymentIntentId) {
            return res.status(400).json({
                error: 'Missing required field: paymentIntentId'
            });
        }
        const payment = await models_1.PaymentModel.findByStripePaymentIntentId(paymentIntentId);
        if (!payment) {
            return res.status(404).json({
                error: 'Payment not found'
            });
        }
        const confirmedIntent = await StripeService_1.StripeService.confirmPaymentIntent(paymentIntentId, paymentMethodId);
        const updatedPayment = await models_1.PaymentModel.update(payment.id, {
            status: confirmedIntent.status,
            stripePaymentMethodId: paymentMethodId,
            paymentMethodType: confirmedIntent.payment_method?.type,
            paidAt: confirmedIntent.status === 'succeeded' ? new Date() : undefined,
            failedAt: confirmedIntent.status === 'failed' ? new Date() : undefined,
            failureReason: confirmedIntent.last_payment_error?.message,
            failureCode: confirmedIntent.last_payment_error?.code,
            receiptUrl: confirmedIntent.charges?.data[0]?.receipt_url
        });
        res.json({
            success: true,
            data: {
                payment: updatedPayment,
                status: confirmedIntent.status
            }
        });
    }
    catch (error) {
        console.error('Error confirming payment:', error);
        res.status(500).json({
            error: 'Failed to confirm payment',
            details: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
});
router.get('/:id', async (req, res) => {
    try {
        const paymentId = parseInt(req.params.id);
        if (isNaN(paymentId)) {
            return res.status(400).json({
                error: 'Invalid payment ID'
            });
        }
        const payment = await models_1.PaymentModel.findById(paymentId);
        if (!payment) {
            return res.status(404).json({
                error: 'Payment not found'
            });
        }
        const invoices = await models_1.InvoiceModel.findByPaymentId(paymentId);
        const refunds = await models_1.RefundModel.findByPaymentId(paymentId);
        res.json({
            success: true,
            data: {
                payment,
                invoices,
                refunds
            }
        });
    }
    catch (error) {
        console.error('Error fetching payment:', error);
        res.status(500).json({
            error: 'Failed to fetch payment',
            details: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
});
router.get('/order/:orderId', async (req, res) => {
    try {
        const orderId = parseInt(req.params.orderId);
        if (isNaN(orderId)) {
            return res.status(400).json({
                error: 'Invalid order ID'
            });
        }
        const payments = await models_1.PaymentModel.findByOrderId(orderId);
        res.json({
            success: true,
            data: payments
        });
    }
    catch (error) {
        console.error('Error fetching payments for order:', error);
        res.status(500).json({
            error: 'Failed to fetch payments for order',
            details: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
});
router.post('/:id/refund', adminAuth_1.adminAuth, refundRateLimit, async (req, res) => {
    try {
        const paymentId = parseInt(req.params.id);
        const { amount, reason, description } = req.body;
        const adminId = req.admin?.id;
        if (isNaN(paymentId)) {
            return res.status(400).json({
                error: 'Invalid payment ID'
            });
        }
        const payment = await models_1.PaymentModel.findById(paymentId);
        if (!payment) {
            return res.status(404).json({
                error: 'Payment not found'
            });
        }
        if (payment.status !== types_1.PaymentStatus.SUCCEEDED) {
            return res.status(400).json({
                error: 'Payment must be succeeded to be refunded'
            });
        }
        const totalRefunded = await models_1.RefundModel.getTotalRefundedForPayment(paymentId);
        const maxRefundAmount = payment.amount - totalRefunded;
        if (amount > maxRefundAmount) {
            return res.status(400).json({
                error: `Refund amount cannot exceed ${maxRefundAmount} cents (already refunded: ${totalRefunded} cents)`
            });
        }
        const stripeRefund = await StripeService_1.StripeService.createRefund({
            paymentIntentId: payment.stripePaymentIntentId,
            amount,
            reason: reason || 'requested_by_customer'
        });
        const refund = await models_1.RefundModel.create({
            paymentId,
            stripeRefundId: stripeRefund.id,
            amount,
            currency: payment.currency,
            reason,
            status: stripeRefund.status,
            description,
            createdBy: adminId
        });
        res.status(201).json({
            success: true,
            data: refund
        });
    }
    catch (error) {
        console.error('Error creating refund:', error);
        res.status(500).json({
            error: 'Failed to create refund',
            details: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
});
router.get('/', adminAuth_1.adminAuth, async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 50;
        const status = req.query.status;
        const offset = (page - 1) * limit;
        const result = await models_1.PaymentModel.findAll(offset, limit, status);
        res.json({
            success: true,
            data: result.payments,
            pagination: {
                page,
                limit,
                total: result.total,
                pages: Math.ceil(result.total / limit)
            }
        });
    }
    catch (error) {
        console.error('Error fetching payments:', error);
        res.status(500).json({
            error: 'Failed to fetch payments',
            details: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
});
router.get('/stats', adminAuth_1.adminAuth, async (req, res) => {
    try {
        const stats = await models_1.PaymentModel.getStatistics();
        res.json({
            success: true,
            data: stats
        });
    }
    catch (error) {
        console.error('Error fetching payment statistics:', error);
        res.status(500).json({
            error: 'Failed to fetch payment statistics',
            details: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
});
exports.default = router;
//# sourceMappingURL=payments.js.map