"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const models_1 = require("../models");
const MulticaixaService_1 = require("../services/MulticaixaService");
const adminAuth_1 = require("../middleware/adminAuth");
const types_1 = require("../types");
function mapMulticaixaStatusToPaymentStatus(multicaixaStatus) {
    switch (multicaixaStatus) {
        case MulticaixaService_1.MulticaixaPaymentStatus.PENDING:
            return types_1.PaymentStatus.PENDING;
        case MulticaixaService_1.MulticaixaPaymentStatus.PROCESSING:
            return types_1.PaymentStatus.PROCESSING;
        case MulticaixaService_1.MulticaixaPaymentStatus.COMPLETED:
            return types_1.PaymentStatus.COMPLETED;
        case MulticaixaService_1.MulticaixaPaymentStatus.FAILED:
            return types_1.PaymentStatus.FAILED;
        case MulticaixaService_1.MulticaixaPaymentStatus.CANCELLED:
            return types_1.PaymentStatus.CANCELLED;
        case MulticaixaService_1.MulticaixaPaymentStatus.EXPIRED:
            return types_1.PaymentStatus.EXPIRED;
        default:
            return types_1.PaymentStatus.PENDING;
    }
}
const router = express_1.default.Router();
const paymentRateLimit = (0, express_rate_limit_1.default)({
    windowMs: 15 * 60 * 1000,
    max: 10,
    message: {
        error: 'Too many payment requests from this IP, please try again later.'
    }
});
const refundRateLimit = (0, express_rate_limit_1.default)({
    windowMs: 60 * 60 * 1000,
    max: 5,
    message: {
        error: 'Too many refund requests from this IP, please try again later.'
    }
});
router.post('/create', paymentRateLimit, async (req, res) => {
    try {
        const { orderId, amount, currency = 'AOA', customerEmail, customerPhone, description } = req.body;
        if (!orderId || !amount || !customerEmail) {
            return res.error('Missing required fields: orderId, amount, customerEmail', 400);
        }
        if (amount < 100) {
            return res.error('Amount must be at least 100 AOA', 400);
        }
        const existingPayments = await models_1.PaymentModel.findByOrderId(orderId);
        const pendingPayment = existingPayments.find(p => p.status === types_1.PaymentStatus.PENDING || p.status === types_1.PaymentStatus.PROCESSING);
        if (pendingPayment) {
            return res.error('A payment is already pending for this order', 400, {
                paymentId: pendingPayment.id
            });
        }
        const multicaixaPayment = await MulticaixaService_1.MulticaixaService.createPayment({
            amount,
            currency,
            orderId,
            customerEmail,
            customerPhone,
            description: description || `WePrint AI - Pedido #${orderId}`,
            metadata: {
                orderId: orderId.toString(),
                customerEmail
            }
        });
        const payment = await models_1.PaymentModel.create({
            orderId,
            customerEmail,
            multicaixaTransactionId: multicaixaPayment.transactionId,
            multicaixaPaymentUrl: multicaixaPayment.paymentUrl,
            amount,
            currency,
            status: types_1.PaymentStatus.PENDING,
            paymentMethodType: 'MULTICAIXA_EXPRESS',
            description: description || `Payment for order #${orderId}`
        });
        res.success({
            paymentId: payment.id,
            transactionId: multicaixaPayment.transactionId,
            paymentUrl: multicaixaPayment.paymentUrl,
            qrCode: multicaixaPayment.qrCode,
            amount: payment.amount,
            currency: payment.currency,
            expiresAt: multicaixaPayment.expiresAt
        }, 'Payment created successfully');
    }
    catch (error) {
        console.error('Error creating Multicaixa payment:', error);
        const message = error instanceof Error ? error.message : 'Unknown error';
        res.error('Failed to create payment', 500, {
            details: process.env.NODE_ENV === 'development' ? message : undefined
        });
    }
});
router.post('/multicaixa/webhook', async (req, res) => {
    try {
        const webhookPayload = req.body;
        if (!MulticaixaService_1.MulticaixaService.verifyWebhookSignature(webhookPayload)) {
            return res.error('Invalid webhook signature', 401);
        }
        const webhookData = MulticaixaService_1.MulticaixaService.processWebhook(webhookPayload);
        const payment = await models_1.PaymentModel.findByMulticaixaTransactionId(webhookData.transactionId);
        if (!payment) {
            return res.error('Payment not found', 404);
        }
        const mappedStatus = mapMulticaixaStatusToPaymentStatus(webhookData.status);
        const updateData = {
            status: mappedStatus
        };
        if (webhookData.status === MulticaixaService_1.MulticaixaPaymentStatus.COMPLETED && webhookData.paidAt) {
            updateData.paidAt = webhookData.paidAt;
        }
        else if (webhookData.status === MulticaixaService_1.MulticaixaPaymentStatus.FAILED) {
            updateData.failedAt = new Date();
            updateData.failureReason = webhookData.failureReason;
        }
        else if (webhookData.status === MulticaixaService_1.MulticaixaPaymentStatus.EXPIRED) {
            updateData.expiredAt = new Date();
        }
        const updatedPayment = await models_1.PaymentModel.update(payment.id, updateData);
        res.success({ received: true }, 'Webhook processed successfully');
    }
    catch (error) {
        console.error('Error processing Multicaixa webhook:', error);
        const message = error instanceof Error ? error.message : 'Unknown error';
        res.error('Failed to process webhook', 500, {
            details: process.env.NODE_ENV === 'development' ? message : undefined
        });
    }
});
router.post('/:id/check-status', async (req, res) => {
    try {
        const paymentId = parseInt(req.params.id);
        if (isNaN(paymentId)) {
            return res.error('Invalid payment ID', 400);
        }
        const payment = await models_1.PaymentModel.findById(paymentId);
        if (!payment) {
            return res.error('Payment not found', 404);
        }
        if (!payment.multicaixaTransactionId) {
            return res.error('No Multicaixa transaction ID found', 400);
        }
        const currentMulticaixaStatus = await MulticaixaService_1.MulticaixaService.getPaymentStatus(payment.multicaixaTransactionId);
        const currentMappedStatus = mapMulticaixaStatusToPaymentStatus(currentMulticaixaStatus);
        if (currentMappedStatus !== payment.status) {
            const updateData = { status: currentMappedStatus };
            if (currentMulticaixaStatus === MulticaixaService_1.MulticaixaPaymentStatus.COMPLETED) {
                updateData.paidAt = new Date();
            }
            else if (currentMulticaixaStatus === MulticaixaService_1.MulticaixaPaymentStatus.FAILED) {
                updateData.failedAt = new Date();
            }
            else if (currentMulticaixaStatus === MulticaixaService_1.MulticaixaPaymentStatus.EXPIRED) {
                updateData.expiredAt = new Date();
            }
            const updatedPayment = await models_1.PaymentModel.update(paymentId, updateData);
            return res.success(updatedPayment, 'Payment status updated');
        }
        res.success(payment, 'Payment status checked');
    }
    catch (error) {
        console.error('Error checking payment status:', error);
        const message = error instanceof Error ? error.message : 'Unknown error';
        res.error('Failed to check payment status', 500, {
            details: process.env.NODE_ENV === 'development' ? message : undefined
        });
    }
});
router.get('/:id', async (req, res) => {
    try {
        const paymentId = parseInt(req.params.id);
        if (isNaN(paymentId)) {
            return res.error('Invalid payment ID', 400);
        }
        const payment = await models_1.PaymentModel.findById(paymentId);
        if (!payment) {
            return res.error('Payment not found', 404);
        }
        const invoices = await models_1.InvoiceModel.findByPaymentId(paymentId);
        const refunds = await models_1.RefundModel.findByPaymentId(paymentId);
        res.success({
            payment,
            invoices,
            refunds
        }, 'Payment details retrieved successfully');
    }
    catch (error) {
        console.error('Error fetching payment:', error);
        const message = error instanceof Error ? error.message : 'Unknown error';
        res.error('Failed to fetch payment', 500, {
            details: process.env.NODE_ENV === 'development' ? message : undefined
        });
    }
});
router.get('/order/:orderId', async (req, res) => {
    try {
        const orderId = parseInt(req.params.orderId);
        if (isNaN(orderId)) {
            return res.error('Invalid order ID', 400);
        }
        const payments = await models_1.PaymentModel.findByOrderId(orderId);
        res.success(payments, 'Payments retrieved successfully');
    }
    catch (error) {
        console.error('Error fetching payments for order:', error);
        const message = error instanceof Error ? error.message : 'Unknown error';
        res.error('Failed to fetch payments for order', 500, {
            details: process.env.NODE_ENV === 'development' ? message : undefined
        });
    }
});
router.post('/:id/refund', adminAuth_1.adminAuth, refundRateLimit, async (req, res) => {
    try {
        const paymentId = parseInt(req.params.id);
        const { amount, reason, description } = req.body;
        const adminId = req.admin?.id;
        if (isNaN(paymentId)) {
            return res.status(400).json({
                error: 'Invalid payment ID'
            });
        }
        const payment = await models_1.PaymentModel.findById(paymentId);
        if (!payment) {
            return res.status(404).json({
                error: 'Payment not found'
            });
        }
        if (payment.status !== types_1.PaymentStatus.COMPLETED) {
            return res.error('Payment must be completed to be refunded', 400);
        }
        const totalRefunded = await models_1.RefundModel.getTotalRefundedForPayment(paymentId);
        const maxRefundAmount = payment.amount - totalRefunded;
        if (amount > maxRefundAmount) {
            return res.status(400).json({
                error: `Refund amount cannot exceed ${maxRefundAmount} cents (already refunded: ${totalRefunded} cents)`
            });
        }
        const refundData = {
            paymentId,
            amount,
            currency: payment.currency,
            reason: reason || 'requested_by_customer',
            status: types_1.RefundStatus.PENDING,
            description: description || `Refund for payment #${paymentId}`
        };
        if (adminId !== undefined) {
            refundData.createdBy = adminId;
        }
        const refund = await models_1.RefundModel.create(refundData);
        res.success(refund, 'Refund request created successfully');
    }
    catch (error) {
        console.error('Error creating refund:', error);
        const message = error instanceof Error ? error.message : 'Unknown error';
        res.error('Failed to create refund', 500, {
            details: process.env.NODE_ENV === 'development' ? message : undefined
        });
    }
});
router.get('/', adminAuth_1.adminAuth, async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 50;
        const status = req.query.status;
        const offset = (page - 1) * limit;
        const result = await models_1.PaymentModel.findAll(offset, limit, status);
        res.json({
            success: true,
            data: result.payments,
            pagination: {
                page,
                limit,
                total: result.total,
                pages: Math.ceil(result.total / limit)
            }
        });
    }
    catch (error) {
        console.error('Error fetching payments:', error);
        const message = error instanceof Error ? error.message : 'Unknown error';
        res.error('Failed to fetch payments', 500, {
            details: process.env.NODE_ENV === 'development' ? message : undefined
        });
    }
});
router.get('/stats', adminAuth_1.adminAuth, async (req, res) => {
    try {
        const stats = await models_1.PaymentModel.getStatistics();
        res.json({
            success: true,
            data: stats
        });
    }
    catch (error) {
        console.error('Error fetching payment statistics:', error);
        const message = error instanceof Error ? error.message : 'Unknown error';
        res.error('Failed to fetch payment statistics', 500, {
            details: process.env.NODE_ENV === 'development' ? message : undefined
        });
    }
});
exports.default = router;
//# sourceMappingURL=payments.js.map