"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const Admin_1 = require("../../models/Admin");
const AdminActivityLog_1 = require("../../models/AdminActivityLog");
const adminAuth_1 = require("../../middleware/adminAuth");
const types_1 = require("../../types");
const router = (0, express_1.Router)();
router.post('/login', async (req, res) => {
    try {
        const { email, password } = req.body;
        if (!email || !password) {
            return res.error('Email e senha são obrigatórios', 400);
        }
        const authResult = await Admin_1.AdminModel.authenticate({ email, password });
        if (!authResult) {
            const logData = {
                adminId: 0,
                adminEmail: email,
                action: 'login_failed',
                resource: 'auth',
                details: { reason: 'invalid_credentials' }
            };
            if (req.ip)
                logData.ipAddress = req.ip;
            if (req.get('User-Agent'))
                logData.userAgent = req.get('User-Agent');
            await AdminActivityLog_1.AdminActivityLogModel.create(logData);
            return res.error('Credenciais inválidas', 401);
        }
        await AdminActivityLog_1.AdminActivityLogModel.logAction(authResult.admin.id, authResult.admin.email, 'login_success', 'auth', undefined, { loginTime: new Date() }, req);
        res.success(authResult, 'Login realizado com sucesso');
    }
    catch (error) {
        console.error('Admin login error:', error);
        res.error('Erro interno do servidor', 500);
    }
});
router.post('/logout', adminAuth_1.authenticateAdmin, (0, adminAuth_1.logAdminActivity)('logout', 'auth'), async (req, res) => {
    try {
        res.success(null, 'Logout realizado com sucesso');
    }
    catch (error) {
        console.error('Admin logout error:', error);
        res.error('Erro interno do servidor', 500);
    }
});
router.get('/me', adminAuth_1.authenticateAdmin, async (req, res) => {
    try {
        if (!req.admin) {
            return res.error('Administrador não encontrado', 404);
        }
        const admin = await Admin_1.AdminModel.findById(req.admin.id);
        if (!admin) {
            return res.error('Administrador não encontrado', 404);
        }
        res.success(admin, 'Informações do administrador');
    }
    catch (error) {
        console.error('Get admin info error:', error);
        res.error('Erro interno do servidor', 500);
    }
});
router.put('/profile', adminAuth_1.authenticateAdmin, (0, adminAuth_1.logAdminActivity)('update_profile', 'admin'), async (req, res) => {
    try {
        if (!req.admin) {
            return res.error('Administrador não encontrado', 404);
        }
        const { name, email, currentPassword, newPassword } = req.body;
        if (newPassword) {
            if (!currentPassword) {
                return res.error('Senha atual é obrigatória para alterar a senha', 400);
            }
            const adminWithPassword = await Admin_1.AdminModel.findByEmailWithPassword(req.admin.email);
            if (!adminWithPassword) {
                return res.error('Administrador não encontrado', 404);
            }
            const bcrypt = require('bcryptjs');
            const isCurrentPasswordValid = await bcrypt.compare(currentPassword, adminWithPassword.password);
            if (!isCurrentPasswordValid) {
                return res.error('Senha atual incorreta', 400);
            }
        }
        const updateData = {};
        if (name)
            updateData.name = name;
        if (email && email !== req.admin.email) {
            const existingAdmin = await Admin_1.AdminModel.findByEmail(email);
            if (existingAdmin && existingAdmin.id !== req.admin.id) {
                return res.error('Email já está em uso', 400);
            }
            updateData.email = email;
        }
        if (newPassword)
            updateData.password = newPassword;
        const updatedAdmin = await Admin_1.AdminModel.update(req.admin.id, updateData);
        if (!updatedAdmin) {
            return res.error('Erro ao atualizar perfil', 500);
        }
        res.success(updatedAdmin, 'Perfil atualizado com sucesso');
    }
    catch (error) {
        console.error('Update admin profile error:', error);
        res.error('Erro interno do servidor', 500);
    }
});
router.post('/refresh', adminAuth_1.authenticateAdmin, async (req, res) => {
    try {
        if (!req.admin) {
            return res.error('Administrador não encontrado', 404);
        }
        const admin = await Admin_1.AdminModel.findById(req.admin.id);
        if (!admin || admin.status !== types_1.AdminStatus.ACTIVE) {
            return res.error('Administrador não encontrado ou inativo', 404);
        }
        const jwt = require('jsonwebtoken');
        const token = jwt.sign({
            adminId: admin.id,
            email: admin.email,
            role: admin.role
        }, process.env.JWT_SECRET || 'default-secret', { expiresIn: process.env.JWT_EXPIRES_IN || '7d' });
        res.success({
            admin,
            token,
            expiresIn: process.env.JWT_EXPIRES_IN || '7d'
        }, 'Token renovado com sucesso');
    }
    catch (error) {
        console.error('Refresh token error:', error);
        res.error('Erro interno do servidor', 500);
    }
});
router.post('/change-password', adminAuth_1.authenticateAdmin, (0, adminAuth_1.logAdminActivity)('change_password', 'admin'), async (req, res) => {
    try {
        if (!req.admin) {
            return res.error('Administrador não encontrado', 404);
        }
        const { currentPassword, newPassword, confirmPassword } = req.body;
        if (!currentPassword || !newPassword || !confirmPassword) {
            return res.error('Todos os campos são obrigatórios', 400);
        }
        if (newPassword !== confirmPassword) {
            return res.error('Nova senha e confirmação não coincidem', 400);
        }
        if (newPassword.length < 8) {
            return res.error('Nova senha deve ter pelo menos 8 caracteres', 400);
        }
        const adminWithPassword = await Admin_1.AdminModel.findByEmailWithPassword(req.admin.email);
        if (!adminWithPassword) {
            return res.error('Administrador não encontrado', 404);
        }
        const bcrypt = require('bcryptjs');
        const isCurrentPasswordValid = await bcrypt.compare(currentPassword, adminWithPassword.password);
        if (!isCurrentPasswordValid) {
            return res.error('Senha atual incorreta', 400);
        }
        await Admin_1.AdminModel.update(req.admin.id, { password: newPassword });
        res.success(null, 'Senha alterada com sucesso');
    }
    catch (error) {
        console.error('Change password error:', error);
        res.error('Erro interno do servidor', 500);
    }
});
router.get('/activity', adminAuth_1.authenticateAdmin, async (req, res) => {
    try {
        if (!req.admin) {
            return res.error('Administrador não encontrado', 404);
        }
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 20;
        const result = await AdminActivityLog_1.AdminActivityLogModel.findByAdminId(req.admin.id, { page, limit });
        res.success(result, 'Histórico de atividades');
    }
    catch (error) {
        console.error('Get admin activity error:', error);
        res.error('Erro interno do servidor', 500);
    }
});
exports.default = router;
//# sourceMappingURL=auth.js.map