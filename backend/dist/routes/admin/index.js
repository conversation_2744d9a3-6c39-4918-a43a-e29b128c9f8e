"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const auth_1 = __importDefault(require("./auth"));
const dashboard_1 = __importDefault(require("./dashboard"));
const admins_1 = __importDefault(require("./admins"));
const orders_1 = __importDefault(require("./orders"));
const adminAuth_1 = require("../../middleware/adminAuth");
const router = (0, express_1.Router)();
router.use((0, adminAuth_1.adminRateLimit)(200, 15 * 60 * 1000));
router.get('/', (req, res) => {
    res.success({
        name: 'WePrint AI Admin API',
        description: 'Administrative interface for WePrint AI platform',
        version: '1.0.0',
        endpoints: {
            auth: {
                login: 'POST /api/admin/auth/login',
                logout: 'POST /api/admin/auth/logout',
                me: 'GET /api/admin/auth/me',
                profile: 'PUT /api/admin/auth/profile',
                refresh: 'POST /api/admin/auth/refresh',
                changePassword: 'POST /api/admin/auth/change-password',
                activity: 'GET /api/admin/auth/activity'
            },
            dashboard: {
                stats: 'GET /api/admin/dashboard/stats',
                overview: 'GET /api/admin/dashboard/overview',
                recentActivity: 'GET /api/admin/dashboard/recent-activity',
                adminActivity: 'GET /api/admin/dashboard/admin-activity',
                adminActivityStats: 'GET /api/admin/dashboard/admin-activity/stats',
                systemHealth: 'GET /api/admin/dashboard/system-health',
                performance: 'GET /api/admin/dashboard/performance',
                cleanup: 'POST /api/admin/dashboard/cleanup',
                export: 'GET /api/admin/dashboard/export'
            },
            admins: {
                list: 'GET /api/admin/admins',
                get: 'GET /api/admin/admins/:id',
                create: 'POST /api/admin/admins',
                update: 'PUT /api/admin/admins/:id',
                delete: 'DELETE /api/admin/admins/:id',
                updateStatus: 'PUT /api/admin/admins/:id/status',
                updateRole: 'PUT /api/admin/admins/:id/role',
                resetPassword: 'POST /api/admin/admins/:id/reset-password'
            },
            orders: {
                list: 'GET /api/admin/orders',
                stats: 'GET /api/admin/orders/stats',
                get: 'GET /api/admin/orders/:id',
                update: 'PUT /api/admin/orders/:id',
                updateStatus: 'PUT /api/admin/orders/:id/status',
                delete: 'DELETE /api/admin/orders/:id',
                addNotes: 'POST /api/admin/orders/:id/notes',
                getFiles: 'GET /api/admin/orders/:id/files',
                bulkUpdate: 'POST /api/admin/orders/bulk-update'
            },
            files: {
                list: 'GET /api/admin/files',
                stats: 'GET /api/admin/files/stats',
                get: 'GET /api/admin/files/:id',
                download: 'GET /api/admin/files/:id/download',
                delete: 'DELETE /api/admin/files/:id',
                bulkDelete: 'POST /api/admin/files/bulk-delete'
            },
            notifications: {
                list: 'GET /api/admin/notifications',
                stats: 'GET /api/admin/notifications/stats',
                get: 'GET /api/admin/notifications/:id',
                create: 'POST /api/admin/notifications',
                send: 'POST /api/admin/notifications/send',
                update: 'PUT /api/admin/notifications/:id',
                delete: 'DELETE /api/admin/notifications/:id',
                bulkUpdate: 'POST /api/admin/notifications/bulk-update',
                testEmail: 'POST /api/admin/notifications/test-email'
            }
        },
        authentication: {
            type: 'Bearer Token (JWT)',
            header: 'Authorization: Bearer <token>',
            loginEndpoint: '/api/admin/auth/login'
        },
        permissions: {
            admin: 'Basic administrative access',
            super_admin: 'Full administrative access including user management'
        },
        rateLimit: {
            requests: 200,
            window: '15 minutes',
            scope: 'per admin user'
        }
    }, 'WePrint AI Admin API');
});
router.get('/health', (req, res) => {
    res.success({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        environment: process.env.NODE_ENV || 'development',
        version: '1.0.0',
        services: {
            database: 'connected',
            email: 'configured',
            websocket: 'active',
            fileStorage: 'available'
        }
    }, 'Admin API is healthy');
});
router.get('/status', adminAuth_1.authenticateAdmin, (req, res) => {
    if (!req.admin) {
        return res.error('Sessão não encontrada', 401);
    }
    res.success({
        authenticated: true,
        admin: {
            id: req.admin.id,
            email: req.admin.email,
            name: req.admin.name,
            role: req.admin.role,
            status: req.admin.status
        },
        session: {
            active: true,
            timestamp: new Date().toISOString()
        }
    }, 'Status da sessão administrativa');
});
router.use('/auth', auth_1.default);
router.use('/dashboard', dashboard_1.default);
router.use('/admins', admins_1.default);
router.use('/orders', orders_1.default);
router.use('/files', (req, res) => {
    res.error('Endpoint de arquivos ainda não implementado', 501);
});
router.use('/notifications', (req, res) => {
    res.error('Endpoint de notificações administrativas ainda não implementado', 501);
});
router.use('*', (req, res) => {
    res.error('Endpoint administrativo não encontrado', 404);
});
exports.default = router;
//# sourceMappingURL=index.js.map