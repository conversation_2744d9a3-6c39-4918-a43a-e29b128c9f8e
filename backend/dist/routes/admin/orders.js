"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const Order_1 = require("../../models/Order");
const File_1 = require("../../models/File");
const NotificationService_1 = require("../../services/NotificationService");
const adminAuth_1 = require("../../middleware/adminAuth");
const types_1 = require("../../types");
const router = (0, express_1.Router)();
router.get('/', adminAuth_1.authenticateAdmin, (0, adminAuth_1.logAdminActivity)('list_orders', 'orders'), async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 20;
        const status = req.query.status;
        const customerEmail = req.query.customerEmail;
        const search = req.query.search;
        let dateFrom;
        let dateTo;
        if (req.query.dateFrom) {
            dateFrom = new Date(req.query.dateFrom);
        }
        if (req.query.dateTo) {
            dateTo = new Date(req.query.dateTo);
        }
        const result = await Order_1.OrderModel.findAll({
            limit,
            offset: (page - 1) * limit,
            status,
            customerEmail
        });
        res.success({
            orders: result.orders,
            total: result.total,
            page,
            limit,
            totalPages: Math.ceil(result.total / limit)
        }, 'Lista de pedidos');
    }
    catch (error) {
        console.error('Get orders error:', error);
        res.error('Erro ao obter lista de pedidos', 500);
    }
});
router.get('/stats', adminAuth_1.authenticateAdmin, (0, adminAuth_1.logAdminActivity)('view_order_stats', 'orders'), async (req, res) => {
    try {
        const stats = await Order_1.OrderModel.getStatistics();
        res.success(stats, 'Estatísticas de pedidos');
    }
    catch (error) {
        console.error('Get order stats error:', error);
        res.error('Erro ao obter estatísticas de pedidos', 500);
    }
});
router.get('/:id', adminAuth_1.authenticateAdmin, (0, adminAuth_1.logAdminActivity)('view_order', 'orders'), async (req, res) => {
    try {
        const id = parseInt(req.params.id || '0');
        if (isNaN(id) || id === 0) {
            return res.error('ID inválido', 400);
        }
        const order = await Order_1.OrderModel.findById(id);
        if (!order) {
            return res.error('Pedido não encontrado', 404);
        }
        const files = await File_1.FileModel.findByOrderId(id);
        const orderWithFiles = {
            ...order,
            files
        };
        res.success(orderWithFiles, 'Detalhes do pedido');
    }
    catch (error) {
        console.error('Get order error:', error);
        res.error('Erro ao obter pedido', 500);
    }
});
router.put('/:id', adminAuth_1.authenticateAdmin, (0, adminAuth_1.canModifyResource)('orders'), (0, adminAuth_1.logAdminActivity)('update_order', 'orders'), async (req, res) => {
    try {
        const id = parseInt(req.params.id || '0');
        if (isNaN(id) || id === 0) {
            return res.error('ID inválido', 400);
        }
        const updateData = req.body;
        if (updateData.status && !Object.values(types_1.OrderStatus).includes(updateData.status)) {
            return res.error('Status inválido', 400);
        }
        const currentOrder = await Order_1.OrderModel.findById(id);
        if (!currentOrder) {
            return res.error('Pedido não encontrado', 404);
        }
        const updatedOrder = await Order_1.OrderModel.update(id, updateData);
        if (!updatedOrder) {
            return res.error('Erro ao atualizar pedido', 500);
        }
        if (updateData.status && updateData.status !== currentOrder.status && req.admin) {
            try {
                const notificationService = NotificationService_1.NotificationService.getInstance();
                await notificationService.sendOrderStatusNotification(updatedOrder.id, updatedOrder.status, updatedOrder.customerEmail);
            }
            catch (notificationError) {
                console.error('Failed to send status notification:', notificationError);
            }
        }
        res.success(updatedOrder, 'Pedido atualizado com sucesso');
    }
    catch (error) {
        console.error('Update order error:', error);
        res.error('Erro ao atualizar pedido', 500);
    }
});
router.put('/:id/status', adminAuth_1.authenticateAdmin, (0, adminAuth_1.canModifyResource)('orders'), (0, adminAuth_1.logAdminActivity)('update_order_status', 'orders'), async (req, res) => {
    try {
        const id = parseInt(req.params.id || '0');
        const { status, notes } = req.body;
        if (isNaN(id) || id === 0) {
            return res.error('ID inválido', 400);
        }
        if (!status || !Object.values(types_1.OrderStatus).includes(status)) {
            return res.error('Status inválido', 400);
        }
        const currentOrder = await Order_1.OrderModel.findById(id);
        if (!currentOrder) {
            return res.error('Pedido não encontrado', 404);
        }
        const updateData = { status };
        if (notes) {
            updateData.notes = notes;
        }
        const updatedOrder = await Order_1.OrderModel.update(id, updateData);
        if (!updatedOrder) {
            return res.error('Erro ao atualizar status do pedido', 500);
        }
        if (req.admin) {
            try {
                const notificationService = NotificationService_1.NotificationService.getInstance();
                await notificationService.sendOrderStatusNotification(updatedOrder.id, updatedOrder.status, updatedOrder.customerEmail);
            }
            catch (notificationError) {
                console.error('Failed to send status notification:', notificationError);
            }
        }
        res.success(updatedOrder, 'Status do pedido atualizado com sucesso');
    }
    catch (error) {
        console.error('Update order status error:', error);
        res.error('Erro ao atualizar status do pedido', 500);
    }
});
router.delete('/:id', adminAuth_1.authenticateAdmin, (0, adminAuth_1.canModifyResource)('orders'), (0, adminAuth_1.logAdminActivity)('delete_order', 'orders'), async (req, res) => {
    try {
        const id = parseInt(req.params.id || '0');
        if (isNaN(id) || id === 0) {
            return res.error('ID inválido', 400);
        }
        const deleted = await Order_1.OrderModel.delete(id);
        if (!deleted) {
            return res.error('Pedido não encontrado', 404);
        }
        res.success(null, 'Pedido excluído com sucesso');
    }
    catch (error) {
        console.error('Delete order error:', error);
        res.error('Erro ao excluir pedido', 500);
    }
});
router.post('/:id/notes', adminAuth_1.authenticateAdmin, (0, adminAuth_1.canModifyResource)('orders'), (0, adminAuth_1.logAdminActivity)('add_order_notes', 'orders'), async (req, res) => {
    try {
        const id = parseInt(req.params.id || '0');
        const { notes } = req.body;
        if (isNaN(id) || id === 0) {
            return res.error('ID inválido', 400);
        }
        if (!notes || notes.trim().length === 0) {
            return res.error('Notas são obrigatórias', 400);
        }
        const updatedOrder = await Order_1.OrderModel.update(id, { notes: notes.trim() });
        if (!updatedOrder) {
            return res.error('Pedido não encontrado', 404);
        }
        res.success(updatedOrder, 'Notas adicionadas ao pedido com sucesso');
    }
    catch (error) {
        console.error('Add order notes error:', error);
        res.error('Erro ao adicionar notas ao pedido', 500);
    }
});
router.get('/:id/files', adminAuth_1.authenticateAdmin, (0, adminAuth_1.logAdminActivity)('view_order_files', 'orders'), async (req, res) => {
    try {
        const id = parseInt(req.params.id || '0');
        if (isNaN(id) || id === 0) {
            return res.error('ID inválido', 400);
        }
        const order = await Order_1.OrderModel.findById(id);
        if (!order) {
            return res.error('Pedido não encontrado', 404);
        }
        const files = await File_1.FileModel.findByOrderId(id);
        res.success(files, 'Arquivos do pedido');
    }
    catch (error) {
        console.error('Get order files error:', error);
        res.error('Erro ao obter arquivos do pedido', 500);
    }
});
router.post('/bulk-update', adminAuth_1.authenticateAdmin, (0, adminAuth_1.canModifyResource)('orders'), (0, adminAuth_1.logAdminActivity)('bulk_update_orders', 'orders'), async (req, res) => {
    try {
        const { orderIds, updateData } = req.body;
        if (!orderIds || !Array.isArray(orderIds) || orderIds.length === 0) {
            return res.error('IDs de pedidos são obrigatórios', 400);
        }
        if (!updateData || Object.keys(updateData).length === 0) {
            return res.error('Dados de atualização são obrigatórios', 400);
        }
        if (updateData.status && !Object.values(types_1.OrderStatus).includes(updateData.status)) {
            return res.error('Status inválido', 400);
        }
        const results = [];
        const errors = [];
        for (const orderId of orderIds) {
            try {
                const updatedOrder = await Order_1.OrderModel.update(orderId, updateData);
                if (updatedOrder) {
                    results.push(updatedOrder);
                    if (updateData.status && req.admin) {
                        try {
                            const notificationService = NotificationService_1.NotificationService.getInstance();
                            await notificationService.sendOrderStatusNotification(updatedOrder.id, updatedOrder.status, updatedOrder.customerEmail);
                        }
                        catch (notificationError) {
                            console.error('Failed to send notification for order', orderId, notificationError);
                        }
                    }
                }
                else {
                    errors.push({ orderId, error: 'Pedido não encontrado' });
                }
            }
            catch (error) {
                errors.push({ orderId, error: error instanceof Error ? error.message : 'Erro desconhecido' });
            }
        }
        res.success({
            updated: results,
            errors,
            totalProcessed: orderIds.length,
            successCount: results.length,
            errorCount: errors.length
        }, 'Atualização em lote concluída');
    }
    catch (error) {
        console.error('Bulk update orders error:', error);
        res.error('Erro na atualização em lote', 500);
    }
});
exports.default = router;
//# sourceMappingURL=orders.js.map