"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const Admin_1 = require("../../models/Admin");
const adminAuth_1 = require("../../middleware/adminAuth");
const types_1 = require("../../types");
const router = (0, express_1.Router)();
router.get('/', adminAuth_1.authenticateAdmin, adminAuth_1.requireSuperAdmin, (0, adminAuth_1.logAdminActivity)('list_admins', 'admins'), async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 20;
        const role = req.query.role;
        const status = req.query.status;
        const result = await Admin_1.AdminModel.findMany({
            page,
            limit,
            role,
            status
        });
        res.success(result, 'Lista de administradores');
    }
    catch (error) {
        console.error('Get admins error:', error);
        res.error('Erro ao obter lista de administradores', 500);
    }
});
router.get('/:id', adminAuth_1.authenticateAdmin, adminAuth_1.requireSuperAdmin, (0, adminAuth_1.logAdminActivity)('view_admin', 'admins'), async (req, res) => {
    try {
        const id = parseInt(req.params.id);
        if (isNaN(id)) {
            return res.error('ID inválido', 400);
        }
        const admin = await Admin_1.AdminModel.findById(id);
        if (!admin) {
            return res.error('Administrador não encontrado', 404);
        }
        res.success(admin, 'Detalhes do administrador');
    }
    catch (error) {
        console.error('Get admin error:', error);
        res.error('Erro ao obter administrador', 500);
    }
});
router.post('/', adminAuth_1.authenticateAdmin, adminAuth_1.requireSuperAdmin, (0, adminAuth_1.canModifyResource)('admins'), (0, adminAuth_1.logAdminActivity)('create_admin', 'admins'), async (req, res) => {
    try {
        const { email, name, password, role, status } = req.body;
        if (!email || !name || !password || !role) {
            return res.error('Email, nome, senha e papel são obrigatórios', 400);
        }
        if (!Object.values(types_1.AdminRole).includes(role)) {
            return res.error('Papel de administrador inválido', 400);
        }
        if (status && !Object.values(types_1.AdminStatus).includes(status)) {
            return res.error('Status de administrador inválido', 400);
        }
        if (password.length < 8) {
            return res.error('Senha deve ter pelo menos 8 caracteres', 400);
        }
        const existingAdmin = await Admin_1.AdminModel.existsByEmail(email);
        if (existingAdmin) {
            return res.error('Email já está em uso', 400);
        }
        const admin = await Admin_1.AdminModel.create({
            email,
            name,
            password,
            role,
            status: status || types_1.AdminStatus.ACTIVE
        });
        res.success(admin, 'Administrador criado com sucesso', 201);
    }
    catch (error) {
        console.error('Create admin error:', error);
        res.error('Erro ao criar administrador', 500);
    }
});
router.put('/:id', adminAuth_1.authenticateAdmin, adminAuth_1.requireSuperAdmin, (0, adminAuth_1.canModifyResource)('admins'), adminAuth_1.preventSelfModification, (0, adminAuth_1.logAdminActivity)('update_admin', 'admins'), async (req, res) => {
    try {
        const id = parseInt(req.params.id);
        if (isNaN(id)) {
            return res.error('ID inválido', 400);
        }
        const { email, name, password, role, status } = req.body;
        if (role && !Object.values(types_1.AdminRole).includes(role)) {
            return res.error('Papel de administrador inválido', 400);
        }
        if (status && !Object.values(types_1.AdminStatus).includes(status)) {
            return res.error('Status de administrador inválido', 400);
        }
        if (password && password.length < 8) {
            return res.error('Senha deve ter pelo menos 8 caracteres', 400);
        }
        if (email) {
            const existingAdmin = await Admin_1.AdminModel.findByEmail(email);
            if (existingAdmin && existingAdmin.id !== id) {
                return res.error('Email já está em uso', 400);
            }
        }
        const updatedAdmin = await Admin_1.AdminModel.update(id, {
            email,
            name,
            password,
            role,
            status
        });
        if (!updatedAdmin) {
            return res.error('Administrador não encontrado', 404);
        }
        res.success(updatedAdmin, 'Administrador atualizado com sucesso');
    }
    catch (error) {
        console.error('Update admin error:', error);
        res.error('Erro ao atualizar administrador', 500);
    }
});
router.delete('/:id', adminAuth_1.authenticateAdmin, adminAuth_1.requireSuperAdmin, (0, adminAuth_1.canModifyResource)('admins'), adminAuth_1.preventSelfModification, (0, adminAuth_1.logAdminActivity)('delete_admin', 'admins'), async (req, res) => {
    try {
        const id = parseInt(req.params.id);
        if (isNaN(id)) {
            return res.error('ID inválido', 400);
        }
        if (req.admin && req.admin.id === id) {
            return res.error('Não é possível excluir a própria conta', 403);
        }
        const deleted = await Admin_1.AdminModel.delete(id);
        if (!deleted) {
            return res.error('Administrador não encontrado', 404);
        }
        res.success(null, 'Administrador excluído com sucesso');
    }
    catch (error) {
        console.error('Delete admin error:', error);
        res.error('Erro ao excluir administrador', 500);
    }
});
router.put('/:id/status', adminAuth_1.authenticateAdmin, adminAuth_1.requireSuperAdmin, (0, adminAuth_1.canModifyResource)('admins'), adminAuth_1.preventSelfModification, (0, adminAuth_1.logAdminActivity)('update_admin_status', 'admins'), async (req, res) => {
    try {
        const id = parseInt(req.params.id);
        const { status } = req.body;
        if (isNaN(id)) {
            return res.error('ID inválido', 400);
        }
        if (!status || !Object.values(types_1.AdminStatus).includes(status)) {
            return res.error('Status inválido', 400);
        }
        if (req.admin && req.admin.id === id) {
            return res.error('Não é possível alterar o próprio status', 403);
        }
        const updatedAdmin = await Admin_1.AdminModel.update(id, { status });
        if (!updatedAdmin) {
            return res.error('Administrador não encontrado', 404);
        }
        res.success(updatedAdmin, 'Status do administrador atualizado com sucesso');
    }
    catch (error) {
        console.error('Update admin status error:', error);
        res.error('Erro ao atualizar status do administrador', 500);
    }
});
router.put('/:id/role', adminAuth_1.authenticateAdmin, adminAuth_1.requireSuperAdmin, (0, adminAuth_1.canModifyResource)('admins'), adminAuth_1.preventSelfModification, (0, adminAuth_1.logAdminActivity)('update_admin_role', 'admins'), async (req, res) => {
    try {
        const id = parseInt(req.params.id);
        const { role } = req.body;
        if (isNaN(id)) {
            return res.error('ID inválido', 400);
        }
        if (!role || !Object.values(types_1.AdminRole).includes(role)) {
            return res.error('Papel inválido', 400);
        }
        if (req.admin && req.admin.id === id) {
            return res.error('Não é possível alterar o próprio papel', 403);
        }
        const updatedAdmin = await Admin_1.AdminModel.update(id, { role });
        if (!updatedAdmin) {
            return res.error('Administrador não encontrado', 404);
        }
        res.success(updatedAdmin, 'Papel do administrador atualizado com sucesso');
    }
    catch (error) {
        console.error('Update admin role error:', error);
        res.error('Erro ao atualizar papel do administrador', 500);
    }
});
router.post('/:id/reset-password', adminAuth_1.authenticateAdmin, adminAuth_1.requireSuperAdmin, (0, adminAuth_1.canModifyResource)('admins'), (0, adminAuth_1.logAdminActivity)('reset_admin_password', 'admins'), async (req, res) => {
    try {
        const id = parseInt(req.params.id);
        const { newPassword } = req.body;
        if (isNaN(id)) {
            return res.error('ID inválido', 400);
        }
        if (!newPassword || newPassword.length < 8) {
            return res.error('Nova senha deve ter pelo menos 8 caracteres', 400);
        }
        const updatedAdmin = await Admin_1.AdminModel.update(id, { password: newPassword });
        if (!updatedAdmin) {
            return res.error('Administrador não encontrado', 404);
        }
        res.success(null, 'Senha do administrador redefinida com sucesso');
    }
    catch (error) {
        console.error('Reset admin password error:', error);
        res.error('Erro ao redefinir senha do administrador', 500);
    }
});
exports.default = router;
//# sourceMappingURL=admins.js.map