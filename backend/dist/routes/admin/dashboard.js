"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const DashboardService_1 = require("../../services/DashboardService");
const AdminActivityLog_1 = require("../../models/AdminActivityLog");
const adminAuth_1 = require("../../middleware/adminAuth");
const router = (0, express_1.Router)();
router.get('/stats', adminAuth_1.authenticateAdmin, (0, adminAuth_1.logAdminActivity)('view_dashboard_stats', 'dashboard'), async (req, res) => {
    try {
        const stats = await DashboardService_1.DashboardService.getDashboardStats();
        res.success(stats, 'Estatísticas do dashboard');
    }
    catch (error) {
        console.error('Get dashboard stats error:', error);
        res.error('Erro ao obter estatísticas do dashboard', 500);
    }
});
router.get('/overview', adminAuth_1.authenticateAdmin, (0, adminAuth_1.logAdminActivity)('view_dashboard_overview', 'dashboard'), async (req, res) => {
    try {
        const fullStats = await DashboardService_1.DashboardService.getDashboardStats();
        const overview = {
            overview: fullStats.overview,
            systemHealth: {
                uptime: fullStats.systemHealth.uptime,
                memoryUsage: fullStats.systemHealth.memoryUsage,
                databaseConnections: fullStats.systemHealth.databaseConnections
            }
        };
        res.success(overview, 'Visão geral do dashboard');
    }
    catch (error) {
        console.error('Get dashboard overview error:', error);
        res.error('Erro ao obter visão geral do dashboard', 500);
    }
});
router.get('/recent-activity', adminAuth_1.authenticateAdmin, (0, adminAuth_1.logAdminActivity)('view_recent_activity', 'dashboard'), async (req, res) => {
    try {
        const limit = parseInt(req.query.limit) || 20;
        const activity = await DashboardService_1.DashboardService.getRecentActivity(limit);
        res.success(activity, 'Atividade recente do sistema');
    }
    catch (error) {
        console.error('Get recent activity error:', error);
        res.error('Erro ao obter atividade recente', 500);
    }
});
router.get('/admin-activity', adminAuth_1.authenticateAdmin, (0, adminAuth_1.logAdminActivity)('view_admin_activity', 'dashboard'), async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 50;
        const adminId = req.query.adminId ? parseInt(req.query.adminId) : undefined;
        const action = req.query.action;
        const resource = req.query.resource;
        let dateFrom;
        let dateTo;
        if (req.query.dateFrom) {
            dateFrom = new Date(req.query.dateFrom);
        }
        if (req.query.dateTo) {
            dateTo = new Date(req.query.dateTo);
        }
        const result = await AdminActivityLog_1.AdminActivityLogModel.findMany({
            page,
            limit,
            adminId,
            action,
            resource,
            dateFrom,
            dateTo
        });
        res.success(result, 'Logs de atividade dos administradores');
    }
    catch (error) {
        console.error('Get admin activity error:', error);
        res.error('Erro ao obter logs de atividade', 500);
    }
});
router.get('/admin-activity/stats', adminAuth_1.authenticateAdmin, (0, adminAuth_1.logAdminActivity)('view_admin_activity_stats', 'dashboard'), async (req, res) => {
    try {
        const days = parseInt(req.query.days) || 30;
        const stats = await AdminActivityLog_1.AdminActivityLogModel.getStatistics(days);
        res.success(stats, 'Estatísticas de atividade dos administradores');
    }
    catch (error) {
        console.error('Get admin activity stats error:', error);
        res.error('Erro ao obter estatísticas de atividade', 500);
    }
});
router.get('/system-health', adminAuth_1.authenticateAdmin, (0, adminAuth_1.logAdminActivity)('view_system_health', 'dashboard'), async (req, res) => {
    try {
        const fullStats = await DashboardService_1.DashboardService.getDashboardStats();
        const systemHealth = {
            ...fullStats.systemHealth,
            nodeVersion: process.version,
            platform: process.platform,
            architecture: process.arch,
            environment: process.env.NODE_ENV || 'development',
            processId: process.pid,
            startTime: new Date(Date.now() - (process.uptime() * 1000))
        };
        res.success(systemHealth, 'Informações de saúde do sistema');
    }
    catch (error) {
        console.error('Get system health error:', error);
        res.error('Erro ao obter informações do sistema', 500);
    }
});
router.get('/performance', adminAuth_1.authenticateAdmin, (0, adminAuth_1.logAdminActivity)('view_performance_metrics', 'dashboard'), async (req, res) => {
    try {
        const memUsage = process.memoryUsage();
        const cpuUsage = process.cpuUsage();
        let dbPerformance = null;
        try {
            const database = require('../../database').default;
            const dbQuery = `
          SELECT 
            count(*) as active_connections,
            (SELECT count(*) FROM pg_stat_activity WHERE state = 'active') as active_queries,
            (SELECT count(*) FROM pg_stat_activity WHERE state = 'idle') as idle_connections
          FROM pg_stat_activity 
          WHERE datname = current_database()
        `;
            const dbResult = await database.query(dbQuery);
            dbPerformance = dbResult.rows[0];
        }
        catch (dbError) {
            console.warn('Could not get database performance:', dbError);
        }
        const performance = {
            memory: {
                rss: memUsage.rss,
                heapTotal: memUsage.heapTotal,
                heapUsed: memUsage.heapUsed,
                external: memUsage.external,
                arrayBuffers: memUsage.arrayBuffers
            },
            cpu: {
                user: cpuUsage.user,
                system: cpuUsage.system
            },
            uptime: process.uptime(),
            database: dbPerformance,
            timestamp: new Date()
        };
        res.success(performance, 'Métricas de performance do sistema');
    }
    catch (error) {
        console.error('Get performance metrics error:', error);
        res.error('Erro ao obter métricas de performance', 500);
    }
});
router.post('/cleanup', adminAuth_1.authenticateAdmin, (0, adminAuth_1.logAdminActivity)('system_cleanup', 'dashboard'), async (req, res) => {
    try {
        const { cleanupLogs = false, cleanupNotifications = false, daysOld = 365 } = req.body;
        const results = {};
        if (cleanupLogs) {
            const deletedLogs = await AdminActivityLog_1.AdminActivityLogModel.cleanup(daysOld);
            results.deletedLogs = deletedLogs;
        }
        if (cleanupNotifications) {
            results.deletedNotifications = 0;
        }
        res.success(results, 'Limpeza do sistema concluída');
    }
    catch (error) {
        console.error('System cleanup error:', error);
        res.error('Erro durante a limpeza do sistema', 500);
    }
});
router.get('/export', adminAuth_1.authenticateAdmin, (0, adminAuth_1.logAdminActivity)('export_dashboard_data', 'dashboard'), async (req, res) => {
    try {
        const format = req.query.format || 'json';
        const type = req.query.type || 'stats';
        let data;
        switch (type) {
            case 'stats':
                data = await DashboardService_1.DashboardService.getDashboardStats();
                break;
            case 'activity':
                const activityResult = await AdminActivityLog_1.AdminActivityLogModel.findMany({ limit: 1000 });
                data = activityResult.logs;
                break;
            case 'recent':
                data = await DashboardService_1.DashboardService.getRecentActivity(100);
                break;
            default:
                return res.error('Tipo de exportação inválido', 400);
        }
        if (format === 'csv') {
            const csv = JSON.stringify(data);
            res.setHeader('Content-Type', 'text/csv');
            res.setHeader('Content-Disposition', `attachment; filename="dashboard-${type}-${Date.now()}.csv"`);
            return res.send(csv);
        }
        res.setHeader('Content-Type', 'application/json');
        res.setHeader('Content-Disposition', `attachment; filename="dashboard-${type}-${Date.now()}.json"`);
        res.send(JSON.stringify(data, null, 2));
    }
    catch (error) {
        console.error('Export dashboard data error:', error);
        res.error('Erro ao exportar dados do dashboard', 500);
    }
});
exports.default = router;
//# sourceMappingURL=dashboard.js.map