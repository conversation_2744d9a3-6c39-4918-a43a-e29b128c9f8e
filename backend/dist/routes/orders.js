"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.setOrderNotificationService = void 0;
const express_1 = require("express");
const models_1 = require("../models");
const types_1 = require("../types");
let notificationService = null;
const setOrderNotificationService = (service) => {
    notificationService = service;
};
exports.setOrderNotificationService = setOrderNotificationService;
const router = (0, express_1.Router)();
router.post('/', async (req, res) => {
    try {
        const orderData = req.body;
        if (!orderData.fileId || !orderData.format || !orderData.paperType || !orderData.finish) {
            return res.error('Missing required fields: fileId, format, paperType, finish', 400);
        }
        const order = await models_1.OrderModel.create(orderData);
        if (notificationService && order) {
            try {
                notificationService.emitOrderStatusChange(order, types_1.OrderStatus.PENDING);
            }
            catch (error) {
                console.error('Failed to send order creation notification:', error);
            }
        }
        res.success({
            order,
            priceBreakdown: orderData.pages ? models_1.OrderModel.calculatePrice({
                pages: orderData.pages,
                copies: orderData.copies || 1,
                format: orderData.format,
                paperType: orderData.paperType,
                finish: orderData.finish,
                ...(orderData.hasColor !== undefined && { hasColor: orderData.hasColor }),
                ...(orderData.complexity !== undefined && { complexity: orderData.complexity })
            }) : null
        }, 'Order created successfully');
    }
    catch (error) {
        console.error('Error creating order:', error);
        res.error('Failed to create order', 500);
    }
});
router.get('/', async (req, res) => {
    try {
        const { page = '1', limit = '50', status, customerEmail, orderBy = 'created_at', orderDirection = 'DESC' } = req.query;
        const pageNum = parseInt(page);
        const limitNum = parseInt(limit);
        const offset = (pageNum - 1) * limitNum;
        const result = await models_1.OrderModel.findAll({
            limit: limitNum,
            offset,
            status: status,
            customerEmail: customerEmail,
            orderBy: orderBy,
            orderDirection: orderDirection
        });
        res.success({
            orders: result.orders,
            pagination: {
                page: pageNum,
                limit: limitNum,
                total: result.total,
                totalPages: Math.ceil(result.total / limitNum)
            }
        }, 'Orders retrieved successfully');
    }
    catch (error) {
        console.error('Error fetching orders:', error);
        res.error('Failed to fetch orders', 500);
    }
});
router.get('/stats', async (req, res) => {
    try {
        const { period = 'month' } = req.query;
        const stats = await models_1.OrderModel.getStatistics(period);
        res.success({ stats }, 'Statistics retrieved successfully');
    }
    catch (error) {
        console.error('Error fetching statistics:', error);
        res.error('Failed to fetch statistics', 500);
    }
});
router.get('/ready', async (req, res) => {
    try {
        const { limit = '20' } = req.query;
        const orders = await models_1.OrderModel.getReadyOrders(parseInt(limit));
        res.success({ orders }, 'Ready orders retrieved successfully');
    }
    catch (error) {
        console.error('Error fetching ready orders:', error);
        res.error('Failed to fetch ready orders', 500);
    }
});
router.get('/overdue', async (req, res) => {
    try {
        const orders = await models_1.OrderModel.getOverdueOrders();
        res.success({ orders }, 'Overdue orders retrieved successfully');
    }
    catch (error) {
        console.error('Error fetching overdue orders:', error);
        res.error('Failed to fetch overdue orders', 500);
    }
});
router.get('/customer/:email', async (req, res) => {
    try {
        const { email } = req.params;
        const { limit = '50' } = req.query;
        const orders = await models_1.OrderModel.findByCustomerEmail(email, parseInt(limit));
        res.success({ orders }, 'Customer orders retrieved successfully');
    }
    catch (error) {
        console.error('Error fetching customer orders:', error);
        res.error('Failed to fetch customer orders', 500);
    }
});
router.get('/:id', async (req, res) => {
    try {
        const orderId = parseInt(req.params.id || '0');
        if (!orderId) {
            return res.error('Invalid order ID', 400);
        }
        const order = await models_1.OrderModel.findByIdWithFile(orderId);
        if (!order) {
            return res.error('Order not found', 404);
        }
        res.success({ order }, 'Order retrieved successfully');
    }
    catch (error) {
        console.error('Error fetching order:', error);
        res.error('Failed to fetch order', 500);
    }
});
router.put('/:id', async (req, res) => {
    try {
        const orderId = parseInt(req.params.id || '0');
        if (!orderId) {
            return res.error('Invalid order ID', 400);
        }
        const updateData = req.body;
        const order = await models_1.OrderModel.update(orderId, updateData);
        if (!order) {
            return res.error('Order not found', 404);
        }
        res.success({ order }, 'Order updated successfully');
    }
    catch (error) {
        console.error('Error updating order:', error);
        res.error('Failed to update order', 500);
    }
});
router.put('/:id/status', async (req, res) => {
    try {
        const orderId = parseInt(req.params.id || '0');
        const { status, notes } = req.body;
        if (!orderId) {
            return res.error('Invalid order ID', 400);
        }
        if (!status) {
            return res.error('Status is required', 400);
        }
        const validStatuses = [
            types_1.OrderStatus.PENDING,
            types_1.OrderStatus.CONFIRMED,
            types_1.OrderStatus.PROCESSING,
            types_1.OrderStatus.READY,
            types_1.OrderStatus.COMPLETED,
            types_1.OrderStatus.CANCELLED
        ];
        if (!validStatuses.includes(status)) {
            return res.error('Invalid status', 400);
        }
        const currentOrder = await models_1.OrderModel.findById(orderId);
        if (!currentOrder) {
            return res.error('Order not found', 404);
        }
        const order = await models_1.OrderModel.updateStatus(orderId, status, notes);
        if (!order) {
            return res.error('Order not found', 404);
        }
        if (notificationService && currentOrder.status !== status) {
            try {
                notificationService.emitOrderStatusChange(order, status);
            }
            catch (error) {
                console.error('Failed to send order status change notification:', error);
            }
        }
        res.success({ order }, 'Order status updated successfully');
    }
    catch (error) {
        console.error('Error updating order status:', error);
        if (error instanceof Error && error.message.includes('Invalid status transition')) {
            res.error(error.message, 400);
        }
        else {
            res.error('Failed to update order status', 500);
        }
    }
});
router.post('/calculate-price', async (req, res) => {
    try {
        const { pages, copies = 1, format, paperType, finish, hasColor, complexity } = req.body;
        if (!pages || !format || !paperType || !finish) {
            return res.error('Missing required fields: pages, format, paperType, finish', 400);
        }
        const priceCalculation = models_1.OrderModel.calculatePrice({
            pages,
            copies,
            format,
            paperType,
            finish,
            hasColor,
            complexity
        });
        res.success({ priceCalculation }, 'Price calculated successfully');
    }
    catch (error) {
        console.error('Error calculating price:', error);
        res.error('Failed to calculate price', 500);
    }
});
router.delete('/:id', async (req, res) => {
    try {
        const orderId = parseInt(req.params.id || '0');
        if (!orderId) {
            return res.error('Invalid order ID', 400);
        }
        const success = await models_1.OrderModel.delete(orderId);
        if (!success) {
            return res.error('Order not found', 404);
        }
        res.success('Order deleted successfully');
    }
    catch (error) {
        console.error('Error deleting order:', error);
        res.error('Failed to delete order', 500);
    }
});
exports.default = router;
//# sourceMappingURL=orders.js.map