declare global {
    namespace jest {
        interface Matchers<R> {
            toBeValidDate(): R;
            toBeValidEmail(): R;
            toBeValidPhone(): R;
        }
    }
}
export declare const testData: {
    validFile: {
        filename: string;
        originalName: string;
        mimetype: string;
        size: number;
        url: string;
    };
    validOrder: {
        fileId: number;
        customerName: string;
        customerEmail: string;
        customerPhone: string;
        format: string;
        paperType: string;
        finish: string;
        copies: number;
        notes: string;
    };
    invalidEmail: string;
    invalidPhone: string;
    validEmails: string[];
    validPhones: string[];
};
declare const _default: {};
export default _default;
//# sourceMappingURL=setup.d.ts.map