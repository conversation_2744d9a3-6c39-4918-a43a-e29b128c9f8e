"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.testData = void 0;
const dotenv_1 = __importDefault(require("dotenv"));
dotenv_1.default.config({ path: '.env.test' });
process.env.NODE_ENV = 'test';
process.env.LOG_LEVEL = 'silent';
jest.setTimeout(30000);
global.console = {
    ...console,
    log: jest.fn(),
    debug: jest.fn(),
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
};
expect.extend({
    toBeValidDate(received) {
        const pass = received instanceof Date && !isNaN(received.getTime());
        if (pass) {
            return {
                message: () => `expected ${received} not to be a valid date`,
                pass: true,
            };
        }
        else {
            return {
                message: () => `expected ${received} to be a valid date`,
                pass: false,
            };
        }
    },
    toBeValidEmail(received) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        const pass = typeof received === 'string' && emailRegex.test(received);
        if (pass) {
            return {
                message: () => `expected ${received} not to be a valid email`,
                pass: true,
            };
        }
        else {
            return {
                message: () => `expected ${received} to be a valid email`,
                pass: false,
            };
        }
    },
    toBeValidPhone(received) {
        const phoneRegex = /^(\+244\s?)?9\d{2}\s?\d{3}\s?\d{3}$/;
        const pass = typeof received === 'string' && phoneRegex.test(received.replace(/\s/g, ''));
        if (pass) {
            return {
                message: () => `expected ${received} not to be a valid phone number`,
                pass: true,
            };
        }
        else {
            return {
                message: () => `expected ${received} to be a valid phone number`,
                pass: false,
            };
        }
    },
});
afterEach(() => {
    jest.clearAllMocks();
});
exports.testData = {
    validFile: {
        filename: 'test-document.pdf',
        originalName: 'Test Document.pdf',
        mimetype: 'application/pdf',
        size: 1024000,
        url: '/uploads/test-document.pdf'
    },
    validOrder: {
        fileId: 1,
        customerName: 'João Silva',
        customerEmail: '<EMAIL>',
        customerPhone: '+244 923 456 789',
        format: 'A4',
        paperType: 'standard',
        finish: 'none',
        copies: 1,
        notes: 'Test order'
    },
    invalidEmail: 'invalid-email',
    invalidPhone: '123456',
    validEmails: [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
    ],
    validPhones: [
        '+244 923 456 789',
        '923456789',
        '+244923456789'
    ]
};
exports.default = {};
//# sourceMappingURL=setup.js.map