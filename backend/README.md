# README — Backend (Node.js/Express)

Este diretório contém o backend do MVP WePrint AI, desenvolvido em Node.js com Express e TypeScript.

## Estrutura Recomendada

```
backend/
├── src/
│   ├── controllers/
│   │   ├── FileUploadController.ts
│   │   ├── OrderController.ts
│   │   ├── AdminController.ts
│   │   └── StatusController.ts
│   ├── db.ts
│   ├── index.ts
│   └── types/
├── .env
├── tsconfig.json
├── package.json
└── README.md
```

## Comandos Básicos

- Instalar dependências:
  ```bash
  npm install
  ```
- Rodar em modo desenvolvimento:
  ```bash
  npx ts-node src/index.ts
  ```

## Observações
- Separe a lógica de cada rota em controllers.
- Utilize variáveis de ambiente para configuração do banco.
- Documente cada endpoint e função com comentários claros.
- Siga o modelo de dados definido em `db/init.sql`.
