# WePrint AI Backend Environment Variables
# Copy this file to .env and update the values

# Server Configuration
NODE_ENV=development
PORT=8000
HOST=localhost

# Database Configuration
DATABASE_URL=postgres://postgres:postgres@localhost:5432/weprint
DB_HOST=localhost
DB_PORT=5432
DB_NAME=weprint
DB_USER=postgres
DB_PASSWORD=postgres
DB_SSL=false

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d

# File Upload Configuration
UPLOAD_DIR=uploads
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=pdf,doc,docx,txt,jpg,jpeg,png

# CORS Configuration
CORS_ORIGIN=http://localhost:3000

# Logging
LOG_LEVEL=info

# Email Configuration (SMTP)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
SMTP_FROM_NAME=WePrint AI
SMTP_FROM_EMAIL=<EMAIL>

# Frontend URL (for email links)
FRONTEND_URL=http://localhost:3000

# Admin Configuration
ADMIN_EMAIL=<EMAIL>

# JWT Configuration for Admin Authentication
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d

# Admin Rate Limiting
ADMIN_RATE_LIMIT_REQUESTS=200
ADMIN_RATE_LIMIT_WINDOW_MS=900000

# External Services (Future)
# STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
# SENDGRID_API_KEY=your_sendgrid_api_key
# AWS_ACCESS_KEY_ID=your_aws_access_key
# AWS_SECRET_ACCESS_KEY=your_aws_secret_key
# AWS_REGION=us-east-1
# AWS_S3_BUCKET=weprint-files

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Security
BCRYPT_ROUNDS=12
