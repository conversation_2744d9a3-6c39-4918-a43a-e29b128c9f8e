# WePrint AI Prometheus Configuration

global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alert_rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  # WePrint AI Backend
  - job_name: 'weprint-ai-backend'
    static_configs:
      - targets: ['backend:8000']
    metrics_path: '/metrics'
    scrape_interval: 30s
    scrape_timeout: 10s

  # WePrint AI Frontend (Nginx)
  - job_name: 'weprint-ai-frontend'
    static_configs:
      - targets: ['frontend:80']
    metrics_path: '/nginx_status'
    scrape_interval: 30s

  # PostgreSQL Database
  - job_name: 'weprint-ai-database'
    static_configs:
      - targets: ['database:5432']
    scrape_interval: 30s

  # Redis Cache
  - job_name: 'weprint-ai-redis'
    static_configs:
      - targets: ['redis:6379']
    scrape_interval: 30s

  # Node Exporter (System Metrics)
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s

  # Docker Container Metrics
  - job_name: 'docker'
    static_configs:
      - targets: ['docker-exporter:9323']
    scrape_interval: 30s
