# README — Frontend (React + Tailwind)

Este diretório contém o frontend do MVP WePrint AI, desenvolvido em React.js com TypeScript e Tailwind CSS.

## Estrutura Recomendada

```
frontend/
├── public/
├── src/
│   ├── components/
│   ├── pages/
│   │   ├── LandingPage.tsx
│   │   ├── UploadPage.tsx
│   │   ├── PreviewPage.tsx
│   │   ├── OptionsForm.tsx
│   │   ├── OrderSummary.tsx
│   │   ├── CheckoutPage.tsx
│   │   ├── ClientPanel.tsx
│   │   └── AdminPanel.tsx
│   ├── App.tsx
│   ├── index.tsx
│   └── index.css
├── tailwind.config.js
├── tsconfig.json
└── package.json
```

## Comandos Básicos

- Instalar dependências:
  ```bash
  yarn install
  # ou
  npm install
  ```
- Rodar em modo desenvolvimento:
  ```bash
  yarn start
  # ou
  npm start
  ```

## Observações
- Utilize componentes reutilizáveis em `components/`.
- Documente cada página e componente com comentários claros.
- Siga o padrão de navegação definido no wireframe.
- Utilize Tailwind para estilização rápida e responsiva.
