# lodash._baseflatten v3.1.4

The [modern build](https://github.com/lodash/lodash/wiki/Build-Differences) of [lodash’s](https://lodash.com/) internal `baseFlatten` exported as a [Node.js](http://nodejs.org/)/[io.js](https://iojs.org/) module.

## Installation

Using npm:

```bash
$ {sudo -H} npm i -g npm
$ npm i --save lodash._baseflatten
```

In Node.js/io.js:

```js
var baseFlatten = require('lodash._baseflatten');
```

See the [package source](https://github.com/lodash/lodash/blob/3.1.4-npm-packages/lodash._baseflatten) for more details.
