# lodash.keysin v3.0.8

The [modern build](https://github.com/lodash/lodash/wiki/Build-Differences) of [lodash’s](https://lodash.com/) `_.keysIn` exported as a [Node.js](http://nodejs.org/)/[io.js](https://iojs.org/) module.

## Installation

Using npm:

```bash
$ {sudo -H} npm i -g npm
$ npm i --save lodash.keysin
```

In Node.js/io.js:

```js
var keysIn = require('lodash.keysin');
```

See the [documentation](https://lodash.com/docs#keysIn) or [package source](https://github.com/lodash/lodash/blob/3.0.8-npm-packages/lodash.keysin) for more details.
