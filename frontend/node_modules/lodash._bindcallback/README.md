# lodash._bindcallback v3.0.1

The [modern build](https://github.com/lodash/lodash/wiki/Build-Differences) of [lodash’s](https://lodash.com/) internal `bindCallback` exported as a [Node.js](http://nodejs.org/)/[io.js](https://iojs.org/) module.

## Installation

Using npm:

```bash
$ {sudo -H} npm i -g npm
$ npm i --save lodash._bindcallback
```

In Node.js/io.js:

```js
var bindCallback = require('lodash._bindcallback');
```

See the [package source](https://github.com/lodash/lodash/blob/3.0.1-npm-packages/lodash._bindcallback) for more details.
