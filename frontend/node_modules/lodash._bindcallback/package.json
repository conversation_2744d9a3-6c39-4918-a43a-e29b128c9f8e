{"name": "lodash._bindcallback", "version": "3.0.1", "description": "The modern build of lodash’s internal `bindCallback` as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "author": "<PERSON><PERSON><PERSON> <<EMAIL>> (http://allyoucanleet.com/)", "contributors": ["<PERSON><PERSON><PERSON> <<EMAIL>> (http://allyoucanleet.com/)", "<PERSON> <<EMAIL>> (https://d10.github.io/)", "<PERSON> <<EMAIL>> (http://www.iceddev.com/)", "<PERSON> <<EMAIL>> (http://kitcambridge.be/)", "<PERSON> <<EMAIL>> (https://mathiasbynens.be/)"], "repository": "lodash/lodash", "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}}