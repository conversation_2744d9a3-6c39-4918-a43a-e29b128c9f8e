{"ast": null, "code": "import React from'react';import ReactDOM from'react-dom/client';import'./index.css';import App from'./App';import*as serviceWorkerRegistration from'./serviceWorkerRegistration';import reportWebVitals from'./reportWebVitals';import{jsx as _jsx}from\"react/jsx-runtime\";const root=ReactDOM.createRoot(document.getElementById('root'));root.render(/*#__PURE__*/_jsx(React.StrictMode,{children:/*#__PURE__*/_jsx(App,{})}));// If you want your app to work offline and load faster, you can change\n// unregister() to register() below. Note this comes with some pitfalls.\n// Learn more about service workers: https://cra.link/PWA\nserviceWorkerRegistration.register();// If you want to start measuring performance in your app, pass a function\n// to log results (for example: reportWebVitals(console.log))\n// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals\nreportWebVitals();", "map": {"version": 3, "names": ["React", "ReactDOM", "App", "serviceWorkerRegistration", "reportWebVitals", "jsx", "_jsx", "root", "createRoot", "document", "getElementById", "render", "StrictMode", "children", "register"], "sources": ["/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/index.tsx"], "sourcesContent": ["import React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport './index.css';\nimport App from './App';\nimport * as serviceWorkerRegistration from './serviceWorkerRegistration';\nimport reportWebVitals from './reportWebVitals';\n\nconst root = ReactDOM.createRoot(\n  document.getElementById('root') as HTMLElement\n);\nroot.render(\n  <React.StrictMode>\n    <App />\n  </React.StrictMode>\n);\n\n// If you want your app to work offline and load faster, you can change\n// unregister() to register() below. Note this comes with some pitfalls.\n// Learn more about service workers: https://cra.link/PWA\nserviceWorkerRegistration.register();\n\n// If you want to start measuring performance in your app, pass a function\n// to log results (for example: reportWebVitals(console.log))\n// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals\nreportWebVitals();\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,MAAO,CAAAC,QAAQ,KAAM,kBAAkB,CACvC,MAAO,aAAa,CACpB,MAAO,CAAAC,GAAG,KAAM,OAAO,CACvB,MAAO,GAAK,CAAAC,yBAAyB,KAAM,6BAA6B,CACxE,MAAO,CAAAC,eAAe,KAAM,mBAAmB,CAAC,OAAAC,GAAA,IAAAC,IAAA,yBAEhD,KAAM,CAAAC,IAAI,CAAGN,QAAQ,CAACO,UAAU,CAC9BC,QAAQ,CAACC,cAAc,CAAC,MAAM,CAChC,CAAC,CACDH,IAAI,CAACI,MAAM,cACTL,IAAA,CAACN,KAAK,CAACY,UAAU,EAAAC,QAAA,cACfP,IAAA,CAACJ,GAAG,GAAE,CAAC,CACS,CACpB,CAAC,CAED;AACA;AACA;AACAC,yBAAyB,CAACW,QAAQ,CAAC,CAAC,CAEpC;AACA;AACA;AACAV,eAAe,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}