{"ast": null, "code": "var _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport { useState, useCallback, useEffect } from 'react';\nimport { MulticaixaService } from '../services/multicaixa';\n/**\n * Custom hook for managing Multicaixa payments\n */\nexport const useMulticaixa = () => {\n  _s();\n  const [state, setState] = useState({\n    loading: false,\n    error: null,\n    payment: null,\n    paymentUrl: null,\n    qrCode: null,\n    status: null\n  });\n\n  /**\n   * Create a new Multicaixa payment\n   */\n  const createPayment = useCallback(async data => {\n    setState(prev => ({\n      ...prev,\n      loading: true,\n      error: null\n    }));\n    try {\n      const response = await MulticaixaService.createPayment(data);\n      if (response.success) {\n        setState(prev => ({\n          ...prev,\n          loading: false,\n          paymentUrl: response.paymentUrl || null,\n          qrCode: response.qrCode || null,\n          status: 'PENDING'\n        }));\n      } else {\n        setState(prev => ({\n          ...prev,\n          loading: false,\n          error: response.error || 'Failed to create payment'\n        }));\n      }\n      return response;\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Network error';\n      setState(prev => ({\n        ...prev,\n        loading: false,\n        error: errorMessage\n      }));\n      return {\n        success: false,\n        error: errorMessage\n      };\n    }\n  }, []);\n\n  /**\n   * Check payment status\n   */\n  const checkStatus = useCallback(async paymentId => {\n    setState(prev => ({\n      ...prev,\n      loading: true,\n      error: null\n    }));\n    try {\n      const response = await MulticaixaService.checkPaymentStatus(paymentId);\n      if (response.success && response.status) {\n        setState(prev => ({\n          ...prev,\n          loading: false,\n          status: response.status\n        }));\n      } else {\n        setState(prev => ({\n          ...prev,\n          loading: false,\n          error: response.error || 'Failed to check payment status'\n        }));\n      }\n    } catch (error) {\n      setState(prev => ({\n        ...prev,\n        loading: false,\n        error: error instanceof Error ? error.message : 'Network error'\n      }));\n    }\n  }, []);\n\n  /**\n   * Request a refund\n   */\n  const requestRefund = useCallback(async (paymentId, amount, reason) => {\n    setState(prev => ({\n      ...prev,\n      loading: true,\n      error: null\n    }));\n    try {\n      const response = await MulticaixaService.requestRefund(paymentId, amount, reason);\n      if (response.success) {\n        setState(prev => ({\n          ...prev,\n          loading: false,\n          status: 'REFUNDED'\n        }));\n        return true;\n      } else {\n        setState(prev => ({\n          ...prev,\n          loading: false,\n          error: response.error || 'Failed to request refund'\n        }));\n        return false;\n      }\n    } catch (error) {\n      setState(prev => ({\n        ...prev,\n        loading: false,\n        error: error instanceof Error ? error.message : 'Network error'\n      }));\n      return false;\n    }\n  }, []);\n\n  /**\n   * Clear error state\n   */\n  const clearError = useCallback(() => {\n    setState(prev => ({\n      ...prev,\n      error: null\n    }));\n  }, []);\n\n  /**\n   * Reset all state\n   */\n  const reset = useCallback(() => {\n    setState({\n      loading: false,\n      error: null,\n      payment: null,\n      paymentUrl: null,\n      qrCode: null,\n      status: null\n    });\n  }, []);\n  return {\n    ...state,\n    createPayment,\n    checkStatus,\n    requestRefund,\n    clearError,\n    reset\n  };\n};\n\n/**\n * Hook for polling payment status\n */\n_s(useMulticaixa, \"sUsx8Hk0poiIH7tRU7yNq/Hxbuo=\");\nexport const usePaymentStatusPolling = (paymentId, intervalMs = 5000, maxAttempts = 60) => {\n  _s2();\n  const [attempts, setAttempts] = useState(0);\n  const [isPolling, setIsPolling] = useState(false);\n  const {\n    checkStatus,\n    status,\n    loading,\n    error\n  } = useMulticaixa();\n  useEffect(() => {\n    if (!paymentId || MulticaixaService.isPaymentFinal(status || '')) {\n      setIsPolling(false);\n      return;\n    }\n    if (attempts >= maxAttempts) {\n      setIsPolling(false);\n      return;\n    }\n    setIsPolling(true);\n    const interval = setInterval(async () => {\n      await checkStatus(paymentId);\n      setAttempts(prev => prev + 1);\n    }, intervalMs);\n    return () => {\n      clearInterval(interval);\n      setIsPolling(false);\n    };\n  }, [paymentId, status, attempts, maxAttempts, intervalMs, checkStatus]);\n  const startPolling = useCallback(() => {\n    setAttempts(0);\n    setIsPolling(true);\n  }, []);\n  const stopPolling = useCallback(() => {\n    setIsPolling(false);\n  }, []);\n  return {\n    isPolling,\n    attempts,\n    maxAttempts,\n    status,\n    loading,\n    error,\n    startPolling,\n    stopPolling\n  };\n};\n_s2(usePaymentStatusPolling, \"pmfQHnVOS9cNgeA5Skrx2wMmc04=\", false, function () {\n  return [useMulticaixa];\n});\nexport default useMulticaixa;", "map": {"version": 3, "names": ["useState", "useCallback", "useEffect", "MulticaixaService", "useMulticaixa", "_s", "state", "setState", "loading", "error", "payment", "paymentUrl", "qrCode", "status", "createPayment", "data", "prev", "response", "success", "errorMessage", "Error", "message", "checkStatus", "paymentId", "checkPaymentStatus", "requestRefund", "amount", "reason", "clearError", "reset", "usePaymentStatusPolling", "intervalMs", "maxAttempts", "_s2", "attempts", "setAttempts", "isPolling", "setIsPolling", "isPaymentFinal", "interval", "setInterval", "clearInterval", "startPolling", "stopPolling"], "sources": ["/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/hooks/useMulticaixa.ts"], "sourcesContent": ["import { useState, useCallback, useEffect } from 'react';\nimport { MulticaixaService } from '../services/multicaixa';\nimport { \n  MulticaixaPaymentRequest, \n  MulticaixaPaymentResponse, \n  Payment,\n  PaymentStatus \n} from '../types';\n\ninterface UseMulticaixaState {\n  loading: boolean;\n  error: string | null;\n  payment: Payment | null;\n  paymentUrl: string | null;\n  qrCode: string | null;\n  status: PaymentStatus | null;\n}\n\ninterface UseMulticaixaActions {\n  createPayment: (data: MulticaixaPaymentRequest) => Promise<MulticaixaPaymentResponse>;\n  checkStatus: (paymentId: string) => Promise<void>;\n  requestRefund: (paymentId: string, amount?: number, reason?: string) => Promise<boolean>;\n  clearError: () => void;\n  reset: () => void;\n}\n\n/**\n * Custom hook for managing Multicaixa payments\n */\nexport const useMulticaixa = (): UseMulticaixaState & UseMulticaixaActions => {\n  const [state, setState] = useState<UseMulticaixaState>({\n    loading: false,\n    error: null,\n    payment: null,\n    paymentUrl: null,\n    qrCode: null,\n    status: null,\n  });\n\n  /**\n   * Create a new Multicaixa payment\n   */\n  const createPayment = useCallback(async (data: MulticaixaPaymentRequest): Promise<MulticaixaPaymentResponse> => {\n    setState(prev => ({ ...prev, loading: true, error: null }));\n\n    try {\n      const response = await MulticaixaService.createPayment(data);\n      \n      if (response.success) {\n        setState(prev => ({\n          ...prev,\n          loading: false,\n          paymentUrl: response.paymentUrl || null,\n          qrCode: response.qrCode || null,\n          status: 'PENDING',\n        }));\n      } else {\n        setState(prev => ({\n          ...prev,\n          loading: false,\n          error: response.error || 'Failed to create payment',\n        }));\n      }\n\n      return response;\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Network error';\n      setState(prev => ({\n        ...prev,\n        loading: false,\n        error: errorMessage,\n      }));\n      \n      return {\n        success: false,\n        error: errorMessage,\n      };\n    }\n  }, []);\n\n  /**\n   * Check payment status\n   */\n  const checkStatus = useCallback(async (paymentId: string): Promise<void> => {\n    setState(prev => ({ ...prev, loading: true, error: null }));\n\n    try {\n      const response = await MulticaixaService.checkPaymentStatus(paymentId);\n      \n      if (response.success && response.status) {\n        setState(prev => ({\n          ...prev,\n          loading: false,\n          status: response.status!,\n        }));\n      } else {\n        setState(prev => ({\n          ...prev,\n          loading: false,\n          error: response.error || 'Failed to check payment status',\n        }));\n      }\n    } catch (error) {\n      setState(prev => ({\n        ...prev,\n        loading: false,\n        error: error instanceof Error ? error.message : 'Network error',\n      }));\n    }\n  }, []);\n\n  /**\n   * Request a refund\n   */\n  const requestRefund = useCallback(async (\n    paymentId: string, \n    amount?: number, \n    reason?: string\n  ): Promise<boolean> => {\n    setState(prev => ({ ...prev, loading: true, error: null }));\n\n    try {\n      const response = await MulticaixaService.requestRefund(paymentId, amount, reason);\n      \n      if (response.success) {\n        setState(prev => ({\n          ...prev,\n          loading: false,\n          status: 'REFUNDED',\n        }));\n        return true;\n      } else {\n        setState(prev => ({\n          ...prev,\n          loading: false,\n          error: response.error || 'Failed to request refund',\n        }));\n        return false;\n      }\n    } catch (error) {\n      setState(prev => ({\n        ...prev,\n        loading: false,\n        error: error instanceof Error ? error.message : 'Network error',\n      }));\n      return false;\n    }\n  }, []);\n\n  /**\n   * Clear error state\n   */\n  const clearError = useCallback(() => {\n    setState(prev => ({ ...prev, error: null }));\n  }, []);\n\n  /**\n   * Reset all state\n   */\n  const reset = useCallback(() => {\n    setState({\n      loading: false,\n      error: null,\n      payment: null,\n      paymentUrl: null,\n      qrCode: null,\n      status: null,\n    });\n  }, []);\n\n  return {\n    ...state,\n    createPayment,\n    checkStatus,\n    requestRefund,\n    clearError,\n    reset,\n  };\n};\n\n/**\n * Hook for polling payment status\n */\nexport const usePaymentStatusPolling = (\n  paymentId: string | null,\n  intervalMs: number = 5000,\n  maxAttempts: number = 60\n) => {\n  const [attempts, setAttempts] = useState(0);\n  const [isPolling, setIsPolling] = useState(false);\n  const { checkStatus, status, loading, error } = useMulticaixa();\n\n  useEffect(() => {\n    if (!paymentId || MulticaixaService.isPaymentFinal(status || '')) {\n      setIsPolling(false);\n      return;\n    }\n\n    if (attempts >= maxAttempts) {\n      setIsPolling(false);\n      return;\n    }\n\n    setIsPolling(true);\n    const interval = setInterval(async () => {\n      await checkStatus(paymentId);\n      setAttempts(prev => prev + 1);\n    }, intervalMs);\n\n    return () => {\n      clearInterval(interval);\n      setIsPolling(false);\n    };\n  }, [paymentId, status, attempts, maxAttempts, intervalMs, checkStatus]);\n\n  const startPolling = useCallback(() => {\n    setAttempts(0);\n    setIsPolling(true);\n  }, []);\n\n  const stopPolling = useCallback(() => {\n    setIsPolling(false);\n  }, []);\n\n  return {\n    isPolling,\n    attempts,\n    maxAttempts,\n    status,\n    loading,\n    error,\n    startPolling,\n    stopPolling,\n  };\n};\n\nexport default useMulticaixa;\n"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,WAAW,EAAEC,SAAS,QAAQ,OAAO;AACxD,SAASC,iBAAiB,QAAQ,wBAAwB;AAyB1D;AACA;AACA;AACA,OAAO,MAAMC,aAAa,GAAGA,CAAA,KAAiD;EAAAC,EAAA;EAC5E,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGP,QAAQ,CAAqB;IACrDQ,OAAO,EAAE,KAAK;IACdC,KAAK,EAAE,IAAI;IACXC,OAAO,EAAE,IAAI;IACbC,UAAU,EAAE,IAAI;IAChBC,MAAM,EAAE,IAAI;IACZC,MAAM,EAAE;EACV,CAAC,CAAC;;EAEF;AACF;AACA;EACE,MAAMC,aAAa,GAAGb,WAAW,CAAC,MAAOc,IAA8B,IAAyC;IAC9GR,QAAQ,CAACS,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAER,OAAO,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAK,CAAC,CAAC,CAAC;IAE3D,IAAI;MACF,MAAMQ,QAAQ,GAAG,MAAMd,iBAAiB,CAACW,aAAa,CAACC,IAAI,CAAC;MAE5D,IAAIE,QAAQ,CAACC,OAAO,EAAE;QACpBX,QAAQ,CAACS,IAAI,KAAK;UAChB,GAAGA,IAAI;UACPR,OAAO,EAAE,KAAK;UACdG,UAAU,EAAEM,QAAQ,CAACN,UAAU,IAAI,IAAI;UACvCC,MAAM,EAAEK,QAAQ,CAACL,MAAM,IAAI,IAAI;UAC/BC,MAAM,EAAE;QACV,CAAC,CAAC,CAAC;MACL,CAAC,MAAM;QACLN,QAAQ,CAACS,IAAI,KAAK;UAChB,GAAGA,IAAI;UACPR,OAAO,EAAE,KAAK;UACdC,KAAK,EAAEQ,QAAQ,CAACR,KAAK,IAAI;QAC3B,CAAC,CAAC,CAAC;MACL;MAEA,OAAOQ,QAAQ;IACjB,CAAC,CAAC,OAAOR,KAAK,EAAE;MACd,MAAMU,YAAY,GAAGV,KAAK,YAAYW,KAAK,GAAGX,KAAK,CAACY,OAAO,GAAG,eAAe;MAC7Ed,QAAQ,CAACS,IAAI,KAAK;QAChB,GAAGA,IAAI;QACPR,OAAO,EAAE,KAAK;QACdC,KAAK,EAAEU;MACT,CAAC,CAAC,CAAC;MAEH,OAAO;QACLD,OAAO,EAAE,KAAK;QACdT,KAAK,EAAEU;MACT,CAAC;IACH;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;AACF;AACA;EACE,MAAMG,WAAW,GAAGrB,WAAW,CAAC,MAAOsB,SAAiB,IAAoB;IAC1EhB,QAAQ,CAACS,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAER,OAAO,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAK,CAAC,CAAC,CAAC;IAE3D,IAAI;MACF,MAAMQ,QAAQ,GAAG,MAAMd,iBAAiB,CAACqB,kBAAkB,CAACD,SAAS,CAAC;MAEtE,IAAIN,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACJ,MAAM,EAAE;QACvCN,QAAQ,CAACS,IAAI,KAAK;UAChB,GAAGA,IAAI;UACPR,OAAO,EAAE,KAAK;UACdK,MAAM,EAAEI,QAAQ,CAACJ;QACnB,CAAC,CAAC,CAAC;MACL,CAAC,MAAM;QACLN,QAAQ,CAACS,IAAI,KAAK;UAChB,GAAGA,IAAI;UACPR,OAAO,EAAE,KAAK;UACdC,KAAK,EAAEQ,QAAQ,CAACR,KAAK,IAAI;QAC3B,CAAC,CAAC,CAAC;MACL;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdF,QAAQ,CAACS,IAAI,KAAK;QAChB,GAAGA,IAAI;QACPR,OAAO,EAAE,KAAK;QACdC,KAAK,EAAEA,KAAK,YAAYW,KAAK,GAAGX,KAAK,CAACY,OAAO,GAAG;MAClD,CAAC,CAAC,CAAC;IACL;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;AACF;AACA;EACE,MAAMI,aAAa,GAAGxB,WAAW,CAAC,OAChCsB,SAAiB,EACjBG,MAAe,EACfC,MAAe,KACM;IACrBpB,QAAQ,CAACS,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAER,OAAO,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAK,CAAC,CAAC,CAAC;IAE3D,IAAI;MACF,MAAMQ,QAAQ,GAAG,MAAMd,iBAAiB,CAACsB,aAAa,CAACF,SAAS,EAAEG,MAAM,EAAEC,MAAM,CAAC;MAEjF,IAAIV,QAAQ,CAACC,OAAO,EAAE;QACpBX,QAAQ,CAACS,IAAI,KAAK;UAChB,GAAGA,IAAI;UACPR,OAAO,EAAE,KAAK;UACdK,MAAM,EAAE;QACV,CAAC,CAAC,CAAC;QACH,OAAO,IAAI;MACb,CAAC,MAAM;QACLN,QAAQ,CAACS,IAAI,KAAK;UAChB,GAAGA,IAAI;UACPR,OAAO,EAAE,KAAK;UACdC,KAAK,EAAEQ,QAAQ,CAACR,KAAK,IAAI;QAC3B,CAAC,CAAC,CAAC;QACH,OAAO,KAAK;MACd;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdF,QAAQ,CAACS,IAAI,KAAK;QAChB,GAAGA,IAAI;QACPR,OAAO,EAAE,KAAK;QACdC,KAAK,EAAEA,KAAK,YAAYW,KAAK,GAAGX,KAAK,CAACY,OAAO,GAAG;MAClD,CAAC,CAAC,CAAC;MACH,OAAO,KAAK;IACd;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;AACF;AACA;EACE,MAAMO,UAAU,GAAG3B,WAAW,CAAC,MAAM;IACnCM,QAAQ,CAACS,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEP,KAAK,EAAE;IAAK,CAAC,CAAC,CAAC;EAC9C,CAAC,EAAE,EAAE,CAAC;;EAEN;AACF;AACA;EACE,MAAMoB,KAAK,GAAG5B,WAAW,CAAC,MAAM;IAC9BM,QAAQ,CAAC;MACPC,OAAO,EAAE,KAAK;MACdC,KAAK,EAAE,IAAI;MACXC,OAAO,EAAE,IAAI;MACbC,UAAU,EAAE,IAAI;MAChBC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,OAAO;IACL,GAAGP,KAAK;IACRQ,aAAa;IACbQ,WAAW;IACXG,aAAa;IACbG,UAAU;IACVC;EACF,CAAC;AACH,CAAC;;AAED;AACA;AACA;AAFAxB,EAAA,CAvJaD,aAAa;AA0J1B,OAAO,MAAM0B,uBAAuB,GAAGA,CACrCP,SAAwB,EACxBQ,UAAkB,GAAG,IAAI,EACzBC,WAAmB,GAAG,EAAE,KACrB;EAAAC,GAAA;EACH,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGnC,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAACoC,SAAS,EAAEC,YAAY,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM;IAAEsB,WAAW;IAAET,MAAM;IAAEL,OAAO;IAAEC;EAAM,CAAC,GAAGL,aAAa,CAAC,CAAC;EAE/DF,SAAS,CAAC,MAAM;IACd,IAAI,CAACqB,SAAS,IAAIpB,iBAAiB,CAACmC,cAAc,CAACzB,MAAM,IAAI,EAAE,CAAC,EAAE;MAChEwB,YAAY,CAAC,KAAK,CAAC;MACnB;IACF;IAEA,IAAIH,QAAQ,IAAIF,WAAW,EAAE;MAC3BK,YAAY,CAAC,KAAK,CAAC;MACnB;IACF;IAEAA,YAAY,CAAC,IAAI,CAAC;IAClB,MAAME,QAAQ,GAAGC,WAAW,CAAC,YAAY;MACvC,MAAMlB,WAAW,CAACC,SAAS,CAAC;MAC5BY,WAAW,CAACnB,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IAC/B,CAAC,EAAEe,UAAU,CAAC;IAEd,OAAO,MAAM;MACXU,aAAa,CAACF,QAAQ,CAAC;MACvBF,YAAY,CAAC,KAAK,CAAC;IACrB,CAAC;EACH,CAAC,EAAE,CAACd,SAAS,EAAEV,MAAM,EAAEqB,QAAQ,EAAEF,WAAW,EAAED,UAAU,EAAET,WAAW,CAAC,CAAC;EAEvE,MAAMoB,YAAY,GAAGzC,WAAW,CAAC,MAAM;IACrCkC,WAAW,CAAC,CAAC,CAAC;IACdE,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMM,WAAW,GAAG1C,WAAW,CAAC,MAAM;IACpCoC,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC,EAAE,EAAE,CAAC;EAEN,OAAO;IACLD,SAAS;IACTF,QAAQ;IACRF,WAAW;IACXnB,MAAM;IACNL,OAAO;IACPC,KAAK;IACLiC,YAAY;IACZC;EACF,CAAC;AACH,CAAC;AAACV,GAAA,CAnDWH,uBAAuB;EAAA,QAOc1B,aAAa;AAAA;AA8C/D,eAAeA,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}