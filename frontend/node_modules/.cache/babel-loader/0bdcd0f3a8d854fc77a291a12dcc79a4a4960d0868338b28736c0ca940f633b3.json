{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/LandingPage.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LandingPage = () => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: \"flex flex-col items-center justify-center min-h-screen bg-gray-50\",\n  children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n    className: \"text-3xl font-bold mb-4\",\n    children: \"WePrint AI\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n    className: \"mb-8\",\n    children: \"A gr\\xE1fica que vai at\\xE9 ti\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-x-4\",\n    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"px-6 py-2 bg-blue-600 text-white rounded\",\n      children: \"Come\\xE7ar Encomenda\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"px-6 py-2 bg-gray-300 text-gray-800 rounded\",\n      children: \"Login/Cadastro\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 7,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 4,\n  columnNumber: 3\n}, this);\n_c = LandingPage;\nexport default LandingPage;\nvar _c;\n$RefreshReg$(_c, \"LandingPage\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "LandingPage", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/LandingPage.tsx"], "sourcesContent": ["import React from 'react';\n\nconst LandingPage: React.FC = () => (\n  <div className=\"flex flex-col items-center justify-center min-h-screen bg-gray-50\">\n    <h1 className=\"text-3xl font-bold mb-4\">WePrint AI</h1>\n    <p className=\"mb-8\">A gráfica que vai até ti</p>\n    <div className=\"space-x-4\">\n      <button className=\"px-6 py-2 bg-blue-600 text-white rounded\">Começar Encomenda</button>\n      <button className=\"px-6 py-2 bg-gray-300 text-gray-800 rounded\">Login/Cadastro</button>\n    </div>\n  </div>\n);\n\nexport default LandingPage;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,WAAqB,GAAGA,CAAA,kBAC5BD,OAAA;EAAKE,SAAS,EAAC,mEAAmE;EAAAC,QAAA,gBAChFH,OAAA;IAAIE,SAAS,EAAC,yBAAyB;IAAAC,QAAA,EAAC;EAAU;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAI,CAAC,eACvDP,OAAA;IAAGE,SAAS,EAAC,MAAM;IAAAC,QAAA,EAAC;EAAwB;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAG,CAAC,eAChDP,OAAA;IAAKE,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxBH,OAAA;MAAQE,SAAS,EAAC,0CAA0C;MAAAC,QAAA,EAAC;IAAiB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eACvFP,OAAA;MAAQE,SAAS,EAAC,6CAA6C;MAAAC,QAAA,EAAC;IAAc;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACpF,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACH,CACN;AAACC,EAAA,GATIP,WAAqB;AAW3B,eAAeA,WAAW;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}