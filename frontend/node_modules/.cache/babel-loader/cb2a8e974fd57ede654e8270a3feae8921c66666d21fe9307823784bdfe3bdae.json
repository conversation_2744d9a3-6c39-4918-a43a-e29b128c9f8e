{"ast": null, "code": "// This optional code is used to register a service worker.\n// register() is not called by default.\n\n// This lets the app load faster on subsequent visits in production, and gives\n// it offline capabilities. However, it also means that developers (and users)\n// will only see deployed updates on subsequent visits to a page, after all the\n// existing tabs open on the page have been closed, since previously cached\n// resources are updated in the background.\n\n// To learn more about the benefits of this model and instructions on how to\n// opt-in, read https://cra.link/PWA\n\nconst isLocalhost = Boolean(window.location.hostname === 'localhost' ||\n// [::1] is the IPv6 localhost address.\nwindow.location.hostname === '[::1]' ||\n// *********/8 are considered localhost for IPv4.\nwindow.location.hostname.match(/^127(?:\\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}$/));\nexport function register(config) {\n  if ('serviceWorker' in navigator) {\n    // The URL constructor is available in all browsers that support SW.\n    const publicUrl = new URL(process.env.PUBLIC_URL, window.location.href);\n    if (publicUrl.origin !== window.location.origin) {\n      // Our service worker won't work if PUBLIC_URL is on a different origin\n      // from what our page is served on. This might happen if a CDN is used to\n      // serve assets; see https://github.com/facebook/create-react-app/issues/2374\n      return;\n    }\n    window.addEventListener('load', () => {\n      const swUrl = `${process.env.PUBLIC_URL}/service-worker.js`;\n      if (isLocalhost) {\n        // This is running on localhost. Let's check if a service worker still exists or not.\n        checkValidServiceWorker(swUrl, config);\n\n        // Add some additional logging to localhost, pointing developers to the\n        // service worker/PWA documentation.\n        navigator.serviceWorker.ready.then(() => {\n          console.log('This web app is being served cache-first by a service ' + 'worker. To learn more, visit https://cra.link/PWA');\n        });\n      } else {\n        // Is not localhost. Just register service worker\n        registerValidSW(swUrl, config);\n      }\n    });\n  }\n}\nfunction registerValidSW(swUrl, config) {\n  navigator.serviceWorker.register(swUrl).then(registration => {\n    registration.onupdatefound = () => {\n      const installingWorker = registration.installing;\n      if (installingWorker == null) {\n        return;\n      }\n      installingWorker.onstatechange = () => {\n        if (installingWorker.state === 'installed') {\n          if (navigator.serviceWorker.controller) {\n            // At this point, the updated precached content has been fetched,\n            // but the previous service worker will still serve the older\n            // content until all client tabs are closed.\n            console.log('New content is available and will be used when all ' + 'tabs for this page are closed. See https://cra.link/PWA.');\n\n            // Execute callback\n            if (config && config.onUpdate) {\n              config.onUpdate(registration);\n            }\n          } else {\n            // At this point, everything has been precached.\n            // It's the perfect time to display a\n            // \"Content is cached for offline use.\" message.\n            console.log('Content is cached for offline use.');\n\n            // Execute callback\n            if (config && config.onSuccess) {\n              config.onSuccess(registration);\n            }\n          }\n        }\n      };\n    };\n  }).catch(error => {\n    console.error('Error during service worker registration:', error);\n  });\n}\nfunction checkValidServiceWorker(swUrl, config) {\n  // Check if the service worker can be found. If it can't reload the page.\n  fetch(swUrl, {\n    headers: {\n      'Service-Worker': 'script'\n    }\n  }).then(response => {\n    // Ensure service worker exists, and that we really are getting a JS file.\n    const contentType = response.headers.get('content-type');\n    if (response.status === 404 || contentType != null && contentType.indexOf('javascript') === -1) {\n      // No service worker found. Probably a different app. Reload the page.\n      navigator.serviceWorker.ready.then(registration => {\n        registration.unregister().then(() => {\n          window.location.reload();\n        });\n      });\n    } else {\n      // Service worker found. Proceed as normal.\n      registerValidSW(swUrl, config);\n    }\n  }).catch(() => {\n    console.log('No internet connection found. App is running in offline mode.');\n  });\n}\nexport function unregister() {\n  if ('serviceWorker' in navigator) {\n    navigator.serviceWorker.ready.then(registration => {\n      registration.unregister();\n    }).catch(error => {\n      console.error(error.message);\n    });\n  }\n}", "map": {"version": 3, "names": ["isLocalhost", "Boolean", "window", "location", "hostname", "match", "register", "config", "navigator", "publicUrl", "URL", "process", "env", "PUBLIC_URL", "href", "origin", "addEventListener", "swUrl", "checkValidServiceWorker", "serviceWorker", "ready", "then", "console", "log", "registerValidSW", "registration", "onupdatefound", "installingWorker", "installing", "onstatechange", "state", "controller", "onUpdate", "onSuccess", "catch", "error", "fetch", "headers", "response", "contentType", "get", "status", "indexOf", "unregister", "reload", "message"], "sources": ["/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/serviceWorkerRegistration.ts"], "sourcesContent": ["// This optional code is used to register a service worker.\n// register() is not called by default.\n\n// This lets the app load faster on subsequent visits in production, and gives\n// it offline capabilities. However, it also means that developers (and users)\n// will only see deployed updates on subsequent visits to a page, after all the\n// existing tabs open on the page have been closed, since previously cached\n// resources are updated in the background.\n\n// To learn more about the benefits of this model and instructions on how to\n// opt-in, read https://cra.link/PWA\n\nconst isLocalhost = Boolean(\n  window.location.hostname === 'localhost' ||\n    // [::1] is the IPv6 localhost address.\n    window.location.hostname === '[::1]' ||\n    // *********/8 are considered localhost for IPv4.\n    window.location.hostname.match(\n      /^127(?:\\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}$/\n    )\n);\n\ntype Config = {\n  onSuccess?: (registration: ServiceWorkerRegistration) => void;\n  onUpdate?: (registration: ServiceWorkerRegistration) => void;\n};\n\nexport function register(config?: Config) {\n  if ('serviceWorker' in navigator) {\n    // The URL constructor is available in all browsers that support SW.\n    const publicUrl = new URL(\n      process.env.PUBLIC_URL!,\n      window.location.href\n    );\n    if (publicUrl.origin !== window.location.origin) {\n      // Our service worker won't work if PUBLIC_URL is on a different origin\n      // from what our page is served on. This might happen if a CDN is used to\n      // serve assets; see https://github.com/facebook/create-react-app/issues/2374\n      return;\n    }\n\n    window.addEventListener('load', () => {\n      const swUrl = `${process.env.PUBLIC_URL}/service-worker.js`;\n\n      if (isLocalhost) {\n        // This is running on localhost. Let's check if a service worker still exists or not.\n        checkValidServiceWorker(swUrl, config);\n\n        // Add some additional logging to localhost, pointing developers to the\n        // service worker/PWA documentation.\n        navigator.serviceWorker.ready.then(() => {\n          console.log(\n            'This web app is being served cache-first by a service ' +\n              'worker. To learn more, visit https://cra.link/PWA'\n          );\n        });\n      } else {\n        // Is not localhost. Just register service worker\n        registerValidSW(swUrl, config);\n      }\n    });\n  }\n}\n\nfunction registerValidSW(swUrl: string, config?: Config) {\n  navigator.serviceWorker\n    .register(swUrl)\n    .then((registration) => {\n      registration.onupdatefound = () => {\n        const installingWorker = registration.installing;\n        if (installingWorker == null) {\n          return;\n        }\n        installingWorker.onstatechange = () => {\n          if (installingWorker.state === 'installed') {\n            if (navigator.serviceWorker.controller) {\n              // At this point, the updated precached content has been fetched,\n              // but the previous service worker will still serve the older\n              // content until all client tabs are closed.\n              console.log(\n                'New content is available and will be used when all ' +\n                  'tabs for this page are closed. See https://cra.link/PWA.'\n              );\n\n              // Execute callback\n              if (config && config.onUpdate) {\n                config.onUpdate(registration);\n              }\n            } else {\n              // At this point, everything has been precached.\n              // It's the perfect time to display a\n              // \"Content is cached for offline use.\" message.\n              console.log('Content is cached for offline use.');\n\n              // Execute callback\n              if (config && config.onSuccess) {\n                config.onSuccess(registration);\n              }\n            }\n          }\n        };\n      };\n    })\n    .catch((error) => {\n      console.error('Error during service worker registration:', error);\n    });\n}\n\nfunction checkValidServiceWorker(swUrl: string, config?: Config) {\n  // Check if the service worker can be found. If it can't reload the page.\n  fetch(swUrl, {\n    headers: { 'Service-Worker': 'script' },\n  })\n    .then((response) => {\n      // Ensure service worker exists, and that we really are getting a JS file.\n      const contentType = response.headers.get('content-type');\n      if (\n        response.status === 404 ||\n        (contentType != null && contentType.indexOf('javascript') === -1)\n      ) {\n        // No service worker found. Probably a different app. Reload the page.\n        navigator.serviceWorker.ready.then((registration) => {\n          registration.unregister().then(() => {\n            window.location.reload();\n          });\n        });\n      } else {\n        // Service worker found. Proceed as normal.\n        registerValidSW(swUrl, config);\n      }\n    })\n    .catch(() => {\n      console.log(\n        'No internet connection found. App is running in offline mode.'\n      );\n    });\n}\n\nexport function unregister() {\n  if ('serviceWorker' in navigator) {\n    navigator.serviceWorker.ready\n      .then((registration) => {\n        registration.unregister();\n      })\n      .catch((error) => {\n        console.error(error.message);\n      });\n  }\n}\n"], "mappings": "AAAA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA,MAAMA,WAAW,GAAGC,OAAO,CACzBC,MAAM,CAACC,QAAQ,CAACC,QAAQ,KAAK,WAAW;AACtC;AACAF,MAAM,CAACC,QAAQ,CAACC,QAAQ,KAAK,OAAO;AACpC;AACAF,MAAM,CAACC,QAAQ,CAACC,QAAQ,CAACC,KAAK,CAC5B,wDACF,CACJ,CAAC;AAOD,OAAO,SAASC,QAAQA,CAACC,MAAe,EAAE;EACxC,IAAI,eAAe,IAAIC,SAAS,EAAE;IAChC;IACA,MAAMC,SAAS,GAAG,IAAIC,GAAG,CACvBC,OAAO,CAACC,GAAG,CAACC,UAAU,EACtBX,MAAM,CAACC,QAAQ,CAACW,IAClB,CAAC;IACD,IAAIL,SAAS,CAACM,MAAM,KAAKb,MAAM,CAACC,QAAQ,CAACY,MAAM,EAAE;MAC/C;MACA;MACA;MACA;IACF;IAEAb,MAAM,CAACc,gBAAgB,CAAC,MAAM,EAAE,MAAM;MACpC,MAAMC,KAAK,GAAG,GAAGN,OAAO,CAACC,GAAG,CAACC,UAAU,oBAAoB;MAE3D,IAAIb,WAAW,EAAE;QACf;QACAkB,uBAAuB,CAACD,KAAK,EAAEV,MAAM,CAAC;;QAEtC;QACA;QACAC,SAAS,CAACW,aAAa,CAACC,KAAK,CAACC,IAAI,CAAC,MAAM;UACvCC,OAAO,CAACC,GAAG,CACT,wDAAwD,GACtD,mDACJ,CAAC;QACH,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACAC,eAAe,CAACP,KAAK,EAAEV,MAAM,CAAC;MAChC;IACF,CAAC,CAAC;EACJ;AACF;AAEA,SAASiB,eAAeA,CAACP,KAAa,EAAEV,MAAe,EAAE;EACvDC,SAAS,CAACW,aAAa,CACpBb,QAAQ,CAACW,KAAK,CAAC,CACfI,IAAI,CAAEI,YAAY,IAAK;IACtBA,YAAY,CAACC,aAAa,GAAG,MAAM;MACjC,MAAMC,gBAAgB,GAAGF,YAAY,CAACG,UAAU;MAChD,IAAID,gBAAgB,IAAI,IAAI,EAAE;QAC5B;MACF;MACAA,gBAAgB,CAACE,aAAa,GAAG,MAAM;QACrC,IAAIF,gBAAgB,CAACG,KAAK,KAAK,WAAW,EAAE;UAC1C,IAAItB,SAAS,CAACW,aAAa,CAACY,UAAU,EAAE;YACtC;YACA;YACA;YACAT,OAAO,CAACC,GAAG,CACT,qDAAqD,GACnD,0DACJ,CAAC;;YAED;YACA,IAAIhB,MAAM,IAAIA,MAAM,CAACyB,QAAQ,EAAE;cAC7BzB,MAAM,CAACyB,QAAQ,CAACP,YAAY,CAAC;YAC/B;UACF,CAAC,MAAM;YACL;YACA;YACA;YACAH,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;;YAEjD;YACA,IAAIhB,MAAM,IAAIA,MAAM,CAAC0B,SAAS,EAAE;cAC9B1B,MAAM,CAAC0B,SAAS,CAACR,YAAY,CAAC;YAChC;UACF;QACF;MACF,CAAC;IACH,CAAC;EACH,CAAC,CAAC,CACDS,KAAK,CAAEC,KAAK,IAAK;IAChBb,OAAO,CAACa,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;EACnE,CAAC,CAAC;AACN;AAEA,SAASjB,uBAAuBA,CAACD,KAAa,EAAEV,MAAe,EAAE;EAC/D;EACA6B,KAAK,CAACnB,KAAK,EAAE;IACXoB,OAAO,EAAE;MAAE,gBAAgB,EAAE;IAAS;EACxC,CAAC,CAAC,CACChB,IAAI,CAAEiB,QAAQ,IAAK;IAClB;IACA,MAAMC,WAAW,GAAGD,QAAQ,CAACD,OAAO,CAACG,GAAG,CAAC,cAAc,CAAC;IACxD,IACEF,QAAQ,CAACG,MAAM,KAAK,GAAG,IACtBF,WAAW,IAAI,IAAI,IAAIA,WAAW,CAACG,OAAO,CAAC,YAAY,CAAC,KAAK,CAAC,CAAE,EACjE;MACA;MACAlC,SAAS,CAACW,aAAa,CAACC,KAAK,CAACC,IAAI,CAAEI,YAAY,IAAK;QACnDA,YAAY,CAACkB,UAAU,CAAC,CAAC,CAACtB,IAAI,CAAC,MAAM;UACnCnB,MAAM,CAACC,QAAQ,CAACyC,MAAM,CAAC,CAAC;QAC1B,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACApB,eAAe,CAACP,KAAK,EAAEV,MAAM,CAAC;IAChC;EACF,CAAC,CAAC,CACD2B,KAAK,CAAC,MAAM;IACXZ,OAAO,CAACC,GAAG,CACT,+DACF,CAAC;EACH,CAAC,CAAC;AACN;AAEA,OAAO,SAASoB,UAAUA,CAAA,EAAG;EAC3B,IAAI,eAAe,IAAInC,SAAS,EAAE;IAChCA,SAAS,CAACW,aAAa,CAACC,KAAK,CAC1BC,IAAI,CAAEI,YAAY,IAAK;MACtBA,YAAY,CAACkB,UAAU,CAAC,CAAC;IAC3B,CAAC,CAAC,CACDT,KAAK,CAAEC,KAAK,IAAK;MAChBb,OAAO,CAACa,KAAK,CAACA,KAAK,CAACU,OAAO,CAAC;IAC9B,CAAC,CAAC;EACN;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}