{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/PreviewPage.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PreviewPage = () => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: \"flex flex-col items-center justify-center min-h-screen\",\n  children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n    className: \"text-2xl font-semibold mb-4\",\n    children: \"Preview do Ficheiro\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"w-64 h-80 bg-gray-200 flex items-center justify-center mb-4\",\n    children: \"[Preview]\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n    className: \"px-4 py-2 bg-blue-600 text-white rounded\",\n    children: \"Escolher Op\\xE7\\xF5es\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 7,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 4,\n  columnNumber: 3\n}, this);\n_c = PreviewPage;\nexport default PreviewPage;\nvar _c;\n$RefreshReg$(_c, \"PreviewPage\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "PreviewPage", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/PreviewPage.tsx"], "sourcesContent": ["import React from 'react';\n\nconst PreviewPage: React.FC = () => (\n  <div className=\"flex flex-col items-center justify-center min-h-screen\">\n    <h2 className=\"text-2xl font-semibold mb-4\">Preview do Ficheiro</h2>\n    <div className=\"w-64 h-80 bg-gray-200 flex items-center justify-center mb-4\">[Preview]</div>\n    <button className=\"px-4 py-2 bg-blue-600 text-white rounded\">Escolher Opções</button>\n  </div>\n);\n\nexport default PreviewPage;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,WAAqB,GAAGA,CAAA,kBAC5BD,OAAA;EAAKE,SAAS,EAAC,wDAAwD;EAAAC,QAAA,gBACrEH,OAAA;IAAIE,SAAS,EAAC,6BAA6B;IAAAC,QAAA,EAAC;EAAmB;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAI,CAAC,eACpEP,OAAA;IAAKE,SAAS,EAAC,6DAA6D;IAAAC,QAAA,EAAC;EAAS;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC,eAC5FP,OAAA;IAAQE,SAAS,EAAC,0CAA0C;IAAAC,QAAA,EAAC;EAAe;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAQ,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAClF,CACN;AAACC,EAAA,GANIP,WAAqB;AAQ3B,eAAeA,WAAW;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}