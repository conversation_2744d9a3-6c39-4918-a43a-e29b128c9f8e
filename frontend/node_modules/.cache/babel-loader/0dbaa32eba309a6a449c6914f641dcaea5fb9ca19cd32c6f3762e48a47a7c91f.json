{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/ClientPanel.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ClientPanel = () => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: \"flex flex-col items-center justify-center min-h-screen\",\n  children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n    className: \"text-2xl font-semibold mb-4\",\n    children: \"Painel do Cliente\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"w-96 bg-white shadow rounded p-4\",\n    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n      className: \"font-bold mb-2\",\n      children: \"Hist\\xF3rico de Encomendas\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n      className: \"mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"li\", {\n        children: \"Pedido #1 - Entregue\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        children: \"Pedido #2 - Em produ\\xE7\\xE3o\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 10,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"px-4 py-2 bg-blue-600 text-white rounded\",\n      children: \"Repetir Pedido\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 4,\n  columnNumber: 3\n}, this);\n_c = ClientPanel;\nexport default ClientPanel;\nvar _c;\n$RefreshReg$(_c, \"ClientPanel\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "ClientPanel", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/ClientPanel.tsx"], "sourcesContent": ["import React from 'react';\n\nconst ClientPanel: React.FC = () => (\n  <div className=\"flex flex-col items-center justify-center min-h-screen\">\n    <h2 className=\"text-2xl font-semibold mb-4\">Painel do Cliente</h2>\n    <div className=\"w-96 bg-white shadow rounded p-4\">\n      <h3 className=\"font-bold mb-2\">Histórico de Encomendas</h3>\n      <ul className=\"mb-4\">\n        <li>Pedido #1 - Entregue</li>\n        <li>Pedido #2 - Em produção</li>\n      </ul>\n      <button className=\"px-4 py-2 bg-blue-600 text-white rounded\">Repetir Pedido</button>\n    </div>\n  </div>\n);\n\nexport default ClientPanel;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,WAAqB,GAAGA,CAAA,kBAC5BD,OAAA;EAAKE,SAAS,EAAC,wDAAwD;EAAAC,QAAA,gBACrEH,OAAA;IAAIE,SAAS,EAAC,6BAA6B;IAAAC,QAAA,EAAC;EAAiB;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAI,CAAC,eAClEP,OAAA;IAAKE,SAAS,EAAC,kCAAkC;IAAAC,QAAA,gBAC/CH,OAAA;MAAIE,SAAS,EAAC,gBAAgB;MAAAC,QAAA,EAAC;IAAuB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC3DP,OAAA;MAAIE,SAAS,EAAC,MAAM;MAAAC,QAAA,gBAClBH,OAAA;QAAAG,QAAA,EAAI;MAAoB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC7BP,OAAA;QAAAG,QAAA,EAAI;MAAuB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9B,CAAC,eACLP,OAAA;MAAQE,SAAS,EAAC,0CAA0C;MAAAC,QAAA,EAAC;IAAc;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACjF,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACH,CACN;AAACC,EAAA,GAZIP,WAAqB;AAc3B,eAAeA,WAAW;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}