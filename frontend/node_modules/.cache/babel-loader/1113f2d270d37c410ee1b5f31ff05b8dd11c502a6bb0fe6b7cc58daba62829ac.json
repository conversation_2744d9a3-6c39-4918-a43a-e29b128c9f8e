{"ast": null, "code": "import React from'react';import{useNavigate}from'react-router-dom';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const CTASection=()=>{const navigate=useNavigate();const handleStartPrinting=()=>{navigate('/upload');};return/*#__PURE__*/_jsxs(\"section\",{className:\"py-20 bg-weprint-gradient relative overflow-hidden\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"absolute inset-0 opacity-10\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"absolute top-10 left-10 w-32 h-32 border-4 border-white rounded-full\"}),/*#__PURE__*/_jsx(\"div\",{className:\"absolute top-32 right-20 w-24 h-24 border-4 border-white rounded-full\"}),/*#__PURE__*/_jsx(\"div\",{className:\"absolute bottom-20 left-1/3 w-28 h-28 border-4 border-white rounded-full\"}),/*#__PURE__*/_jsx(\"div\",{className:\"absolute bottom-10 right-10 w-20 h-20 border-4 border-white rounded-full\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"container mx-auto px-6 lg:px-8 relative z-10\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"text-center text-white\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-4xl md:text-6xl font-black mb-6\",children:\"Pronto para Imprimir?\"}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-xl md:text-2xl mb-8 opacity-90 max-w-3xl mx-auto\",children:[\"Junte-se a centenas de clientes satisfeitos e experimente a qualidade WePrint.\",/*#__PURE__*/_jsx(\"span\",{className:\"font-bold\",children:\" Seu primeiro pedido com desconto especial!\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white bg-opacity-20 backdrop-blur-sm rounded-2xl p-6 max-w-2xl mx-auto mb-10\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-3xl font-black text-weprint-yellow mb-2\",children:\"\\uD83C\\uDF89 OFERTA ESPECIAL\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-lg mb-4\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"font-bold\",children:\"15% de desconto\"}),\" no seu primeiro pedido\"]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-sm opacity-80\",children:[\"Use o c\\xF3digo: \",/*#__PURE__*/_jsx(\"span\",{className:\"font-bold bg-white bg-opacity-30 px-2 py-1 rounded\",children:\"PRIMEIRO15\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col sm:flex-row gap-4 justify-center items-center mb-12\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:handleStartPrinting,className:\"bg-white text-weprint-magenta font-bold text-xl px-12 py-4 rounded-full shadow-2xl hover:shadow-3xl transform hover:scale-105 transition-all duration-300 animate-bounce-gentle\",children:\"\\uD83D\\uDE80 Come\\xE7ar Agora\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>window.open('https://wa.me/244900000000','_blank'),className:\"border-2 border-white text-white font-bold text-xl px-12 py-4 rounded-full hover:bg-white hover:text-weprint-magenta transition-all duration-300\",children:\"\\uD83D\\uDCAC Falar no WhatsApp\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-2xl mb-2\",children:\"\\uD83D\\uDCCD\"}),/*#__PURE__*/_jsx(\"div\",{className:\"font-bold mb-1\",children:\"Localiza\\xE7\\xE3o\"}),/*#__PURE__*/_jsx(\"div\",{className:\"opacity-80\",children:\"Luanda, Angola\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-2xl mb-2\",children:\"\\uD83D\\uDCDE\"}),/*#__PURE__*/_jsx(\"div\",{className:\"font-bold mb-1\",children:\"Telefone\"}),/*#__PURE__*/_jsx(\"div\",{className:\"opacity-80\",children:\"+244 900 000 000\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-2xl mb-2\",children:\"\\u2709\\uFE0F\"}),/*#__PURE__*/_jsx(\"div\",{className:\"font-bold mb-1\",children:\"Email\"}),/*#__PURE__*/_jsx(\"div\",{className:\"opacity-80\",children:\"<EMAIL>\"})]})]})]})})]});};export default CTASection;", "map": {"version": 3, "names": ["React", "useNavigate", "jsx", "_jsx", "jsxs", "_jsxs", "CTASection", "navigate", "handleStartPrinting", "className", "children", "onClick", "window", "open"], "sources": ["/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/landing/CTASection.tsx"], "sourcesContent": ["import React from 'react';\nimport { useNavigate } from 'react-router-dom';\n\nconst CTASection: React.FC = () => {\n  const navigate = useNavigate();\n\n  const handleStartPrinting = () => {\n    navigate('/upload');\n  };\n\n  return (\n    <section className=\"py-20 bg-weprint-gradient relative overflow-hidden\">\n      {/* Background Pattern */}\n      <div className=\"absolute inset-0 opacity-10\">\n        <div className=\"absolute top-10 left-10 w-32 h-32 border-4 border-white rounded-full\"></div>\n        <div className=\"absolute top-32 right-20 w-24 h-24 border-4 border-white rounded-full\"></div>\n        <div className=\"absolute bottom-20 left-1/3 w-28 h-28 border-4 border-white rounded-full\"></div>\n        <div className=\"absolute bottom-10 right-10 w-20 h-20 border-4 border-white rounded-full\"></div>\n      </div>\n\n      <div className=\"container mx-auto px-6 lg:px-8 relative z-10\">\n        <div className=\"text-center text-white\">\n          {/* Main CTA */}\n          <h2 className=\"text-4xl md:text-6xl font-black mb-6\">\n            Pronto para Imprimir?\n          </h2>\n          \n          <p className=\"text-xl md:text-2xl mb-8 opacity-90 max-w-3xl mx-auto\">\n            Junte-se a centenas de clientes satisfeitos e experimente a qualidade WePrint. \n            <span className=\"font-bold\"> Seu primeiro pedido com desconto especial!</span>\n          </p>\n\n          {/* Special Offer */}\n          <div className=\"bg-white bg-opacity-20 backdrop-blur-sm rounded-2xl p-6 max-w-2xl mx-auto mb-10\">\n            <div className=\"text-3xl font-black text-weprint-yellow mb-2\">\n              🎉 OFERTA ESPECIAL\n            </div>\n            <div className=\"text-lg mb-4\">\n              <span className=\"font-bold\">15% de desconto</span> no seu primeiro pedido\n            </div>\n            <div className=\"text-sm opacity-80\">\n              Use o código: <span className=\"font-bold bg-white bg-opacity-30 px-2 py-1 rounded\">PRIMEIRO15</span>\n            </div>\n          </div>\n\n          {/* CTA Buttons */}\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center mb-12\">\n            <button\n              onClick={handleStartPrinting}\n              className=\"bg-white text-weprint-magenta font-bold text-xl px-12 py-4 rounded-full shadow-2xl hover:shadow-3xl transform hover:scale-105 transition-all duration-300 animate-bounce-gentle\"\n            >\n              🚀 Começar Agora\n            </button>\n            <button\n              onClick={() => window.open('https://wa.me/244900000000', '_blank')}\n              className=\"border-2 border-white text-white font-bold text-xl px-12 py-4 rounded-full hover:bg-white hover:text-weprint-magenta transition-all duration-300\"\n            >\n              💬 Falar no WhatsApp\n            </button>\n          </div>\n\n          {/* Contact Info */}\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto\">\n            <div className=\"text-center\">\n              <div className=\"text-2xl mb-2\">📍</div>\n              <div className=\"font-bold mb-1\">Localização</div>\n              <div className=\"opacity-80\">Luanda, Angola</div>\n            </div>\n            \n            <div className=\"text-center\">\n              <div className=\"text-2xl mb-2\">📞</div>\n              <div className=\"font-bold mb-1\">Telefone</div>\n              <div className=\"opacity-80\">+244 900 000 000</div>\n            </div>\n            \n            <div className=\"text-center\">\n              <div className=\"text-2xl mb-2\">✉️</div>\n              <div className=\"font-bold mb-1\">Email</div>\n              <div className=\"opacity-80\"><EMAIL></div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default CTASection;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,WAAW,KAAQ,kBAAkB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE/C,KAAM,CAAAC,UAAoB,CAAGA,CAAA,GAAM,CACjC,KAAM,CAAAC,QAAQ,CAAGN,WAAW,CAAC,CAAC,CAE9B,KAAM,CAAAO,mBAAmB,CAAGA,CAAA,GAAM,CAChCD,QAAQ,CAAC,SAAS,CAAC,CACrB,CAAC,CAED,mBACEF,KAAA,YAASI,SAAS,CAAC,oDAAoD,CAAAC,QAAA,eAErEL,KAAA,QAAKI,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1CP,IAAA,QAAKM,SAAS,CAAC,sEAAsE,CAAM,CAAC,cAC5FN,IAAA,QAAKM,SAAS,CAAC,uEAAuE,CAAM,CAAC,cAC7FN,IAAA,QAAKM,SAAS,CAAC,0EAA0E,CAAM,CAAC,cAChGN,IAAA,QAAKM,SAAS,CAAC,0EAA0E,CAAM,CAAC,EAC7F,CAAC,cAENN,IAAA,QAAKM,SAAS,CAAC,8CAA8C,CAAAC,QAAA,cAC3DL,KAAA,QAAKI,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eAErCP,IAAA,OAAIM,SAAS,CAAC,sCAAsC,CAAAC,QAAA,CAAC,uBAErD,CAAI,CAAC,cAELL,KAAA,MAAGI,SAAS,CAAC,uDAAuD,CAAAC,QAAA,EAAC,gFAEnE,cAAAP,IAAA,SAAMM,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAC,6CAA2C,CAAM,CAAC,EAC7E,CAAC,cAGJL,KAAA,QAAKI,SAAS,CAAC,iFAAiF,CAAAC,QAAA,eAC9FP,IAAA,QAAKM,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,8BAE9D,CAAK,CAAC,cACNL,KAAA,QAAKI,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BP,IAAA,SAAMM,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAC,iBAAe,CAAM,CAAC,0BACpD,EAAK,CAAC,cACNL,KAAA,QAAKI,SAAS,CAAC,oBAAoB,CAAAC,QAAA,EAAC,mBACpB,cAAAP,IAAA,SAAMM,SAAS,CAAC,oDAAoD,CAAAC,QAAA,CAAC,YAAU,CAAM,CAAC,EACjG,CAAC,EACH,CAAC,cAGNL,KAAA,QAAKI,SAAS,CAAC,mEAAmE,CAAAC,QAAA,eAChFP,IAAA,WACEQ,OAAO,CAAEH,mBAAoB,CAC7BC,SAAS,CAAC,iLAAiL,CAAAC,QAAA,CAC5L,+BAED,CAAQ,CAAC,cACTP,IAAA,WACEQ,OAAO,CAAEA,CAAA,GAAMC,MAAM,CAACC,IAAI,CAAC,4BAA4B,CAAE,QAAQ,CAAE,CACnEJ,SAAS,CAAC,kJAAkJ,CAAAC,QAAA,CAC7J,gCAED,CAAQ,CAAC,EACN,CAAC,cAGNL,KAAA,QAAKI,SAAS,CAAC,yDAAyD,CAAAC,QAAA,eACtEL,KAAA,QAAKI,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BP,IAAA,QAAKM,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,cAAE,CAAK,CAAC,cACvCP,IAAA,QAAKM,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAAC,mBAAW,CAAK,CAAC,cACjDP,IAAA,QAAKM,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,gBAAc,CAAK,CAAC,EAC7C,CAAC,cAENL,KAAA,QAAKI,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BP,IAAA,QAAKM,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,cAAE,CAAK,CAAC,cACvCP,IAAA,QAAKM,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAAC,UAAQ,CAAK,CAAC,cAC9CP,IAAA,QAAKM,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,kBAAgB,CAAK,CAAC,EAC/C,CAAC,cAENL,KAAA,QAAKI,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BP,IAAA,QAAKM,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,cAAE,CAAK,CAAC,cACvCP,IAAA,QAAKM,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAAC,OAAK,CAAK,CAAC,cAC3CP,IAAA,QAAKM,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,oBAAkB,CAAK,CAAC,EACjD,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,EACC,CAAC,CAEd,CAAC,CAED,cAAe,CAAAJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}