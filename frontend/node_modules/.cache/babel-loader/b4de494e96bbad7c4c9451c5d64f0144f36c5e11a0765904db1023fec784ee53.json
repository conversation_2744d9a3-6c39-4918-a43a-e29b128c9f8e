{"ast": null, "code": "import _objectSpread from\"/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import{useState,useCallback,useEffect}from'react';import{MulticaixaService}from'../services/multicaixa';/**\n * Custom hook for managing Multicaixa payments\n */export const useMulticaixa=()=>{const[state,setState]=useState({loading:false,error:null,payment:null,paymentUrl:null,qrCode:null,status:null});/**\n   * Create a new Multicaixa payment\n   */const createPayment=useCallback(async data=>{setState(prev=>_objectSpread(_objectSpread({},prev),{},{loading:true,error:null}));try{const response=await MulticaixaService.createPayment(data);if(response.success){setState(prev=>_objectSpread(_objectSpread({},prev),{},{loading:false,paymentUrl:response.paymentUrl||null,qrCode:response.qrCode||null,status:'PENDING'}));}else{setState(prev=>_objectSpread(_objectSpread({},prev),{},{loading:false,error:response.error||'Failed to create payment'}));}return response;}catch(error){const errorMessage=error instanceof Error?error.message:'Network error';setState(prev=>_objectSpread(_objectSpread({},prev),{},{loading:false,error:errorMessage}));return{success:false,error:errorMessage};}},[]);/**\n   * Check payment status\n   */const checkStatus=useCallback(async paymentId=>{setState(prev=>_objectSpread(_objectSpread({},prev),{},{loading:true,error:null}));try{const response=await MulticaixaService.checkPaymentStatus(paymentId);if(response.success&&response.status){setState(prev=>_objectSpread(_objectSpread({},prev),{},{loading:false,status:response.status}));}else{setState(prev=>_objectSpread(_objectSpread({},prev),{},{loading:false,error:response.error||'Failed to check payment status'}));}}catch(error){setState(prev=>_objectSpread(_objectSpread({},prev),{},{loading:false,error:error instanceof Error?error.message:'Network error'}));}},[]);/**\n   * Request a refund\n   */const requestRefund=useCallback(async(paymentId,amount,reason)=>{setState(prev=>_objectSpread(_objectSpread({},prev),{},{loading:true,error:null}));try{const response=await MulticaixaService.requestRefund(paymentId,amount,reason);if(response.success){setState(prev=>_objectSpread(_objectSpread({},prev),{},{loading:false,status:'REFUNDED'}));return true;}else{setState(prev=>_objectSpread(_objectSpread({},prev),{},{loading:false,error:response.error||'Failed to request refund'}));return false;}}catch(error){setState(prev=>_objectSpread(_objectSpread({},prev),{},{loading:false,error:error instanceof Error?error.message:'Network error'}));return false;}},[]);/**\n   * Clear error state\n   */const clearError=useCallback(()=>{setState(prev=>_objectSpread(_objectSpread({},prev),{},{error:null}));},[]);/**\n   * Reset all state\n   */const reset=useCallback(()=>{setState({loading:false,error:null,payment:null,paymentUrl:null,qrCode:null,status:null});},[]);return _objectSpread(_objectSpread({},state),{},{createPayment,checkStatus,requestRefund,clearError,reset});};/**\n * Hook for polling payment status\n */export const usePaymentStatusPolling=function(paymentId){let intervalMs=arguments.length>1&&arguments[1]!==undefined?arguments[1]:5000;let maxAttempts=arguments.length>2&&arguments[2]!==undefined?arguments[2]:60;const[attempts,setAttempts]=useState(0);const[isPolling,setIsPolling]=useState(false);const{checkStatus,status,loading,error}=useMulticaixa();useEffect(()=>{if(!paymentId||MulticaixaService.isPaymentFinal(status||'')){setIsPolling(false);return;}if(attempts>=maxAttempts){setIsPolling(false);return;}setIsPolling(true);const interval=setInterval(async()=>{await checkStatus(paymentId);setAttempts(prev=>prev+1);},intervalMs);return()=>{clearInterval(interval);setIsPolling(false);};},[paymentId,status,attempts,maxAttempts,intervalMs,checkStatus]);const startPolling=useCallback(()=>{setAttempts(0);setIsPolling(true);},[]);const stopPolling=useCallback(()=>{setIsPolling(false);},[]);return{isPolling,attempts,maxAttempts,status,loading,error,startPolling,stopPolling};};export default useMulticaixa;", "map": {"version": 3, "names": ["useState", "useCallback", "useEffect", "MulticaixaService", "useMulticaixa", "state", "setState", "loading", "error", "payment", "paymentUrl", "qrCode", "status", "createPayment", "data", "prev", "_objectSpread", "response", "success", "errorMessage", "Error", "message", "checkStatus", "paymentId", "checkPaymentStatus", "requestRefund", "amount", "reason", "clearError", "reset", "usePaymentStatusPolling", "intervalMs", "arguments", "length", "undefined", "maxAttempts", "attempts", "setAttempts", "isPolling", "setIsPolling", "isPaymentFinal", "interval", "setInterval", "clearInterval", "startPolling", "stopPolling"], "sources": ["/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/hooks/useMulticaixa.ts"], "sourcesContent": ["import { useState, useCallback, useEffect } from 'react';\nimport { MulticaixaService } from '../services/multicaixa';\nimport { \n  MulticaixaPaymentRequest, \n  MulticaixaPaymentResponse, \n  Payment,\n  PaymentStatus \n} from '../types';\n\ninterface UseMulticaixaState {\n  loading: boolean;\n  error: string | null;\n  payment: Payment | null;\n  paymentUrl: string | null;\n  qrCode: string | null;\n  status: PaymentStatus | null;\n}\n\ninterface UseMulticaixaActions {\n  createPayment: (data: MulticaixaPaymentRequest) => Promise<MulticaixaPaymentResponse>;\n  checkStatus: (paymentId: string) => Promise<void>;\n  requestRefund: (paymentId: string, amount?: number, reason?: string) => Promise<boolean>;\n  clearError: () => void;\n  reset: () => void;\n}\n\n/**\n * Custom hook for managing Multicaixa payments\n */\nexport const useMulticaixa = (): UseMulticaixaState & UseMulticaixaActions => {\n  const [state, setState] = useState<UseMulticaixaState>({\n    loading: false,\n    error: null,\n    payment: null,\n    paymentUrl: null,\n    qrCode: null,\n    status: null,\n  });\n\n  /**\n   * Create a new Multicaixa payment\n   */\n  const createPayment = useCallback(async (data: MulticaixaPaymentRequest): Promise<MulticaixaPaymentResponse> => {\n    setState(prev => ({ ...prev, loading: true, error: null }));\n\n    try {\n      const response = await MulticaixaService.createPayment(data);\n      \n      if (response.success) {\n        setState(prev => ({\n          ...prev,\n          loading: false,\n          paymentUrl: response.paymentUrl || null,\n          qrCode: response.qrCode || null,\n          status: 'PENDING',\n        }));\n      } else {\n        setState(prev => ({\n          ...prev,\n          loading: false,\n          error: response.error || 'Failed to create payment',\n        }));\n      }\n\n      return response;\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Network error';\n      setState(prev => ({\n        ...prev,\n        loading: false,\n        error: errorMessage,\n      }));\n      \n      return {\n        success: false,\n        error: errorMessage,\n      };\n    }\n  }, []);\n\n  /**\n   * Check payment status\n   */\n  const checkStatus = useCallback(async (paymentId: string): Promise<void> => {\n    setState(prev => ({ ...prev, loading: true, error: null }));\n\n    try {\n      const response = await MulticaixaService.checkPaymentStatus(paymentId);\n      \n      if (response.success && response.status) {\n        setState(prev => ({\n          ...prev,\n          loading: false,\n          status: response.status!,\n        }));\n      } else {\n        setState(prev => ({\n          ...prev,\n          loading: false,\n          error: response.error || 'Failed to check payment status',\n        }));\n      }\n    } catch (error) {\n      setState(prev => ({\n        ...prev,\n        loading: false,\n        error: error instanceof Error ? error.message : 'Network error',\n      }));\n    }\n  }, []);\n\n  /**\n   * Request a refund\n   */\n  const requestRefund = useCallback(async (\n    paymentId: string, \n    amount?: number, \n    reason?: string\n  ): Promise<boolean> => {\n    setState(prev => ({ ...prev, loading: true, error: null }));\n\n    try {\n      const response = await MulticaixaService.requestRefund(paymentId, amount, reason);\n      \n      if (response.success) {\n        setState(prev => ({\n          ...prev,\n          loading: false,\n          status: 'REFUNDED',\n        }));\n        return true;\n      } else {\n        setState(prev => ({\n          ...prev,\n          loading: false,\n          error: response.error || 'Failed to request refund',\n        }));\n        return false;\n      }\n    } catch (error) {\n      setState(prev => ({\n        ...prev,\n        loading: false,\n        error: error instanceof Error ? error.message : 'Network error',\n      }));\n      return false;\n    }\n  }, []);\n\n  /**\n   * Clear error state\n   */\n  const clearError = useCallback(() => {\n    setState(prev => ({ ...prev, error: null }));\n  }, []);\n\n  /**\n   * Reset all state\n   */\n  const reset = useCallback(() => {\n    setState({\n      loading: false,\n      error: null,\n      payment: null,\n      paymentUrl: null,\n      qrCode: null,\n      status: null,\n    });\n  }, []);\n\n  return {\n    ...state,\n    createPayment,\n    checkStatus,\n    requestRefund,\n    clearError,\n    reset,\n  };\n};\n\n/**\n * Hook for polling payment status\n */\nexport const usePaymentStatusPolling = (\n  paymentId: string | null,\n  intervalMs: number = 5000,\n  maxAttempts: number = 60\n) => {\n  const [attempts, setAttempts] = useState(0);\n  const [isPolling, setIsPolling] = useState(false);\n  const { checkStatus, status, loading, error } = useMulticaixa();\n\n  useEffect(() => {\n    if (!paymentId || MulticaixaService.isPaymentFinal(status || '')) {\n      setIsPolling(false);\n      return;\n    }\n\n    if (attempts >= maxAttempts) {\n      setIsPolling(false);\n      return;\n    }\n\n    setIsPolling(true);\n    const interval = setInterval(async () => {\n      await checkStatus(paymentId);\n      setAttempts(prev => prev + 1);\n    }, intervalMs);\n\n    return () => {\n      clearInterval(interval);\n      setIsPolling(false);\n    };\n  }, [paymentId, status, attempts, maxAttempts, intervalMs, checkStatus]);\n\n  const startPolling = useCallback(() => {\n    setAttempts(0);\n    setIsPolling(true);\n  }, []);\n\n  const stopPolling = useCallback(() => {\n    setIsPolling(false);\n  }, []);\n\n  return {\n    isPolling,\n    attempts,\n    maxAttempts,\n    status,\n    loading,\n    error,\n    startPolling,\n    stopPolling,\n  };\n};\n\nexport default useMulticaixa;\n"], "mappings": "sIAAA,OAASA,QAAQ,CAAEC,WAAW,CAAEC,SAAS,KAAQ,OAAO,CACxD,OAASC,iBAAiB,KAAQ,wBAAwB,CAyB1D;AACA;AACA,GACA,MAAO,MAAM,CAAAC,aAAa,CAAGA,CAAA,GAAiD,CAC5E,KAAM,CAACC,KAAK,CAAEC,QAAQ,CAAC,CAAGN,QAAQ,CAAqB,CACrDO,OAAO,CAAE,KAAK,CACdC,KAAK,CAAE,IAAI,CACXC,OAAO,CAAE,IAAI,CACbC,UAAU,CAAE,IAAI,CAChBC,MAAM,CAAE,IAAI,CACZC,MAAM,CAAE,IACV,CAAC,CAAC,CAEF;AACF;AACA,KACE,KAAM,CAAAC,aAAa,CAAGZ,WAAW,CAAC,KAAO,CAAAa,IAA8B,EAAyC,CAC9GR,QAAQ,CAACS,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAER,OAAO,CAAE,IAAI,CAAEC,KAAK,CAAE,IAAI,EAAG,CAAC,CAE3D,GAAI,CACF,KAAM,CAAAS,QAAQ,CAAG,KAAM,CAAAd,iBAAiB,CAACU,aAAa,CAACC,IAAI,CAAC,CAE5D,GAAIG,QAAQ,CAACC,OAAO,CAAE,CACpBZ,QAAQ,CAACS,IAAI,EAAAC,aAAA,CAAAA,aAAA,IACRD,IAAI,MACPR,OAAO,CAAE,KAAK,CACdG,UAAU,CAAEO,QAAQ,CAACP,UAAU,EAAI,IAAI,CACvCC,MAAM,CAAEM,QAAQ,CAACN,MAAM,EAAI,IAAI,CAC/BC,MAAM,CAAE,SAAS,EACjB,CAAC,CACL,CAAC,IAAM,CACLN,QAAQ,CAACS,IAAI,EAAAC,aAAA,CAAAA,aAAA,IACRD,IAAI,MACPR,OAAO,CAAE,KAAK,CACdC,KAAK,CAAES,QAAQ,CAACT,KAAK,EAAI,0BAA0B,EACnD,CAAC,CACL,CAEA,MAAO,CAAAS,QAAQ,CACjB,CAAE,MAAOT,KAAK,CAAE,CACd,KAAM,CAAAW,YAAY,CAAGX,KAAK,WAAY,CAAAY,KAAK,CAAGZ,KAAK,CAACa,OAAO,CAAG,eAAe,CAC7Ef,QAAQ,CAACS,IAAI,EAAAC,aAAA,CAAAA,aAAA,IACRD,IAAI,MACPR,OAAO,CAAE,KAAK,CACdC,KAAK,CAAEW,YAAY,EACnB,CAAC,CAEH,MAAO,CACLD,OAAO,CAAE,KAAK,CACdV,KAAK,CAAEW,YACT,CAAC,CACH,CACF,CAAC,CAAE,EAAE,CAAC,CAEN;AACF;AACA,KACE,KAAM,CAAAG,WAAW,CAAGrB,WAAW,CAAC,KAAO,CAAAsB,SAAiB,EAAoB,CAC1EjB,QAAQ,CAACS,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAER,OAAO,CAAE,IAAI,CAAEC,KAAK,CAAE,IAAI,EAAG,CAAC,CAE3D,GAAI,CACF,KAAM,CAAAS,QAAQ,CAAG,KAAM,CAAAd,iBAAiB,CAACqB,kBAAkB,CAACD,SAAS,CAAC,CAEtE,GAAIN,QAAQ,CAACC,OAAO,EAAID,QAAQ,CAACL,MAAM,CAAE,CACvCN,QAAQ,CAACS,IAAI,EAAAC,aAAA,CAAAA,aAAA,IACRD,IAAI,MACPR,OAAO,CAAE,KAAK,CACdK,MAAM,CAAEK,QAAQ,CAACL,MAAO,EACxB,CAAC,CACL,CAAC,IAAM,CACLN,QAAQ,CAACS,IAAI,EAAAC,aAAA,CAAAA,aAAA,IACRD,IAAI,MACPR,OAAO,CAAE,KAAK,CACdC,KAAK,CAAES,QAAQ,CAACT,KAAK,EAAI,gCAAgC,EACzD,CAAC,CACL,CACF,CAAE,MAAOA,KAAK,CAAE,CACdF,QAAQ,CAACS,IAAI,EAAAC,aAAA,CAAAA,aAAA,IACRD,IAAI,MACPR,OAAO,CAAE,KAAK,CACdC,KAAK,CAAEA,KAAK,WAAY,CAAAY,KAAK,CAAGZ,KAAK,CAACa,OAAO,CAAG,eAAe,EAC/D,CAAC,CACL,CACF,CAAC,CAAE,EAAE,CAAC,CAEN;AACF;AACA,KACE,KAAM,CAAAI,aAAa,CAAGxB,WAAW,CAAC,MAChCsB,SAAiB,CACjBG,MAAe,CACfC,MAAe,GACM,CACrBrB,QAAQ,CAACS,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAER,OAAO,CAAE,IAAI,CAAEC,KAAK,CAAE,IAAI,EAAG,CAAC,CAE3D,GAAI,CACF,KAAM,CAAAS,QAAQ,CAAG,KAAM,CAAAd,iBAAiB,CAACsB,aAAa,CAACF,SAAS,CAAEG,MAAM,CAAEC,MAAM,CAAC,CAEjF,GAAIV,QAAQ,CAACC,OAAO,CAAE,CACpBZ,QAAQ,CAACS,IAAI,EAAAC,aAAA,CAAAA,aAAA,IACRD,IAAI,MACPR,OAAO,CAAE,KAAK,CACdK,MAAM,CAAE,UAAU,EAClB,CAAC,CACH,MAAO,KAAI,CACb,CAAC,IAAM,CACLN,QAAQ,CAACS,IAAI,EAAAC,aAAA,CAAAA,aAAA,IACRD,IAAI,MACPR,OAAO,CAAE,KAAK,CACdC,KAAK,CAAES,QAAQ,CAACT,KAAK,EAAI,0BAA0B,EACnD,CAAC,CACH,MAAO,MAAK,CACd,CACF,CAAE,MAAOA,KAAK,CAAE,CACdF,QAAQ,CAACS,IAAI,EAAAC,aAAA,CAAAA,aAAA,IACRD,IAAI,MACPR,OAAO,CAAE,KAAK,CACdC,KAAK,CAAEA,KAAK,WAAY,CAAAY,KAAK,CAAGZ,KAAK,CAACa,OAAO,CAAG,eAAe,EAC/D,CAAC,CACH,MAAO,MAAK,CACd,CACF,CAAC,CAAE,EAAE,CAAC,CAEN;AACF;AACA,KACE,KAAM,CAAAO,UAAU,CAAG3B,WAAW,CAAC,IAAM,CACnCK,QAAQ,CAACS,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAEP,KAAK,CAAE,IAAI,EAAG,CAAC,CAC9C,CAAC,CAAE,EAAE,CAAC,CAEN;AACF;AACA,KACE,KAAM,CAAAqB,KAAK,CAAG5B,WAAW,CAAC,IAAM,CAC9BK,QAAQ,CAAC,CACPC,OAAO,CAAE,KAAK,CACdC,KAAK,CAAE,IAAI,CACXC,OAAO,CAAE,IAAI,CACbC,UAAU,CAAE,IAAI,CAChBC,MAAM,CAAE,IAAI,CACZC,MAAM,CAAE,IACV,CAAC,CAAC,CACJ,CAAC,CAAE,EAAE,CAAC,CAEN,OAAAI,aAAA,CAAAA,aAAA,IACKX,KAAK,MACRQ,aAAa,CACbS,WAAW,CACXG,aAAa,CACbG,UAAU,CACVC,KAAK,GAET,CAAC,CAED;AACA;AACA,GACA,MAAO,MAAM,CAAAC,uBAAuB,CAAG,QAAAA,CACrCP,SAAwB,CAGrB,IAFH,CAAAQ,UAAkB,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,IAAI,IACzB,CAAAG,WAAmB,CAAAH,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,EAAE,CAExB,KAAM,CAACI,QAAQ,CAAEC,WAAW,CAAC,CAAGrC,QAAQ,CAAC,CAAC,CAAC,CAC3C,KAAM,CAACsC,SAAS,CAAEC,YAAY,CAAC,CAAGvC,QAAQ,CAAC,KAAK,CAAC,CACjD,KAAM,CAAEsB,WAAW,CAAEV,MAAM,CAAEL,OAAO,CAAEC,KAAM,CAAC,CAAGJ,aAAa,CAAC,CAAC,CAE/DF,SAAS,CAAC,IAAM,CACd,GAAI,CAACqB,SAAS,EAAIpB,iBAAiB,CAACqC,cAAc,CAAC5B,MAAM,EAAI,EAAE,CAAC,CAAE,CAChE2B,YAAY,CAAC,KAAK,CAAC,CACnB,OACF,CAEA,GAAIH,QAAQ,EAAID,WAAW,CAAE,CAC3BI,YAAY,CAAC,KAAK,CAAC,CACnB,OACF,CAEAA,YAAY,CAAC,IAAI,CAAC,CAClB,KAAM,CAAAE,QAAQ,CAAGC,WAAW,CAAC,SAAY,CACvC,KAAM,CAAApB,WAAW,CAACC,SAAS,CAAC,CAC5Bc,WAAW,CAACtB,IAAI,EAAIA,IAAI,CAAG,CAAC,CAAC,CAC/B,CAAC,CAAEgB,UAAU,CAAC,CAEd,MAAO,IAAM,CACXY,aAAa,CAACF,QAAQ,CAAC,CACvBF,YAAY,CAAC,KAAK,CAAC,CACrB,CAAC,CACH,CAAC,CAAE,CAAChB,SAAS,CAAEX,MAAM,CAAEwB,QAAQ,CAAED,WAAW,CAAEJ,UAAU,CAAET,WAAW,CAAC,CAAC,CAEvE,KAAM,CAAAsB,YAAY,CAAG3C,WAAW,CAAC,IAAM,CACrCoC,WAAW,CAAC,CAAC,CAAC,CACdE,YAAY,CAAC,IAAI,CAAC,CACpB,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAM,WAAW,CAAG5C,WAAW,CAAC,IAAM,CACpCsC,YAAY,CAAC,KAAK,CAAC,CACrB,CAAC,CAAE,EAAE,CAAC,CAEN,MAAO,CACLD,SAAS,CACTF,QAAQ,CACRD,WAAW,CACXvB,MAAM,CACNL,OAAO,CACPC,KAAK,CACLoC,YAAY,CACZC,WACF,CAAC,CACH,CAAC,CAED,cAAe,CAAAzC,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}