{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/PaymentFlow.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useMulticaixa, usePaymentStatusPolling } from '../hooks/useMulticaixa';\nimport { MulticaixaService } from '../services/multicaixa';\nimport PaymentStatus from './PaymentStatus';\nimport PaymentQRCode from './PaymentQRCode';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PaymentFlow = ({\n  orderId,\n  amount,\n  currency = 'AOA',\n  description,\n  customerEmail,\n  customerPhone,\n  onPaymentComplete,\n  onPaymentFailed,\n  onCancel,\n  className = ''\n}) => {\n  _s();\n  const [paymentId, setPaymentId] = useState(null);\n  const [transactionId, setTransactionId] = useState(null);\n  const [expiresAt, setExpiresAt] = useState(null);\n  const {\n    loading,\n    error,\n    paymentUrl,\n    qrCode,\n    status,\n    createPayment,\n    clearError,\n    reset\n  } = useMulticaixa();\n  const {\n    isPolling,\n    status: pollingStatus,\n    startPolling,\n    stopPolling\n  } = usePaymentStatusPolling(paymentId, 5000, 60);\n\n  // Use polling status if available, otherwise use hook status\n  const currentStatus = pollingStatus || status;\n  useEffect(() => {\n    // Handle payment completion\n    if (currentStatus === 'COMPLETED' && paymentId) {\n      stopPolling();\n      onPaymentComplete === null || onPaymentComplete === void 0 ? void 0 : onPaymentComplete(paymentId);\n    }\n\n    // Handle payment failure\n    if (['FAILED', 'EXPIRED', 'CANCELLED'].includes(currentStatus || '')) {\n      stopPolling();\n      if (currentStatus === 'FAILED') {\n        onPaymentFailed === null || onPaymentFailed === void 0 ? void 0 : onPaymentFailed('Payment failed');\n      } else if (currentStatus === 'EXPIRED') {\n        onPaymentFailed === null || onPaymentFailed === void 0 ? void 0 : onPaymentFailed('Payment expired');\n      }\n    }\n  }, [currentStatus, paymentId, onPaymentComplete, onPaymentFailed, stopPolling]);\n  const handleCreatePayment = async () => {\n    clearError();\n\n    // Validate amount\n    if (!MulticaixaService.validatePaymentAmount(amount, currency)) {\n      const minAmount = currency === 'AOA' ? '100 AOA' : '1 EUR (≈850 AOA)';\n      onPaymentFailed === null || onPaymentFailed === void 0 ? void 0 : onPaymentFailed(`Valor mínimo de pagamento: ${minAmount}`);\n      return;\n    }\n    const paymentData = {\n      orderId,\n      amount,\n      currency,\n      description,\n      customerEmail,\n      customerPhone\n    };\n    const response = await createPayment(paymentData);\n    if (response.success) {\n      setPaymentId(response.paymentId || null);\n      setTransactionId(response.transactionId || null);\n      setExpiresAt(response.expiresAt || null);\n\n      // Start polling for status updates\n      if (response.paymentId) {\n        startPolling();\n      }\n    } else {\n      onPaymentFailed === null || onPaymentFailed === void 0 ? void 0 : onPaymentFailed(response.error || 'Failed to create payment');\n    }\n  };\n  const handleCancel = () => {\n    stopPolling();\n    reset();\n    setPaymentId(null);\n    setTransactionId(null);\n    setExpiresAt(null);\n    onCancel === null || onCancel === void 0 ? void 0 : onCancel();\n  };\n  const handleRetry = () => {\n    reset();\n    setPaymentId(null);\n    setTransactionId(null);\n    setExpiresAt(null);\n    handleCreatePayment();\n  };\n  const formatAmount = (amount, currency) => {\n    if (currency === 'AOA') {\n      return MulticaixaService.formatAoaAmount(amount);\n    }\n    return `${amount} ${currency}`;\n  };\n\n  // Initial state - show payment details and create button\n  if (!paymentId && !loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `bg-white rounded-lg shadow-lg p-6 max-w-md mx-auto ${className}`,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-gray-900 mb-2\",\n          children: \"Confirmar Pagamento\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-3xl font-bold text-blue-600 mb-2\",\n          children: formatAmount(amount, currency)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 11\n        }, this), description && /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-gray-600\",\n          children: description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 9\n      }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-4 p-3 bg-red-50 border border-red-200 rounded-md\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-red-700\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex gap-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleCreatePayment,\n          disabled: loading,\n          className: \"flex-1 bg-blue-600 text-white py-3 px-4 rounded-md font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n          children: loading ? 'Criando...' : 'Pagar com Multicaixa'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 11\n        }, this), onCancel && /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleCancel,\n          className: \"px-4 py-3 border border-gray-300 text-gray-700 rounded-md font-medium hover:bg-gray-50 transition-colors\",\n          children: \"Cancelar\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 138,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Loading state\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `bg-white rounded-lg shadow-lg p-6 max-w-md mx-auto ${className}`,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Criando pagamento...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 181,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Payment created - show QR code and status\n  if (paymentUrl && qrCode) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `space-y-4 ${className}`,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-lg p-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900\",\n              children: \"Status do Pagamento\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 15\n            }, this), transactionId && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-500\",\n              children: [\"ID: \", transactionId]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(PaymentStatus, {\n            status: currentStatus || 'PENDING'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 11\n        }, this), isPolling && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-3 flex items-center gap-2 text-sm text-blue-600\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 15\n          }, this), \"Verificando status...\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 9\n      }, this), currentStatus !== 'COMPLETED' && /*#__PURE__*/_jsxDEV(PaymentQRCode, {\n        qrCode: qrCode,\n        paymentUrl: paymentUrl,\n        amount: amount,\n        currency: currency,\n        expiresAt: expiresAt || undefined\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 218,\n        columnNumber: 11\n      }, this), currentStatus === 'COMPLETED' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-green-50 border border-green-200 rounded-lg p-6 text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n          className: \"w-16 h-16 text-green-500 mx-auto mb-4\",\n          fill: \"currentColor\",\n          viewBox: \"0 0 20 20\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            fillRule: \"evenodd\",\n            d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n            clipRule: \"evenodd\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-green-900 mb-2\",\n          children: \"Pagamento Conclu\\xEDdo!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-green-700\",\n          children: \"O seu pagamento foi processado com sucesso.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 11\n      }, this), ['FAILED', 'EXPIRED', 'CANCELLED'].includes(currentStatus || '') && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-red-50 border border-red-200 rounded-lg p-6 text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n          className: \"w-16 h-16 text-red-500 mx-auto mb-4\",\n          fill: \"currentColor\",\n          viewBox: \"0 0 20 20\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            fillRule: \"evenodd\",\n            d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n            clipRule: \"evenodd\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-red-900 mb-2\",\n          children: currentStatus === 'EXPIRED' ? 'Pagamento Expirado' : 'Pagamento Falhado'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-red-700 mb-4\",\n          children: currentStatus === 'EXPIRED' ? 'O tempo limite para pagamento foi excedido.' : 'Ocorreu um erro durante o processamento do pagamento.'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex gap-3 justify-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleRetry,\n            className: \"bg-blue-600 text-white py-2 px-4 rounded-md font-medium hover:bg-blue-700 transition-colors\",\n            children: \"Tentar Novamente\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 15\n          }, this), onCancel && /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleCancel,\n            className: \"border border-gray-300 text-gray-700 py-2 px-4 rounded-md font-medium hover:bg-gray-50 transition-colors\",\n            children: \"Cancelar\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 244,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 193,\n      columnNumber: 7\n    }, this);\n  }\n  return null;\n};\n_s(PaymentFlow, \"ezoHJ2Ph+r6FnI2qOoajuU4vCHc=\", false, function () {\n  return [useMulticaixa, usePaymentStatusPolling];\n});\n_c = PaymentFlow;\nexport default PaymentFlow;\nvar _c;\n$RefreshReg$(_c, \"PaymentFlow\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useMulticaixa", "usePaymentStatusPolling", "MulticaixaService", "PaymentStatus", "PaymentQRCode", "jsxDEV", "_jsxDEV", "PaymentFlow", "orderId", "amount", "currency", "description", "customerEmail", "customerPhone", "onPaymentComplete", "onPaymentFailed", "onCancel", "className", "_s", "paymentId", "setPaymentId", "transactionId", "setTransactionId", "expiresAt", "setExpiresAt", "loading", "error", "paymentUrl", "qrCode", "status", "createPayment", "clearError", "reset", "isPolling", "pollingStatus", "startPolling", "stopPolling", "currentStatus", "includes", "handleCreatePayment", "validatePaymentAmount", "minAmount", "paymentData", "response", "success", "handleCancel", "handleRetry", "formatAmount", "formatAoaAmount", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "disabled", "undefined", "fill", "viewBox", "fillRule", "d", "clipRule", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/PaymentFlow.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useMulticaixa, usePaymentStatusPolling } from '../hooks/useMulticaixa';\nimport { MulticaixaService } from '../services/multicaixa';\nimport PaymentStatus from './PaymentStatus';\nimport PaymentQRCode from './PaymentQRCode';\nimport { MulticaixaPaymentRequest } from '../types';\n\ninterface PaymentFlowProps {\n  orderId: string;\n  amount: number;\n  currency?: string;\n  description?: string;\n  customerEmail?: string;\n  customerPhone?: string;\n  onPaymentComplete?: (paymentId: string) => void;\n  onPaymentFailed?: (error: string) => void;\n  onCancel?: () => void;\n  className?: string;\n}\n\nconst PaymentFlow: React.FC<PaymentFlowProps> = ({\n  orderId,\n  amount,\n  currency = 'AOA',\n  description,\n  customerEmail,\n  customerPhone,\n  onPaymentComplete,\n  onPaymentFailed,\n  onCancel,\n  className = ''\n}) => {\n  const [paymentId, setPaymentId] = useState<string | null>(null);\n  const [transactionId, setTransactionId] = useState<string | null>(null);\n  const [expiresAt, setExpiresAt] = useState<string | null>(null);\n  \n  const {\n    loading,\n    error,\n    paymentUrl,\n    qrCode,\n    status,\n    createPayment,\n    clearError,\n    reset\n  } = useMulticaixa();\n\n  const {\n    isPolling,\n    status: pollingStatus,\n    startPolling,\n    stopPolling\n  } = usePaymentStatusPolling(paymentId, 5000, 60);\n\n  // Use polling status if available, otherwise use hook status\n  const currentStatus = pollingStatus || status;\n\n  useEffect(() => {\n    // Handle payment completion\n    if (currentStatus === 'COMPLETED' && paymentId) {\n      stopPolling();\n      onPaymentComplete?.(paymentId);\n    }\n    \n    // Handle payment failure\n    if (['FAILED', 'EXPIRED', 'CANCELLED'].includes(currentStatus || '')) {\n      stopPolling();\n      if (currentStatus === 'FAILED') {\n        onPaymentFailed?.('Payment failed');\n      } else if (currentStatus === 'EXPIRED') {\n        onPaymentFailed?.('Payment expired');\n      }\n    }\n  }, [currentStatus, paymentId, onPaymentComplete, onPaymentFailed, stopPolling]);\n\n  const handleCreatePayment = async () => {\n    clearError();\n    \n    // Validate amount\n    if (!MulticaixaService.validatePaymentAmount(amount, currency)) {\n      const minAmount = currency === 'AOA' ? '100 AOA' : '1 EUR (≈850 AOA)';\n      onPaymentFailed?.(`Valor mínimo de pagamento: ${minAmount}`);\n      return;\n    }\n\n    const paymentData: MulticaixaPaymentRequest = {\n      orderId,\n      amount,\n      currency,\n      description,\n      customerEmail,\n      customerPhone,\n    };\n\n    const response = await createPayment(paymentData);\n    \n    if (response.success) {\n      setPaymentId(response.paymentId || null);\n      setTransactionId(response.transactionId || null);\n      setExpiresAt(response.expiresAt || null);\n      \n      // Start polling for status updates\n      if (response.paymentId) {\n        startPolling();\n      }\n    } else {\n      onPaymentFailed?.(response.error || 'Failed to create payment');\n    }\n  };\n\n  const handleCancel = () => {\n    stopPolling();\n    reset();\n    setPaymentId(null);\n    setTransactionId(null);\n    setExpiresAt(null);\n    onCancel?.();\n  };\n\n  const handleRetry = () => {\n    reset();\n    setPaymentId(null);\n    setTransactionId(null);\n    setExpiresAt(null);\n    handleCreatePayment();\n  };\n\n  const formatAmount = (amount: number, currency: string) => {\n    if (currency === 'AOA') {\n      return MulticaixaService.formatAoaAmount(amount);\n    }\n    return `${amount} ${currency}`;\n  };\n\n  // Initial state - show payment details and create button\n  if (!paymentId && !loading) {\n    return (\n      <div className={`bg-white rounded-lg shadow-lg p-6 max-w-md mx-auto ${className}`}>\n        <div className=\"text-center mb-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">\n            Confirmar Pagamento\n          </h3>\n          <p className=\"text-3xl font-bold text-blue-600 mb-2\">\n            {formatAmount(amount, currency)}\n          </p>\n          {description && (\n            <p className=\"text-sm text-gray-600\">{description}</p>\n          )}\n        </div>\n\n        {error && (\n          <div className=\"mb-4 p-3 bg-red-50 border border-red-200 rounded-md\">\n            <p className=\"text-sm text-red-700\">{error}</p>\n          </div>\n        )}\n\n        <div className=\"flex gap-3\">\n          <button\n            onClick={handleCreatePayment}\n            disabled={loading}\n            className=\"flex-1 bg-blue-600 text-white py-3 px-4 rounded-md font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n          >\n            {loading ? 'Criando...' : 'Pagar com Multicaixa'}\n          </button>\n          {onCancel && (\n            <button\n              onClick={handleCancel}\n              className=\"px-4 py-3 border border-gray-300 text-gray-700 rounded-md font-medium hover:bg-gray-50 transition-colors\"\n            >\n              Cancelar\n            </button>\n          )}\n        </div>\n      </div>\n    );\n  }\n\n  // Loading state\n  if (loading) {\n    return (\n      <div className={`bg-white rounded-lg shadow-lg p-6 max-w-md mx-auto ${className}`}>\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600\">Criando pagamento...</p>\n        </div>\n      </div>\n    );\n  }\n\n  // Payment created - show QR code and status\n  if (paymentUrl && qrCode) {\n    return (\n      <div className={`space-y-4 ${className}`}>\n        {/* Status Header */}\n        <div className=\"bg-white rounded-lg shadow-lg p-4\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h3 className=\"text-lg font-semibold text-gray-900\">\n                Status do Pagamento\n              </h3>\n              {transactionId && (\n                <p className=\"text-sm text-gray-500\">ID: {transactionId}</p>\n              )}\n            </div>\n            <PaymentStatus status={currentStatus || 'PENDING'} />\n          </div>\n          \n          {isPolling && (\n            <div className=\"mt-3 flex items-center gap-2 text-sm text-blue-600\">\n              <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600\"></div>\n              Verificando status...\n            </div>\n          )}\n        </div>\n\n        {/* QR Code */}\n        {currentStatus !== 'COMPLETED' && (\n          <PaymentQRCode\n            qrCode={qrCode}\n            paymentUrl={paymentUrl}\n            amount={amount}\n            currency={currency}\n            expiresAt={expiresAt || undefined}\n          />\n        )}\n\n        {/* Success Message */}\n        {currentStatus === 'COMPLETED' && (\n          <div className=\"bg-green-50 border border-green-200 rounded-lg p-6 text-center\">\n            <svg className=\"w-16 h-16 text-green-500 mx-auto mb-4\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n            </svg>\n            <h3 className=\"text-lg font-semibold text-green-900 mb-2\">\n              Pagamento Concluído!\n            </h3>\n            <p className=\"text-green-700\">\n              O seu pagamento foi processado com sucesso.\n            </p>\n          </div>\n        )}\n\n        {/* Error State */}\n        {['FAILED', 'EXPIRED', 'CANCELLED'].includes(currentStatus || '') && (\n          <div className=\"bg-red-50 border border-red-200 rounded-lg p-6 text-center\">\n            <svg className=\"w-16 h-16 text-red-500 mx-auto mb-4\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\n            </svg>\n            <h3 className=\"text-lg font-semibold text-red-900 mb-2\">\n              {currentStatus === 'EXPIRED' ? 'Pagamento Expirado' : 'Pagamento Falhado'}\n            </h3>\n            <p className=\"text-red-700 mb-4\">\n              {currentStatus === 'EXPIRED' \n                ? 'O tempo limite para pagamento foi excedido.'\n                : 'Ocorreu um erro durante o processamento do pagamento.'\n              }\n            </p>\n            <div className=\"flex gap-3 justify-center\">\n              <button\n                onClick={handleRetry}\n                className=\"bg-blue-600 text-white py-2 px-4 rounded-md font-medium hover:bg-blue-700 transition-colors\"\n              >\n                Tentar Novamente\n              </button>\n              {onCancel && (\n                <button\n                  onClick={handleCancel}\n                  className=\"border border-gray-300 text-gray-700 py-2 px-4 rounded-md font-medium hover:bg-gray-50 transition-colors\"\n                >\n                  Cancelar\n                </button>\n              )}\n            </div>\n          </div>\n        )}\n      </div>\n    );\n  }\n\n  return null;\n};\n\nexport default PaymentFlow;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,aAAa,EAAEC,uBAAuB,QAAQ,wBAAwB;AAC/E,SAASC,iBAAiB,QAAQ,wBAAwB;AAC1D,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,aAAa,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAgB5C,MAAMC,WAAuC,GAAGA,CAAC;EAC/CC,OAAO;EACPC,MAAM;EACNC,QAAQ,GAAG,KAAK;EAChBC,WAAW;EACXC,aAAa;EACbC,aAAa;EACbC,iBAAiB;EACjBC,eAAe;EACfC,QAAQ;EACRC,SAAS,GAAG;AACd,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGtB,QAAQ,CAAgB,IAAI,CAAC;EAC/D,MAAM,CAACuB,aAAa,EAAEC,gBAAgB,CAAC,GAAGxB,QAAQ,CAAgB,IAAI,CAAC;EACvE,MAAM,CAACyB,SAAS,EAAEC,YAAY,CAAC,GAAG1B,QAAQ,CAAgB,IAAI,CAAC;EAE/D,MAAM;IACJ2B,OAAO;IACPC,KAAK;IACLC,UAAU;IACVC,MAAM;IACNC,MAAM;IACNC,aAAa;IACbC,UAAU;IACVC;EACF,CAAC,GAAGhC,aAAa,CAAC,CAAC;EAEnB,MAAM;IACJiC,SAAS;IACTJ,MAAM,EAAEK,aAAa;IACrBC,YAAY;IACZC;EACF,CAAC,GAAGnC,uBAAuB,CAACkB,SAAS,EAAE,IAAI,EAAE,EAAE,CAAC;;EAEhD;EACA,MAAMkB,aAAa,GAAGH,aAAa,IAAIL,MAAM;EAE7C9B,SAAS,CAAC,MAAM;IACd;IACA,IAAIsC,aAAa,KAAK,WAAW,IAAIlB,SAAS,EAAE;MAC9CiB,WAAW,CAAC,CAAC;MACbtB,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAGK,SAAS,CAAC;IAChC;;IAEA;IACA,IAAI,CAAC,QAAQ,EAAE,SAAS,EAAE,WAAW,CAAC,CAACmB,QAAQ,CAACD,aAAa,IAAI,EAAE,CAAC,EAAE;MACpED,WAAW,CAAC,CAAC;MACb,IAAIC,aAAa,KAAK,QAAQ,EAAE;QAC9BtB,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAG,gBAAgB,CAAC;MACrC,CAAC,MAAM,IAAIsB,aAAa,KAAK,SAAS,EAAE;QACtCtB,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAG,iBAAiB,CAAC;MACtC;IACF;EACF,CAAC,EAAE,CAACsB,aAAa,EAAElB,SAAS,EAAEL,iBAAiB,EAAEC,eAAe,EAAEqB,WAAW,CAAC,CAAC;EAE/E,MAAMG,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtCR,UAAU,CAAC,CAAC;;IAEZ;IACA,IAAI,CAAC7B,iBAAiB,CAACsC,qBAAqB,CAAC/B,MAAM,EAAEC,QAAQ,CAAC,EAAE;MAC9D,MAAM+B,SAAS,GAAG/B,QAAQ,KAAK,KAAK,GAAG,SAAS,GAAG,kBAAkB;MACrEK,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAG,8BAA8B0B,SAAS,EAAE,CAAC;MAC5D;IACF;IAEA,MAAMC,WAAqC,GAAG;MAC5ClC,OAAO;MACPC,MAAM;MACNC,QAAQ;MACRC,WAAW;MACXC,aAAa;MACbC;IACF,CAAC;IAED,MAAM8B,QAAQ,GAAG,MAAMb,aAAa,CAACY,WAAW,CAAC;IAEjD,IAAIC,QAAQ,CAACC,OAAO,EAAE;MACpBxB,YAAY,CAACuB,QAAQ,CAACxB,SAAS,IAAI,IAAI,CAAC;MACxCG,gBAAgB,CAACqB,QAAQ,CAACtB,aAAa,IAAI,IAAI,CAAC;MAChDG,YAAY,CAACmB,QAAQ,CAACpB,SAAS,IAAI,IAAI,CAAC;;MAExC;MACA,IAAIoB,QAAQ,CAACxB,SAAS,EAAE;QACtBgB,YAAY,CAAC,CAAC;MAChB;IACF,CAAC,MAAM;MACLpB,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAG4B,QAAQ,CAACjB,KAAK,IAAI,0BAA0B,CAAC;IACjE;EACF,CAAC;EAED,MAAMmB,YAAY,GAAGA,CAAA,KAAM;IACzBT,WAAW,CAAC,CAAC;IACbJ,KAAK,CAAC,CAAC;IACPZ,YAAY,CAAC,IAAI,CAAC;IAClBE,gBAAgB,CAAC,IAAI,CAAC;IACtBE,YAAY,CAAC,IAAI,CAAC;IAClBR,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAG,CAAC;EACd,CAAC;EAED,MAAM8B,WAAW,GAAGA,CAAA,KAAM;IACxBd,KAAK,CAAC,CAAC;IACPZ,YAAY,CAAC,IAAI,CAAC;IAClBE,gBAAgB,CAAC,IAAI,CAAC;IACtBE,YAAY,CAAC,IAAI,CAAC;IAClBe,mBAAmB,CAAC,CAAC;EACvB,CAAC;EAED,MAAMQ,YAAY,GAAGA,CAACtC,MAAc,EAAEC,QAAgB,KAAK;IACzD,IAAIA,QAAQ,KAAK,KAAK,EAAE;MACtB,OAAOR,iBAAiB,CAAC8C,eAAe,CAACvC,MAAM,CAAC;IAClD;IACA,OAAO,GAAGA,MAAM,IAAIC,QAAQ,EAAE;EAChC,CAAC;;EAED;EACA,IAAI,CAACS,SAAS,IAAI,CAACM,OAAO,EAAE;IAC1B,oBACEnB,OAAA;MAAKW,SAAS,EAAE,sDAAsDA,SAAS,EAAG;MAAAgC,QAAA,gBAChF3C,OAAA;QAAKW,SAAS,EAAC,kBAAkB;QAAAgC,QAAA,gBAC/B3C,OAAA;UAAIW,SAAS,EAAC,0CAA0C;UAAAgC,QAAA,EAAC;QAEzD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL/C,OAAA;UAAGW,SAAS,EAAC,uCAAuC;UAAAgC,QAAA,EACjDF,YAAY,CAACtC,MAAM,EAAEC,QAAQ;QAAC;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,EACH1C,WAAW,iBACVL,OAAA;UAAGW,SAAS,EAAC,uBAAuB;UAAAgC,QAAA,EAAEtC;QAAW;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CACtD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAEL3B,KAAK,iBACJpB,OAAA;QAAKW,SAAS,EAAC,qDAAqD;QAAAgC,QAAA,eAClE3C,OAAA;UAAGW,SAAS,EAAC,sBAAsB;UAAAgC,QAAA,EAAEvB;QAAK;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CACN,eAED/C,OAAA;QAAKW,SAAS,EAAC,YAAY;QAAAgC,QAAA,gBACzB3C,OAAA;UACEgD,OAAO,EAAEf,mBAAoB;UAC7BgB,QAAQ,EAAE9B,OAAQ;UAClBR,SAAS,EAAC,oJAAoJ;UAAAgC,QAAA,EAE7JxB,OAAO,GAAG,YAAY,GAAG;QAAsB;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC,EACRrC,QAAQ,iBACPV,OAAA;UACEgD,OAAO,EAAET,YAAa;UACtB5B,SAAS,EAAC,0GAA0G;UAAAgC,QAAA,EACrH;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,IAAI5B,OAAO,EAAE;IACX,oBACEnB,OAAA;MAAKW,SAAS,EAAE,sDAAsDA,SAAS,EAAG;MAAAgC,QAAA,eAChF3C,OAAA;QAAKW,SAAS,EAAC,aAAa;QAAAgC,QAAA,gBAC1B3C,OAAA;UAAKW,SAAS,EAAC;QAA6E;UAAAiC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnG/C,OAAA;UAAGW,SAAS,EAAC,eAAe;UAAAgC,QAAA,EAAC;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,IAAI1B,UAAU,IAAIC,MAAM,EAAE;IACxB,oBACEtB,OAAA;MAAKW,SAAS,EAAE,aAAaA,SAAS,EAAG;MAAAgC,QAAA,gBAEvC3C,OAAA;QAAKW,SAAS,EAAC,mCAAmC;QAAAgC,QAAA,gBAChD3C,OAAA;UAAKW,SAAS,EAAC,mCAAmC;UAAAgC,QAAA,gBAChD3C,OAAA;YAAA2C,QAAA,gBACE3C,OAAA;cAAIW,SAAS,EAAC,qCAAqC;cAAAgC,QAAA,EAAC;YAEpD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EACJhC,aAAa,iBACZf,OAAA;cAAGW,SAAS,EAAC,uBAAuB;cAAAgC,QAAA,GAAC,MAAI,EAAC5B,aAAa;YAAA;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAC5D;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACN/C,OAAA,CAACH,aAAa;YAAC0B,MAAM,EAAEQ,aAAa,IAAI;UAAU;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD,CAAC,EAELpB,SAAS,iBACR3B,OAAA;UAAKW,SAAS,EAAC,oDAAoD;UAAAgC,QAAA,gBACjE3C,OAAA;YAAKW,SAAS,EAAC;UAA8D;YAAAiC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,yBAEtF;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAGLhB,aAAa,KAAK,WAAW,iBAC5B/B,OAAA,CAACF,aAAa;QACZwB,MAAM,EAAEA,MAAO;QACfD,UAAU,EAAEA,UAAW;QACvBlB,MAAM,EAAEA,MAAO;QACfC,QAAQ,EAAEA,QAAS;QACnBa,SAAS,EAAEA,SAAS,IAAIiC;MAAU;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CACF,EAGAhB,aAAa,KAAK,WAAW,iBAC5B/B,OAAA;QAAKW,SAAS,EAAC,gEAAgE;QAAAgC,QAAA,gBAC7E3C,OAAA;UAAKW,SAAS,EAAC,uCAAuC;UAACwC,IAAI,EAAC,cAAc;UAACC,OAAO,EAAC,WAAW;UAAAT,QAAA,eAC5F3C,OAAA;YAAMqD,QAAQ,EAAC,SAAS;YAACC,CAAC,EAAC,uIAAuI;YAACC,QAAQ,EAAC;UAAS;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrL,CAAC,eACN/C,OAAA;UAAIW,SAAS,EAAC,2CAA2C;UAAAgC,QAAA,EAAC;QAE1D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL/C,OAAA;UAAGW,SAAS,EAAC,gBAAgB;UAAAgC,QAAA,EAAC;QAE9B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACN,EAGA,CAAC,QAAQ,EAAE,SAAS,EAAE,WAAW,CAAC,CAACf,QAAQ,CAACD,aAAa,IAAI,EAAE,CAAC,iBAC/D/B,OAAA;QAAKW,SAAS,EAAC,4DAA4D;QAAAgC,QAAA,gBACzE3C,OAAA;UAAKW,SAAS,EAAC,qCAAqC;UAACwC,IAAI,EAAC,cAAc;UAACC,OAAO,EAAC,WAAW;UAAAT,QAAA,eAC1F3C,OAAA;YAAMqD,QAAQ,EAAC,SAAS;YAACC,CAAC,EAAC,yNAAyN;YAACC,QAAQ,EAAC;UAAS;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvQ,CAAC,eACN/C,OAAA;UAAIW,SAAS,EAAC,yCAAyC;UAAAgC,QAAA,EACpDZ,aAAa,KAAK,SAAS,GAAG,oBAAoB,GAAG;QAAmB;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvE,CAAC,eACL/C,OAAA;UAAGW,SAAS,EAAC,mBAAmB;UAAAgC,QAAA,EAC7BZ,aAAa,KAAK,SAAS,GACxB,6CAA6C,GAC7C;QAAuD;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAE1D,CAAC,eACJ/C,OAAA;UAAKW,SAAS,EAAC,2BAA2B;UAAAgC,QAAA,gBACxC3C,OAAA;YACEgD,OAAO,EAAER,WAAY;YACrB7B,SAAS,EAAC,6FAA6F;YAAAgC,QAAA,EACxG;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EACRrC,QAAQ,iBACPV,OAAA;YACEgD,OAAO,EAAET,YAAa;YACtB5B,SAAS,EAAC,0GAA0G;YAAAgC,QAAA,EACrH;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAEV;EAEA,OAAO,IAAI;AACb,CAAC;AAACnC,EAAA,CAnQIX,WAAuC;EAAA,QAyBvCP,aAAa,EAObC,uBAAuB;AAAA;AAAA6D,EAAA,GAhCvBvD,WAAuC;AAqQ7C,eAAeA,WAAW;AAAC,IAAAuD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}