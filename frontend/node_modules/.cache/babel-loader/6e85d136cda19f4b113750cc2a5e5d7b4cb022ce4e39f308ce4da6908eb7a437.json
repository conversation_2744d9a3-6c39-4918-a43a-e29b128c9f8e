{"ast": null, "code": "import _objectSpread from\"/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState}from'react';import{useNavigate}from'react-router-dom';import{PaymentFlow}from'../components';import{MulticaixaService}from'../services/multicaixa';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const CheckoutPage=()=>{const navigate=useNavigate();const[step,setStep]=useState('form');const[formData,setFormData]=useState({name:'',deliveryAddress:'',phone:'',email:'',paymentMethod:'MULTICAIXA_EXPRESS'});const[orderId,setOrderId]=useState('');const[orderAmount]=useState(2500);// Example amount in AOA\nconst handleInputChange=e=>{const{name,value}=e.target;setFormData(prev=>_objectSpread(_objectSpread({},prev),{},{[name]:value}));};const handleSubmitForm=e=>{e.preventDefault();// Validate form\nif(!formData.name||!formData.phone||!formData.email){alert('Por favor, preencha todos os campos obrigatórios.');return;}// Generate order ID (in real app, this would come from backend)\nconst newOrderId=\"ORDER-\".concat(Date.now());setOrderId(newOrderId);// Move to payment step if Multicaixa is selected\nif(formData.paymentMethod==='MULTICAIXA_EXPRESS'){setStep('payment');}else{// Handle other payment methods\nalert('Método de pagamento não implementado ainda.');}};const handlePaymentComplete=paymentId=>{console.log('Payment completed:',paymentId);setStep('success');};const handlePaymentFailed=error=>{console.error('Payment failed:',error);alert(\"Erro no pagamento: \".concat(error));setStep('form');};const handleBackToForm=()=>{setStep('form');};const handleGoHome=()=>{navigate('/');};// Form Step\nif(step==='form'){return/*#__PURE__*/_jsx(\"div\",{className:\"min-h-screen bg-gray-50 py-8\",children:/*#__PURE__*/_jsx(\"div\",{className:\"max-w-md mx-auto\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-lg shadow-lg p-6\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-semibold text-gray-900 mb-6 text-center\",children:\"Finalizar Pedido\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"mb-6 p-4 bg-blue-50 rounded-lg\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"font-medium text-blue-900 mb-2\",children:\"Resumo do Pedido\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between text-sm text-blue-800\",children:[/*#__PURE__*/_jsx(\"span\",{children:\"Total:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"font-semibold\",children:MulticaixaService.formatAoaAmount(orderAmount)})]})]}),/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleSubmitForm,className:\"space-y-4\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-1\",children:\"Nome Completo *\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",name:\"name\",value:formData.name,onChange:handleInputChange,className:\"w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",placeholder:\"Seu nome completo\",required:true})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-1\",children:\"Email *\"}),/*#__PURE__*/_jsx(\"input\",{type:\"email\",name:\"email\",value:formData.email,onChange:handleInputChange,className:\"w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",placeholder:\"<EMAIL>\",required:true})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-1\",children:\"Telefone *\"}),/*#__PURE__*/_jsx(\"input\",{type:\"tel\",name:\"phone\",value:formData.phone,onChange:handleInputChange,className:\"w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",placeholder:\"+244 900 000 000\",required:true})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-1\",children:\"Endere\\xE7o de Entrega\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",name:\"deliveryAddress\",value:formData.deliveryAddress,onChange:handleInputChange,className:\"w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",placeholder:\"Endere\\xE7o para entrega (opcional)\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-1\",children:\"M\\xE9todo de Pagamento\"}),/*#__PURE__*/_jsxs(\"select\",{name:\"paymentMethod\",value:formData.paymentMethod,onChange:handleInputChange,className:\"w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"MULTICAIXA_EXPRESS\",children:\"Multicaixa Express\"}),/*#__PURE__*/_jsx(\"option\",{value:\"CASH\",children:\"Pagamento em Dinheiro\"}),/*#__PURE__*/_jsx(\"option\",{value:\"BANK_TRANSFER\",children:\"Transfer\\xEAncia Banc\\xE1ria\"})]})]}),/*#__PURE__*/_jsx(\"button\",{type:\"submit\",className:\"w-full bg-blue-600 text-white py-3 px-4 rounded-md font-medium hover:bg-blue-700 transition-colors\",children:\"Continuar para Pagamento\"})]})]})})});}// Payment Step\nif(step==='payment'){return/*#__PURE__*/_jsx(\"div\",{className:\"min-h-screen bg-gray-50 py-8\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"max-w-md mx-auto\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"mb-4\",children:/*#__PURE__*/_jsxs(\"button\",{onClick:handleBackToForm,className:\"flex items-center gap-2 text-blue-600 hover:text-blue-700\",children:[/*#__PURE__*/_jsx(\"svg\",{className:\"w-4 h-4\",fill:\"currentColor\",viewBox:\"0 0 20 20\",children:/*#__PURE__*/_jsx(\"path\",{fillRule:\"evenodd\",d:\"M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z\",clipRule:\"evenodd\"})}),\"Voltar\"]})}),/*#__PURE__*/_jsx(PaymentFlow,{orderId:orderId,amount:orderAmount,currency:\"AOA\",description:\"Servi\\xE7os de Impress\\xE3o WePrint AI\",customerEmail:formData.email,customerPhone:formData.phone,onPaymentComplete:handlePaymentComplete,onPaymentFailed:handlePaymentFailed,onCancel:handleBackToForm})]})});}// Success Step\nif(step==='success'){return/*#__PURE__*/_jsx(\"div\",{className:\"min-h-screen bg-gray-50 py-8\",children:/*#__PURE__*/_jsx(\"div\",{className:\"max-w-md mx-auto\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-lg shadow-lg p-6 text-center\",children:[/*#__PURE__*/_jsx(\"svg\",{className:\"w-20 h-20 text-green-500 mx-auto mb-4\",fill:\"currentColor\",viewBox:\"0 0 20 20\",children:/*#__PURE__*/_jsx(\"path\",{fillRule:\"evenodd\",d:\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",clipRule:\"evenodd\"})}),/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-semibold text-gray-900 mb-2\",children:\"Pedido Confirmado!\"}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-gray-600 mb-2\",children:[\"Obrigado, \",formData.name,\"!\"]}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-500 mb-6\",children:\"O seu pedido foi processado com sucesso. Receber\\xE1 uma confirma\\xE7\\xE3o por email em breve.\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-3\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:handleGoHome,className:\"w-full bg-blue-600 text-white py-3 px-4 rounded-md font-medium hover:bg-blue-700 transition-colors\",children:\"Voltar ao In\\xEDcio\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>navigate('/orders'),className:\"w-full border border-gray-300 text-gray-700 py-3 px-4 rounded-md font-medium hover:bg-gray-50 transition-colors\",children:\"Ver Meus Pedidos\"})]})]})})});}return null;};export default CheckoutPage;", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "PaymentFlow", "MulticaixaService", "jsx", "_jsx", "jsxs", "_jsxs", "CheckoutPage", "navigate", "step", "setStep", "formData", "setFormData", "name", "deliveryAddress", "phone", "email", "paymentMethod", "orderId", "setOrderId", "orderAmount", "handleInputChange", "e", "value", "target", "prev", "_objectSpread", "handleSubmitForm", "preventDefault", "alert", "newOrderId", "concat", "Date", "now", "handlePaymentComplete", "paymentId", "console", "log", "handlePaymentFailed", "error", "handleBackToForm", "handleGoHome", "className", "children", "formatAoaAmount", "onSubmit", "type", "onChange", "placeholder", "required", "onClick", "fill", "viewBox", "fillRule", "d", "clipRule", "amount", "currency", "description", "customerEmail", "customerPhone", "onPaymentComplete", "onPaymentFailed", "onCancel"], "sources": ["/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/CheckoutPage.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { PaymentFlow } from '../components';\nimport { MulticaixaService } from '../services/multicaixa';\n\ninterface CheckoutFormData {\n  name: string;\n  deliveryAddress: string;\n  phone: string;\n  email: string;\n  paymentMethod: 'MULTICAIXA_EXPRESS' | 'CASH' | 'BANK_TRANSFER';\n}\n\nconst CheckoutPage: React.FC = () => {\n  const navigate = useNavigate();\n  const [step, setStep] = useState<'form' | 'payment' | 'success'>('form');\n  const [formData, setFormData] = useState<CheckoutFormData>({\n    name: '',\n    deliveryAddress: '',\n    phone: '',\n    email: '',\n    paymentMethod: 'MULTICAIXA_EXPRESS',\n  });\n  const [orderId, setOrderId] = useState<string>('');\n  const [orderAmount] = useState<number>(2500); // Example amount in AOA\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({ ...prev, [name]: value }));\n  };\n\n  const handleSubmitForm = (e: React.FormEvent) => {\n    e.preventDefault();\n\n    // Validate form\n    if (!formData.name || !formData.phone || !formData.email) {\n      alert('Por favor, preencha todos os campos obrigatórios.');\n      return;\n    }\n\n    // Generate order ID (in real app, this would come from backend)\n    const newOrderId = `ORDER-${Date.now()}`;\n    setOrderId(newOrderId);\n\n    // Move to payment step if Multicaixa is selected\n    if (formData.paymentMethod === 'MULTICAIXA_EXPRESS') {\n      setStep('payment');\n    } else {\n      // Handle other payment methods\n      alert('Método de pagamento não implementado ainda.');\n    }\n  };\n\n  const handlePaymentComplete = (paymentId: string) => {\n    console.log('Payment completed:', paymentId);\n    setStep('success');\n  };\n\n  const handlePaymentFailed = (error: string) => {\n    console.error('Payment failed:', error);\n    alert(`Erro no pagamento: ${error}`);\n    setStep('form');\n  };\n\n  const handleBackToForm = () => {\n    setStep('form');\n  };\n\n  const handleGoHome = () => {\n    navigate('/');\n  };\n\n  // Form Step\n  if (step === 'form') {\n    return (\n      <div className=\"min-h-screen bg-gray-50 py-8\">\n        <div className=\"max-w-md mx-auto\">\n          <div className=\"bg-white rounded-lg shadow-lg p-6\">\n            <h2 className=\"text-2xl font-semibold text-gray-900 mb-6 text-center\">\n              Finalizar Pedido\n            </h2>\n\n            {/* Order Summary */}\n            <div className=\"mb-6 p-4 bg-blue-50 rounded-lg\">\n              <h3 className=\"font-medium text-blue-900 mb-2\">Resumo do Pedido</h3>\n              <div className=\"flex justify-between text-sm text-blue-800\">\n                <span>Total:</span>\n                <span className=\"font-semibold\">\n                  {MulticaixaService.formatAoaAmount(orderAmount)}\n                </span>\n              </div>\n            </div>\n\n            <form onSubmit={handleSubmitForm} className=\"space-y-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Nome Completo *\n                </label>\n                <input\n                  type=\"text\"\n                  name=\"name\"\n                  value={formData.name}\n                  onChange={handleInputChange}\n                  className=\"w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                  placeholder=\"Seu nome completo\"\n                  required\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Email *\n                </label>\n                <input\n                  type=\"email\"\n                  name=\"email\"\n                  value={formData.email}\n                  onChange={handleInputChange}\n                  className=\"w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                  placeholder=\"<EMAIL>\"\n                  required\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Telefone *\n                </label>\n                <input\n                  type=\"tel\"\n                  name=\"phone\"\n                  value={formData.phone}\n                  onChange={handleInputChange}\n                  className=\"w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                  placeholder=\"+244 900 000 000\"\n                  required\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Endereço de Entrega\n                </label>\n                <input\n                  type=\"text\"\n                  name=\"deliveryAddress\"\n                  value={formData.deliveryAddress}\n                  onChange={handleInputChange}\n                  className=\"w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                  placeholder=\"Endereço para entrega (opcional)\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Método de Pagamento\n                </label>\n                <select\n                  name=\"paymentMethod\"\n                  value={formData.paymentMethod}\n                  onChange={handleInputChange}\n                  className=\"w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                >\n                  <option value=\"MULTICAIXA_EXPRESS\">Multicaixa Express</option>\n                  <option value=\"CASH\">Pagamento em Dinheiro</option>\n                  <option value=\"BANK_TRANSFER\">Transferência Bancária</option>\n                </select>\n              </div>\n\n              <button\n                type=\"submit\"\n                className=\"w-full bg-blue-600 text-white py-3 px-4 rounded-md font-medium hover:bg-blue-700 transition-colors\"\n              >\n                Continuar para Pagamento\n              </button>\n            </form>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  // Payment Step\n  if (step === 'payment') {\n    return (\n      <div className=\"min-h-screen bg-gray-50 py-8\">\n        <div className=\"max-w-md mx-auto\">\n          <div className=\"mb-4\">\n            <button\n              onClick={handleBackToForm}\n              className=\"flex items-center gap-2 text-blue-600 hover:text-blue-700\"\n            >\n              <svg className=\"w-4 h-4\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                <path fillRule=\"evenodd\" d=\"M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z\" clipRule=\"evenodd\" />\n              </svg>\n              Voltar\n            </button>\n          </div>\n\n          <PaymentFlow\n            orderId={orderId}\n            amount={orderAmount}\n            currency=\"AOA\"\n            description=\"Serviços de Impressão WePrint AI\"\n            customerEmail={formData.email}\n            customerPhone={formData.phone}\n            onPaymentComplete={handlePaymentComplete}\n            onPaymentFailed={handlePaymentFailed}\n            onCancel={handleBackToForm}\n          />\n        </div>\n      </div>\n    );\n  }\n\n  // Success Step\n  if (step === 'success') {\n    return (\n      <div className=\"min-h-screen bg-gray-50 py-8\">\n        <div className=\"max-w-md mx-auto\">\n          <div className=\"bg-white rounded-lg shadow-lg p-6 text-center\">\n            <svg className=\"w-20 h-20 text-green-500 mx-auto mb-4\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n            </svg>\n\n            <h2 className=\"text-2xl font-semibold text-gray-900 mb-2\">\n              Pedido Confirmado!\n            </h2>\n\n            <p className=\"text-gray-600 mb-2\">\n              Obrigado, {formData.name}!\n            </p>\n\n            <p className=\"text-sm text-gray-500 mb-6\">\n              O seu pedido foi processado com sucesso. Receberá uma confirmação por email em breve.\n            </p>\n\n            <div className=\"space-y-3\">\n              <button\n                onClick={handleGoHome}\n                className=\"w-full bg-blue-600 text-white py-3 px-4 rounded-md font-medium hover:bg-blue-700 transition-colors\"\n              >\n                Voltar ao Início\n              </button>\n\n              <button\n                onClick={() => navigate('/orders')}\n                className=\"w-full border border-gray-300 text-gray-700 py-3 px-4 rounded-md font-medium hover:bg-gray-50 transition-colors\"\n              >\n                Ver Meus Pedidos\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return null;\n};\n\nexport default CheckoutPage;\n"], "mappings": "sIAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,OAASC,WAAW,KAAQ,eAAe,CAC3C,OAASC,iBAAiB,KAAQ,wBAAwB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAU3D,KAAM,CAAAC,YAAsB,CAAGA,CAAA,GAAM,CACnC,KAAM,CAAAC,QAAQ,CAAGR,WAAW,CAAC,CAAC,CAC9B,KAAM,CAACS,IAAI,CAAEC,OAAO,CAAC,CAAGX,QAAQ,CAAiC,MAAM,CAAC,CACxE,KAAM,CAACY,QAAQ,CAAEC,WAAW,CAAC,CAAGb,QAAQ,CAAmB,CACzDc,IAAI,CAAE,EAAE,CACRC,eAAe,CAAE,EAAE,CACnBC,KAAK,CAAE,EAAE,CACTC,KAAK,CAAE,EAAE,CACTC,aAAa,CAAE,oBACjB,CAAC,CAAC,CACF,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAGpB,QAAQ,CAAS,EAAE,CAAC,CAClD,KAAM,CAACqB,WAAW,CAAC,CAAGrB,QAAQ,CAAS,IAAI,CAAC,CAAE;AAE9C,KAAM,CAAAsB,iBAAiB,CAAIC,CAA0D,EAAK,CACxF,KAAM,CAAET,IAAI,CAAEU,KAAM,CAAC,CAAGD,CAAC,CAACE,MAAM,CAChCZ,WAAW,CAACa,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAE,CAACZ,IAAI,EAAGU,KAAK,EAAG,CAAC,CACnD,CAAC,CAED,KAAM,CAAAI,gBAAgB,CAAIL,CAAkB,EAAK,CAC/CA,CAAC,CAACM,cAAc,CAAC,CAAC,CAElB;AACA,GAAI,CAACjB,QAAQ,CAACE,IAAI,EAAI,CAACF,QAAQ,CAACI,KAAK,EAAI,CAACJ,QAAQ,CAACK,KAAK,CAAE,CACxDa,KAAK,CAAC,mDAAmD,CAAC,CAC1D,OACF,CAEA;AACA,KAAM,CAAAC,UAAU,UAAAC,MAAA,CAAYC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAE,CACxCd,UAAU,CAACW,UAAU,CAAC,CAEtB;AACA,GAAInB,QAAQ,CAACM,aAAa,GAAK,oBAAoB,CAAE,CACnDP,OAAO,CAAC,SAAS,CAAC,CACpB,CAAC,IAAM,CACL;AACAmB,KAAK,CAAC,6CAA6C,CAAC,CACtD,CACF,CAAC,CAED,KAAM,CAAAK,qBAAqB,CAAIC,SAAiB,EAAK,CACnDC,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAEF,SAAS,CAAC,CAC5CzB,OAAO,CAAC,SAAS,CAAC,CACpB,CAAC,CAED,KAAM,CAAA4B,mBAAmB,CAAIC,KAAa,EAAK,CAC7CH,OAAO,CAACG,KAAK,CAAC,iBAAiB,CAAEA,KAAK,CAAC,CACvCV,KAAK,uBAAAE,MAAA,CAAuBQ,KAAK,CAAE,CAAC,CACpC7B,OAAO,CAAC,MAAM,CAAC,CACjB,CAAC,CAED,KAAM,CAAA8B,gBAAgB,CAAGA,CAAA,GAAM,CAC7B9B,OAAO,CAAC,MAAM,CAAC,CACjB,CAAC,CAED,KAAM,CAAA+B,YAAY,CAAGA,CAAA,GAAM,CACzBjC,QAAQ,CAAC,GAAG,CAAC,CACf,CAAC,CAED;AACA,GAAIC,IAAI,GAAK,MAAM,CAAE,CACnB,mBACEL,IAAA,QAAKsC,SAAS,CAAC,8BAA8B,CAAAC,QAAA,cAC3CvC,IAAA,QAAKsC,SAAS,CAAC,kBAAkB,CAAAC,QAAA,cAC/BrC,KAAA,QAAKoC,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDvC,IAAA,OAAIsC,SAAS,CAAC,uDAAuD,CAAAC,QAAA,CAAC,kBAEtE,CAAI,CAAC,cAGLrC,KAAA,QAAKoC,SAAS,CAAC,gCAAgC,CAAAC,QAAA,eAC7CvC,IAAA,OAAIsC,SAAS,CAAC,gCAAgC,CAAAC,QAAA,CAAC,kBAAgB,CAAI,CAAC,cACpErC,KAAA,QAAKoC,SAAS,CAAC,4CAA4C,CAAAC,QAAA,eACzDvC,IAAA,SAAAuC,QAAA,CAAM,QAAM,CAAM,CAAC,cACnBvC,IAAA,SAAMsC,SAAS,CAAC,eAAe,CAAAC,QAAA,CAC5BzC,iBAAiB,CAAC0C,eAAe,CAACxB,WAAW,CAAC,CAC3C,CAAC,EACJ,CAAC,EACH,CAAC,cAENd,KAAA,SAAMuC,QAAQ,CAAElB,gBAAiB,CAACe,SAAS,CAAC,WAAW,CAAAC,QAAA,eACrDrC,KAAA,QAAAqC,QAAA,eACEvC,IAAA,UAAOsC,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,iBAEhE,CAAO,CAAC,cACRvC,IAAA,UACE0C,IAAI,CAAC,MAAM,CACXjC,IAAI,CAAC,MAAM,CACXU,KAAK,CAAEZ,QAAQ,CAACE,IAAK,CACrBkC,QAAQ,CAAE1B,iBAAkB,CAC5BqB,SAAS,CAAC,qGAAqG,CAC/GM,WAAW,CAAC,mBAAmB,CAC/BC,QAAQ,MACT,CAAC,EACC,CAAC,cAEN3C,KAAA,QAAAqC,QAAA,eACEvC,IAAA,UAAOsC,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,SAEhE,CAAO,CAAC,cACRvC,IAAA,UACE0C,IAAI,CAAC,OAAO,CACZjC,IAAI,CAAC,OAAO,CACZU,KAAK,CAAEZ,QAAQ,CAACK,KAAM,CACtB+B,QAAQ,CAAE1B,iBAAkB,CAC5BqB,SAAS,CAAC,qGAAqG,CAC/GM,WAAW,CAAC,eAAe,CAC3BC,QAAQ,MACT,CAAC,EACC,CAAC,cAEN3C,KAAA,QAAAqC,QAAA,eACEvC,IAAA,UAAOsC,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,YAEhE,CAAO,CAAC,cACRvC,IAAA,UACE0C,IAAI,CAAC,KAAK,CACVjC,IAAI,CAAC,OAAO,CACZU,KAAK,CAAEZ,QAAQ,CAACI,KAAM,CACtBgC,QAAQ,CAAE1B,iBAAkB,CAC5BqB,SAAS,CAAC,qGAAqG,CAC/GM,WAAW,CAAC,kBAAkB,CAC9BC,QAAQ,MACT,CAAC,EACC,CAAC,cAEN3C,KAAA,QAAAqC,QAAA,eACEvC,IAAA,UAAOsC,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,wBAEhE,CAAO,CAAC,cACRvC,IAAA,UACE0C,IAAI,CAAC,MAAM,CACXjC,IAAI,CAAC,iBAAiB,CACtBU,KAAK,CAAEZ,QAAQ,CAACG,eAAgB,CAChCiC,QAAQ,CAAE1B,iBAAkB,CAC5BqB,SAAS,CAAC,qGAAqG,CAC/GM,WAAW,CAAC,qCAAkC,CAC/C,CAAC,EACC,CAAC,cAEN1C,KAAA,QAAAqC,QAAA,eACEvC,IAAA,UAAOsC,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,wBAEhE,CAAO,CAAC,cACRrC,KAAA,WACEO,IAAI,CAAC,eAAe,CACpBU,KAAK,CAAEZ,QAAQ,CAACM,aAAc,CAC9B8B,QAAQ,CAAE1B,iBAAkB,CAC5BqB,SAAS,CAAC,qGAAqG,CAAAC,QAAA,eAE/GvC,IAAA,WAAQmB,KAAK,CAAC,oBAAoB,CAAAoB,QAAA,CAAC,oBAAkB,CAAQ,CAAC,cAC9DvC,IAAA,WAAQmB,KAAK,CAAC,MAAM,CAAAoB,QAAA,CAAC,uBAAqB,CAAQ,CAAC,cACnDvC,IAAA,WAAQmB,KAAK,CAAC,eAAe,CAAAoB,QAAA,CAAC,8BAAsB,CAAQ,CAAC,EACvD,CAAC,EACN,CAAC,cAENvC,IAAA,WACE0C,IAAI,CAAC,QAAQ,CACbJ,SAAS,CAAC,oGAAoG,CAAAC,QAAA,CAC/G,0BAED,CAAQ,CAAC,EACL,CAAC,EACJ,CAAC,CACH,CAAC,CACH,CAAC,CAEV,CAEA;AACA,GAAIlC,IAAI,GAAK,SAAS,CAAE,CACtB,mBACEL,IAAA,QAAKsC,SAAS,CAAC,8BAA8B,CAAAC,QAAA,cAC3CrC,KAAA,QAAKoC,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/BvC,IAAA,QAAKsC,SAAS,CAAC,MAAM,CAAAC,QAAA,cACnBrC,KAAA,WACE4C,OAAO,CAAEV,gBAAiB,CAC1BE,SAAS,CAAC,2DAA2D,CAAAC,QAAA,eAErEvC,IAAA,QAAKsC,SAAS,CAAC,SAAS,CAACS,IAAI,CAAC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAAT,QAAA,cAC9DvC,IAAA,SAAMiD,QAAQ,CAAC,SAAS,CAACC,CAAC,CAAC,uIAAuI,CAACC,QAAQ,CAAC,SAAS,CAAE,CAAC,CACrL,CAAC,SAER,EAAQ,CAAC,CACN,CAAC,cAENnD,IAAA,CAACH,WAAW,EACViB,OAAO,CAAEA,OAAQ,CACjBsC,MAAM,CAAEpC,WAAY,CACpBqC,QAAQ,CAAC,KAAK,CACdC,WAAW,CAAC,wCAAkC,CAC9CC,aAAa,CAAEhD,QAAQ,CAACK,KAAM,CAC9B4C,aAAa,CAAEjD,QAAQ,CAACI,KAAM,CAC9B8C,iBAAiB,CAAE3B,qBAAsB,CACzC4B,eAAe,CAAExB,mBAAoB,CACrCyB,QAAQ,CAAEvB,gBAAiB,CAC5B,CAAC,EACC,CAAC,CACH,CAAC,CAEV,CAEA;AACA,GAAI/B,IAAI,GAAK,SAAS,CAAE,CACtB,mBACEL,IAAA,QAAKsC,SAAS,CAAC,8BAA8B,CAAAC,QAAA,cAC3CvC,IAAA,QAAKsC,SAAS,CAAC,kBAAkB,CAAAC,QAAA,cAC/BrC,KAAA,QAAKoC,SAAS,CAAC,+CAA+C,CAAAC,QAAA,eAC5DvC,IAAA,QAAKsC,SAAS,CAAC,uCAAuC,CAACS,IAAI,CAAC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAAT,QAAA,cAC5FvC,IAAA,SAAMiD,QAAQ,CAAC,SAAS,CAACC,CAAC,CAAC,uIAAuI,CAACC,QAAQ,CAAC,SAAS,CAAE,CAAC,CACrL,CAAC,cAENnD,IAAA,OAAIsC,SAAS,CAAC,2CAA2C,CAAAC,QAAA,CAAC,oBAE1D,CAAI,CAAC,cAELrC,KAAA,MAAGoC,SAAS,CAAC,oBAAoB,CAAAC,QAAA,EAAC,YACtB,CAAChC,QAAQ,CAACE,IAAI,CAAC,GAC3B,EAAG,CAAC,cAEJT,IAAA,MAAGsC,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,gGAE1C,CAAG,CAAC,cAEJrC,KAAA,QAAKoC,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBvC,IAAA,WACE8C,OAAO,CAAET,YAAa,CACtBC,SAAS,CAAC,oGAAoG,CAAAC,QAAA,CAC/G,qBAED,CAAQ,CAAC,cAETvC,IAAA,WACE8C,OAAO,CAAEA,CAAA,GAAM1C,QAAQ,CAAC,SAAS,CAAE,CACnCkC,SAAS,CAAC,iHAAiH,CAAAC,QAAA,CAC5H,kBAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CACH,CAAC,CACH,CAAC,CAEV,CAEA,MAAO,KAAI,CACb,CAAC,CAED,cAAe,CAAApC,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}