{"ast": null, "code": "import React from'react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const AdminPanel=()=>/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col items-center justify-center min-h-screen\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-semibold mb-4\",children:\"Painel Admin\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"w-96 bg-white shadow rounded p-4\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"font-bold mb-2\",children:\"Encomendas Recebidas\"}),/*#__PURE__*/_jsxs(\"ul\",{className:\"mb-4\",children:[/*#__PURE__*/_jsxs(\"li\",{children:[\"Pedido #1 - \",/*#__PURE__*/_jsx(\"span\",{className:\"text-green-600\",children:\"Entregue\"})]}),/*#__PURE__*/_jsxs(\"li\",{children:[\"Pedido #2 - \",/*#__PURE__*/_jsx(\"span\",{className:\"text-yellow-600\",children:\"Em produ\\xE7\\xE3o\"})]})]}),/*#__PURE__*/_jsx(\"button\",{className:\"px-4 py-2 bg-green-600 text-white rounded\",children:\"Atualizar Status\"})]})]});export default AdminPanel;", "map": {"version": 3, "names": ["React", "jsx", "_jsx", "jsxs", "_jsxs", "AdminPanel", "className", "children"], "sources": ["/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/AdminPanel.tsx"], "sourcesContent": ["import React from 'react';\n\nconst AdminPanel: React.FC = () => (\n  <div className=\"flex flex-col items-center justify-center min-h-screen\">\n    <h2 className=\"text-2xl font-semibold mb-4\">Painel Admin</h2>\n    <div className=\"w-96 bg-white shadow rounded p-4\">\n      <h3 className=\"font-bold mb-2\">Encomendas Recebidas</h3>\n      <ul className=\"mb-4\">\n        <li>Pedido #1 - <span className=\"text-green-600\">Entregue</span></li>\n        <li>Pedido #2 - <span className=\"text-yellow-600\">Em produção</span></li>\n      </ul>\n      <button className=\"px-4 py-2 bg-green-600 text-white rounded\">Atualizar Status</button>\n    </div>\n  </div>\n);\n\nexport default AdminPanel;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE1B,KAAM,CAAAC,UAAoB,CAAGA,CAAA,gBAC3BD,KAAA,QAAKE,SAAS,CAAC,wDAAwD,CAAAC,QAAA,eACrEL,IAAA,OAAII,SAAS,CAAC,6BAA6B,CAAAC,QAAA,CAAC,cAAY,CAAI,CAAC,cAC7DH,KAAA,QAAKE,SAAS,CAAC,kCAAkC,CAAAC,QAAA,eAC/CL,IAAA,OAAII,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAAC,sBAAoB,CAAI,CAAC,cACxDH,KAAA,OAAIE,SAAS,CAAC,MAAM,CAAAC,QAAA,eAClBH,KAAA,OAAAG,QAAA,EAAI,cAAY,cAAAL,IAAA,SAAMI,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAAC,UAAQ,CAAM,CAAC,EAAI,CAAC,cACrEH,KAAA,OAAAG,QAAA,EAAI,cAAY,cAAAL,IAAA,SAAMI,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAAC,mBAAW,CAAM,CAAC,EAAI,CAAC,EACvE,CAAC,cACLL,IAAA,WAAQI,SAAS,CAAC,2CAA2C,CAAAC,QAAA,CAAC,kBAAgB,CAAQ,CAAC,EACpF,CAAC,EACH,CACN,CAED,cAAe,CAAAF,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}