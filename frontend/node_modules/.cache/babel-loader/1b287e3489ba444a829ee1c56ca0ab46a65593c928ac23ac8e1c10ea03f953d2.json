{"ast": null, "code": "import React from'react';import{jsx as _jsx}from\"react/jsx-runtime\";const Button=_ref=>{let{children,onClick,type='button',variant='primary',size='md',disabled=false,className=''}=_ref;const baseClasses='inline-flex items-center justify-center font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors';const variantClasses={primary:'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500',secondary:'bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500',outline:'border border-gray-300 text-gray-700 bg-white hover:bg-gray-50 focus:ring-blue-500'};const sizeClasses={sm:'px-3 py-2 text-sm',md:'px-4 py-2 text-sm',lg:'px-6 py-3 text-base'};const disabledClasses=disabled?'opacity-50 cursor-not-allowed':'';const classes=\"\".concat(baseClasses,\" \").concat(variantClasses[variant],\" \").concat(sizeClasses[size],\" \").concat(disabledClasses,\" \").concat(className);return/*#__PURE__*/_jsx(\"button\",{type:type,onClick:onClick,disabled:disabled,className:classes,children:children});};export default Button;", "map": {"version": 3, "names": ["React", "jsx", "_jsx", "<PERSON><PERSON>", "_ref", "children", "onClick", "type", "variant", "size", "disabled", "className", "baseClasses", "variantClasses", "primary", "secondary", "outline", "sizeClasses", "sm", "md", "lg", "disabledClasses", "classes", "concat"], "sources": ["/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/Button.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface ButtonProps {\n  children: React.ReactNode;\n  onClick?: () => void;\n  type?: 'button' | 'submit' | 'reset';\n  variant?: 'primary' | 'secondary' | 'outline';\n  size?: 'sm' | 'md' | 'lg';\n  disabled?: boolean;\n  className?: string;\n}\n\nconst Button: React.FC<ButtonProps> = ({\n  children,\n  onClick,\n  type = 'button',\n  variant = 'primary',\n  size = 'md',\n  disabled = false,\n  className = '',\n}) => {\n  const baseClasses = 'inline-flex items-center justify-center font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors';\n  \n  const variantClasses = {\n    primary: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500',\n    secondary: 'bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500',\n    outline: 'border border-gray-300 text-gray-700 bg-white hover:bg-gray-50 focus:ring-blue-500',\n  };\n  \n  const sizeClasses = {\n    sm: 'px-3 py-2 text-sm',\n    md: 'px-4 py-2 text-sm',\n    lg: 'px-6 py-3 text-base',\n  };\n  \n  const disabledClasses = disabled ? 'opacity-50 cursor-not-allowed' : '';\n  \n  const classes = `${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${disabledClasses} ${className}`;\n  \n  return (\n    <button\n      type={type}\n      onClick={onClick}\n      disabled={disabled}\n      className={classes}\n    >\n      {children}\n    </button>\n  );\n};\n\nexport default Button;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,yBAY1B,KAAM,CAAAC,MAA6B,CAAGC,IAAA,EAQhC,IARiC,CACrCC,QAAQ,CACRC,OAAO,CACPC,IAAI,CAAG,QAAQ,CACfC,OAAO,CAAG,SAAS,CACnBC,IAAI,CAAG,IAAI,CACXC,QAAQ,CAAG,KAAK,CAChBC,SAAS,CAAG,EACd,CAAC,CAAAP,IAAA,CACC,KAAM,CAAAQ,WAAW,CAAG,sIAAsI,CAE1J,KAAM,CAAAC,cAAc,CAAG,CACrBC,OAAO,CAAE,8DAA8D,CACvEC,SAAS,CAAE,8DAA8D,CACzEC,OAAO,CAAE,oFACX,CAAC,CAED,KAAM,CAAAC,WAAW,CAAG,CAClBC,EAAE,CAAE,mBAAmB,CACvBC,EAAE,CAAE,mBAAmB,CACvBC,EAAE,CAAE,qBACN,CAAC,CAED,KAAM,CAAAC,eAAe,CAAGX,QAAQ,CAAG,+BAA+B,CAAG,EAAE,CAEvE,KAAM,CAAAY,OAAO,IAAAC,MAAA,CAAMX,WAAW,MAAAW,MAAA,CAAIV,cAAc,CAACL,OAAO,CAAC,MAAAe,MAAA,CAAIN,WAAW,CAACR,IAAI,CAAC,MAAAc,MAAA,CAAIF,eAAe,MAAAE,MAAA,CAAIZ,SAAS,CAAE,CAEhH,mBACET,IAAA,WACEK,IAAI,CAAEA,IAAK,CACXD,OAAO,CAAEA,OAAQ,CACjBI,QAAQ,CAAEA,QAAS,CACnBC,SAAS,CAAEW,OAAQ,CAAAjB,QAAA,CAElBA,QAAQ,CACH,CAAC,CAEb,CAAC,CAED,cAAe,CAAAF,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}