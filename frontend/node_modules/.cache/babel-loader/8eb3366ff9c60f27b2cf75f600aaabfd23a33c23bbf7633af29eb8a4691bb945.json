{"ast": null, "code": "import{apiService}from'./api';/**\n * MulticaixaService - Frontend service for Multicaixa Express payment integration\n * Handles communication with backend Multicaixa APIs\n */export class MulticaixaService{/**\n   * Create a new Multicaixa payment\n   */static async createPayment(paymentData){try{const response=await apiService.post('/payments/create',paymentData);if(response.success&&response.data){return{success:true,paymentId:response.data.paymentId,paymentUrl:response.data.paymentUrl,qrCode:response.data.qrCode,transactionId:response.data.transactionId,expiresAt:response.data.expiresAt};}else{return{success:false,error:response.error||'Failed to create payment'};}}catch(error){return{success:false,error:error instanceof Error?error.message:'Network error'};}}/**\n   * Check payment status manually\n   */static async checkPaymentStatus(paymentId){try{const response=await apiService.post(\"/payments/\".concat(paymentId,\"/check-status\"));if(response.success&&response.data){return{success:true,status:response.data.status,transactionId:response.data.transactionId};}else{return{success:false,error:response.error||'Failed to check payment status'};}}catch(error){return{success:false,error:error instanceof Error?error.message:'Network error'};}}/**\n   * Get payment details by ID\n   */static async getPayment(paymentId){try{return await apiService.get(\"/payments/\".concat(paymentId));}catch(error){return{success:false,error:error instanceof Error?error.message:'Network error'};}}/**\n   * Get all payments for an order\n   */static async getOrderPayments(orderId){try{return await apiService.get(\"/orders/\".concat(orderId,\"/payments\"));}catch(error){return{success:false,error:error instanceof Error?error.message:'Network error'};}}/**\n   * Request a refund for a payment\n   */static async requestRefund(paymentId,amount,reason){try{return await apiService.post(\"/payments/\".concat(paymentId,\"/refund\"),{amount,reason});}catch(error){return{success:false,error:error instanceof Error?error.message:'Network error'};}}/**\n   * Convert EUR to AOA using current exchange rate\n   */static convertEurToAoa(eurAmount){let exchangeRate=arguments.length>1&&arguments[1]!==undefined?arguments[1]:850;return Math.round(eurAmount*exchangeRate);}/**\n   * Convert AOA to EUR using current exchange rate\n   */static convertAoaToEur(aoaAmount){let exchangeRate=arguments.length>1&&arguments[1]!==undefined?arguments[1]:850;return Math.round(aoaAmount/exchangeRate*100)/100;}/**\n   * Format AOA amount for display\n   */static formatAoaAmount(amount){return new Intl.NumberFormat('pt-AO',{style:'currency',currency:'AOA',minimumFractionDigits:0,maximumFractionDigits:0}).format(amount);}/**\n   * Validate payment amount (minimum 100 AOA)\n   */static validatePaymentAmount(amount){let currency=arguments.length>1&&arguments[1]!==undefined?arguments[1]:'AOA';if(currency==='AOA'){return amount>=100;}// For EUR, convert to AOA first\nconst aoaAmount=this.convertEurToAoa(amount);return aoaAmount>=100;}/**\n   * Get payment status display text\n   */static getStatusDisplayText(status){const statusMap={'PENDING':'Pendente','PROCESSING':'Processando','COMPLETED':'Concluído','FAILED':'Falhado','CANCELLED':'Cancelado','REFUNDED':'Reembolsado','EXPIRED':'Expirado'};return statusMap[status]||status;}/**\n   * Get payment status color for UI\n   */static getStatusColor(status){const colorMap={'PENDING':'text-yellow-600','PROCESSING':'text-blue-600','COMPLETED':'text-green-600','FAILED':'text-red-600','CANCELLED':'text-gray-600','REFUNDED':'text-purple-600','EXPIRED':'text-orange-600'};return colorMap[status]||'text-gray-600';}/**\n   * Check if payment is in a final state\n   */static isPaymentFinal(status){return['COMPLETED','FAILED','CANCELLED','REFUNDED','EXPIRED'].includes(status);}/**\n   * Check if payment can be refunded\n   */static canRefund(status){return status==='COMPLETED';}}export default MulticaixaService;", "map": {"version": 3, "names": ["apiService", "MulticaixaService", "createPayment", "paymentData", "response", "post", "success", "data", "paymentId", "paymentUrl", "qrCode", "transactionId", "expiresAt", "error", "Error", "message", "checkPaymentStatus", "concat", "status", "getPayment", "get", "getOrderPayments", "orderId", "requestRefund", "amount", "reason", "convertEurToAoa", "eurAmount", "exchangeRate", "arguments", "length", "undefined", "Math", "round", "convertAoaToEur", "aoaAmount", "formatAoaAmount", "Intl", "NumberFormat", "style", "currency", "minimumFractionDigits", "maximumFractionDigits", "format", "validatePaymentAmount", "getStatusDisplayText", "statusMap", "getStatusColor", "colorMap", "isPaymentFinal", "includes", "canRefund"], "sources": ["/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/services/multicaixa.ts"], "sourcesContent": ["import { apiService } from './api';\nimport { \n  MulticaixaPaymentRequest, \n  MulticaixaPaymentResponse, \n  MulticaixaStatusResponse,\n  Payment,\n  ApiResponse \n} from '../types';\n\n/**\n * MulticaixaService - Frontend service for Multicaixa Express payment integration\n * Handles communication with backend Multicaixa APIs\n */\nexport class MulticaixaService {\n  \n  /**\n   * Create a new Multicaixa payment\n   */\n  static async createPayment(\n    paymentData: MulticaixaPaymentRequest\n  ): Promise<MulticaixaPaymentResponse> {\n    try {\n      const response = await apiService.post<{\n        paymentId: string;\n        paymentUrl: string;\n        qrCode: string;\n        transactionId: string;\n        expiresAt: string;\n      }>('/payments/create', paymentData);\n\n      if (response.success && response.data) {\n        return {\n          success: true,\n          paymentId: response.data.paymentId,\n          paymentUrl: response.data.paymentUrl,\n          qrCode: response.data.qrCode,\n          transactionId: response.data.transactionId,\n          expiresAt: response.data.expiresAt,\n        };\n      } else {\n        return {\n          success: false,\n          error: response.error || 'Failed to create payment',\n        };\n      }\n    } catch (error) {\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : 'Network error',\n      };\n    }\n  }\n\n  /**\n   * Check payment status manually\n   */\n  static async checkPaymentStatus(paymentId: string): Promise<MulticaixaStatusResponse> {\n    try {\n      const response = await apiService.post<{\n        status: string;\n        transactionId: string;\n      }>(`/payments/${paymentId}/check-status`);\n\n      if (response.success && response.data) {\n        return {\n          success: true,\n          status: response.data.status as any,\n          transactionId: response.data.transactionId,\n        };\n      } else {\n        return {\n          success: false,\n          error: response.error || 'Failed to check payment status',\n        };\n      }\n    } catch (error) {\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : 'Network error',\n      };\n    }\n  }\n\n  /**\n   * Get payment details by ID\n   */\n  static async getPayment(paymentId: string): Promise<ApiResponse<Payment>> {\n    try {\n      return await apiService.get<Payment>(`/payments/${paymentId}`);\n    } catch (error) {\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : 'Network error',\n      };\n    }\n  }\n\n  /**\n   * Get all payments for an order\n   */\n  static async getOrderPayments(orderId: string): Promise<ApiResponse<Payment[]>> {\n    try {\n      return await apiService.get<Payment[]>(`/orders/${orderId}/payments`);\n    } catch (error) {\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : 'Network error',\n      };\n    }\n  }\n\n  /**\n   * Request a refund for a payment\n   */\n  static async requestRefund(\n    paymentId: string, \n    amount?: number, \n    reason?: string\n  ): Promise<ApiResponse<{ refundId: string; status: string }>> {\n    try {\n      return await apiService.post<{ refundId: string; status: string }>(\n        `/payments/${paymentId}/refund`,\n        { amount, reason }\n      );\n    } catch (error) {\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : 'Network error',\n      };\n    }\n  }\n\n  /**\n   * Convert EUR to AOA using current exchange rate\n   */\n  static convertEurToAoa(eurAmount: number, exchangeRate: number = 850): number {\n    return Math.round(eurAmount * exchangeRate);\n  }\n\n  /**\n   * Convert AOA to EUR using current exchange rate\n   */\n  static convertAoaToEur(aoaAmount: number, exchangeRate: number = 850): number {\n    return Math.round((aoaAmount / exchangeRate) * 100) / 100;\n  }\n\n  /**\n   * Format AOA amount for display\n   */\n  static formatAoaAmount(amount: number): string {\n    return new Intl.NumberFormat('pt-AO', {\n      style: 'currency',\n      currency: 'AOA',\n      minimumFractionDigits: 0,\n      maximumFractionDigits: 0,\n    }).format(amount);\n  }\n\n  /**\n   * Validate payment amount (minimum 100 AOA)\n   */\n  static validatePaymentAmount(amount: number, currency: string = 'AOA'): boolean {\n    if (currency === 'AOA') {\n      return amount >= 100;\n    }\n    // For EUR, convert to AOA first\n    const aoaAmount = this.convertEurToAoa(amount);\n    return aoaAmount >= 100;\n  }\n\n  /**\n   * Get payment status display text\n   */\n  static getStatusDisplayText(status: string): string {\n    const statusMap: Record<string, string> = {\n      'PENDING': 'Pendente',\n      'PROCESSING': 'Processando',\n      'COMPLETED': 'Concluído',\n      'FAILED': 'Falhado',\n      'CANCELLED': 'Cancelado',\n      'REFUNDED': 'Reembolsado',\n      'EXPIRED': 'Expirado',\n    };\n    return statusMap[status] || status;\n  }\n\n  /**\n   * Get payment status color for UI\n   */\n  static getStatusColor(status: string): string {\n    const colorMap: Record<string, string> = {\n      'PENDING': 'text-yellow-600',\n      'PROCESSING': 'text-blue-600',\n      'COMPLETED': 'text-green-600',\n      'FAILED': 'text-red-600',\n      'CANCELLED': 'text-gray-600',\n      'REFUNDED': 'text-purple-600',\n      'EXPIRED': 'text-orange-600',\n    };\n    return colorMap[status] || 'text-gray-600';\n  }\n\n  /**\n   * Check if payment is in a final state\n   */\n  static isPaymentFinal(status: string): boolean {\n    return ['COMPLETED', 'FAILED', 'CANCELLED', 'REFUNDED', 'EXPIRED'].includes(status);\n  }\n\n  /**\n   * Check if payment can be refunded\n   */\n  static canRefund(status: string): boolean {\n    return status === 'COMPLETED';\n  }\n}\n\nexport default MulticaixaService;\n"], "mappings": "AAAA,OAASA,UAAU,KAAQ,OAAO,CASlC;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAC,iBAAkB,CAE7B;AACF;AACA,KACE,YAAa,CAAAC,aAAaA,CACxBC,WAAqC,CACD,CACpC,GAAI,CACF,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAJ,UAAU,CAACK,IAAI,CAMnC,kBAAkB,CAAEF,WAAW,CAAC,CAEnC,GAAIC,QAAQ,CAACE,OAAO,EAAIF,QAAQ,CAACG,IAAI,CAAE,CACrC,MAAO,CACLD,OAAO,CAAE,IAAI,CACbE,SAAS,CAAEJ,QAAQ,CAACG,IAAI,CAACC,SAAS,CAClCC,UAAU,CAAEL,QAAQ,CAACG,IAAI,CAACE,UAAU,CACpCC,MAAM,CAAEN,QAAQ,CAACG,IAAI,CAACG,MAAM,CAC5BC,aAAa,CAAEP,QAAQ,CAACG,IAAI,CAACI,aAAa,CAC1CC,SAAS,CAAER,QAAQ,CAACG,IAAI,CAACK,SAC3B,CAAC,CACH,CAAC,IAAM,CACL,MAAO,CACLN,OAAO,CAAE,KAAK,CACdO,KAAK,CAAET,QAAQ,CAACS,KAAK,EAAI,0BAC3B,CAAC,CACH,CACF,CAAE,MAAOA,KAAK,CAAE,CACd,MAAO,CACLP,OAAO,CAAE,KAAK,CACdO,KAAK,CAAEA,KAAK,WAAY,CAAAC,KAAK,CAAGD,KAAK,CAACE,OAAO,CAAG,eAClD,CAAC,CACH,CACF,CAEA;AACF;AACA,KACE,YAAa,CAAAC,kBAAkBA,CAACR,SAAiB,CAAqC,CACpF,GAAI,CACF,KAAM,CAAAJ,QAAQ,CAAG,KAAM,CAAAJ,UAAU,CAACK,IAAI,cAAAY,MAAA,CAGtBT,SAAS,iBAAe,CAAC,CAEzC,GAAIJ,QAAQ,CAACE,OAAO,EAAIF,QAAQ,CAACG,IAAI,CAAE,CACrC,MAAO,CACLD,OAAO,CAAE,IAAI,CACbY,MAAM,CAAEd,QAAQ,CAACG,IAAI,CAACW,MAAa,CACnCP,aAAa,CAAEP,QAAQ,CAACG,IAAI,CAACI,aAC/B,CAAC,CACH,CAAC,IAAM,CACL,MAAO,CACLL,OAAO,CAAE,KAAK,CACdO,KAAK,CAAET,QAAQ,CAACS,KAAK,EAAI,gCAC3B,CAAC,CACH,CACF,CAAE,MAAOA,KAAK,CAAE,CACd,MAAO,CACLP,OAAO,CAAE,KAAK,CACdO,KAAK,CAAEA,KAAK,WAAY,CAAAC,KAAK,CAAGD,KAAK,CAACE,OAAO,CAAG,eAClD,CAAC,CACH,CACF,CAEA;AACF;AACA,KACE,YAAa,CAAAI,UAAUA,CAACX,SAAiB,CAAiC,CACxE,GAAI,CACF,MAAO,MAAM,CAAAR,UAAU,CAACoB,GAAG,cAAAH,MAAA,CAAuBT,SAAS,CAAE,CAAC,CAChE,CAAE,MAAOK,KAAK,CAAE,CACd,MAAO,CACLP,OAAO,CAAE,KAAK,CACdO,KAAK,CAAEA,KAAK,WAAY,CAAAC,KAAK,CAAGD,KAAK,CAACE,OAAO,CAAG,eAClD,CAAC,CACH,CACF,CAEA;AACF;AACA,KACE,YAAa,CAAAM,gBAAgBA,CAACC,OAAe,CAAmC,CAC9E,GAAI,CACF,MAAO,MAAM,CAAAtB,UAAU,CAACoB,GAAG,YAAAH,MAAA,CAAuBK,OAAO,aAAW,CAAC,CACvE,CAAE,MAAOT,KAAK,CAAE,CACd,MAAO,CACLP,OAAO,CAAE,KAAK,CACdO,KAAK,CAAEA,KAAK,WAAY,CAAAC,KAAK,CAAGD,KAAK,CAACE,OAAO,CAAG,eAClD,CAAC,CACH,CACF,CAEA;AACF;AACA,KACE,YAAa,CAAAQ,aAAaA,CACxBf,SAAiB,CACjBgB,MAAe,CACfC,MAAe,CAC6C,CAC5D,GAAI,CACF,MAAO,MAAM,CAAAzB,UAAU,CAACK,IAAI,cAAAY,MAAA,CACbT,SAAS,YACtB,CAAEgB,MAAM,CAAEC,MAAO,CACnB,CAAC,CACH,CAAE,MAAOZ,KAAK,CAAE,CACd,MAAO,CACLP,OAAO,CAAE,KAAK,CACdO,KAAK,CAAEA,KAAK,WAAY,CAAAC,KAAK,CAAGD,KAAK,CAACE,OAAO,CAAG,eAClD,CAAC,CACH,CACF,CAEA;AACF;AACA,KACE,MAAO,CAAAW,eAAeA,CAACC,SAAiB,CAAsC,IAApC,CAAAC,YAAoB,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,GAAG,CAClE,MAAO,CAAAG,IAAI,CAACC,KAAK,CAACN,SAAS,CAAGC,YAAY,CAAC,CAC7C,CAEA;AACF;AACA,KACE,MAAO,CAAAM,eAAeA,CAACC,SAAiB,CAAsC,IAApC,CAAAP,YAAoB,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,GAAG,CAClE,MAAO,CAAAG,IAAI,CAACC,KAAK,CAAEE,SAAS,CAAGP,YAAY,CAAI,GAAG,CAAC,CAAG,GAAG,CAC3D,CAEA;AACF;AACA,KACE,MAAO,CAAAQ,eAAeA,CAACZ,MAAc,CAAU,CAC7C,MAAO,IAAI,CAAAa,IAAI,CAACC,YAAY,CAAC,OAAO,CAAE,CACpCC,KAAK,CAAE,UAAU,CACjBC,QAAQ,CAAE,KAAK,CACfC,qBAAqB,CAAE,CAAC,CACxBC,qBAAqB,CAAE,CACzB,CAAC,CAAC,CAACC,MAAM,CAACnB,MAAM,CAAC,CACnB,CAEA;AACF;AACA,KACE,MAAO,CAAAoB,qBAAqBA,CAACpB,MAAc,CAAqC,IAAnC,CAAAgB,QAAgB,CAAAX,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,KAAK,CACnE,GAAIW,QAAQ,GAAK,KAAK,CAAE,CACtB,MAAO,CAAAhB,MAAM,EAAI,GAAG,CACtB,CACA;AACA,KAAM,CAAAW,SAAS,CAAG,IAAI,CAACT,eAAe,CAACF,MAAM,CAAC,CAC9C,MAAO,CAAAW,SAAS,EAAI,GAAG,CACzB,CAEA;AACF;AACA,KACE,MAAO,CAAAU,oBAAoBA,CAAC3B,MAAc,CAAU,CAClD,KAAM,CAAA4B,SAAiC,CAAG,CACxC,SAAS,CAAE,UAAU,CACrB,YAAY,CAAE,aAAa,CAC3B,WAAW,CAAE,WAAW,CACxB,QAAQ,CAAE,SAAS,CACnB,WAAW,CAAE,WAAW,CACxB,UAAU,CAAE,aAAa,CACzB,SAAS,CAAE,UACb,CAAC,CACD,MAAO,CAAAA,SAAS,CAAC5B,MAAM,CAAC,EAAIA,MAAM,CACpC,CAEA;AACF;AACA,KACE,MAAO,CAAA6B,cAAcA,CAAC7B,MAAc,CAAU,CAC5C,KAAM,CAAA8B,QAAgC,CAAG,CACvC,SAAS,CAAE,iBAAiB,CAC5B,YAAY,CAAE,eAAe,CAC7B,WAAW,CAAE,gBAAgB,CAC7B,QAAQ,CAAE,cAAc,CACxB,WAAW,CAAE,eAAe,CAC5B,UAAU,CAAE,iBAAiB,CAC7B,SAAS,CAAE,iBACb,CAAC,CACD,MAAO,CAAAA,QAAQ,CAAC9B,MAAM,CAAC,EAAI,eAAe,CAC5C,CAEA;AACF;AACA,KACE,MAAO,CAAA+B,cAAcA,CAAC/B,MAAc,CAAW,CAC7C,MAAO,CAAC,WAAW,CAAE,QAAQ,CAAE,WAAW,CAAE,UAAU,CAAE,SAAS,CAAC,CAACgC,QAAQ,CAAChC,MAAM,CAAC,CACrF,CAEA;AACF;AACA,KACE,MAAO,CAAAiC,SAASA,CAACjC,MAAc,CAAW,CACxC,MAAO,CAAAA,MAAM,GAAK,WAAW,CAC/B,CACF,CAEA,cAAe,CAAAjB,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}