{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/ClientPanel.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { Layout, Button } from '../components';\nimport { apiService } from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ClientPanel = () => {\n  _s();\n  const navigate = useNavigate();\n  const [customer, setCustomer] = useState(null);\n  const [orders, setOrders] = useState([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [activeTab, setActiveTab] = useState('orders');\n  useEffect(() => {\n    const loadCustomerData = async () => {\n      // Get customer email from localStorage or URL params\n      // In a real app, this would come from authentication\n      const urlParams = new URLSearchParams(window.location.search);\n      const customerEmail = urlParams.get('email') || localStorage.getItem('customerEmail') || '<EMAIL>';\n      try {\n        // Load customer orders from backend\n        const ordersResponse = await apiService.get(`/orders/customer/${customerEmail}`);\n        if (ordersResponse.success && ordersResponse.data) {\n          var _backendOrders$, _backendOrders$2;\n          const backendOrders = ordersResponse.data;\n\n          // Transform backend orders to frontend format\n          const transformedOrders = backendOrders.map(order => {\n            var _order$file;\n            return {\n              id: order.id.toString(),\n              orderNumber: order.orderNumber,\n              fileName: ((_order$file = order.file) === null || _order$file === void 0 ? void 0 : _order$file.originalName) || 'Arquivo não encontrado',\n              status: order.status,\n              total: order.price || 0,\n              createdAt: new Date(order.createdAt).toLocaleDateString('pt-BR'),\n              estimatedDelivery: order.estimatedCompletionDate ? new Date(order.estimatedCompletionDate).toLocaleDateString('pt-BR') : undefined\n            };\n          });\n          setOrders(transformedOrders);\n\n          // Calculate customer stats\n          const totalOrders = transformedOrders.length;\n          const totalSpent = transformedOrders.reduce((sum, order) => sum + order.total, 0);\n          setCustomer({\n            name: ((_backendOrders$ = backendOrders[0]) === null || _backendOrders$ === void 0 ? void 0 : _backendOrders$.customerName) || 'Cliente',\n            email: customerEmail,\n            phone: ((_backendOrders$2 = backendOrders[0]) === null || _backendOrders$2 === void 0 ? void 0 : _backendOrders$2.customerPhone) || '',\n            totalOrders,\n            totalSpent\n          });\n        } else {\n          // No orders found, set empty customer\n          setCustomer({\n            name: 'Cliente',\n            email: customerEmail,\n            phone: '',\n            totalOrders: 0,\n            totalSpent: 0\n          });\n          setOrders([]);\n        }\n      } catch (error) {\n        console.error('Error loading customer data:', error);\n        // Fallback to empty state\n        setCustomer({\n          name: 'Cliente',\n          email: customerEmail,\n          phone: '',\n          totalOrders: 0,\n          totalSpent: 0\n        });\n        setOrders([]);\n      } finally {\n        setIsLoading(false);\n      }\n    };\n    loadCustomerData();\n  }, []);\n  const getStatusColor = status => {\n    const colors = {\n      pending: 'bg-yellow-100 text-yellow-800',\n      processing: 'bg-blue-100 text-blue-800',\n      printing: 'bg-purple-100 text-purple-800',\n      shipped: 'bg-indigo-100 text-indigo-800',\n      delivered: 'bg-green-100 text-green-800',\n      cancelled: 'bg-red-100 text-red-800'\n    };\n    return colors[status];\n  };\n  const getStatusText = status => {\n    const texts = {\n      pending: 'Pendente',\n      processing: 'Em Processamento',\n      printing: 'Imprimindo',\n      shipped: 'Enviado',\n      delivered: 'Entregue',\n      cancelled: 'Cancelado'\n    };\n    return texts[status];\n  };\n  const formatPrice = price => {\n    return `${price.toFixed(2)} AOA`;\n  };\n  const formatDate = dateString => {\n    return new Date(dateString).toLocaleDateString('pt-AO');\n  };\n  const handleNewOrder = () => {\n    navigate('/upload');\n  };\n  const handleViewOrder = orderId => {\n    // In a real app, this would navigate to order details\n    alert(`Ver detalhes do pedido ${orderId}`);\n  };\n  const handleReorderOrder = orderId => {\n    // In a real app, this would duplicate the order\n    alert(`Repetir pedido ${orderId}`);\n  };\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(Layout, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-center min-h-screen\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: \"Carregando painel...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 148,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Layout, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-6xl mx-auto py-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold text-gray-900 mb-4\",\n          children: \"Painel do Cliente\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-gray-600\",\n          children: [\"Bem-vindo de volta, \", customer === null || customer === void 0 ? void 0 : customer.name, \"!\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 162,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-lg p-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-3 rounded-full bg-blue-100\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-6 h-6 text-blue-600\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 20 20\",\n                children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M9 2a1 1 0 000 2h2a1 1 0 100-2H9z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 177,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  fillRule: \"evenodd\",\n                  d: \"M4 5a2 2 0 012-2v1a1 1 0 001 1h6a1 1 0 001-1V3a2 2 0 012 2v6.5a1.5 1.5 0 01-1.5 1.5h-9A1.5 1.5 0 014 11.5V5zM7 7a1 1 0 012 0v3a1 1 0 11-2 0V7zm5 0a1 1 0 10-2 0v3a1 1 0 102 0V7z\",\n                  clipRule: \"evenodd\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 178,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 176,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm font-medium text-gray-600\",\n                children: \"Total de Pedidos\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-2xl font-bold text-gray-900\",\n                children: customer === null || customer === void 0 ? void 0 : customer.totalOrders\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-lg p-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-3 rounded-full bg-green-100\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-6 h-6 text-green-600\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 20 20\",\n                children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 192,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  fillRule: \"evenodd\",\n                  d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z\",\n                  clipRule: \"evenodd\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 193,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm font-medium text-gray-600\",\n                children: \"Total Gasto\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-2xl font-bold text-gray-900\",\n                children: formatPrice((customer === null || customer === void 0 ? void 0 : customer.totalSpent) || 0)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-lg p-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-3 rounded-full bg-purple-100\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-6 h-6 text-purple-600\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 20 20\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  fillRule: \"evenodd\",\n                  d: \"M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                  clipRule: \"evenodd\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 207,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm font-medium text-gray-600\",\n                children: \"Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-2xl font-bold text-gray-900\",\n                children: \"Premium\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 172,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-lg\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"border-b border-gray-200\",\n          children: /*#__PURE__*/_jsxDEV(\"nav\", {\n            className: \"-mb-px flex\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setActiveTab('orders'),\n              className: `py-4 px-6 text-sm font-medium border-b-2 ${activeTab === 'orders' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`,\n              children: \"Meus Pedidos\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setActiveTab('profile'),\n              className: `py-4 px-6 text-sm font-medium border-b-2 ${activeTab === 'profile' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`,\n              children: \"Perfil\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6\",\n          children: [activeTab === 'orders' && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between items-center mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-xl font-semibold text-gray-900\",\n                children: \"Hist\\xF3rico de Pedidos\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 249,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                onClick: handleNewOrder,\n                children: \"Novo Pedido\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: orders.map(order => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"border border-gray-200 rounded-lg p-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"font-medium text-gray-900\",\n                      children: [\"Pedido #\", order.orderNumber]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 262,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(order.status)}`,\n                      children: getStatusText(order.status)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 265,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 261,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-right\",\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"font-semibold text-gray-900\",\n                      children: formatPrice(order.total)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 270,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-600\",\n                      children: formatDate(order.createdAt)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 271,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 269,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 260,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-600 mb-1\",\n                      children: [\"Arquivo: \", order.fileName]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 277,\n                      columnNumber: 27\n                    }, this), order.estimatedDelivery && /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-600\",\n                      children: [\"Entrega estimada: \", formatDate(order.estimatedDelivery)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 281,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 276,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex space-x-2\",\n                    children: [/*#__PURE__*/_jsxDEV(Button, {\n                      variant: \"outline\",\n                      size: \"sm\",\n                      onClick: () => handleViewOrder(order.id),\n                      children: \"Ver Detalhes\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 288,\n                      columnNumber: 27\n                    }, this), order.status === 'delivered' && /*#__PURE__*/_jsxDEV(Button, {\n                      variant: \"outline\",\n                      size: \"sm\",\n                      onClick: () => handleReorderOrder(order.id),\n                      children: \"Repetir\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 296,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 287,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 275,\n                  columnNumber: 23\n                }, this)]\n              }, order.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 259,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 15\n          }, this), activeTab === 'profile' && customer && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl font-semibold text-gray-900 mb-6\",\n              children: \"Informa\\xE7\\xF5es do Perfil\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 314,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Nome Completo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 320,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: customer.name,\n                  className: \"w-full p-3 border border-gray-300 rounded-md bg-gray-50\",\n                  readOnly: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 323,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 319,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Email\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 332,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"email\",\n                  value: customer.email,\n                  className: \"w-full p-3 border border-gray-300 rounded-md bg-gray-50\",\n                  readOnly: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 335,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 331,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Telefone\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 344,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"tel\",\n                  value: customer.phone,\n                  className: \"w-full p-3 border border-gray-300 rounded-md bg-gray-50\",\n                  readOnly: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 347,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 343,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Status da Conta\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 356,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"px-3 py-1 bg-purple-100 text-purple-800 text-sm font-medium rounded-full\",\n                    children: \"Premium\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 360,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm text-gray-600\",\n                    children: \"Desde Janeiro 2024\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 363,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 359,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 355,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 318,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-6 p-4 bg-blue-50 rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"font-medium text-blue-900 mb-2\",\n                children: \"Benef\\xEDcios Premium\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 371,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                className: \"text-sm text-blue-800 space-y-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"\\u2022 Entrega priorit\\xE1ria\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 373,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"\\u2022 Desconto de 10% em todos os pedidos\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 374,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"\\u2022 Suporte t\\xE9cnico dedicado\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 375,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"\\u2022 Acesso antecipado a novos servi\\xE7os\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 376,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 372,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 370,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-6\",\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outline\",\n                children: \"Editar Perfil\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 381,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 380,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 313,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 219,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 161,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 160,\n    columnNumber: 5\n  }, this);\n};\n_s(ClientPanel, \"vMPvTy8N9Fd1gWMdzuEDwbRq8VQ=\", false, function () {\n  return [useNavigate];\n});\n_c = ClientPanel;\nexport default ClientPanel;\nvar _c;\n$RefreshReg$(_c, \"ClientPanel\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "Layout", "<PERSON><PERSON>", "apiService", "jsxDEV", "_jsxDEV", "ClientPanel", "_s", "navigate", "customer", "setCustomer", "orders", "setOrders", "isLoading", "setIsLoading", "activeTab", "setActiveTab", "loadCustomerData", "urlParams", "URLSearchParams", "window", "location", "search", "customerEmail", "get", "localStorage", "getItem", "ordersResponse", "success", "data", "_backendOrders$", "_backendOrders$2", "backendOrders", "transformedOrders", "map", "order", "_order$file", "id", "toString", "orderNumber", "fileName", "file", "originalName", "status", "total", "price", "createdAt", "Date", "toLocaleDateString", "estimatedDelivery", "estimatedCompletionDate", "undefined", "totalOrders", "length", "totalSpent", "reduce", "sum", "name", "customerName", "email", "phone", "customerPhone", "error", "console", "getStatusColor", "colors", "pending", "processing", "printing", "shipped", "delivered", "cancelled", "getStatusText", "texts", "formatPrice", "toFixed", "formatDate", "dateString", "handleNewOrder", "handleViewOrder", "orderId", "alert", "handleReorderOrder", "children", "className", "_jsxFileName", "lineNumber", "columnNumber", "fill", "viewBox", "d", "fillRule", "clipRule", "onClick", "variant", "size", "type", "value", "readOnly", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/ClientPanel.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { Layout, Button } from '../components';\nimport { apiService } from '../services/api';\n\ninterface Order {\n  id: string;\n  orderNumber: string;\n  fileName: string;\n  status: 'pending' | 'processing' | 'printing' | 'shipped' | 'delivered' | 'cancelled';\n  total: number;\n  createdAt: string;\n  estimatedDelivery?: string;\n}\n\ninterface Customer {\n  name: string;\n  email: string;\n  phone: string;\n  totalOrders: number;\n  totalSpent: number;\n}\n\nconst ClientPanel: React.FC = () => {\n  const navigate = useNavigate();\n  const [customer, setCustomer] = useState<Customer | null>(null);\n  const [orders, setOrders] = useState<Order[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [activeTab, setActiveTab] = useState<'orders' | 'profile'>('orders');\n\n  useEffect(() => {\n    const loadCustomerData = async () => {\n      // Get customer email from localStorage or URL params\n      // In a real app, this would come from authentication\n      const urlParams = new URLSearchParams(window.location.search);\n      const customerEmail = urlParams.get('email') || localStorage.getItem('customerEmail') || '<EMAIL>';\n\n      try {\n        // Load customer orders from backend\n        const ordersResponse = await apiService.get(`/orders/customer/${customerEmail}`);\n\n        if (ordersResponse.success && ordersResponse.data) {\n          const backendOrders = ordersResponse.data as any[];\n\n          // Transform backend orders to frontend format\n          const transformedOrders: Order[] = backendOrders.map(order => ({\n            id: order.id.toString(),\n            orderNumber: order.orderNumber,\n            fileName: order.file?.originalName || 'Arquivo não encontrado',\n            status: order.status,\n            total: order.price || 0,\n            createdAt: new Date(order.createdAt).toLocaleDateString('pt-BR'),\n            estimatedDelivery: order.estimatedCompletionDate ?\n              new Date(order.estimatedCompletionDate).toLocaleDateString('pt-BR') : undefined\n          }));\n\n          setOrders(transformedOrders);\n\n          // Calculate customer stats\n          const totalOrders = transformedOrders.length;\n          const totalSpent = transformedOrders.reduce((sum, order) => sum + order.total, 0);\n\n          setCustomer({\n            name: backendOrders[0]?.customerName || 'Cliente',\n            email: customerEmail,\n            phone: backendOrders[0]?.customerPhone || '',\n            totalOrders,\n            totalSpent\n          });\n        } else {\n          // No orders found, set empty customer\n          setCustomer({\n            name: 'Cliente',\n            email: customerEmail,\n            phone: '',\n            totalOrders: 0,\n            totalSpent: 0\n          });\n          setOrders([]);\n        }\n      } catch (error) {\n        console.error('Error loading customer data:', error);\n        // Fallback to empty state\n        setCustomer({\n          name: 'Cliente',\n          email: customerEmail,\n          phone: '',\n          totalOrders: 0,\n          totalSpent: 0\n        });\n        setOrders([]);\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    loadCustomerData();\n  }, []);\n\n  const getStatusColor = (status: Order['status']) => {\n    const colors = {\n      pending: 'bg-yellow-100 text-yellow-800',\n      processing: 'bg-blue-100 text-blue-800',\n      printing: 'bg-purple-100 text-purple-800',\n      shipped: 'bg-indigo-100 text-indigo-800',\n      delivered: 'bg-green-100 text-green-800',\n      cancelled: 'bg-red-100 text-red-800',\n    };\n    return colors[status];\n  };\n\n  const getStatusText = (status: Order['status']) => {\n    const texts = {\n      pending: 'Pendente',\n      processing: 'Em Processamento',\n      printing: 'Imprimindo',\n      shipped: 'Enviado',\n      delivered: 'Entregue',\n      cancelled: 'Cancelado',\n    };\n    return texts[status];\n  };\n\n  const formatPrice = (price: number) => {\n    return `${price.toFixed(2)} AOA`;\n  };\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('pt-AO');\n  };\n\n  const handleNewOrder = () => {\n    navigate('/upload');\n  };\n\n  const handleViewOrder = (orderId: string) => {\n    // In a real app, this would navigate to order details\n    alert(`Ver detalhes do pedido ${orderId}`);\n  };\n\n  const handleReorderOrder = (orderId: string) => {\n    // In a real app, this would duplicate the order\n    alert(`Repetir pedido ${orderId}`);\n  };\n\n  if (isLoading) {\n    return (\n      <Layout>\n        <div className=\"flex items-center justify-center min-h-screen\">\n          <div className=\"text-center\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n            <p className=\"text-gray-600\">Carregando painel...</p>\n          </div>\n        </div>\n      </Layout>\n    );\n  }\n\n  return (\n    <Layout>\n      <div className=\"max-w-6xl mx-auto py-8\">\n        <div className=\"text-center mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-4\">\n            Painel do Cliente\n          </h1>\n          <p className=\"text-lg text-gray-600\">\n            Bem-vindo de volta, {customer?.name}!\n          </p>\n        </div>\n\n        {/* Stats Cards */}\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\">\n          <div className=\"bg-white rounded-lg shadow-lg p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-3 rounded-full bg-blue-100\">\n                <svg className=\"w-6 h-6 text-blue-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path d=\"M9 2a1 1 0 000 2h2a1 1 0 100-2H9z\" />\n                  <path fillRule=\"evenodd\" d=\"M4 5a2 2 0 012-2v1a1 1 0 001 1h6a1 1 0 001-1V3a2 2 0 012 2v6.5a1.5 1.5 0 01-1.5 1.5h-9A1.5 1.5 0 014 11.5V5zM7 7a1 1 0 012 0v3a1 1 0 11-2 0V7zm5 0a1 1 0 10-2 0v3a1 1 0 102 0V7z\" clipRule=\"evenodd\" />\n                </svg>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">Total de Pedidos</p>\n                <p className=\"text-2xl font-bold text-gray-900\">{customer?.totalOrders}</p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow-lg p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-3 rounded-full bg-green-100\">\n                <svg className=\"w-6 h-6 text-green-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path d=\"M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z\" />\n                  <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z\" clipRule=\"evenodd\" />\n                </svg>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">Total Gasto</p>\n                <p className=\"text-2xl font-bold text-gray-900\">{formatPrice(customer?.totalSpent || 0)}</p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow-lg p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-3 rounded-full bg-purple-100\">\n                <svg className=\"w-6 h-6 text-purple-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path fillRule=\"evenodd\" d=\"M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n                </svg>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">Status</p>\n                <p className=\"text-2xl font-bold text-gray-900\">Premium</p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Tabs */}\n        <div className=\"bg-white rounded-lg shadow-lg\">\n          <div className=\"border-b border-gray-200\">\n            <nav className=\"-mb-px flex\">\n              <button\n                onClick={() => setActiveTab('orders')}\n                className={`py-4 px-6 text-sm font-medium border-b-2 ${\n                  activeTab === 'orders'\n                    ? 'border-blue-500 text-blue-600'\n                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                }`}\n              >\n                Meus Pedidos\n              </button>\n              <button\n                onClick={() => setActiveTab('profile')}\n                className={`py-4 px-6 text-sm font-medium border-b-2 ${\n                  activeTab === 'profile'\n                    ? 'border-blue-500 text-blue-600'\n                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                }`}\n              >\n                Perfil\n              </button>\n            </nav>\n          </div>\n\n          <div className=\"p-6\">\n            {activeTab === 'orders' && (\n              <div>\n                <div className=\"flex justify-between items-center mb-6\">\n                  <h2 className=\"text-xl font-semibold text-gray-900\">\n                    Histórico de Pedidos\n                  </h2>\n                  <Button onClick={handleNewOrder}>\n                    Novo Pedido\n                  </Button>\n                </div>\n\n                <div className=\"space-y-4\">\n                  {orders.map((order) => (\n                    <div key={order.id} className=\"border border-gray-200 rounded-lg p-4\">\n                      <div className=\"flex items-center justify-between mb-3\">\n                        <div className=\"flex items-center space-x-3\">\n                          <h3 className=\"font-medium text-gray-900\">\n                            Pedido #{order.orderNumber}\n                          </h3>\n                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(order.status)}`}>\n                            {getStatusText(order.status)}\n                          </span>\n                        </div>\n                        <div className=\"text-right\">\n                          <p className=\"font-semibold text-gray-900\">{formatPrice(order.total)}</p>\n                          <p className=\"text-sm text-gray-600\">{formatDate(order.createdAt)}</p>\n                        </div>\n                      </div>\n\n                      <div className=\"flex items-center justify-between\">\n                        <div>\n                          <p className=\"text-sm text-gray-600 mb-1\">\n                            Arquivo: {order.fileName}\n                          </p>\n                          {order.estimatedDelivery && (\n                            <p className=\"text-sm text-gray-600\">\n                              Entrega estimada: {formatDate(order.estimatedDelivery)}\n                            </p>\n                          )}\n                        </div>\n\n                        <div className=\"flex space-x-2\">\n                          <Button\n                            variant=\"outline\"\n                            size=\"sm\"\n                            onClick={() => handleViewOrder(order.id)}\n                          >\n                            Ver Detalhes\n                          </Button>\n                          {order.status === 'delivered' && (\n                            <Button\n                              variant=\"outline\"\n                              size=\"sm\"\n                              onClick={() => handleReorderOrder(order.id)}\n                            >\n                              Repetir\n                            </Button>\n                          )}\n                        </div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            )}\n\n            {activeTab === 'profile' && customer && (\n              <div>\n                <h2 className=\"text-xl font-semibold text-gray-900 mb-6\">\n                  Informações do Perfil\n                </h2>\n\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Nome Completo\n                    </label>\n                    <input\n                      type=\"text\"\n                      value={customer.name}\n                      className=\"w-full p-3 border border-gray-300 rounded-md bg-gray-50\"\n                      readOnly\n                    />\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Email\n                    </label>\n                    <input\n                      type=\"email\"\n                      value={customer.email}\n                      className=\"w-full p-3 border border-gray-300 rounded-md bg-gray-50\"\n                      readOnly\n                    />\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Telefone\n                    </label>\n                    <input\n                      type=\"tel\"\n                      value={customer.phone}\n                      className=\"w-full p-3 border border-gray-300 rounded-md bg-gray-50\"\n                      readOnly\n                    />\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Status da Conta\n                    </label>\n                    <div className=\"flex items-center space-x-2\">\n                      <span className=\"px-3 py-1 bg-purple-100 text-purple-800 text-sm font-medium rounded-full\">\n                        Premium\n                      </span>\n                      <span className=\"text-sm text-gray-600\">\n                        Desde Janeiro 2024\n                      </span>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"mt-6 p-4 bg-blue-50 rounded-lg\">\n                  <h3 className=\"font-medium text-blue-900 mb-2\">Benefícios Premium</h3>\n                  <ul className=\"text-sm text-blue-800 space-y-1\">\n                    <li>• Entrega prioritária</li>\n                    <li>• Desconto de 10% em todos os pedidos</li>\n                    <li>• Suporte técnico dedicado</li>\n                    <li>• Acesso antecipado a novos serviços</li>\n                  </ul>\n                </div>\n\n                <div className=\"mt-6\">\n                  <Button variant=\"outline\">\n                    Editar Perfil\n                  </Button>\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    </Layout>\n  );\n};\n\nexport default ClientPanel;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,MAAM,EAAEC,MAAM,QAAQ,eAAe;AAC9C,SAASC,UAAU,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAoB7C,MAAMC,WAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClC,MAAMC,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACS,QAAQ,EAAEC,WAAW,CAAC,GAAGZ,QAAQ,CAAkB,IAAI,CAAC;EAC/D,MAAM,CAACa,MAAM,EAAEC,SAAS,CAAC,GAAGd,QAAQ,CAAU,EAAE,CAAC;EACjD,MAAM,CAACe,SAAS,EAAEC,YAAY,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACiB,SAAS,EAAEC,YAAY,CAAC,GAAGlB,QAAQ,CAAuB,QAAQ,CAAC;EAE1EC,SAAS,CAAC,MAAM;IACd,MAAMkB,gBAAgB,GAAG,MAAAA,CAAA,KAAY;MACnC;MACA;MACA,MAAMC,SAAS,GAAG,IAAIC,eAAe,CAACC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC;MAC7D,MAAMC,aAAa,GAAGL,SAAS,CAACM,GAAG,CAAC,OAAO,CAAC,IAAIC,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC,IAAI,oBAAoB;MAE7G,IAAI;QACF;QACA,MAAMC,cAAc,GAAG,MAAMxB,UAAU,CAACqB,GAAG,CAAC,oBAAoBD,aAAa,EAAE,CAAC;QAEhF,IAAII,cAAc,CAACC,OAAO,IAAID,cAAc,CAACE,IAAI,EAAE;UAAA,IAAAC,eAAA,EAAAC,gBAAA;UACjD,MAAMC,aAAa,GAAGL,cAAc,CAACE,IAAa;;UAElD;UACA,MAAMI,iBAA0B,GAAGD,aAAa,CAACE,GAAG,CAACC,KAAK;YAAA,IAAAC,WAAA;YAAA,OAAK;cAC7DC,EAAE,EAAEF,KAAK,CAACE,EAAE,CAACC,QAAQ,CAAC,CAAC;cACvBC,WAAW,EAAEJ,KAAK,CAACI,WAAW;cAC9BC,QAAQ,EAAE,EAAAJ,WAAA,GAAAD,KAAK,CAACM,IAAI,cAAAL,WAAA,uBAAVA,WAAA,CAAYM,YAAY,KAAI,wBAAwB;cAC9DC,MAAM,EAAER,KAAK,CAACQ,MAAM;cACpBC,KAAK,EAAET,KAAK,CAACU,KAAK,IAAI,CAAC;cACvBC,SAAS,EAAE,IAAIC,IAAI,CAACZ,KAAK,CAACW,SAAS,CAAC,CAACE,kBAAkB,CAAC,OAAO,CAAC;cAChEC,iBAAiB,EAAEd,KAAK,CAACe,uBAAuB,GAC9C,IAAIH,IAAI,CAACZ,KAAK,CAACe,uBAAuB,CAAC,CAACF,kBAAkB,CAAC,OAAO,CAAC,GAAGG;YAC1E,CAAC;UAAA,CAAC,CAAC;UAEHvC,SAAS,CAACqB,iBAAiB,CAAC;;UAE5B;UACA,MAAMmB,WAAW,GAAGnB,iBAAiB,CAACoB,MAAM;UAC5C,MAAMC,UAAU,GAAGrB,iBAAiB,CAACsB,MAAM,CAAC,CAACC,GAAG,EAAErB,KAAK,KAAKqB,GAAG,GAAGrB,KAAK,CAACS,KAAK,EAAE,CAAC,CAAC;UAEjFlC,WAAW,CAAC;YACV+C,IAAI,EAAE,EAAA3B,eAAA,GAAAE,aAAa,CAAC,CAAC,CAAC,cAAAF,eAAA,uBAAhBA,eAAA,CAAkB4B,YAAY,KAAI,SAAS;YACjDC,KAAK,EAAEpC,aAAa;YACpBqC,KAAK,EAAE,EAAA7B,gBAAA,GAAAC,aAAa,CAAC,CAAC,CAAC,cAAAD,gBAAA,uBAAhBA,gBAAA,CAAkB8B,aAAa,KAAI,EAAE;YAC5CT,WAAW;YACXE;UACF,CAAC,CAAC;QACJ,CAAC,MAAM;UACL;UACA5C,WAAW,CAAC;YACV+C,IAAI,EAAE,SAAS;YACfE,KAAK,EAAEpC,aAAa;YACpBqC,KAAK,EAAE,EAAE;YACTR,WAAW,EAAE,CAAC;YACdE,UAAU,EAAE;UACd,CAAC,CAAC;UACF1C,SAAS,CAAC,EAAE,CAAC;QACf;MACF,CAAC,CAAC,OAAOkD,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QACpD;QACApD,WAAW,CAAC;UACV+C,IAAI,EAAE,SAAS;UACfE,KAAK,EAAEpC,aAAa;UACpBqC,KAAK,EAAE,EAAE;UACTR,WAAW,EAAE,CAAC;UACdE,UAAU,EAAE;QACd,CAAC,CAAC;QACF1C,SAAS,CAAC,EAAE,CAAC;MACf,CAAC,SAAS;QACRE,YAAY,CAAC,KAAK,CAAC;MACrB;IACF,CAAC;IAEDG,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM+C,cAAc,GAAIrB,MAAuB,IAAK;IAClD,MAAMsB,MAAM,GAAG;MACbC,OAAO,EAAE,+BAA+B;MACxCC,UAAU,EAAE,2BAA2B;MACvCC,QAAQ,EAAE,+BAA+B;MACzCC,OAAO,EAAE,+BAA+B;MACxCC,SAAS,EAAE,6BAA6B;MACxCC,SAAS,EAAE;IACb,CAAC;IACD,OAAON,MAAM,CAACtB,MAAM,CAAC;EACvB,CAAC;EAED,MAAM6B,aAAa,GAAI7B,MAAuB,IAAK;IACjD,MAAM8B,KAAK,GAAG;MACZP,OAAO,EAAE,UAAU;MACnBC,UAAU,EAAE,kBAAkB;MAC9BC,QAAQ,EAAE,YAAY;MACtBC,OAAO,EAAE,SAAS;MAClBC,SAAS,EAAE,UAAU;MACrBC,SAAS,EAAE;IACb,CAAC;IACD,OAAOE,KAAK,CAAC9B,MAAM,CAAC;EACtB,CAAC;EAED,MAAM+B,WAAW,GAAI7B,KAAa,IAAK;IACrC,OAAO,GAAGA,KAAK,CAAC8B,OAAO,CAAC,CAAC,CAAC,MAAM;EAClC,CAAC;EAED,MAAMC,UAAU,GAAIC,UAAkB,IAAK;IACzC,OAAO,IAAI9B,IAAI,CAAC8B,UAAU,CAAC,CAAC7B,kBAAkB,CAAC,OAAO,CAAC;EACzD,CAAC;EAED,MAAM8B,cAAc,GAAGA,CAAA,KAAM;IAC3BtE,QAAQ,CAAC,SAAS,CAAC;EACrB,CAAC;EAED,MAAMuE,eAAe,GAAIC,OAAe,IAAK;IAC3C;IACAC,KAAK,CAAC,0BAA0BD,OAAO,EAAE,CAAC;EAC5C,CAAC;EAED,MAAME,kBAAkB,GAAIF,OAAe,IAAK;IAC9C;IACAC,KAAK,CAAC,kBAAkBD,OAAO,EAAE,CAAC;EACpC,CAAC;EAED,IAAInE,SAAS,EAAE;IACb,oBACER,OAAA,CAACJ,MAAM;MAAAkF,QAAA,eACL9E,OAAA;QAAK+E,SAAS,EAAC,+CAA+C;QAAAD,QAAA,eAC5D9E,OAAA;UAAK+E,SAAS,EAAC,aAAa;UAAAD,QAAA,gBAC1B9E,OAAA;YAAK+E,SAAS,EAAC;UAA6E;YAAA5C,QAAA,EAAA6C,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACnGlF,OAAA;YAAG+E,SAAS,EAAC,eAAe;YAAAD,QAAA,EAAC;UAAoB;YAAA3C,QAAA,EAAA6C,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAA/C,QAAA,EAAA6C,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD;MAAC;QAAA/C,QAAA,EAAA6C,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAA/C,QAAA,EAAA6C,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAEb;EAEA,oBACElF,OAAA,CAACJ,MAAM;IAAAkF,QAAA,eACL9E,OAAA;MAAK+E,SAAS,EAAC,wBAAwB;MAAAD,QAAA,gBACrC9E,OAAA;QAAK+E,SAAS,EAAC,kBAAkB;QAAAD,QAAA,gBAC/B9E,OAAA;UAAI+E,SAAS,EAAC,uCAAuC;UAAAD,QAAA,EAAC;QAEtD;UAAA3C,QAAA,EAAA6C,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLlF,OAAA;UAAG+E,SAAS,EAAC,uBAAuB;UAAAD,QAAA,GAAC,sBACf,EAAC1E,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEgD,IAAI,EAAC,GACtC;QAAA;UAAAjB,QAAA,EAAA6C,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAA/C,QAAA,EAAA6C,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGNlF,OAAA;QAAK+E,SAAS,EAAC,4CAA4C;QAAAD,QAAA,gBACzD9E,OAAA;UAAK+E,SAAS,EAAC,mCAAmC;UAAAD,QAAA,eAChD9E,OAAA;YAAK+E,SAAS,EAAC,mBAAmB;YAAAD,QAAA,gBAChC9E,OAAA;cAAK+E,SAAS,EAAC,8BAA8B;cAAAD,QAAA,eAC3C9E,OAAA;gBAAK+E,SAAS,EAAC,uBAAuB;gBAACI,IAAI,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAAN,QAAA,gBAC5E9E,OAAA;kBAAMqF,CAAC,EAAC;gBAAmC;kBAAAlD,QAAA,EAAA6C,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC9ClF,OAAA;kBAAMsF,QAAQ,EAAC,SAAS;kBAACD,CAAC,EAAC,kLAAkL;kBAACE,QAAQ,EAAC;gBAAS;kBAAApD,QAAA,EAAA6C,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAA/C,QAAA,EAAA6C,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChO;YAAC;cAAA/C,QAAA,EAAA6C,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNlF,OAAA;cAAK+E,SAAS,EAAC,MAAM;cAAAD,QAAA,gBACnB9E,OAAA;gBAAG+E,SAAS,EAAC,mCAAmC;gBAAAD,QAAA,EAAC;cAAgB;gBAAA3C,QAAA,EAAA6C,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACrElF,OAAA;gBAAG+E,SAAS,EAAC,kCAAkC;gBAAAD,QAAA,EAAE1E,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE2C;cAAW;gBAAAZ,QAAA,EAAA6C,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAA/C,QAAA,EAAA6C,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxE,CAAC;UAAA;YAAA/C,QAAA,EAAA6C,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAA/C,QAAA,EAAA6C,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENlF,OAAA;UAAK+E,SAAS,EAAC,mCAAmC;UAAAD,QAAA,eAChD9E,OAAA;YAAK+E,SAAS,EAAC,mBAAmB;YAAAD,QAAA,gBAChC9E,OAAA;cAAK+E,SAAS,EAAC,+BAA+B;cAAAD,QAAA,eAC5C9E,OAAA;gBAAK+E,SAAS,EAAC,wBAAwB;gBAACI,IAAI,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAAN,QAAA,gBAC7E9E,OAAA;kBAAMqF,CAAC,EAAC;gBAA4O;kBAAAlD,QAAA,EAAA6C,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACvPlF,OAAA;kBAAMsF,QAAQ,EAAC,SAAS;kBAACD,CAAC,EAAC,sdAAsd;kBAACE,QAAQ,EAAC;gBAAS;kBAAApD,QAAA,EAAA6C,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAA/C,QAAA,EAAA6C,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpgB;YAAC;cAAA/C,QAAA,EAAA6C,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNlF,OAAA;cAAK+E,SAAS,EAAC,MAAM;cAAAD,QAAA,gBACnB9E,OAAA;gBAAG+E,SAAS,EAAC,mCAAmC;gBAAAD,QAAA,EAAC;cAAW;gBAAA3C,QAAA,EAAA6C,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAChElF,OAAA;gBAAG+E,SAAS,EAAC,kCAAkC;gBAAAD,QAAA,EAAET,WAAW,CAAC,CAAAjE,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE6C,UAAU,KAAI,CAAC;cAAC;gBAAAd,QAAA,EAAA6C,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAA/C,QAAA,EAAA6C,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzF,CAAC;UAAA;YAAA/C,QAAA,EAAA6C,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAA/C,QAAA,EAAA6C,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENlF,OAAA;UAAK+E,SAAS,EAAC,mCAAmC;UAAAD,QAAA,eAChD9E,OAAA;YAAK+E,SAAS,EAAC,mBAAmB;YAAAD,QAAA,gBAChC9E,OAAA;cAAK+E,SAAS,EAAC,gCAAgC;cAAAD,QAAA,eAC7C9E,OAAA;gBAAK+E,SAAS,EAAC,yBAAyB;gBAACI,IAAI,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAAN,QAAA,eAC9E9E,OAAA;kBAAMsF,QAAQ,EAAC,SAAS;kBAACD,CAAC,EAAC,iiBAAiiB;kBAACE,QAAQ,EAAC;gBAAS;kBAAApD,QAAA,EAAA6C,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAA/C,QAAA,EAAA6C,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/kB;YAAC;cAAA/C,QAAA,EAAA6C,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNlF,OAAA;cAAK+E,SAAS,EAAC,MAAM;cAAAD,QAAA,gBACnB9E,OAAA;gBAAG+E,SAAS,EAAC,mCAAmC;gBAAAD,QAAA,EAAC;cAAM;gBAAA3C,QAAA,EAAA6C,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC3DlF,OAAA;gBAAG+E,SAAS,EAAC,kCAAkC;gBAAAD,QAAA,EAAC;cAAO;gBAAA3C,QAAA,EAAA6C,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAA/C,QAAA,EAAA6C,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CAAC;UAAA;YAAA/C,QAAA,EAAA6C,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAA/C,QAAA,EAAA6C,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAA/C,QAAA,EAAA6C,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNlF,OAAA;QAAK+E,SAAS,EAAC,+BAA+B;QAAAD,QAAA,gBAC5C9E,OAAA;UAAK+E,SAAS,EAAC,0BAA0B;UAAAD,QAAA,eACvC9E,OAAA;YAAK+E,SAAS,EAAC,aAAa;YAAAD,QAAA,gBAC1B9E,OAAA;cACEwF,OAAO,EAAEA,CAAA,KAAM7E,YAAY,CAAC,QAAQ,CAAE;cACtCoE,SAAS,EAAE,4CACTrE,SAAS,KAAK,QAAQ,GAClB,+BAA+B,GAC/B,4EAA4E,EAC/E;cAAAoE,QAAA,EACJ;YAED;cAAA3C,QAAA,EAAA6C,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTlF,OAAA;cACEwF,OAAO,EAAEA,CAAA,KAAM7E,YAAY,CAAC,SAAS,CAAE;cACvCoE,SAAS,EAAE,4CACTrE,SAAS,KAAK,SAAS,GACnB,+BAA+B,GAC/B,4EAA4E,EAC/E;cAAAoE,QAAA,EACJ;YAED;cAAA3C,QAAA,EAAA6C,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAA/C,QAAA,EAAA6C,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAA/C,QAAA,EAAA6C,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENlF,OAAA;UAAK+E,SAAS,EAAC,KAAK;UAAAD,QAAA,GACjBpE,SAAS,KAAK,QAAQ,iBACrBV,OAAA;YAAA8E,QAAA,gBACE9E,OAAA;cAAK+E,SAAS,EAAC,wCAAwC;cAAAD,QAAA,gBACrD9E,OAAA;gBAAI+E,SAAS,EAAC,qCAAqC;gBAAAD,QAAA,EAAC;cAEpD;gBAAA3C,QAAA,EAAA6C,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLlF,OAAA,CAACH,MAAM;gBAAC2F,OAAO,EAAEf,cAAe;gBAAAK,QAAA,EAAC;cAEjC;gBAAA3C,QAAA,EAAA6C,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAA/C,QAAA,EAAA6C,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENlF,OAAA;cAAK+E,SAAS,EAAC,WAAW;cAAAD,QAAA,EACvBxE,MAAM,CAACuB,GAAG,CAAEC,KAAK,iBAChB9B,OAAA;gBAAoB+E,SAAS,EAAC,uCAAuC;gBAAAD,QAAA,gBACnE9E,OAAA;kBAAK+E,SAAS,EAAC,wCAAwC;kBAAAD,QAAA,gBACrD9E,OAAA;oBAAK+E,SAAS,EAAC,6BAA6B;oBAAAD,QAAA,gBAC1C9E,OAAA;sBAAI+E,SAAS,EAAC,2BAA2B;sBAAAD,QAAA,GAAC,UAChC,EAAChD,KAAK,CAACI,WAAW;oBAAA;sBAAAC,QAAA,EAAA6C,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxB,CAAC,eACLlF,OAAA;sBAAM+E,SAAS,EAAE,8CAA8CpB,cAAc,CAAC7B,KAAK,CAACQ,MAAM,CAAC,EAAG;sBAAAwC,QAAA,EAC3FX,aAAa,CAACrC,KAAK,CAACQ,MAAM;oBAAC;sBAAAH,QAAA,EAAA6C,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxB,CAAC;kBAAA;oBAAA/C,QAAA,EAAA6C,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACNlF,OAAA;oBAAK+E,SAAS,EAAC,YAAY;oBAAAD,QAAA,gBACzB9E,OAAA;sBAAG+E,SAAS,EAAC,6BAA6B;sBAAAD,QAAA,EAAET,WAAW,CAACvC,KAAK,CAACS,KAAK;oBAAC;sBAAAJ,QAAA,EAAA6C,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACzElF,OAAA;sBAAG+E,SAAS,EAAC,uBAAuB;sBAAAD,QAAA,EAAEP,UAAU,CAACzC,KAAK,CAACW,SAAS;oBAAC;sBAAAN,QAAA,EAAA6C,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAA/C,QAAA,EAAA6C,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnE,CAAC;gBAAA;kBAAA/C,QAAA,EAAA6C,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENlF,OAAA;kBAAK+E,SAAS,EAAC,mCAAmC;kBAAAD,QAAA,gBAChD9E,OAAA;oBAAA8E,QAAA,gBACE9E,OAAA;sBAAG+E,SAAS,EAAC,4BAA4B;sBAAAD,QAAA,GAAC,WAC/B,EAAChD,KAAK,CAACK,QAAQ;oBAAA;sBAAAA,QAAA,EAAA6C,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvB,CAAC,EACHpD,KAAK,CAACc,iBAAiB,iBACtB5C,OAAA;sBAAG+E,SAAS,EAAC,uBAAuB;sBAAAD,QAAA,GAAC,oBACjB,EAACP,UAAU,CAACzC,KAAK,CAACc,iBAAiB,CAAC;oBAAA;sBAAAT,QAAA,EAAA6C,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrD,CACJ;kBAAA;oBAAA/C,QAAA,EAAA6C,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eAENlF,OAAA;oBAAK+E,SAAS,EAAC,gBAAgB;oBAAAD,QAAA,gBAC7B9E,OAAA,CAACH,MAAM;sBACL4F,OAAO,EAAC,SAAS;sBACjBC,IAAI,EAAC,IAAI;sBACTF,OAAO,EAAEA,CAAA,KAAMd,eAAe,CAAC5C,KAAK,CAACE,EAAE,CAAE;sBAAA8C,QAAA,EAC1C;oBAED;sBAAA3C,QAAA,EAAA6C,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,EACRpD,KAAK,CAACQ,MAAM,KAAK,WAAW,iBAC3BtC,OAAA,CAACH,MAAM;sBACL4F,OAAO,EAAC,SAAS;sBACjBC,IAAI,EAAC,IAAI;sBACTF,OAAO,EAAEA,CAAA,KAAMX,kBAAkB,CAAC/C,KAAK,CAACE,EAAE,CAAE;sBAAA8C,QAAA,EAC7C;oBAED;sBAAA3C,QAAA,EAAA6C,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CACT;kBAAA;oBAAA/C,QAAA,EAAA6C,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAA/C,QAAA,EAAA6C,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA,GA9CEpD,KAAK,CAACE,EAAE;gBAAAG,QAAA,EAAA6C,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA+Cb,CACN;YAAC;cAAA/C,QAAA,EAAA6C,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAA/C,QAAA,EAAA6C,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAEAxE,SAAS,KAAK,SAAS,IAAIN,QAAQ,iBAClCJ,OAAA;YAAA8E,QAAA,gBACE9E,OAAA;cAAI+E,SAAS,EAAC,0CAA0C;cAAAD,QAAA,EAAC;YAEzD;cAAA3C,QAAA,EAAA6C,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAELlF,OAAA;cAAK+E,SAAS,EAAC,uCAAuC;cAAAD,QAAA,gBACpD9E,OAAA;gBAAA8E,QAAA,gBACE9E,OAAA;kBAAO+E,SAAS,EAAC,8CAA8C;kBAAAD,QAAA,EAAC;gBAEhE;kBAAA3C,QAAA,EAAA6C,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRlF,OAAA;kBACE2F,IAAI,EAAC,MAAM;kBACXC,KAAK,EAAExF,QAAQ,CAACgD,IAAK;kBACrB2B,SAAS,EAAC,yDAAyD;kBACnEc,QAAQ;gBAAA;kBAAA1D,QAAA,EAAA6C,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAA/C,QAAA,EAAA6C,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENlF,OAAA;gBAAA8E,QAAA,gBACE9E,OAAA;kBAAO+E,SAAS,EAAC,8CAA8C;kBAAAD,QAAA,EAAC;gBAEhE;kBAAA3C,QAAA,EAAA6C,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRlF,OAAA;kBACE2F,IAAI,EAAC,OAAO;kBACZC,KAAK,EAAExF,QAAQ,CAACkD,KAAM;kBACtByB,SAAS,EAAC,yDAAyD;kBACnEc,QAAQ;gBAAA;kBAAA1D,QAAA,EAAA6C,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAA/C,QAAA,EAAA6C,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENlF,OAAA;gBAAA8E,QAAA,gBACE9E,OAAA;kBAAO+E,SAAS,EAAC,8CAA8C;kBAAAD,QAAA,EAAC;gBAEhE;kBAAA3C,QAAA,EAAA6C,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRlF,OAAA;kBACE2F,IAAI,EAAC,KAAK;kBACVC,KAAK,EAAExF,QAAQ,CAACmD,KAAM;kBACtBwB,SAAS,EAAC,yDAAyD;kBACnEc,QAAQ;gBAAA;kBAAA1D,QAAA,EAAA6C,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAA/C,QAAA,EAAA6C,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENlF,OAAA;gBAAA8E,QAAA,gBACE9E,OAAA;kBAAO+E,SAAS,EAAC,8CAA8C;kBAAAD,QAAA,EAAC;gBAEhE;kBAAA3C,QAAA,EAAA6C,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRlF,OAAA;kBAAK+E,SAAS,EAAC,6BAA6B;kBAAAD,QAAA,gBAC1C9E,OAAA;oBAAM+E,SAAS,EAAC,0EAA0E;oBAAAD,QAAA,EAAC;kBAE3F;oBAAA3C,QAAA,EAAA6C,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACPlF,OAAA;oBAAM+E,SAAS,EAAC,uBAAuB;oBAAAD,QAAA,EAAC;kBAExC;oBAAA3C,QAAA,EAAA6C,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAA/C,QAAA,EAAA6C,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAA/C,QAAA,EAAA6C,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAA/C,QAAA,EAAA6C,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENlF,OAAA;cAAK+E,SAAS,EAAC,gCAAgC;cAAAD,QAAA,gBAC7C9E,OAAA;gBAAI+E,SAAS,EAAC,gCAAgC;gBAAAD,QAAA,EAAC;cAAkB;gBAAA3C,QAAA,EAAA6C,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtElF,OAAA;gBAAI+E,SAAS,EAAC,iCAAiC;gBAAAD,QAAA,gBAC7C9E,OAAA;kBAAA8E,QAAA,EAAI;gBAAqB;kBAAA3C,QAAA,EAAA6C,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC9BlF,OAAA;kBAAA8E,QAAA,EAAI;gBAAqC;kBAAA3C,QAAA,EAAA6C,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC9ClF,OAAA;kBAAA8E,QAAA,EAAI;gBAA0B;kBAAA3C,QAAA,EAAA6C,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACnClF,OAAA;kBAAA8E,QAAA,EAAI;gBAAoC;kBAAA3C,QAAA,EAAA6C,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAA/C,QAAA,EAAA6C,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC;YAAA;cAAA/C,QAAA,EAAA6C,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAENlF,OAAA;cAAK+E,SAAS,EAAC,MAAM;cAAAD,QAAA,eACnB9E,OAAA,CAACH,MAAM;gBAAC4F,OAAO,EAAC,SAAS;gBAAAX,QAAA,EAAC;cAE1B;gBAAA3C,QAAA,EAAA6C,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAA/C,QAAA,EAAA6C,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAA/C,QAAA,EAAA6C,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAA/C,QAAA,EAAA6C,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAA/C,QAAA,EAAA6C,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAA/C,QAAA,EAAA6C,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAA/C,QAAA,EAAA6C,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAAChF,EAAA,CAhXID,WAAqB;EAAA,QACRN,WAAW;AAAA;AAAAmG,EAAA,GADxB7F,WAAqB;AAkX3B,eAAeA,WAAW;AAAC,IAAA6F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}