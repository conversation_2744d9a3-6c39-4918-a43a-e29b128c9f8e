{"ast": null, "code": "import React from'react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const OrderSummary=()=>/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col items-center justify-center min-h-screen\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-semibold mb-4\",children:\"Resumo do Pedido\"}),/*#__PURE__*/_jsx(\"div\",{className:\"w-64 h-40 bg-gray-100 flex items-center justify-center mb-4\",children:\"[Mockup do Pedido]\"}),/*#__PURE__*/_jsx(\"p\",{className:\"mb-2\",children:\"Formato: A4\"}),/*#__PURE__*/_jsx(\"p\",{className:\"mb-2\",children:\"Papel: Offset\"}),/*#__PURE__*/_jsx(\"p\",{className:\"mb-2\",children:\"Acabamento: Brilho\"}),/*#__PURE__*/_jsx(\"p\",{className:\"mb-4 font-bold\",children:\"Pre\\xE7o: 0,00 AKZ\"}),/*#__PURE__*/_jsx(\"button\",{className:\"px-4 py-2 bg-blue-600 text-white rounded\",children:\"Finalizar Pedido\"})]});export default OrderSummary;", "map": {"version": 3, "names": ["React", "jsx", "_jsx", "jsxs", "_jsxs", "OrderSummary", "className", "children"], "sources": ["/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/OrderSummary.tsx"], "sourcesContent": ["import React from 'react';\n\nconst OrderSummary: React.FC = () => (\n  <div className=\"flex flex-col items-center justify-center min-h-screen\">\n    <h2 className=\"text-2xl font-semibold mb-4\">Resumo do Pedido</h2>\n    <div className=\"w-64 h-40 bg-gray-100 flex items-center justify-center mb-4\">[Mockup do Pedido]</div>\n    <p className=\"mb-2\">Formato: A4</p>\n    <p className=\"mb-2\">Papel: Offset</p>\n    <p className=\"mb-2\">Acabamento: Brilho</p>\n    <p className=\"mb-4 font-bold\">Preço: 0,00 AKZ</p>\n    <button className=\"px-4 py-2 bg-blue-600 text-white rounded\">Finalizar Pedido</button>\n  </div>\n);\n\nexport default OrderSummary;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE1B,KAAM,CAAAC,YAAsB,CAAGA,CAAA,gBAC7BD,KAAA,QAAKE,SAAS,CAAC,wDAAwD,CAAAC,QAAA,eACrEL,IAAA,OAAII,SAAS,CAAC,6BAA6B,CAAAC,QAAA,CAAC,kBAAgB,CAAI,CAAC,cACjEL,IAAA,QAAKI,SAAS,CAAC,6DAA6D,CAAAC,QAAA,CAAC,oBAAkB,CAAK,CAAC,cACrGL,IAAA,MAAGI,SAAS,CAAC,MAAM,CAAAC,QAAA,CAAC,aAAW,CAAG,CAAC,cACnCL,IAAA,MAAGI,SAAS,CAAC,MAAM,CAAAC,QAAA,CAAC,eAAa,CAAG,CAAC,cACrCL,IAAA,MAAGI,SAAS,CAAC,MAAM,CAAAC,QAAA,CAAC,oBAAkB,CAAG,CAAC,cAC1CL,IAAA,MAAGI,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAAC,oBAAe,CAAG,CAAC,cACjDL,IAAA,WAAQI,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,kBAAgB,CAAQ,CAAC,EACnF,CACN,CAED,cAAe,CAAAF,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}