{"ast": null, "code": "const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000/api';\nclass ApiService {\n  constructor(baseURL = API_BASE_URL) {\n    this.baseURL = void 0;\n    this.baseURL = baseURL;\n  }\n  async request(endpoint, options = {}) {\n    const url = `${this.baseURL}${endpoint}`;\n    const defaultHeaders = {\n      'Content-Type': 'application/json'\n    };\n    const config = {\n      headers: {\n        ...defaultHeaders,\n        ...options.headers\n      },\n      ...options\n    };\n    try {\n      const response = await fetch(url, config);\n      const data = await response.json();\n      if (!response.ok) {\n        return {\n          success: false,\n          error: data.message || 'An error occurred'\n        };\n      }\n      return {\n        success: true,\n        data\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : 'Network error'\n      };\n    }\n  }\n\n  // GET request\n  async get(endpoint) {\n    return this.request(endpoint, {\n      method: 'GET'\n    });\n  }\n\n  // POST request\n  async post(endpoint, data) {\n    return this.request(endpoint, {\n      method: 'POST',\n      body: data ? JSON.stringify(data) : undefined\n    });\n  }\n\n  // PUT request\n  async put(endpoint, data) {\n    return this.request(endpoint, {\n      method: 'PUT',\n      body: data ? JSON.stringify(data) : undefined\n    });\n  }\n\n  // DELETE request\n  async delete(endpoint) {\n    return this.request(endpoint, {\n      method: 'DELETE'\n    });\n  }\n\n  // File upload\n  async uploadFile(endpoint, file) {\n    const formData = new FormData();\n    formData.append('file', file);\n    return this.request(endpoint, {\n      method: 'POST',\n      body: formData,\n      headers: {} // Let browser set Content-Type for FormData\n    });\n  }\n}\nexport const apiService = new ApiService();\nexport default ApiService;", "map": {"version": 3, "names": ["API_BASE_URL", "process", "env", "REACT_APP_API_URL", "ApiService", "constructor", "baseURL", "request", "endpoint", "options", "url", "defaultHeaders", "config", "headers", "response", "fetch", "data", "json", "ok", "success", "error", "message", "Error", "get", "method", "post", "body", "JSON", "stringify", "undefined", "put", "delete", "uploadFile", "file", "formData", "FormData", "append", "apiService"], "sources": ["/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/services/api.ts"], "sourcesContent": ["import { ApiResponse } from '../types';\n\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000/api';\n\nclass ApiService {\n  private baseURL: string;\n\n  constructor(baseURL: string = API_BASE_URL) {\n    this.baseURL = baseURL;\n  }\n\n  private async request<T>(\n    endpoint: string,\n    options: RequestInit = {}\n  ): Promise<ApiResponse<T>> {\n    const url = `${this.baseURL}${endpoint}`;\n    \n    const defaultHeaders = {\n      'Content-Type': 'application/json',\n    };\n\n    const config: RequestInit = {\n      headers: { ...defaultHeaders, ...options.headers },\n      ...options,\n    };\n\n    try {\n      const response = await fetch(url, config);\n      const data = await response.json();\n\n      if (!response.ok) {\n        return {\n          success: false,\n          error: data.message || 'An error occurred',\n        };\n      }\n\n      return {\n        success: true,\n        data,\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : 'Network error',\n      };\n    }\n  }\n\n  // GET request\n  async get<T>(endpoint: string): Promise<ApiResponse<T>> {\n    return this.request<T>(endpoint, { method: 'GET' });\n  }\n\n  // POST request\n  async post<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {\n    return this.request<T>(endpoint, {\n      method: 'POST',\n      body: data ? JSON.stringify(data) : undefined,\n    });\n  }\n\n  // PUT request\n  async put<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {\n    return this.request<T>(endpoint, {\n      method: 'PUT',\n      body: data ? JSON.stringify(data) : undefined,\n    });\n  }\n\n  // DELETE request\n  async delete<T>(endpoint: string): Promise<ApiResponse<T>> {\n    return this.request<T>(endpoint, { method: 'DELETE' });\n  }\n\n  // File upload\n  async uploadFile<T>(endpoint: string, file: File): Promise<ApiResponse<T>> {\n    const formData = new FormData();\n    formData.append('file', file);\n\n    return this.request<T>(endpoint, {\n      method: 'POST',\n      body: formData,\n      headers: {}, // Let browser set Content-Type for FormData\n    });\n  }\n}\n\nexport const apiService = new ApiService();\nexport default ApiService;\n"], "mappings": "AAEA,MAAMA,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,2BAA2B;AAEjF,MAAMC,UAAU,CAAC;EAGfC,WAAWA,CAACC,OAAe,GAAGN,YAAY,EAAE;IAAA,KAFpCM,OAAO;IAGb,IAAI,CAACA,OAAO,GAAGA,OAAO;EACxB;EAEA,MAAcC,OAAOA,CACnBC,QAAgB,EAChBC,OAAoB,GAAG,CAAC,CAAC,EACA;IACzB,MAAMC,GAAG,GAAG,GAAG,IAAI,CAACJ,OAAO,GAAGE,QAAQ,EAAE;IAExC,MAAMG,cAAc,GAAG;MACrB,cAAc,EAAE;IAClB,CAAC;IAED,MAAMC,MAAmB,GAAG;MAC1BC,OAAO,EAAE;QAAE,GAAGF,cAAc;QAAE,GAAGF,OAAO,CAACI;MAAQ,CAAC;MAClD,GAAGJ;IACL,CAAC;IAED,IAAI;MACF,MAAMK,QAAQ,GAAG,MAAMC,KAAK,CAACL,GAAG,EAAEE,MAAM,CAAC;MACzC,MAAMI,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAElC,IAAI,CAACH,QAAQ,CAACI,EAAE,EAAE;QAChB,OAAO;UACLC,OAAO,EAAE,KAAK;UACdC,KAAK,EAAEJ,IAAI,CAACK,OAAO,IAAI;QACzB,CAAC;MACH;MAEA,OAAO;QACLF,OAAO,EAAE,IAAI;QACbH;MACF,CAAC;IACH,CAAC,CAAC,OAAOI,KAAK,EAAE;MACd,OAAO;QACLD,OAAO,EAAE,KAAK;QACdC,KAAK,EAAEA,KAAK,YAAYE,KAAK,GAAGF,KAAK,CAACC,OAAO,GAAG;MAClD,CAAC;IACH;EACF;;EAEA;EACA,MAAME,GAAGA,CAAIf,QAAgB,EAA2B;IACtD,OAAO,IAAI,CAACD,OAAO,CAAIC,QAAQ,EAAE;MAAEgB,MAAM,EAAE;IAAM,CAAC,CAAC;EACrD;;EAEA;EACA,MAAMC,IAAIA,CAAIjB,QAAgB,EAAEQ,IAAU,EAA2B;IACnE,OAAO,IAAI,CAACT,OAAO,CAAIC,QAAQ,EAAE;MAC/BgB,MAAM,EAAE,MAAM;MACdE,IAAI,EAAEV,IAAI,GAAGW,IAAI,CAACC,SAAS,CAACZ,IAAI,CAAC,GAAGa;IACtC,CAAC,CAAC;EACJ;;EAEA;EACA,MAAMC,GAAGA,CAAItB,QAAgB,EAAEQ,IAAU,EAA2B;IAClE,OAAO,IAAI,CAACT,OAAO,CAAIC,QAAQ,EAAE;MAC/BgB,MAAM,EAAE,KAAK;MACbE,IAAI,EAAEV,IAAI,GAAGW,IAAI,CAACC,SAAS,CAACZ,IAAI,CAAC,GAAGa;IACtC,CAAC,CAAC;EACJ;;EAEA;EACA,MAAME,MAAMA,CAAIvB,QAAgB,EAA2B;IACzD,OAAO,IAAI,CAACD,OAAO,CAAIC,QAAQ,EAAE;MAAEgB,MAAM,EAAE;IAAS,CAAC,CAAC;EACxD;;EAEA;EACA,MAAMQ,UAAUA,CAAIxB,QAAgB,EAAEyB,IAAU,EAA2B;IACzE,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEH,IAAI,CAAC;IAE7B,OAAO,IAAI,CAAC1B,OAAO,CAAIC,QAAQ,EAAE;MAC/BgB,MAAM,EAAE,MAAM;MACdE,IAAI,EAAEQ,QAAQ;MACdrB,OAAO,EAAE,CAAC,CAAC,CAAE;IACf,CAAC,CAAC;EACJ;AACF;AAEA,OAAO,MAAMwB,UAAU,GAAG,IAAIjC,UAAU,CAAC,CAAC;AAC1C,eAAeA,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}