{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/OptionsForm.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst OptionsForm = () => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: \"flex flex-col items-center justify-center min-h-screen\",\n  children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n    className: \"text-2xl font-semibold mb-4\",\n    children: \"Op\\xE7\\xF5es de Impress\\xE3o\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n    className: \"space-y-4\",\n    children: [/*#__PURE__*/_jsxDEV(\"select\", {\n      className: \"block w-64 p-2 border rounded\",\n      children: [/*#__PURE__*/_jsxDEV(\"option\", {\n        children: \"Formato\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n        children: \"A4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n        children: \"A3\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 10,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n        children: \"Cart\\xE3o\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 11,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n      className: \"block w-64 p-2 border rounded\",\n      children: [/*#__PURE__*/_jsxDEV(\"option\", {\n        children: \"Papel\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 14,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n        children: \"Offset\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 15,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n        children: \"Couch\\xEA\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n      className: \"block w-64 p-2 border rounded\",\n      children: [/*#__PURE__*/_jsxDEV(\"option\", {\n        children: \"Acabamento\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n        children: \"Brilho\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n        children: \"Fosco\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"px-4 py-2 bg-blue-600 text-white rounded\",\n      children: \"Ver Resumo\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 4,\n  columnNumber: 3\n}, this);\n_c = OptionsForm;\nexport default OptionsForm;\nvar _c;\n$RefreshReg$(_c, \"OptionsForm\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "OptionsForm", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/OptionsForm.tsx"], "sourcesContent": ["import React from 'react';\n\nconst OptionsForm: React.FC = () => (\n  <div className=\"flex flex-col items-center justify-center min-h-screen\">\n    <h2 className=\"text-2xl font-semibold mb-4\">Opções de Impressão</h2>\n    <form className=\"space-y-4\">\n      <select className=\"block w-64 p-2 border rounded\">\n        <option>Formato</option>\n        <option>A4</option>\n        <option>A3</option>\n        <option>Cartão</option>\n      </select>\n      <select className=\"block w-64 p-2 border rounded\">\n        <option>Papel</option>\n        <option>Offset</option>\n        <option>Couchê</option>\n      </select>\n      <select className=\"block w-64 p-2 border rounded\">\n        <option>Acabamento</option>\n        <option>Brilho</option>\n        <option>Fosco</option>\n      </select>\n      <button className=\"px-4 py-2 bg-blue-600 text-white rounded\">Ver Resumo</button>\n    </form>\n  </div>\n);\n\nexport default OptionsForm;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,WAAqB,GAAGA,CAAA,kBAC5BD,OAAA;EAAKE,SAAS,EAAC,wDAAwD;EAAAC,QAAA,gBACrEH,OAAA;IAAIE,SAAS,EAAC,6BAA6B;IAAAC,QAAA,EAAC;EAAmB;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAI,CAAC,eACpEP,OAAA;IAAME,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACzBH,OAAA;MAAQE,SAAS,EAAC,+BAA+B;MAAAC,QAAA,gBAC/CH,OAAA;QAAAG,QAAA,EAAQ;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACxBP,OAAA;QAAAG,QAAA,EAAQ;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACnBP,OAAA;QAAAG,QAAA,EAAQ;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACnBP,OAAA;QAAAG,QAAA,EAAQ;MAAM;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC,eACTP,OAAA;MAAQE,SAAS,EAAC,+BAA+B;MAAAC,QAAA,gBAC/CH,OAAA;QAAAG,QAAA,EAAQ;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACtBP,OAAA;QAAAG,QAAA,EAAQ;MAAM;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACvBP,OAAA;QAAAG,QAAA,EAAQ;MAAM;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC,eACTP,OAAA;MAAQE,SAAS,EAAC,+BAA+B;MAAAC,QAAA,gBAC/CH,OAAA;QAAAG,QAAA,EAAQ;MAAU;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC3BP,OAAA;QAAAG,QAAA,EAAQ;MAAM;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACvBP,OAAA;QAAAG,QAAA,EAAQ;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CAAC,eACTP,OAAA;MAAQE,SAAS,EAAC,0CAA0C;MAAAC,QAAA,EAAC;IAAU;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC5E,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACJ,CACN;AAACC,EAAA,GAvBIP,WAAqB;AAyB3B,eAAeA,WAAW;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}