{"ast": null, "code": "import React,{useState}from'react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const PaymentQRCode=_ref=>{let{qrCode,paymentUrl,amount,currency='AOA',expiresAt,className=''}=_ref;const[copied,setCopied]=useState(false);const handleCopyUrl=async()=>{try{await navigator.clipboard.writeText(paymentUrl);setCopied(true);setTimeout(()=>setCopied(false),2000);}catch(error){console.error('Failed to copy URL:',error);}};const formatAmount=(amount,currency)=>{if(currency==='AOA'){return new Intl.NumberFormat('pt-AO',{style:'currency',currency:'AOA',minimumFractionDigits:0,maximumFractionDigits:0}).format(amount);}return\"\".concat(amount,\" \").concat(currency);};const formatExpiryTime=expiresAt=>{const expiryDate=new Date(expiresAt);const now=new Date();const diffMs=expiryDate.getTime()-now.getTime();if(diffMs<=0){return'Expirado';}const diffMinutes=Math.floor(diffMs/(1000*60));const diffHours=Math.floor(diffMinutes/60);if(diffHours>0){return\"Expira em \".concat(diffHours,\"h \").concat(diffMinutes%60,\"m\");}return\"Expira em \".concat(diffMinutes,\"m\");};return/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-lg shadow-lg p-6 max-w-md mx-auto \".concat(className),children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-center mb-6\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold text-gray-900 mb-2\",children:\"Pagamento Multicaixa Express\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-2xl font-bold text-blue-600\",children:formatAmount(amount,currency)}),expiresAt&&/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-orange-600 mt-1\",children:formatExpiryTime(expiresAt)})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex justify-center mb-6\",children:/*#__PURE__*/_jsx(\"div\",{className:\"bg-white p-4 rounded-lg border-2 border-gray-200\",children:/*#__PURE__*/_jsx(\"img\",{src:qrCode,alt:\"QR Code de Pagamento\",className:\"w-48 h-48 object-contain\"})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-center mb-6\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-600 mb-2\",children:\"Escaneie o c\\xF3digo QR com a app Multicaixa Express\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xs text-gray-500\",children:\"ou use o link de pagamento abaixo\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mb-4\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"Link de Pagamento:\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center gap-2\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:paymentUrl,readOnly:true,className:\"flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm bg-gray-50 text-gray-600\"}),/*#__PURE__*/_jsx(\"button\",{onClick:handleCopyUrl,className:\"px-3 py-2 text-sm font-medium rounded-md transition-colors \".concat(copied?'bg-green-100 text-green-700 border border-green-300':'bg-blue-100 text-blue-700 border border-blue-300 hover:bg-blue-200'),children:copied?/*#__PURE__*/_jsx(\"svg\",{className:\"w-4 h-4\",fill:\"currentColor\",viewBox:\"0 0 20 20\",children:/*#__PURE__*/_jsx(\"path\",{fillRule:\"evenodd\",d:\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",clipRule:\"evenodd\"})}):/*#__PURE__*/_jsxs(\"svg\",{className:\"w-4 h-4\",fill:\"currentColor\",viewBox:\"0 0 20 20\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z\"})]})})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex gap-3\",children:[/*#__PURE__*/_jsx(\"a\",{href:paymentUrl,target:\"_blank\",rel:\"noopener noreferrer\",className:\"flex-1 bg-blue-600 text-white text-center py-2 px-4 rounded-md font-medium hover:bg-blue-700 transition-colors\",children:\"Abrir Multicaixa\"}),/*#__PURE__*/_jsx(\"button\",{onClick:handleCopyUrl,className:\"px-4 py-2 border border-gray-300 text-gray-700 rounded-md font-medium hover:bg-gray-50 transition-colors\",children:\"Copiar Link\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-start gap-2\",children:[/*#__PURE__*/_jsx(\"svg\",{className:\"w-5 h-5 text-yellow-600 mt-0.5 flex-shrink-0\",fill:\"currentColor\",viewBox:\"0 0 20 20\",children:/*#__PURE__*/_jsx(\"path\",{fillRule:\"evenodd\",d:\"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\",clipRule:\"evenodd\"})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm font-medium text-yellow-800\",children:\"Importante\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xs text-yellow-700 mt-1\",children:\"N\\xE3o partilhe este c\\xF3digo QR ou link com terceiros. Use apenas a app oficial Multicaixa Express.\"})]})]})})]});};export default PaymentQRCode;", "map": {"version": 3, "names": ["React", "useState", "jsx", "_jsx", "jsxs", "_jsxs", "PaymentQRCode", "_ref", "qrCode", "paymentUrl", "amount", "currency", "expiresAt", "className", "copied", "setCopied", "handleCopyUrl", "navigator", "clipboard", "writeText", "setTimeout", "error", "console", "formatAmount", "Intl", "NumberFormat", "style", "minimumFractionDigits", "maximumFractionDigits", "format", "concat", "formatExpiryTime", "expiryDate", "Date", "now", "diffMs", "getTime", "diffMinutes", "Math", "floor", "diffHours", "children", "src", "alt", "type", "value", "readOnly", "onClick", "fill", "viewBox", "fillRule", "d", "clipRule", "href", "target", "rel"], "sources": ["/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/PaymentQRCode.tsx"], "sourcesContent": ["import React, { useState } from 'react';\n\ninterface PaymentQRCodeProps {\n  qrCode: string;\n  paymentUrl: string;\n  amount: number;\n  currency?: string;\n  expiresAt?: string;\n  className?: string;\n}\n\nconst PaymentQRCode: React.FC<PaymentQRCodeProps> = ({\n  qrCode,\n  paymentUrl,\n  amount,\n  currency = 'AOA',\n  expiresAt,\n  className = ''\n}) => {\n  const [copied, setCopied] = useState(false);\n\n  const handleCopyUrl = async () => {\n    try {\n      await navigator.clipboard.writeText(paymentUrl);\n      setCopied(true);\n      setTimeout(() => setCopied(false), 2000);\n    } catch (error) {\n      console.error('Failed to copy URL:', error);\n    }\n  };\n\n  const formatAmount = (amount: number, currency: string) => {\n    if (currency === 'AOA') {\n      return new Intl.NumberFormat('pt-AO', {\n        style: 'currency',\n        currency: 'AOA',\n        minimumFractionDigits: 0,\n        maximumFractionDigits: 0,\n      }).format(amount);\n    }\n    return `${amount} ${currency}`;\n  };\n\n  const formatExpiryTime = (expiresAt: string) => {\n    const expiryDate = new Date(expiresAt);\n    const now = new Date();\n    const diffMs = expiryDate.getTime() - now.getTime();\n    \n    if (diffMs <= 0) {\n      return 'Expirado';\n    }\n\n    const diffMinutes = Math.floor(diffMs / (1000 * 60));\n    const diffHours = Math.floor(diffMinutes / 60);\n    \n    if (diffHours > 0) {\n      return `Expira em ${diffHours}h ${diffMinutes % 60}m`;\n    }\n    return `Expira em ${diffMinutes}m`;\n  };\n\n  return (\n    <div className={`bg-white rounded-lg shadow-lg p-6 max-w-md mx-auto ${className}`}>\n      {/* Header */}\n      <div className=\"text-center mb-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">\n          Pagamento Multicaixa Express\n        </h3>\n        <p className=\"text-2xl font-bold text-blue-600\">\n          {formatAmount(amount, currency)}\n        </p>\n        {expiresAt && (\n          <p className=\"text-sm text-orange-600 mt-1\">\n            {formatExpiryTime(expiresAt)}\n          </p>\n        )}\n      </div>\n\n      {/* QR Code */}\n      <div className=\"flex justify-center mb-6\">\n        <div className=\"bg-white p-4 rounded-lg border-2 border-gray-200\">\n          <img \n            src={qrCode} \n            alt=\"QR Code de Pagamento\" \n            className=\"w-48 h-48 object-contain\"\n          />\n        </div>\n      </div>\n\n      {/* Instructions */}\n      <div className=\"text-center mb-6\">\n        <p className=\"text-sm text-gray-600 mb-2\">\n          Escaneie o código QR com a app Multicaixa Express\n        </p>\n        <p className=\"text-xs text-gray-500\">\n          ou use o link de pagamento abaixo\n        </p>\n      </div>\n\n      {/* Payment URL */}\n      <div className=\"mb-4\">\n        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n          Link de Pagamento:\n        </label>\n        <div className=\"flex items-center gap-2\">\n          <input\n            type=\"text\"\n            value={paymentUrl}\n            readOnly\n            className=\"flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm bg-gray-50 text-gray-600\"\n          />\n          <button\n            onClick={handleCopyUrl}\n            className={`px-3 py-2 text-sm font-medium rounded-md transition-colors ${\n              copied\n                ? 'bg-green-100 text-green-700 border border-green-300'\n                : 'bg-blue-100 text-blue-700 border border-blue-300 hover:bg-blue-200'\n            }`}\n          >\n            {copied ? (\n              <svg className=\"w-4 h-4\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n              </svg>\n            ) : (\n              <svg className=\"w-4 h-4\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                <path d=\"M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z\" />\n                <path d=\"M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z\" />\n              </svg>\n            )}\n          </button>\n        </div>\n      </div>\n\n      {/* Action Buttons */}\n      <div className=\"flex gap-3\">\n        <a\n          href={paymentUrl}\n          target=\"_blank\"\n          rel=\"noopener noreferrer\"\n          className=\"flex-1 bg-blue-600 text-white text-center py-2 px-4 rounded-md font-medium hover:bg-blue-700 transition-colors\"\n        >\n          Abrir Multicaixa\n        </a>\n        <button\n          onClick={handleCopyUrl}\n          className=\"px-4 py-2 border border-gray-300 text-gray-700 rounded-md font-medium hover:bg-gray-50 transition-colors\"\n        >\n          Copiar Link\n        </button>\n      </div>\n\n      {/* Security Notice */}\n      <div className=\"mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md\">\n        <div className=\"flex items-start gap-2\">\n          <svg className=\"w-5 h-5 text-yellow-600 mt-0.5 flex-shrink-0\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n            <path fillRule=\"evenodd\" d=\"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n          </svg>\n          <div>\n            <p className=\"text-sm font-medium text-yellow-800\">Importante</p>\n            <p className=\"text-xs text-yellow-700 mt-1\">\n              Não partilhe este código QR ou link com terceiros. Use apenas a app oficial Multicaixa Express.\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default PaymentQRCode;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAWxC,KAAM,CAAAC,aAA2C,CAAGC,IAAA,EAO9C,IAP+C,CACnDC,MAAM,CACNC,UAAU,CACVC,MAAM,CACNC,QAAQ,CAAG,KAAK,CAChBC,SAAS,CACTC,SAAS,CAAG,EACd,CAAC,CAAAN,IAAA,CACC,KAAM,CAACO,MAAM,CAAEC,SAAS,CAAC,CAAGd,QAAQ,CAAC,KAAK,CAAC,CAE3C,KAAM,CAAAe,aAAa,CAAG,KAAAA,CAAA,GAAY,CAChC,GAAI,CACF,KAAM,CAAAC,SAAS,CAACC,SAAS,CAACC,SAAS,CAACV,UAAU,CAAC,CAC/CM,SAAS,CAAC,IAAI,CAAC,CACfK,UAAU,CAAC,IAAML,SAAS,CAAC,KAAK,CAAC,CAAE,IAAI,CAAC,CAC1C,CAAE,MAAOM,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,CAAEA,KAAK,CAAC,CAC7C,CACF,CAAC,CAED,KAAM,CAAAE,YAAY,CAAGA,CAACb,MAAc,CAAEC,QAAgB,GAAK,CACzD,GAAIA,QAAQ,GAAK,KAAK,CAAE,CACtB,MAAO,IAAI,CAAAa,IAAI,CAACC,YAAY,CAAC,OAAO,CAAE,CACpCC,KAAK,CAAE,UAAU,CACjBf,QAAQ,CAAE,KAAK,CACfgB,qBAAqB,CAAE,CAAC,CACxBC,qBAAqB,CAAE,CACzB,CAAC,CAAC,CAACC,MAAM,CAACnB,MAAM,CAAC,CACnB,CACA,SAAAoB,MAAA,CAAUpB,MAAM,MAAAoB,MAAA,CAAInB,QAAQ,EAC9B,CAAC,CAED,KAAM,CAAAoB,gBAAgB,CAAInB,SAAiB,EAAK,CAC9C,KAAM,CAAAoB,UAAU,CAAG,GAAI,CAAAC,IAAI,CAACrB,SAAS,CAAC,CACtC,KAAM,CAAAsB,GAAG,CAAG,GAAI,CAAAD,IAAI,CAAC,CAAC,CACtB,KAAM,CAAAE,MAAM,CAAGH,UAAU,CAACI,OAAO,CAAC,CAAC,CAAGF,GAAG,CAACE,OAAO,CAAC,CAAC,CAEnD,GAAID,MAAM,EAAI,CAAC,CAAE,CACf,MAAO,UAAU,CACnB,CAEA,KAAM,CAAAE,WAAW,CAAGC,IAAI,CAACC,KAAK,CAACJ,MAAM,EAAI,IAAI,CAAG,EAAE,CAAC,CAAC,CACpD,KAAM,CAAAK,SAAS,CAAGF,IAAI,CAACC,KAAK,CAACF,WAAW,CAAG,EAAE,CAAC,CAE9C,GAAIG,SAAS,CAAG,CAAC,CAAE,CACjB,mBAAAV,MAAA,CAAoBU,SAAS,OAAAV,MAAA,CAAKO,WAAW,CAAG,EAAE,MACpD,CACA,mBAAAP,MAAA,CAAoBO,WAAW,MACjC,CAAC,CAED,mBACEhC,KAAA,QAAKQ,SAAS,uDAAAiB,MAAA,CAAwDjB,SAAS,CAAG,CAAA4B,QAAA,eAEhFpC,KAAA,QAAKQ,SAAS,CAAC,kBAAkB,CAAA4B,QAAA,eAC/BtC,IAAA,OAAIU,SAAS,CAAC,0CAA0C,CAAA4B,QAAA,CAAC,8BAEzD,CAAI,CAAC,cACLtC,IAAA,MAAGU,SAAS,CAAC,kCAAkC,CAAA4B,QAAA,CAC5ClB,YAAY,CAACb,MAAM,CAAEC,QAAQ,CAAC,CAC9B,CAAC,CACHC,SAAS,eACRT,IAAA,MAAGU,SAAS,CAAC,8BAA8B,CAAA4B,QAAA,CACxCV,gBAAgB,CAACnB,SAAS,CAAC,CAC3B,CACJ,EACE,CAAC,cAGNT,IAAA,QAAKU,SAAS,CAAC,0BAA0B,CAAA4B,QAAA,cACvCtC,IAAA,QAAKU,SAAS,CAAC,kDAAkD,CAAA4B,QAAA,cAC/DtC,IAAA,QACEuC,GAAG,CAAElC,MAAO,CACZmC,GAAG,CAAC,sBAAsB,CAC1B9B,SAAS,CAAC,0BAA0B,CACrC,CAAC,CACC,CAAC,CACH,CAAC,cAGNR,KAAA,QAAKQ,SAAS,CAAC,kBAAkB,CAAA4B,QAAA,eAC/BtC,IAAA,MAAGU,SAAS,CAAC,4BAA4B,CAAA4B,QAAA,CAAC,sDAE1C,CAAG,CAAC,cACJtC,IAAA,MAAGU,SAAS,CAAC,uBAAuB,CAAA4B,QAAA,CAAC,mCAErC,CAAG,CAAC,EACD,CAAC,cAGNpC,KAAA,QAAKQ,SAAS,CAAC,MAAM,CAAA4B,QAAA,eACnBtC,IAAA,UAAOU,SAAS,CAAC,8CAA8C,CAAA4B,QAAA,CAAC,oBAEhE,CAAO,CAAC,cACRpC,KAAA,QAAKQ,SAAS,CAAC,yBAAyB,CAAA4B,QAAA,eACtCtC,IAAA,UACEyC,IAAI,CAAC,MAAM,CACXC,KAAK,CAAEpC,UAAW,CAClBqC,QAAQ,MACRjC,SAAS,CAAC,qFAAqF,CAChG,CAAC,cACFV,IAAA,WACE4C,OAAO,CAAE/B,aAAc,CACvBH,SAAS,+DAAAiB,MAAA,CACPhB,MAAM,CACF,qDAAqD,CACrD,oEAAoE,CACvE,CAAA2B,QAAA,CAEF3B,MAAM,cACLX,IAAA,QAAKU,SAAS,CAAC,SAAS,CAACmC,IAAI,CAAC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAAR,QAAA,cAC9DtC,IAAA,SAAM+C,QAAQ,CAAC,SAAS,CAACC,CAAC,CAAC,oHAAoH,CAACC,QAAQ,CAAC,SAAS,CAAE,CAAC,CAClK,CAAC,cAEN/C,KAAA,QAAKQ,SAAS,CAAC,SAAS,CAACmC,IAAI,CAAC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAAR,QAAA,eAC9DtC,IAAA,SAAMgD,CAAC,CAAC,gDAAgD,CAAE,CAAC,cAC3DhD,IAAA,SAAMgD,CAAC,CAAC,4FAA4F,CAAE,CAAC,EACpG,CACN,CACK,CAAC,EACN,CAAC,EACH,CAAC,cAGN9C,KAAA,QAAKQ,SAAS,CAAC,YAAY,CAAA4B,QAAA,eACzBtC,IAAA,MACEkD,IAAI,CAAE5C,UAAW,CACjB6C,MAAM,CAAC,QAAQ,CACfC,GAAG,CAAC,qBAAqB,CACzB1C,SAAS,CAAC,gHAAgH,CAAA4B,QAAA,CAC3H,kBAED,CAAG,CAAC,cACJtC,IAAA,WACE4C,OAAO,CAAE/B,aAAc,CACvBH,SAAS,CAAC,0GAA0G,CAAA4B,QAAA,CACrH,aAED,CAAQ,CAAC,EACN,CAAC,cAGNtC,IAAA,QAAKU,SAAS,CAAC,2DAA2D,CAAA4B,QAAA,cACxEpC,KAAA,QAAKQ,SAAS,CAAC,wBAAwB,CAAA4B,QAAA,eACrCtC,IAAA,QAAKU,SAAS,CAAC,8CAA8C,CAACmC,IAAI,CAAC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAAR,QAAA,cACnGtC,IAAA,SAAM+C,QAAQ,CAAC,SAAS,CAACC,CAAC,CAAC,mNAAmN,CAACC,QAAQ,CAAC,SAAS,CAAE,CAAC,CACjQ,CAAC,cACN/C,KAAA,QAAAoC,QAAA,eACEtC,IAAA,MAAGU,SAAS,CAAC,qCAAqC,CAAA4B,QAAA,CAAC,YAAU,CAAG,CAAC,cACjEtC,IAAA,MAAGU,SAAS,CAAC,8BAA8B,CAAA4B,QAAA,CAAC,uGAE5C,CAAG,CAAC,EACD,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAnC,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}