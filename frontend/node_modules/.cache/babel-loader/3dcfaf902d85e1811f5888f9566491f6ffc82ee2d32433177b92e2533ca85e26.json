{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/landing/ServicesSection.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ServiceCard = ({\n  icon,\n  title,\n  description,\n  features,\n  price,\n  popular\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `relative bg-white rounded-2xl shadow-xl p-8 transform hover:scale-105 transition-all duration-300 ${popular ? 'ring-4 ring-weprint-magenta' : ''}`,\n    children: [popular && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute -top-4 left-1/2 transform -translate-x-1/2\",\n      children: /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"bg-weprint-magenta text-white px-6 py-2 rounded-full text-sm font-bold\",\n        children: \"MAIS POPULAR\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-6xl mb-4\",\n        children: icon\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-2xl font-bold text-weprint-black mb-2\",\n        children: title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600\",\n        children: description\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n      className: \"space-y-3 mb-8\",\n      children: features.map((feature, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-2 h-2 bg-weprint-cyan rounded-full mr-3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-gray-700\",\n          children: feature\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 13\n        }, this)]\n      }, index, true, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-3xl font-bold text-weprint-magenta mb-4\",\n        children: price\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"w-full bg-weprint-gradient text-white font-bold py-3 rounded-full hover:shadow-lg transition-all duration-300\",\n        children: \"Escolher Plano\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 14,\n    columnNumber: 5\n  }, this);\n};\n_c = ServiceCard;\nconst ServicesSection = () => {\n  const services = [{\n    icon: \"📄\",\n    title: \"Documentos\",\n    description: \"Impressão de documentos profissionais\",\n    features: [\"PDF, Word, Excel\", \"Preto e branco ou colorido\", \"Papel A4, A3, Carta\", \"Acabamento profissional\"],\n    price: \"A partir de 50 AOA\"\n  }, {\n    icon: \"📊\",\n    title: \"Apresentações\",\n    description: \"Impressões para reuniões e eventos\",\n    features: [\"PowerPoint, PDF\", \"Papel premium\", \"Cores vibrantes\", \"Encadernação disponível\"],\n    price: \"A partir de 100 AOA\",\n    popular: true\n  }, {\n    icon: \"📸\",\n    title: \"Fotografias\",\n    description: \"Impressão fotográfica de alta qualidade\",\n    features: [\"Papel fotográfico\", \"Vários tamanhos\", \"Cores profissionais\", \"Acabamento brilhante/fosco\"],\n    price: \"A partir de 200 AOA\"\n  }, {\n    icon: \"📚\",\n    title: \"Livros & Revistas\",\n    description: \"Impressão de publicações completas\",\n    features: [\"Encadernação profissional\", \"Capa personalizada\", \"Múltiplas páginas\", \"Papel de qualidade\"],\n    price: \"A partir de 500 AOA\"\n  }];\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    id: \"services\",\n    className: \"py-20 bg-weprint-gradient-subtle\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-6 lg:px-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-16\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-4xl md:text-5xl font-black text-weprint-black mb-6\",\n          children: [\"Nossos \", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-weprint-magenta\",\n            children: \"Servi\\xE7os\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 20\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n          children: \"Oferecemos uma ampla gama de servi\\xE7os de impress\\xE3o para atender todas as suas necessidades, desde documentos simples at\\xE9 projetos complexos.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-24 h-1 bg-weprint-gradient mx-auto mt-6 rounded-full\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n        children: services.map((service, index) => /*#__PURE__*/_jsxDEV(ServiceCard, {\n          ...service\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-16 text-center\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-2xl shadow-xl p-8 max-w-4xl mx-auto\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-2xl font-bold text-weprint-black mb-4\",\n            children: \"N\\xE3o encontrou o que procura?\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 mb-6\",\n            children: \"Temos solu\\xE7\\xF5es personalizadas para projetos especiais. Entre em contato conosco e vamos criar a solu\\xE7\\xE3o perfeita para voc\\xEA.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"bg-weprint-magenta text-white font-bold px-8 py-3 rounded-full hover:bg-opacity-90 transition-all duration-300\",\n            children: \"Falar com Especialista\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 103,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 102,\n    columnNumber: 5\n  }, this);\n};\n_c2 = ServicesSection;\nexport default ServicesSection;\nvar _c, _c2;\n$RefreshReg$(_c, \"ServiceCard\");\n$RefreshReg$(_c2, \"ServicesSection\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "ServiceCard", "icon", "title", "description", "features", "price", "popular", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "feature", "index", "_c", "ServicesSection", "services", "id", "service", "_c2", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/landing/ServicesSection.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface ServiceCardProps {\n  icon: string;\n  title: string;\n  description: string;\n  features: string[];\n  price: string;\n  popular?: boolean;\n}\n\nconst ServiceCard: React.FC<ServiceCardProps> = ({ icon, title, description, features, price, popular }) => {\n  return (\n    <div className={`relative bg-white rounded-2xl shadow-xl p-8 transform hover:scale-105 transition-all duration-300 ${popular ? 'ring-4 ring-weprint-magenta' : ''}`}>\n      {popular && (\n        <div className=\"absolute -top-4 left-1/2 transform -translate-x-1/2\">\n          <span className=\"bg-weprint-magenta text-white px-6 py-2 rounded-full text-sm font-bold\">\n            MAIS POPULAR\n          </span>\n        </div>\n      )}\n      \n      <div className=\"text-center mb-6\">\n        <div className=\"text-6xl mb-4\">{icon}</div>\n        <h3 className=\"text-2xl font-bold text-weprint-black mb-2\">{title}</h3>\n        <p className=\"text-gray-600\">{description}</p>\n      </div>\n\n      <ul className=\"space-y-3 mb-8\">\n        {features.map((feature, index) => (\n          <li key={index} className=\"flex items-center\">\n            <div className=\"w-2 h-2 bg-weprint-cyan rounded-full mr-3\"></div>\n            <span className=\"text-gray-700\">{feature}</span>\n          </li>\n        ))}\n      </ul>\n\n      <div className=\"text-center\">\n        <div className=\"text-3xl font-bold text-weprint-magenta mb-4\">{price}</div>\n        <button className=\"w-full bg-weprint-gradient text-white font-bold py-3 rounded-full hover:shadow-lg transition-all duration-300\">\n          Escolher Plano\n        </button>\n      </div>\n    </div>\n  );\n};\n\nconst ServicesSection: React.FC = () => {\n  const services = [\n    {\n      icon: \"📄\",\n      title: \"Documentos\",\n      description: \"Impressão de documentos profissionais\",\n      features: [\n        \"PDF, Word, Excel\",\n        \"Preto e branco ou colorido\",\n        \"Papel A4, A3, Carta\",\n        \"Acabamento profissional\"\n      ],\n      price: \"A partir de 50 AOA\"\n    },\n    {\n      icon: \"📊\",\n      title: \"Apresentações\",\n      description: \"Impressões para reuniões e eventos\",\n      features: [\n        \"PowerPoint, PDF\",\n        \"Papel premium\",\n        \"Cores vibrantes\",\n        \"Encadernação disponível\"\n      ],\n      price: \"A partir de 100 AOA\",\n      popular: true\n    },\n    {\n      icon: \"📸\",\n      title: \"Fotografias\",\n      description: \"Impressão fotográfica de alta qualidade\",\n      features: [\n        \"Papel fotográfico\",\n        \"Vários tamanhos\",\n        \"Cores profissionais\",\n        \"Acabamento brilhante/fosco\"\n      ],\n      price: \"A partir de 200 AOA\"\n    },\n    {\n      icon: \"📚\",\n      title: \"Livros & Revistas\",\n      description: \"Impressão de publicações completas\",\n      features: [\n        \"Encadernação profissional\",\n        \"Capa personalizada\",\n        \"Múltiplas páginas\",\n        \"Papel de qualidade\"\n      ],\n      price: \"A partir de 500 AOA\"\n    }\n  ];\n\n  return (\n    <section id=\"services\" className=\"py-20 bg-weprint-gradient-subtle\">\n      <div className=\"container mx-auto px-6 lg:px-8\">\n        {/* Section Header */}\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-4xl md:text-5xl font-black text-weprint-black mb-6\">\n            Nossos <span className=\"text-weprint-magenta\">Serviços</span>\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            Oferecemos uma ampla gama de serviços de impressão para atender todas as suas necessidades, \n            desde documentos simples até projetos complexos.\n          </p>\n          <div className=\"w-24 h-1 bg-weprint-gradient mx-auto mt-6 rounded-full\"></div>\n        </div>\n\n        {/* Services Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {services.map((service, index) => (\n            <ServiceCard key={index} {...service} />\n          ))}\n        </div>\n\n        {/* Additional Info */}\n        <div className=\"mt-16 text-center\">\n          <div className=\"bg-white rounded-2xl shadow-xl p-8 max-w-4xl mx-auto\">\n            <h3 className=\"text-2xl font-bold text-weprint-black mb-4\">\n              Não encontrou o que procura?\n            </h3>\n            <p className=\"text-gray-600 mb-6\">\n              Temos soluções personalizadas para projetos especiais. Entre em contato conosco \n              e vamos criar a solução perfeita para você.\n            </p>\n            <button className=\"bg-weprint-magenta text-white font-bold px-8 py-3 rounded-full hover:bg-opacity-90 transition-all duration-300\">\n              Falar com Especialista\n            </button>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default ServicesSection;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAW1B,MAAMC,WAAuC,GAAGA,CAAC;EAAEC,IAAI;EAAEC,KAAK;EAAEC,WAAW;EAAEC,QAAQ;EAAEC,KAAK;EAAEC;AAAQ,CAAC,KAAK;EAC1G,oBACEP,OAAA;IAAKQ,SAAS,EAAE,qGAAqGD,OAAO,GAAG,6BAA6B,GAAG,EAAE,EAAG;IAAAE,QAAA,GACjKF,OAAO,iBACNP,OAAA;MAAKQ,SAAS,EAAC,qDAAqD;MAAAC,QAAA,eAClET,OAAA;QAAMQ,SAAS,EAAC,wEAAwE;QAAAC,QAAA,EAAC;MAEzF;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CACN,eAEDb,OAAA;MAAKQ,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BT,OAAA;QAAKQ,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAEP;MAAI;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC3Cb,OAAA;QAAIQ,SAAS,EAAC,4CAA4C;QAAAC,QAAA,EAAEN;MAAK;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACvEb,OAAA;QAAGQ,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAEL;MAAW;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3C,CAAC,eAENb,OAAA;MAAIQ,SAAS,EAAC,gBAAgB;MAAAC,QAAA,EAC3BJ,QAAQ,CAACS,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBAC3BhB,OAAA;QAAgBQ,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAC3CT,OAAA;UAAKQ,SAAS,EAAC;QAA2C;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACjEb,OAAA;UAAMQ,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAEM;QAAO;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA,GAFzCG,KAAK;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAGV,CACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAELb,OAAA;MAAKQ,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BT,OAAA;QAAKQ,SAAS,EAAC,8CAA8C;QAAAC,QAAA,EAAEH;MAAK;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC3Eb,OAAA;QAAQQ,SAAS,EAAC,+GAA+G;QAAAC,QAAA,EAAC;MAElI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACI,EAAA,GAlCIhB,WAAuC;AAoC7C,MAAMiB,eAAyB,GAAGA,CAAA,KAAM;EACtC,MAAMC,QAAQ,GAAG,CACf;IACEjB,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,YAAY;IACnBC,WAAW,EAAE,uCAAuC;IACpDC,QAAQ,EAAE,CACR,kBAAkB,EAClB,4BAA4B,EAC5B,qBAAqB,EACrB,yBAAyB,CAC1B;IACDC,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,eAAe;IACtBC,WAAW,EAAE,oCAAoC;IACjDC,QAAQ,EAAE,CACR,iBAAiB,EACjB,eAAe,EACf,iBAAiB,EACjB,yBAAyB,CAC1B;IACDC,KAAK,EAAE,qBAAqB;IAC5BC,OAAO,EAAE;EACX,CAAC,EACD;IACEL,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,aAAa;IACpBC,WAAW,EAAE,yCAAyC;IACtDC,QAAQ,EAAE,CACR,mBAAmB,EACnB,iBAAiB,EACjB,qBAAqB,EACrB,4BAA4B,CAC7B;IACDC,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,mBAAmB;IAC1BC,WAAW,EAAE,oCAAoC;IACjDC,QAAQ,EAAE,CACR,2BAA2B,EAC3B,oBAAoB,EACpB,mBAAmB,EACnB,oBAAoB,CACrB;IACDC,KAAK,EAAE;EACT,CAAC,CACF;EAED,oBACEN,OAAA;IAASoB,EAAE,EAAC,UAAU;IAACZ,SAAS,EAAC,kCAAkC;IAAAC,QAAA,eACjET,OAAA;MAAKQ,SAAS,EAAC,gCAAgC;MAAAC,QAAA,gBAE7CT,OAAA;QAAKQ,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCT,OAAA;UAAIQ,SAAS,EAAC,yDAAyD;UAAAC,QAAA,GAAC,SAC/D,eAAAT,OAAA;YAAMQ,SAAS,EAAC,sBAAsB;YAAAC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CAAC,eACLb,OAAA;UAAGQ,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EAAC;QAGvD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJb,OAAA;UAAKQ,SAAS,EAAC;QAAwD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3E,CAAC,eAGNb,OAAA;QAAKQ,SAAS,EAAC,sDAAsD;QAAAC,QAAA,EAClEU,QAAQ,CAACL,GAAG,CAAC,CAACO,OAAO,EAAEL,KAAK,kBAC3BhB,OAAA,CAACC,WAAW;UAAA,GAAiBoB;QAAO,GAAlBL,KAAK;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAgB,CACxC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNb,OAAA;QAAKQ,SAAS,EAAC,mBAAmB;QAAAC,QAAA,eAChCT,OAAA;UAAKQ,SAAS,EAAC,sDAAsD;UAAAC,QAAA,gBACnET,OAAA;YAAIQ,SAAS,EAAC,4CAA4C;YAAAC,QAAA,EAAC;UAE3D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLb,OAAA;YAAGQ,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAGlC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJb,OAAA;YAAQQ,SAAS,EAAC,gHAAgH;YAAAC,QAAA,EAAC;UAEnI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACS,GAAA,GA7FIJ,eAAyB;AA+F/B,eAAeA,eAAe;AAAC,IAAAD,EAAA,EAAAK,GAAA;AAAAC,YAAA,CAAAN,EAAA;AAAAM,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}