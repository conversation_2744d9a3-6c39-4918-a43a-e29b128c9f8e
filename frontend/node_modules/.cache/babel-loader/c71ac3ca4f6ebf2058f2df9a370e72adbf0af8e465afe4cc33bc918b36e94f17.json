{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/PaymentQRCode.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PaymentQRCode = ({\n  qrCode,\n  paymentUrl,\n  amount,\n  currency = 'AOA',\n  expiresAt,\n  className = ''\n}) => {\n  _s();\n  const [copied, setCopied] = useState(false);\n  const handleCopyUrl = async () => {\n    try {\n      await navigator.clipboard.writeText(paymentUrl);\n      setCopied(true);\n      setTimeout(() => setCopied(false), 2000);\n    } catch (error) {\n      console.error('Failed to copy URL:', error);\n    }\n  };\n  const formatAmount = (amount, currency) => {\n    if (currency === 'AOA') {\n      return new Intl.NumberFormat('pt-AO', {\n        style: 'currency',\n        currency: 'AOA',\n        minimumFractionDigits: 0,\n        maximumFractionDigits: 0\n      }).format(amount);\n    }\n    return `${amount} ${currency}`;\n  };\n  const formatExpiryTime = expiresAt => {\n    const expiryDate = new Date(expiresAt);\n    const now = new Date();\n    const diffMs = expiryDate.getTime() - now.getTime();\n    if (diffMs <= 0) {\n      return 'Expirado';\n    }\n    const diffMinutes = Math.floor(diffMs / (1000 * 60));\n    const diffHours = Math.floor(diffMinutes / 60);\n    if (diffHours > 0) {\n      return `Expira em ${diffHours}h ${diffMinutes % 60}m`;\n    }\n    return `Expira em ${diffMinutes}m`;\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `bg-white rounded-lg shadow-lg p-6 max-w-md mx-auto ${className}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-semibold text-gray-900 mb-2\",\n        children: \"Pagamento Multicaixa Express\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-2xl font-bold text-blue-600\",\n        children: formatAmount(amount, currency)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 9\n      }, this), expiresAt && /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-sm text-orange-600 mt-1\",\n        children: formatExpiryTime(expiresAt)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-center mb-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white p-4 rounded-lg border-2 border-gray-200\",\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: qrCode,\n          alt: \"QR Code de Pagamento\",\n          className: \"w-48 h-48 object-contain\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-sm text-gray-600 mb-2\",\n        children: \"Escaneie o c\\xF3digo QR com a app Multicaixa Express\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-xs text-gray-500\",\n        children: \"ou use o link de pagamento abaixo\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 91,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        className: \"block text-sm font-medium text-gray-700 mb-2\",\n        children: \"Link de Pagamento:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          value: paymentUrl,\n          readOnly: true,\n          className: \"flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm bg-gray-50 text-gray-600\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleCopyUrl,\n          className: `px-3 py-2 text-sm font-medium rounded-md transition-colors ${copied ? 'bg-green-100 text-green-700 border border-green-300' : 'bg-blue-100 text-blue-700 border border-blue-300 hover:bg-blue-200'}`,\n          children: copied ? /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-4 h-4\",\n            fill: \"currentColor\",\n            viewBox: \"0 0 20 20\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              fillRule: \"evenodd\",\n              d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n              clipRule: \"evenodd\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-4 h-4\",\n            fill: \"currentColor\",\n            viewBox: \"0 0 20 20\",\n            children: [/*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 127,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex gap-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"a\", {\n        href: paymentUrl,\n        target: \"_blank\",\n        rel: \"noopener noreferrer\",\n        className: \"flex-1 bg-blue-600 text-white text-center py-2 px-4 rounded-md font-medium hover:bg-blue-700 transition-colors\",\n        children: \"Abrir Multicaixa\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleCopyUrl,\n        className: \"px-4 py-2 border border-gray-300 text-gray-700 rounded-md font-medium hover:bg-gray-50 transition-colors\",\n        children: \"Copiar Link\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 144,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-start gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n          className: \"w-5 h-5 text-yellow-600 mt-0.5 flex-shrink-0\",\n          fill: \"currentColor\",\n          viewBox: \"0 0 20 20\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            fillRule: \"evenodd\",\n            d: \"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\",\n            clipRule: \"evenodd\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm font-medium text-yellow-800\",\n            children: \"Importante\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xs text-yellow-700 mt-1\",\n            children: \"N\\xE3o partilhe este c\\xF3digo QR ou link com terceiros. Use apenas a app oficial Multicaixa Express.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 153,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 63,\n    columnNumber: 5\n  }, this);\n};\n_s(PaymentQRCode, \"NE86rL3vg4NVcTTWDavsT0hUBJs=\");\n_c = PaymentQRCode;\nexport default PaymentQRCode;\nvar _c;\n$RefreshReg$(_c, \"PaymentQRCode\");", "map": {"version": 3, "names": ["React", "useState", "jsxDEV", "_jsxDEV", "PaymentQRCode", "qrCode", "paymentUrl", "amount", "currency", "expiresAt", "className", "_s", "copied", "setCopied", "handleCopyUrl", "navigator", "clipboard", "writeText", "setTimeout", "error", "console", "formatAmount", "Intl", "NumberFormat", "style", "minimumFractionDigits", "maximumFractionDigits", "format", "formatExpiryTime", "expiryDate", "Date", "now", "diffMs", "getTime", "diffMinutes", "Math", "floor", "diffHours", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "src", "alt", "type", "value", "readOnly", "onClick", "fill", "viewBox", "fillRule", "d", "clipRule", "href", "target", "rel", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/PaymentQRCode.tsx"], "sourcesContent": ["import React, { useState } from 'react';\n\ninterface PaymentQRCodeProps {\n  qrCode: string;\n  paymentUrl: string;\n  amount: number;\n  currency?: string;\n  expiresAt?: string;\n  className?: string;\n}\n\nconst PaymentQRCode: React.FC<PaymentQRCodeProps> = ({\n  qrCode,\n  paymentUrl,\n  amount,\n  currency = 'AOA',\n  expiresAt,\n  className = ''\n}) => {\n  const [copied, setCopied] = useState(false);\n\n  const handleCopyUrl = async () => {\n    try {\n      await navigator.clipboard.writeText(paymentUrl);\n      setCopied(true);\n      setTimeout(() => setCopied(false), 2000);\n    } catch (error) {\n      console.error('Failed to copy URL:', error);\n    }\n  };\n\n  const formatAmount = (amount: number, currency: string) => {\n    if (currency === 'AOA') {\n      return new Intl.NumberFormat('pt-AO', {\n        style: 'currency',\n        currency: 'AOA',\n        minimumFractionDigits: 0,\n        maximumFractionDigits: 0,\n      }).format(amount);\n    }\n    return `${amount} ${currency}`;\n  };\n\n  const formatExpiryTime = (expiresAt: string) => {\n    const expiryDate = new Date(expiresAt);\n    const now = new Date();\n    const diffMs = expiryDate.getTime() - now.getTime();\n    \n    if (diffMs <= 0) {\n      return 'Expirado';\n    }\n\n    const diffMinutes = Math.floor(diffMs / (1000 * 60));\n    const diffHours = Math.floor(diffMinutes / 60);\n    \n    if (diffHours > 0) {\n      return `Expira em ${diffHours}h ${diffMinutes % 60}m`;\n    }\n    return `Expira em ${diffMinutes}m`;\n  };\n\n  return (\n    <div className={`bg-white rounded-lg shadow-lg p-6 max-w-md mx-auto ${className}`}>\n      {/* Header */}\n      <div className=\"text-center mb-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">\n          Pagamento Multicaixa Express\n        </h3>\n        <p className=\"text-2xl font-bold text-blue-600\">\n          {formatAmount(amount, currency)}\n        </p>\n        {expiresAt && (\n          <p className=\"text-sm text-orange-600 mt-1\">\n            {formatExpiryTime(expiresAt)}\n          </p>\n        )}\n      </div>\n\n      {/* QR Code */}\n      <div className=\"flex justify-center mb-6\">\n        <div className=\"bg-white p-4 rounded-lg border-2 border-gray-200\">\n          <img \n            src={qrCode} \n            alt=\"QR Code de Pagamento\" \n            className=\"w-48 h-48 object-contain\"\n          />\n        </div>\n      </div>\n\n      {/* Instructions */}\n      <div className=\"text-center mb-6\">\n        <p className=\"text-sm text-gray-600 mb-2\">\n          Escaneie o código QR com a app Multicaixa Express\n        </p>\n        <p className=\"text-xs text-gray-500\">\n          ou use o link de pagamento abaixo\n        </p>\n      </div>\n\n      {/* Payment URL */}\n      <div className=\"mb-4\">\n        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n          Link de Pagamento:\n        </label>\n        <div className=\"flex items-center gap-2\">\n          <input\n            type=\"text\"\n            value={paymentUrl}\n            readOnly\n            className=\"flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm bg-gray-50 text-gray-600\"\n          />\n          <button\n            onClick={handleCopyUrl}\n            className={`px-3 py-2 text-sm font-medium rounded-md transition-colors ${\n              copied\n                ? 'bg-green-100 text-green-700 border border-green-300'\n                : 'bg-blue-100 text-blue-700 border border-blue-300 hover:bg-blue-200'\n            }`}\n          >\n            {copied ? (\n              <svg className=\"w-4 h-4\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n              </svg>\n            ) : (\n              <svg className=\"w-4 h-4\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                <path d=\"M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z\" />\n                <path d=\"M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z\" />\n              </svg>\n            )}\n          </button>\n        </div>\n      </div>\n\n      {/* Action Buttons */}\n      <div className=\"flex gap-3\">\n        <a\n          href={paymentUrl}\n          target=\"_blank\"\n          rel=\"noopener noreferrer\"\n          className=\"flex-1 bg-blue-600 text-white text-center py-2 px-4 rounded-md font-medium hover:bg-blue-700 transition-colors\"\n        >\n          Abrir Multicaixa\n        </a>\n        <button\n          onClick={handleCopyUrl}\n          className=\"px-4 py-2 border border-gray-300 text-gray-700 rounded-md font-medium hover:bg-gray-50 transition-colors\"\n        >\n          Copiar Link\n        </button>\n      </div>\n\n      {/* Security Notice */}\n      <div className=\"mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md\">\n        <div className=\"flex items-start gap-2\">\n          <svg className=\"w-5 h-5 text-yellow-600 mt-0.5 flex-shrink-0\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n            <path fillRule=\"evenodd\" d=\"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n          </svg>\n          <div>\n            <p className=\"text-sm font-medium text-yellow-800\">Importante</p>\n            <p className=\"text-xs text-yellow-700 mt-1\">\n              Não partilhe este código QR ou link com terceiros. Use apenas a app oficial Multicaixa Express.\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default PaymentQRCode;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAWxC,MAAMC,aAA2C,GAAGA,CAAC;EACnDC,MAAM;EACNC,UAAU;EACVC,MAAM;EACNC,QAAQ,GAAG,KAAK;EAChBC,SAAS;EACTC,SAAS,GAAG;AACd,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EAE3C,MAAMa,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMC,SAAS,CAACC,SAAS,CAACC,SAAS,CAACX,UAAU,CAAC;MAC/CO,SAAS,CAAC,IAAI,CAAC;MACfK,UAAU,CAAC,MAAML,SAAS,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;IAC1C,CAAC,CAAC,OAAOM,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;IAC7C;EACF,CAAC;EAED,MAAME,YAAY,GAAGA,CAACd,MAAc,EAAEC,QAAgB,KAAK;IACzD,IAAIA,QAAQ,KAAK,KAAK,EAAE;MACtB,OAAO,IAAIc,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;QACpCC,KAAK,EAAE,UAAU;QACjBhB,QAAQ,EAAE,KAAK;QACfiB,qBAAqB,EAAE,CAAC;QACxBC,qBAAqB,EAAE;MACzB,CAAC,CAAC,CAACC,MAAM,CAACpB,MAAM,CAAC;IACnB;IACA,OAAO,GAAGA,MAAM,IAAIC,QAAQ,EAAE;EAChC,CAAC;EAED,MAAMoB,gBAAgB,GAAInB,SAAiB,IAAK;IAC9C,MAAMoB,UAAU,GAAG,IAAIC,IAAI,CAACrB,SAAS,CAAC;IACtC,MAAMsB,GAAG,GAAG,IAAID,IAAI,CAAC,CAAC;IACtB,MAAME,MAAM,GAAGH,UAAU,CAACI,OAAO,CAAC,CAAC,GAAGF,GAAG,CAACE,OAAO,CAAC,CAAC;IAEnD,IAAID,MAAM,IAAI,CAAC,EAAE;MACf,OAAO,UAAU;IACnB;IAEA,MAAME,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACJ,MAAM,IAAI,IAAI,GAAG,EAAE,CAAC,CAAC;IACpD,MAAMK,SAAS,GAAGF,IAAI,CAACC,KAAK,CAACF,WAAW,GAAG,EAAE,CAAC;IAE9C,IAAIG,SAAS,GAAG,CAAC,EAAE;MACjB,OAAO,aAAaA,SAAS,KAAKH,WAAW,GAAG,EAAE,GAAG;IACvD;IACA,OAAO,aAAaA,WAAW,GAAG;EACpC,CAAC;EAED,oBACE/B,OAAA;IAAKO,SAAS,EAAE,sDAAsDA,SAAS,EAAG;IAAA4B,QAAA,gBAEhFnC,OAAA;MAAKO,SAAS,EAAC,kBAAkB;MAAA4B,QAAA,gBAC/BnC,OAAA;QAAIO,SAAS,EAAC,0CAA0C;QAAA4B,QAAA,EAAC;MAEzD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLvC,OAAA;QAAGO,SAAS,EAAC,kCAAkC;QAAA4B,QAAA,EAC5CjB,YAAY,CAACd,MAAM,EAAEC,QAAQ;MAAC;QAAA+B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC,EACHjC,SAAS,iBACRN,OAAA;QAAGO,SAAS,EAAC,8BAA8B;QAAA4B,QAAA,EACxCV,gBAAgB,CAACnB,SAAS;MAAC;QAAA8B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B,CACJ;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGNvC,OAAA;MAAKO,SAAS,EAAC,0BAA0B;MAAA4B,QAAA,eACvCnC,OAAA;QAAKO,SAAS,EAAC,kDAAkD;QAAA4B,QAAA,eAC/DnC,OAAA;UACEwC,GAAG,EAAEtC,MAAO;UACZuC,GAAG,EAAC,sBAAsB;UAC1BlC,SAAS,EAAC;QAA0B;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNvC,OAAA;MAAKO,SAAS,EAAC,kBAAkB;MAAA4B,QAAA,gBAC/BnC,OAAA;QAAGO,SAAS,EAAC,4BAA4B;QAAA4B,QAAA,EAAC;MAE1C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJvC,OAAA;QAAGO,SAAS,EAAC,uBAAuB;QAAA4B,QAAA,EAAC;MAErC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGNvC,OAAA;MAAKO,SAAS,EAAC,MAAM;MAAA4B,QAAA,gBACnBnC,OAAA;QAAOO,SAAS,EAAC,8CAA8C;QAAA4B,QAAA,EAAC;MAEhE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACRvC,OAAA;QAAKO,SAAS,EAAC,yBAAyB;QAAA4B,QAAA,gBACtCnC,OAAA;UACE0C,IAAI,EAAC,MAAM;UACXC,KAAK,EAAExC,UAAW;UAClByC,QAAQ;UACRrC,SAAS,EAAC;QAAqF;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChG,CAAC,eACFvC,OAAA;UACE6C,OAAO,EAAElC,aAAc;UACvBJ,SAAS,EAAE,8DACTE,MAAM,GACF,qDAAqD,GACrD,oEAAoE,EACvE;UAAA0B,QAAA,EAEF1B,MAAM,gBACLT,OAAA;YAAKO,SAAS,EAAC,SAAS;YAACuC,IAAI,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAAZ,QAAA,eAC9DnC,OAAA;cAAMgD,QAAQ,EAAC,SAAS;cAACC,CAAC,EAAC,oHAAoH;cAACC,QAAQ,EAAC;YAAS;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClK,CAAC,gBAENvC,OAAA;YAAKO,SAAS,EAAC,SAAS;YAACuC,IAAI,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAAZ,QAAA,gBAC9DnC,OAAA;cAAMiD,CAAC,EAAC;YAAgD;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC3DvC,OAAA;cAAMiD,CAAC,EAAC;YAA4F;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpG;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNvC,OAAA;MAAKO,SAAS,EAAC,YAAY;MAAA4B,QAAA,gBACzBnC,OAAA;QACEmD,IAAI,EAAEhD,UAAW;QACjBiD,MAAM,EAAC,QAAQ;QACfC,GAAG,EAAC,qBAAqB;QACzB9C,SAAS,EAAC,gHAAgH;QAAA4B,QAAA,EAC3H;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJvC,OAAA;QACE6C,OAAO,EAAElC,aAAc;QACvBJ,SAAS,EAAC,0GAA0G;QAAA4B,QAAA,EACrH;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNvC,OAAA;MAAKO,SAAS,EAAC,2DAA2D;MAAA4B,QAAA,eACxEnC,OAAA;QAAKO,SAAS,EAAC,wBAAwB;QAAA4B,QAAA,gBACrCnC,OAAA;UAAKO,SAAS,EAAC,8CAA8C;UAACuC,IAAI,EAAC,cAAc;UAACC,OAAO,EAAC,WAAW;UAAAZ,QAAA,eACnGnC,OAAA;YAAMgD,QAAQ,EAAC,SAAS;YAACC,CAAC,EAAC,mNAAmN;YAACC,QAAQ,EAAC;UAAS;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjQ,CAAC,eACNvC,OAAA;UAAAmC,QAAA,gBACEnC,OAAA;YAAGO,SAAS,EAAC,qCAAqC;YAAA4B,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACjEvC,OAAA;YAAGO,SAAS,EAAC,8BAA8B;YAAA4B,QAAA,EAAC;UAE5C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC/B,EAAA,CA5JIP,aAA2C;AAAAqD,EAAA,GAA3CrD,aAA2C;AA8JjD,eAAeA,aAAa;AAAC,IAAAqD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}