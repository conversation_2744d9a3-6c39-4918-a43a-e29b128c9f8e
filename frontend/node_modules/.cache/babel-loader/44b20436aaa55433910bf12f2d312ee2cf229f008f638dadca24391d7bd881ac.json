{"ast": null, "code": "import React,{useState,useRef}from'react';import{useNavigate}from'react-router-dom';import{Layout,Button}from'../components';import{apiService}from'../services/api';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const UploadPage=()=>{const navigate=useNavigate();const fileInputRef=useRef(null);const[uploadedFile,setUploadedFile]=useState(null);const[isDragging,setIsDragging]=useState(false);const[isUploading,setIsUploading]=useState(false);const[uploadProgress,setUploadProgress]=useState(0);const formatFileSize=bytes=>{if(bytes===0)return'0 Bytes';const k=1024;const sizes=['Bytes','KB','MB','GB'];const i=Math.floor(Math.log(bytes)/Math.log(k));return parseFloat((bytes/Math.pow(k,i)).toFixed(2))+' '+sizes[i];};const validateFile=file=>{const allowedTypes=['application/pdf','image/jpeg','image/png','image/jpg'];const maxSize=10*1024*1024;// 10MB\nif(!allowedTypes.includes(file.type)){return'Tipo de arquivo não suportado. Use PDF, JPG ou PNG.';}if(file.size>maxSize){return'Arquivo muito grande. Máximo 10MB.';}return null;};const handleFileSelect=async file=>{const error=validateFile(file);if(error){alert(error);return;}setIsUploading(true);setUploadProgress(0);try{// Start upload progress simulation\nconst progressInterval=setInterval(()=>{setUploadProgress(prev=>{if(prev>=90){clearInterval(progressInterval);return 90;}return prev+10;});},200);// Upload file to backend\nconst uploadResponse=await apiService.uploadFile('/files/upload',file);// Clear progress interval\nclearInterval(progressInterval);if(!uploadResponse.success){throw new Error(uploadResponse.error||'Erro no upload');}// Create file info with backend response\nconst backendData=uploadResponse.data;const fileInfo={file,size:formatFileSize(file.size),pages:backendData===null||backendData===void 0?void 0:backendData.pages};// If it's an image, create preview\nif(file.type.startsWith('image/')){const reader=new FileReader();reader.onload=e=>{var _e$target;fileInfo.preview=(_e$target=e.target)===null||_e$target===void 0?void 0:_e$target.result;setUploadedFile(fileInfo);};reader.readAsDataURL(file);}else{setUploadedFile(fileInfo);}// Complete upload\nsetUploadProgress(100);setIsUploading(false);// Store backend file info for next steps\nlocalStorage.setItem('backendFileInfo',JSON.stringify(backendData));}catch(error){console.error('Upload error:',error);alert(\"Erro no upload: \".concat(error instanceof Error?error.message:'Tente novamente.'));setIsUploading(false);setUploadProgress(0);}};const handleDrop=e=>{e.preventDefault();setIsDragging(false);const files=Array.from(e.dataTransfer.files);if(files.length>0){handleFileSelect(files[0]);}};const handleDragOver=e=>{e.preventDefault();setIsDragging(true);};const handleDragLeave=e=>{e.preventDefault();setIsDragging(false);};const handleFileInputChange=e=>{const files=e.target.files;if(files&&files.length>0){handleFileSelect(files[0]);}};const handleContinue=()=>{if(uploadedFile){// Store file info in localStorage for next step\nlocalStorage.setItem('uploadedFile',JSON.stringify({name:uploadedFile.file.name,size:uploadedFile.size,type:uploadedFile.file.type}));navigate('/options');}};const handleRemoveFile=()=>{setUploadedFile(null);setUploadProgress(0);if(fileInputRef.current){fileInputRef.current.value='';}};return/*#__PURE__*/_jsx(Layout,{children:/*#__PURE__*/_jsxs(\"div\",{className:\"max-w-4xl mx-auto py-8\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-center mb-8\",children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-3xl font-bold text-gray-900 mb-4\",children:\"Upload do Seu Documento\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-lg text-gray-600\",children:\"Fa\\xE7a upload do arquivo que deseja imprimir\"})]}),!uploadedFile?/*#__PURE__*/_jsx(\"div\",{className:\"mb-8\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"border-2 border-dashed rounded-lg p-8 text-center transition-colors \".concat(isDragging?'border-blue-500 bg-blue-50':'border-gray-300 hover:border-gray-400'),onDrop:handleDrop,onDragOver:handleDragOver,onDragLeave:handleDragLeave,children:[/*#__PURE__*/_jsx(\"div\",{className:\"mb-4\",children:/*#__PURE__*/_jsx(\"svg\",{className:\"mx-auto h-12 w-12 text-gray-400\",stroke:\"currentColor\",fill:\"none\",viewBox:\"0 0 48 48\",children:/*#__PURE__*/_jsx(\"path\",{d:\"M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02\",strokeWidth:2,strokeLinecap:\"round\",strokeLinejoin:\"round\"})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"mb-4\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-lg font-medium text-gray-900 mb-2\",children:\"Arraste e solte o seu arquivo aqui\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600\",children:\"ou\"})]}),/*#__PURE__*/_jsx(Button,{onClick:()=>{var _fileInputRef$current;return(_fileInputRef$current=fileInputRef.current)===null||_fileInputRef$current===void 0?void 0:_fileInputRef$current.click();},className:\"mb-4\",children:\"Escolher Arquivo\"}),/*#__PURE__*/_jsx(\"input\",{ref:fileInputRef,type:\"file\",className:\"hidden\",accept:\".pdf,.jpg,.jpeg,.png\",onChange:handleFileInputChange}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-sm text-gray-500\",children:[/*#__PURE__*/_jsx(\"p\",{children:\"Formatos suportados: PDF, JPG, PNG\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Tamanho m\\xE1ximo: 10MB\"})]})]})}):/*#__PURE__*/_jsx(\"div\",{className:\"mb-8\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-lg shadow-lg p-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-start justify-between mb-4\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold text-gray-900\",children:\"Arquivo Carregado\"}),/*#__PURE__*/_jsx(\"button\",{onClick:handleRemoveFile,className:\"text-red-600 hover:text-red-700\",children:/*#__PURE__*/_jsx(\"svg\",{className:\"w-5 h-5\",fill:\"currentColor\",viewBox:\"0 0 20 20\",children:/*#__PURE__*/_jsx(\"path\",{fillRule:\"evenodd\",d:\"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\",clipRule:\"evenodd\"})})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-4\",children:[uploadedFile.preview?/*#__PURE__*/_jsx(\"img\",{src:uploadedFile.preview,alt:\"Preview\",className:\"w-16 h-16 object-cover rounded\"}):/*#__PURE__*/_jsx(\"div\",{className:\"w-16 h-16 bg-red-100 rounded flex items-center justify-center\",children:/*#__PURE__*/_jsx(\"svg\",{className:\"w-8 h-8 text-red-600\",fill:\"currentColor\",viewBox:\"0 0 20 20\",children:/*#__PURE__*/_jsx(\"path\",{fillRule:\"evenodd\",d:\"M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z\",clipRule:\"evenodd\"})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"font-medium text-gray-900\",children:uploadedFile.file.name}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-600\",children:uploadedFile.size}),uploadedFile.pages&&/*#__PURE__*/_jsxs(\"p\",{className:\"text-sm text-gray-600\",children:[uploadedFile.pages,\" p\\xE1ginas\"]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"text-green-600\",children:/*#__PURE__*/_jsx(\"svg\",{className:\"w-6 h-6\",fill:\"currentColor\",viewBox:\"0 0 20 20\",children:/*#__PURE__*/_jsx(\"path\",{fillRule:\"evenodd\",d:\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",clipRule:\"evenodd\"})})})]})]})}),isUploading&&/*#__PURE__*/_jsx(\"div\",{className:\"mb-8\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-lg shadow p-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between mb-2\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-medium text-gray-700\",children:\"Fazendo upload...\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"text-sm text-gray-600\",children:[uploadProgress,\"%\"]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"w-full bg-gray-200 rounded-full h-2\",children:/*#__PURE__*/_jsx(\"div\",{className:\"bg-blue-600 h-2 rounded-full transition-all duration-300\",style:{width:\"\".concat(uploadProgress,\"%\")}})})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between\",children:[/*#__PURE__*/_jsx(Button,{variant:\"outline\",onClick:()=>navigate('/'),children:\"Voltar\"}),/*#__PURE__*/_jsx(Button,{onClick:handleContinue,disabled:!uploadedFile||isUploading,children:\"Continuar\"})]})]})});};export default UploadPage;", "map": {"version": 3, "names": ["React", "useState", "useRef", "useNavigate", "Layout", "<PERSON><PERSON>", "apiService", "jsx", "_jsx", "jsxs", "_jsxs", "UploadPage", "navigate", "fileInputRef", "uploadedFile", "setUploadedFile", "isDragging", "setIsDragging", "isUploading", "setIsUploading", "uploadProgress", "setUploadProgress", "formatFileSize", "bytes", "k", "sizes", "i", "Math", "floor", "log", "parseFloat", "pow", "toFixed", "validateFile", "file", "allowedTypes", "maxSize", "includes", "type", "size", "handleFileSelect", "error", "alert", "progressInterval", "setInterval", "prev", "clearInterval", "uploadResponse", "uploadFile", "success", "Error", "backendData", "data", "fileInfo", "pages", "startsWith", "reader", "FileReader", "onload", "e", "_e$target", "preview", "target", "result", "readAsDataURL", "localStorage", "setItem", "JSON", "stringify", "console", "concat", "message", "handleDrop", "preventDefault", "files", "Array", "from", "dataTransfer", "length", "handleDragOver", "handleDragLeave", "handleFileInputChange", "handleContinue", "name", "handleRemoveFile", "current", "value", "children", "className", "onDrop", "onDragOver", "onDragLeave", "stroke", "fill", "viewBox", "d", "strokeWidth", "strokeLinecap", "strokeLinejoin", "onClick", "_fileInputRef$current", "click", "ref", "accept", "onChange", "fillRule", "clipRule", "src", "alt", "style", "width", "variant", "disabled"], "sources": ["/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/UploadPage.tsx"], "sourcesContent": ["import React, { useState, useRef } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { Layout, Button } from '../components';\nimport { apiService } from '../services/api';\nimport { FileUploadResponse } from '../types';\n\ninterface FileInfo {\n  file: File;\n  preview?: string;\n  pages?: number;\n  size: string;\n}\n\nconst UploadPage: React.FC = () => {\n  const navigate = useNavigate();\n  const fileInputRef = useRef<HTMLInputElement>(null);\n  const [uploadedFile, setUploadedFile] = useState<FileInfo | null>(null);\n  const [isDragging, setIsDragging] = useState(false);\n  const [isUploading, setIsUploading] = useState(false);\n  const [uploadProgress, setUploadProgress] = useState(0);\n\n  const formatFileSize = (bytes: number): string => {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  };\n\n  const validateFile = (file: File): string | null => {\n    const allowedTypes = ['application/pdf', 'image/jpeg', 'image/png', 'image/jpg'];\n    const maxSize = 10 * 1024 * 1024; // 10MB\n\n    if (!allowedTypes.includes(file.type)) {\n      return 'Tipo de arquivo não suportado. Use PDF, JPG ou PNG.';\n    }\n\n    if (file.size > maxSize) {\n      return 'Arquivo muito grande. Máximo 10MB.';\n    }\n\n    return null;\n  };\n\n  const handleFileSelect = async (file: File) => {\n    const error = validateFile(file);\n    if (error) {\n      alert(error);\n      return;\n    }\n\n    setIsUploading(true);\n    setUploadProgress(0);\n\n    try {\n      // Start upload progress simulation\n      const progressInterval = setInterval(() => {\n        setUploadProgress(prev => {\n          if (prev >= 90) {\n            clearInterval(progressInterval);\n            return 90;\n          }\n          return prev + 10;\n        });\n      }, 200);\n\n      // Upload file to backend\n      const uploadResponse = await apiService.uploadFile<FileUploadResponse>('/files/upload', file);\n\n      // Clear progress interval\n      clearInterval(progressInterval);\n\n      if (!uploadResponse.success) {\n        throw new Error(uploadResponse.error || 'Erro no upload');\n      }\n\n      // Create file info with backend response\n      const backendData = uploadResponse.data as FileUploadResponse;\n      const fileInfo: FileInfo = {\n        file,\n        size: formatFileSize(file.size),\n        pages: backendData?.pages,\n      };\n\n      // If it's an image, create preview\n      if (file.type.startsWith('image/')) {\n        const reader = new FileReader();\n        reader.onload = (e) => {\n          fileInfo.preview = e.target?.result as string;\n          setUploadedFile(fileInfo);\n        };\n        reader.readAsDataURL(file);\n      } else {\n        setUploadedFile(fileInfo);\n      }\n\n      // Complete upload\n      setUploadProgress(100);\n      setIsUploading(false);\n\n      // Store backend file info for next steps\n      localStorage.setItem('backendFileInfo', JSON.stringify(backendData));\n\n    } catch (error) {\n      console.error('Upload error:', error);\n      alert(`Erro no upload: ${error instanceof Error ? error.message : 'Tente novamente.'}`);\n      setIsUploading(false);\n      setUploadProgress(0);\n    }\n  };\n\n  const handleDrop = (e: React.DragEvent) => {\n    e.preventDefault();\n    setIsDragging(false);\n\n    const files = Array.from(e.dataTransfer.files);\n    if (files.length > 0) {\n      handleFileSelect(files[0]);\n    }\n  };\n\n  const handleDragOver = (e: React.DragEvent) => {\n    e.preventDefault();\n    setIsDragging(true);\n  };\n\n  const handleDragLeave = (e: React.DragEvent) => {\n    e.preventDefault();\n    setIsDragging(false);\n  };\n\n  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const files = e.target.files;\n    if (files && files.length > 0) {\n      handleFileSelect(files[0]);\n    }\n  };\n\n  const handleContinue = () => {\n    if (uploadedFile) {\n      // Store file info in localStorage for next step\n      localStorage.setItem('uploadedFile', JSON.stringify({\n        name: uploadedFile.file.name,\n        size: uploadedFile.size,\n        type: uploadedFile.file.type,\n      }));\n      navigate('/options');\n    }\n  };\n\n  const handleRemoveFile = () => {\n    setUploadedFile(null);\n    setUploadProgress(0);\n    if (fileInputRef.current) {\n      fileInputRef.current.value = '';\n    }\n  };\n\n  return (\n    <Layout>\n      <div className=\"max-w-4xl mx-auto py-8\">\n        <div className=\"text-center mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-4\">\n            Upload do Seu Documento\n          </h1>\n          <p className=\"text-lg text-gray-600\">\n            Faça upload do arquivo que deseja imprimir\n          </p>\n        </div>\n\n        {!uploadedFile ? (\n          <div className=\"mb-8\">\n            <div\n              className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${\n                isDragging\n                  ? 'border-blue-500 bg-blue-50'\n                  : 'border-gray-300 hover:border-gray-400'\n              }`}\n              onDrop={handleDrop}\n              onDragOver={handleDragOver}\n              onDragLeave={handleDragLeave}\n            >\n              <div className=\"mb-4\">\n                <svg\n                  className=\"mx-auto h-12 w-12 text-gray-400\"\n                  stroke=\"currentColor\"\n                  fill=\"none\"\n                  viewBox=\"0 0 48 48\"\n                >\n                  <path\n                    d=\"M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02\"\n                    strokeWidth={2}\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                  />\n                </svg>\n              </div>\n\n              <div className=\"mb-4\">\n                <p className=\"text-lg font-medium text-gray-900 mb-2\">\n                  Arraste e solte o seu arquivo aqui\n                </p>\n                <p className=\"text-gray-600\">ou</p>\n              </div>\n\n              <Button\n                onClick={() => fileInputRef.current?.click()}\n                className=\"mb-4\"\n              >\n                Escolher Arquivo\n              </Button>\n\n              <input\n                ref={fileInputRef}\n                type=\"file\"\n                className=\"hidden\"\n                accept=\".pdf,.jpg,.jpeg,.png\"\n                onChange={handleFileInputChange}\n              />\n\n              <div className=\"text-sm text-gray-500\">\n                <p>Formatos suportados: PDF, JPG, PNG</p>\n                <p>Tamanho máximo: 10MB</p>\n              </div>\n            </div>\n          </div>\n        ) : (\n          <div className=\"mb-8\">\n            <div className=\"bg-white rounded-lg shadow-lg p-6\">\n              <div className=\"flex items-start justify-between mb-4\">\n                <h3 className=\"text-lg font-semibold text-gray-900\">\n                  Arquivo Carregado\n                </h3>\n                <button\n                  onClick={handleRemoveFile}\n                  className=\"text-red-600 hover:text-red-700\"\n                >\n                  <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\" clipRule=\"evenodd\" />\n                  </svg>\n                </button>\n              </div>\n\n              <div className=\"flex items-center space-x-4\">\n                {uploadedFile.preview ? (\n                  <img\n                    src={uploadedFile.preview}\n                    alt=\"Preview\"\n                    className=\"w-16 h-16 object-cover rounded\"\n                  />\n                ) : (\n                  <div className=\"w-16 h-16 bg-red-100 rounded flex items-center justify-center\">\n                    <svg className=\"w-8 h-8 text-red-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                      <path fillRule=\"evenodd\" d=\"M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z\" clipRule=\"evenodd\" />\n                    </svg>\n                  </div>\n                )}\n\n                <div className=\"flex-1\">\n                  <p className=\"font-medium text-gray-900\">{uploadedFile.file.name}</p>\n                  <p className=\"text-sm text-gray-600\">{uploadedFile.size}</p>\n                  {uploadedFile.pages && (\n                    <p className=\"text-sm text-gray-600\">{uploadedFile.pages} páginas</p>\n                  )}\n                </div>\n\n                <div className=\"text-green-600\">\n                  <svg className=\"w-6 h-6\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n                  </svg>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {isUploading && (\n          <div className=\"mb-8\">\n            <div className=\"bg-white rounded-lg shadow p-4\">\n              <div className=\"flex items-center justify-between mb-2\">\n                <span className=\"text-sm font-medium text-gray-700\">\n                  Fazendo upload...\n                </span>\n                <span className=\"text-sm text-gray-600\">{uploadProgress}%</span>\n              </div>\n              <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                <div\n                  className=\"bg-blue-600 h-2 rounded-full transition-all duration-300\"\n                  style={{ width: `${uploadProgress}%` }}\n                />\n              </div>\n            </div>\n          </div>\n        )}\n\n        <div className=\"flex justify-between\">\n          <Button\n            variant=\"outline\"\n            onClick={() => navigate('/')}\n          >\n            Voltar\n          </Button>\n\n          <Button\n            onClick={handleContinue}\n            disabled={!uploadedFile || isUploading}\n          >\n            Continuar\n          </Button>\n        </div>\n      </div>\n    </Layout>\n  );\n};\n\nexport default UploadPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,MAAM,KAAQ,OAAO,CAC/C,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,OAASC,MAAM,CAAEC,MAAM,KAAQ,eAAe,CAC9C,OAASC,UAAU,KAAQ,iBAAiB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAU7C,KAAM,CAAAC,UAAoB,CAAGA,CAAA,GAAM,CACjC,KAAM,CAAAC,QAAQ,CAAGT,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAU,YAAY,CAAGX,MAAM,CAAmB,IAAI,CAAC,CACnD,KAAM,CAACY,YAAY,CAAEC,eAAe,CAAC,CAAGd,QAAQ,CAAkB,IAAI,CAAC,CACvE,KAAM,CAACe,UAAU,CAAEC,aAAa,CAAC,CAAGhB,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAACiB,WAAW,CAAEC,cAAc,CAAC,CAAGlB,QAAQ,CAAC,KAAK,CAAC,CACrD,KAAM,CAACmB,cAAc,CAAEC,iBAAiB,CAAC,CAAGpB,QAAQ,CAAC,CAAC,CAAC,CAEvD,KAAM,CAAAqB,cAAc,CAAIC,KAAa,EAAa,CAChD,GAAIA,KAAK,GAAK,CAAC,CAAE,MAAO,SAAS,CACjC,KAAM,CAAAC,CAAC,CAAG,IAAI,CACd,KAAM,CAAAC,KAAK,CAAG,CAAC,OAAO,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAC,CACzC,KAAM,CAAAC,CAAC,CAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAACN,KAAK,CAAC,CAAGI,IAAI,CAACE,GAAG,CAACL,CAAC,CAAC,CAAC,CACnD,MAAO,CAAAM,UAAU,CAAC,CAACP,KAAK,CAAGI,IAAI,CAACI,GAAG,CAACP,CAAC,CAAEE,CAAC,CAAC,EAAEM,OAAO,CAAC,CAAC,CAAC,CAAC,CAAG,GAAG,CAAGP,KAAK,CAACC,CAAC,CAAC,CACzE,CAAC,CAED,KAAM,CAAAO,YAAY,CAAIC,IAAU,EAAoB,CAClD,KAAM,CAAAC,YAAY,CAAG,CAAC,iBAAiB,CAAE,YAAY,CAAE,WAAW,CAAE,WAAW,CAAC,CAChF,KAAM,CAAAC,OAAO,CAAG,EAAE,CAAG,IAAI,CAAG,IAAI,CAAE;AAElC,GAAI,CAACD,YAAY,CAACE,QAAQ,CAACH,IAAI,CAACI,IAAI,CAAC,CAAE,CACrC,MAAO,qDAAqD,CAC9D,CAEA,GAAIJ,IAAI,CAACK,IAAI,CAAGH,OAAO,CAAE,CACvB,MAAO,oCAAoC,CAC7C,CAEA,MAAO,KAAI,CACb,CAAC,CAED,KAAM,CAAAI,gBAAgB,CAAG,KAAO,CAAAN,IAAU,EAAK,CAC7C,KAAM,CAAAO,KAAK,CAAGR,YAAY,CAACC,IAAI,CAAC,CAChC,GAAIO,KAAK,CAAE,CACTC,KAAK,CAACD,KAAK,CAAC,CACZ,OACF,CAEAtB,cAAc,CAAC,IAAI,CAAC,CACpBE,iBAAiB,CAAC,CAAC,CAAC,CAEpB,GAAI,CACF;AACA,KAAM,CAAAsB,gBAAgB,CAAGC,WAAW,CAAC,IAAM,CACzCvB,iBAAiB,CAACwB,IAAI,EAAI,CACxB,GAAIA,IAAI,EAAI,EAAE,CAAE,CACdC,aAAa,CAACH,gBAAgB,CAAC,CAC/B,MAAO,GAAE,CACX,CACA,MAAO,CAAAE,IAAI,CAAG,EAAE,CAClB,CAAC,CAAC,CACJ,CAAC,CAAE,GAAG,CAAC,CAEP;AACA,KAAM,CAAAE,cAAc,CAAG,KAAM,CAAAzC,UAAU,CAAC0C,UAAU,CAAqB,eAAe,CAAEd,IAAI,CAAC,CAE7F;AACAY,aAAa,CAACH,gBAAgB,CAAC,CAE/B,GAAI,CAACI,cAAc,CAACE,OAAO,CAAE,CAC3B,KAAM,IAAI,CAAAC,KAAK,CAACH,cAAc,CAACN,KAAK,EAAI,gBAAgB,CAAC,CAC3D,CAEA;AACA,KAAM,CAAAU,WAAW,CAAGJ,cAAc,CAACK,IAA0B,CAC7D,KAAM,CAAAC,QAAkB,CAAG,CACzBnB,IAAI,CACJK,IAAI,CAAEjB,cAAc,CAACY,IAAI,CAACK,IAAI,CAAC,CAC/Be,KAAK,CAAEH,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEG,KACtB,CAAC,CAED;AACA,GAAIpB,IAAI,CAACI,IAAI,CAACiB,UAAU,CAAC,QAAQ,CAAC,CAAE,CAClC,KAAM,CAAAC,MAAM,CAAG,GAAI,CAAAC,UAAU,CAAC,CAAC,CAC/BD,MAAM,CAACE,MAAM,CAAIC,CAAC,EAAK,KAAAC,SAAA,CACrBP,QAAQ,CAACQ,OAAO,EAAAD,SAAA,CAAGD,CAAC,CAACG,MAAM,UAAAF,SAAA,iBAARA,SAAA,CAAUG,MAAgB,CAC7ChD,eAAe,CAACsC,QAAQ,CAAC,CAC3B,CAAC,CACDG,MAAM,CAACQ,aAAa,CAAC9B,IAAI,CAAC,CAC5B,CAAC,IAAM,CACLnB,eAAe,CAACsC,QAAQ,CAAC,CAC3B,CAEA;AACAhC,iBAAiB,CAAC,GAAG,CAAC,CACtBF,cAAc,CAAC,KAAK,CAAC,CAErB;AACA8C,YAAY,CAACC,OAAO,CAAC,iBAAiB,CAAEC,IAAI,CAACC,SAAS,CAACjB,WAAW,CAAC,CAAC,CAEtE,CAAE,MAAOV,KAAK,CAAE,CACd4B,OAAO,CAAC5B,KAAK,CAAC,eAAe,CAAEA,KAAK,CAAC,CACrCC,KAAK,oBAAA4B,MAAA,CAAoB7B,KAAK,WAAY,CAAAS,KAAK,CAAGT,KAAK,CAAC8B,OAAO,CAAG,kBAAkB,CAAE,CAAC,CACvFpD,cAAc,CAAC,KAAK,CAAC,CACrBE,iBAAiB,CAAC,CAAC,CAAC,CACtB,CACF,CAAC,CAED,KAAM,CAAAmD,UAAU,CAAIb,CAAkB,EAAK,CACzCA,CAAC,CAACc,cAAc,CAAC,CAAC,CAClBxD,aAAa,CAAC,KAAK,CAAC,CAEpB,KAAM,CAAAyD,KAAK,CAAGC,KAAK,CAACC,IAAI,CAACjB,CAAC,CAACkB,YAAY,CAACH,KAAK,CAAC,CAC9C,GAAIA,KAAK,CAACI,MAAM,CAAG,CAAC,CAAE,CACpBtC,gBAAgB,CAACkC,KAAK,CAAC,CAAC,CAAC,CAAC,CAC5B,CACF,CAAC,CAED,KAAM,CAAAK,cAAc,CAAIpB,CAAkB,EAAK,CAC7CA,CAAC,CAACc,cAAc,CAAC,CAAC,CAClBxD,aAAa,CAAC,IAAI,CAAC,CACrB,CAAC,CAED,KAAM,CAAA+D,eAAe,CAAIrB,CAAkB,EAAK,CAC9CA,CAAC,CAACc,cAAc,CAAC,CAAC,CAClBxD,aAAa,CAAC,KAAK,CAAC,CACtB,CAAC,CAED,KAAM,CAAAgE,qBAAqB,CAAItB,CAAsC,EAAK,CACxE,KAAM,CAAAe,KAAK,CAAGf,CAAC,CAACG,MAAM,CAACY,KAAK,CAC5B,GAAIA,KAAK,EAAIA,KAAK,CAACI,MAAM,CAAG,CAAC,CAAE,CAC7BtC,gBAAgB,CAACkC,KAAK,CAAC,CAAC,CAAC,CAAC,CAC5B,CACF,CAAC,CAED,KAAM,CAAAQ,cAAc,CAAGA,CAAA,GAAM,CAC3B,GAAIpE,YAAY,CAAE,CAChB;AACAmD,YAAY,CAACC,OAAO,CAAC,cAAc,CAAEC,IAAI,CAACC,SAAS,CAAC,CAClDe,IAAI,CAAErE,YAAY,CAACoB,IAAI,CAACiD,IAAI,CAC5B5C,IAAI,CAAEzB,YAAY,CAACyB,IAAI,CACvBD,IAAI,CAAExB,YAAY,CAACoB,IAAI,CAACI,IAC1B,CAAC,CAAC,CAAC,CACH1B,QAAQ,CAAC,UAAU,CAAC,CACtB,CACF,CAAC,CAED,KAAM,CAAAwE,gBAAgB,CAAGA,CAAA,GAAM,CAC7BrE,eAAe,CAAC,IAAI,CAAC,CACrBM,iBAAiB,CAAC,CAAC,CAAC,CACpB,GAAIR,YAAY,CAACwE,OAAO,CAAE,CACxBxE,YAAY,CAACwE,OAAO,CAACC,KAAK,CAAG,EAAE,CACjC,CACF,CAAC,CAED,mBACE9E,IAAA,CAACJ,MAAM,EAAAmF,QAAA,cACL7E,KAAA,QAAK8E,SAAS,CAAC,wBAAwB,CAAAD,QAAA,eACrC7E,KAAA,QAAK8E,SAAS,CAAC,kBAAkB,CAAAD,QAAA,eAC/B/E,IAAA,OAAIgF,SAAS,CAAC,uCAAuC,CAAAD,QAAA,CAAC,yBAEtD,CAAI,CAAC,cACL/E,IAAA,MAAGgF,SAAS,CAAC,uBAAuB,CAAAD,QAAA,CAAC,+CAErC,CAAG,CAAC,EACD,CAAC,CAEL,CAACzE,YAAY,cACZN,IAAA,QAAKgF,SAAS,CAAC,MAAM,CAAAD,QAAA,cACnB7E,KAAA,QACE8E,SAAS,wEAAAlB,MAAA,CACPtD,UAAU,CACN,4BAA4B,CAC5B,uCAAuC,CAC1C,CACHyE,MAAM,CAAEjB,UAAW,CACnBkB,UAAU,CAAEX,cAAe,CAC3BY,WAAW,CAAEX,eAAgB,CAAAO,QAAA,eAE7B/E,IAAA,QAAKgF,SAAS,CAAC,MAAM,CAAAD,QAAA,cACnB/E,IAAA,QACEgF,SAAS,CAAC,iCAAiC,CAC3CI,MAAM,CAAC,cAAc,CACrBC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CAAAP,QAAA,cAEnB/E,IAAA,SACEuF,CAAC,CAAC,wLAAwL,CAC1LC,WAAW,CAAE,CAAE,CACfC,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACvB,CAAC,CACC,CAAC,CACH,CAAC,cAENxF,KAAA,QAAK8E,SAAS,CAAC,MAAM,CAAAD,QAAA,eACnB/E,IAAA,MAAGgF,SAAS,CAAC,wCAAwC,CAAAD,QAAA,CAAC,oCAEtD,CAAG,CAAC,cACJ/E,IAAA,MAAGgF,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,IAAE,CAAG,CAAC,EAChC,CAAC,cAEN/E,IAAA,CAACH,MAAM,EACL8F,OAAO,CAAEA,CAAA,QAAAC,qBAAA,QAAAA,qBAAA,CAAMvF,YAAY,CAACwE,OAAO,UAAAe,qBAAA,iBAApBA,qBAAA,CAAsBC,KAAK,CAAC,CAAC,EAAC,CAC7Cb,SAAS,CAAC,MAAM,CAAAD,QAAA,CACjB,kBAED,CAAQ,CAAC,cAET/E,IAAA,UACE8F,GAAG,CAAEzF,YAAa,CAClByB,IAAI,CAAC,MAAM,CACXkD,SAAS,CAAC,QAAQ,CAClBe,MAAM,CAAC,sBAAsB,CAC7BC,QAAQ,CAAEvB,qBAAsB,CACjC,CAAC,cAEFvE,KAAA,QAAK8E,SAAS,CAAC,uBAAuB,CAAAD,QAAA,eACpC/E,IAAA,MAAA+E,QAAA,CAAG,oCAAkC,CAAG,CAAC,cACzC/E,IAAA,MAAA+E,QAAA,CAAG,yBAAoB,CAAG,CAAC,EACxB,CAAC,EACH,CAAC,CACH,CAAC,cAEN/E,IAAA,QAAKgF,SAAS,CAAC,MAAM,CAAAD,QAAA,cACnB7E,KAAA,QAAK8E,SAAS,CAAC,mCAAmC,CAAAD,QAAA,eAChD7E,KAAA,QAAK8E,SAAS,CAAC,uCAAuC,CAAAD,QAAA,eACpD/E,IAAA,OAAIgF,SAAS,CAAC,qCAAqC,CAAAD,QAAA,CAAC,mBAEpD,CAAI,CAAC,cACL/E,IAAA,WACE2F,OAAO,CAAEf,gBAAiB,CAC1BI,SAAS,CAAC,iCAAiC,CAAAD,QAAA,cAE3C/E,IAAA,QAAKgF,SAAS,CAAC,SAAS,CAACK,IAAI,CAAC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAAP,QAAA,cAC9D/E,IAAA,SAAMiG,QAAQ,CAAC,SAAS,CAACV,CAAC,CAAC,oMAAoM,CAACW,QAAQ,CAAC,SAAS,CAAE,CAAC,CAClP,CAAC,CACA,CAAC,EACN,CAAC,cAENhG,KAAA,QAAK8E,SAAS,CAAC,6BAA6B,CAAAD,QAAA,EACzCzE,YAAY,CAAC+C,OAAO,cACnBrD,IAAA,QACEmG,GAAG,CAAE7F,YAAY,CAAC+C,OAAQ,CAC1B+C,GAAG,CAAC,SAAS,CACbpB,SAAS,CAAC,gCAAgC,CAC3C,CAAC,cAEFhF,IAAA,QAAKgF,SAAS,CAAC,+DAA+D,CAAAD,QAAA,cAC5E/E,IAAA,QAAKgF,SAAS,CAAC,sBAAsB,CAACK,IAAI,CAAC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAAP,QAAA,cAC3E/E,IAAA,SAAMiG,QAAQ,CAAC,SAAS,CAACV,CAAC,CAAC,oLAAoL,CAACW,QAAQ,CAAC,SAAS,CAAE,CAAC,CAClO,CAAC,CACH,CACN,cAEDhG,KAAA,QAAK8E,SAAS,CAAC,QAAQ,CAAAD,QAAA,eACrB/E,IAAA,MAAGgF,SAAS,CAAC,2BAA2B,CAAAD,QAAA,CAAEzE,YAAY,CAACoB,IAAI,CAACiD,IAAI,CAAI,CAAC,cACrE3E,IAAA,MAAGgF,SAAS,CAAC,uBAAuB,CAAAD,QAAA,CAAEzE,YAAY,CAACyB,IAAI,CAAI,CAAC,CAC3DzB,YAAY,CAACwC,KAAK,eACjB5C,KAAA,MAAG8E,SAAS,CAAC,uBAAuB,CAAAD,QAAA,EAAEzE,YAAY,CAACwC,KAAK,CAAC,aAAQ,EAAG,CACrE,EACE,CAAC,cAEN9C,IAAA,QAAKgF,SAAS,CAAC,gBAAgB,CAAAD,QAAA,cAC7B/E,IAAA,QAAKgF,SAAS,CAAC,SAAS,CAACK,IAAI,CAAC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAAP,QAAA,cAC9D/E,IAAA,SAAMiG,QAAQ,CAAC,SAAS,CAACV,CAAC,CAAC,uIAAuI,CAACW,QAAQ,CAAC,SAAS,CAAE,CAAC,CACrL,CAAC,CACH,CAAC,EACH,CAAC,EACH,CAAC,CACH,CACN,CAEAxF,WAAW,eACVV,IAAA,QAAKgF,SAAS,CAAC,MAAM,CAAAD,QAAA,cACnB7E,KAAA,QAAK8E,SAAS,CAAC,gCAAgC,CAAAD,QAAA,eAC7C7E,KAAA,QAAK8E,SAAS,CAAC,wCAAwC,CAAAD,QAAA,eACrD/E,IAAA,SAAMgF,SAAS,CAAC,mCAAmC,CAAAD,QAAA,CAAC,mBAEpD,CAAM,CAAC,cACP7E,KAAA,SAAM8E,SAAS,CAAC,uBAAuB,CAAAD,QAAA,EAAEnE,cAAc,CAAC,GAAC,EAAM,CAAC,EAC7D,CAAC,cACNZ,IAAA,QAAKgF,SAAS,CAAC,qCAAqC,CAAAD,QAAA,cAClD/E,IAAA,QACEgF,SAAS,CAAC,0DAA0D,CACpEqB,KAAK,CAAE,CAAEC,KAAK,IAAAxC,MAAA,CAAKlD,cAAc,KAAI,CAAE,CACxC,CAAC,CACC,CAAC,EACH,CAAC,CACH,CACN,cAEDV,KAAA,QAAK8E,SAAS,CAAC,sBAAsB,CAAAD,QAAA,eACnC/E,IAAA,CAACH,MAAM,EACL0G,OAAO,CAAC,SAAS,CACjBZ,OAAO,CAAEA,CAAA,GAAMvF,QAAQ,CAAC,GAAG,CAAE,CAAA2E,QAAA,CAC9B,QAED,CAAQ,CAAC,cAET/E,IAAA,CAACH,MAAM,EACL8F,OAAO,CAAEjB,cAAe,CACxB8B,QAAQ,CAAE,CAAClG,YAAY,EAAII,WAAY,CAAAqE,QAAA,CACxC,WAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CACA,CAAC,CAEb,CAAC,CAED,cAAe,CAAA5E,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}