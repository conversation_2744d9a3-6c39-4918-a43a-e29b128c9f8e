{"ast": null, "code": "import React,{useState,useEffect}from'react';import{useNavigate}from'react-router-dom';import{Layout,Button}from'../components';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const OrderSummary=()=>{const navigate=useNavigate();const[fileInfo,setFileInfo]=useState(null);const[printOptions,setPrintOptions]=useState(null);const[priceBreakdown,setPriceBreakdown]=useState(null);useEffect(()=>{// Get data from localStorage\nconst file=localStorage.getItem('uploadedFile');const options=localStorage.getItem('printOptions');const price=localStorage.getItem('priceBreakdown');if(!file||!options||!price){// Redirect to upload if missing data\nnavigate('/upload');return;}setFileInfo(JSON.parse(file));setPrintOptions(JSON.parse(options));setPriceBreakdown(JSON.parse(price));},[navigate]);const formatPrice=price=>{return\"\".concat(price.toFixed(2),\" AOA\");};const getFormatDisplayName=format=>{const formats={'A4':'A4 (210 × 297 mm)','A3':'A3 (297 × 420 mm)','A5':'A5 (148 × 210 mm)','Letter':'Letter (216 × 279 mm)'};return formats[format]||format;};const getPaperDisplayName=paperType=>{const papers={'standard':'Papel Standard (75g/m²)','premium':'Papel Premium (90g/m²)','photo':'Papel Fotográfico (200g/m²)','cardstock':'Cartolina (250g/m²)'};return papers[paperType]||paperType;};const getFinishDisplayName=finish=>{const finishes={'none':'Sem Acabamento','glossy':'Brilhante','matte':'Fosco','laminated':'Plastificado'};return finishes[finish]||finish;};const getComplexityDisplayName=complexity=>{const complexities={'low':'Baixa (Texto simples)','medium':'Média (Texto + Imagens)','high':'Alta (Design complexo)'};return complexities[complexity]||complexity;};const handleContinue=()=>{navigate('/checkout');};const handleEditOptions=()=>{navigate('/options');};if(!fileInfo||!printOptions||!priceBreakdown){return/*#__PURE__*/_jsx(Layout,{children:/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center justify-center min-h-screen\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600\",children:\"Carregando resumo...\"})]})})});}return/*#__PURE__*/_jsx(Layout,{children:/*#__PURE__*/_jsxs(\"div\",{className:\"max-w-4xl mx-auto py-8\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-center mb-8\",children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-3xl font-bold text-gray-900 mb-4\",children:\"Resumo do Pedido\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-lg text-gray-600\",children:\"Revise os detalhes antes de finalizar\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 lg:grid-cols-2 gap-8\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-lg shadow-lg p-6\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-xl font-semibold text-gray-900 mb-6\",children:\"Arquivo\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-4 mb-6\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center\",children:fileInfo.type.startsWith('image/')?/*#__PURE__*/_jsx(\"svg\",{className:\"w-8 h-8 text-blue-600\",fill:\"currentColor\",viewBox:\"0 0 20 20\",children:/*#__PURE__*/_jsx(\"path\",{fillRule:\"evenodd\",d:\"M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z\",clipRule:\"evenodd\"})}):/*#__PURE__*/_jsx(\"svg\",{className:\"w-8 h-8 text-blue-600\",fill:\"currentColor\",viewBox:\"0 0 20 20\",children:/*#__PURE__*/_jsx(\"path\",{fillRule:\"evenodd\",d:\"M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z\",clipRule:\"evenodd\"})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"font-medium text-gray-900\",children:fileInfo.name}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-600\",children:fileInfo.size})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-3\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-600\",children:\"Formato:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"font-medium\",children:getFormatDisplayName(printOptions.format)})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-600\",children:\"Papel:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"font-medium\",children:getPaperDisplayName(printOptions.paperType)})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-600\",children:\"Acabamento:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"font-medium\",children:getFinishDisplayName(printOptions.finish)})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-600\",children:\"C\\xF3pias:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"font-medium\",children:printOptions.copies})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-600\",children:\"Cores:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"font-medium\",children:printOptions.hasColor?'Colorido':'Preto e Branco'})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-600\",children:\"Complexidade:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"font-medium\",children:getComplexityDisplayName(printOptions.complexity)})]}),printOptions.notes&&/*#__PURE__*/_jsxs(\"div\",{className:\"pt-3 border-t\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-600 block mb-1\",children:\"Observa\\xE7\\xF5es:\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-800 bg-gray-50 p-3 rounded\",children:printOptions.notes})]})]}),/*#__PURE__*/_jsx(Button,{variant:\"outline\",onClick:handleEditOptions,className:\"w-full mt-6\",children:\"Editar Op\\xE7\\xF5es\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-lg shadow-lg p-6\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-xl font-semibold text-gray-900 mb-6\",children:\"Detalhes do Pre\\xE7o\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between\",children:[/*#__PURE__*/_jsxs(\"span\",{className:\"text-gray-600\",children:[\"Pre\\xE7o base (\",printOptions.format,\"):\"]}),/*#__PURE__*/_jsx(\"span\",{children:formatPrice(priceBreakdown.basePrice)})]}),priceBreakdown.paperCost>0&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-600\",children:\"Papel premium:\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"text-green-600\",children:[\"+\",formatPrice(priceBreakdown.paperCost)]})]}),priceBreakdown.finishCost>0&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-600\",children:\"Acabamento:\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"text-green-600\",children:[\"+\",formatPrice(priceBreakdown.finishCost)]})]}),priceBreakdown.complexityCost>0&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-600\",children:\"Complexidade:\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"text-green-600\",children:[\"+\",formatPrice(priceBreakdown.complexityCost)]})]}),priceBreakdown.colorCost>0&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-600\",children:\"Impress\\xE3o a cores:\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"text-green-600\",children:[\"+\",formatPrice(priceBreakdown.colorCost)]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-600\",children:\"Quantidade:\"}),/*#__PURE__*/_jsxs(\"span\",{children:[printOptions.copies,\" c\\xF3pia(s)\"]})]}),/*#__PURE__*/_jsx(\"hr\",{className:\"my-4\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between text-xl font-bold\",children:[/*#__PURE__*/_jsx(\"span\",{children:\"Total:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-blue-600\",children:formatPrice(priceBreakdown.total)})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mt-6 p-4 bg-green-50 rounded-lg\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"font-medium text-green-900 mb-2\",children:\"\\u2705 Inclu\\xEDdo no Pre\\xE7o\"}),/*#__PURE__*/_jsxs(\"ul\",{className:\"text-sm text-green-800 space-y-1\",children:[/*#__PURE__*/_jsx(\"li\",{children:\"\\u2022 Entrega gr\\xE1tis em Luanda\"}),/*#__PURE__*/_jsx(\"li\",{children:\"\\u2022 Embalagem protectora\"}),/*#__PURE__*/_jsx(\"li\",{children:\"\\u2022 Garantia de qualidade\"}),/*#__PURE__*/_jsx(\"li\",{children:\"\\u2022 Suporte t\\xE9cnico\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mt-4 p-4 bg-blue-50 rounded-lg\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"font-medium text-blue-900 mb-2\",children:\"\\uD83D\\uDCC5 Prazo de Entrega\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-blue-800\",children:printOptions.complexity==='high'?'4-5 dias úteis':printOptions.complexity==='medium'?'2-3 dias úteis':'1-2 dias úteis'})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between mt-8\",children:[/*#__PURE__*/_jsx(Button,{variant:\"outline\",onClick:()=>navigate('/options'),children:\"Voltar \\xE0s Op\\xE7\\xF5es\"}),/*#__PURE__*/_jsx(Button,{onClick:handleContinue,size:\"lg\",children:\"Finalizar Pedido\"})]})]})});};export default OrderSummary;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "Layout", "<PERSON><PERSON>", "jsx", "_jsx", "jsxs", "_jsxs", "OrderSummary", "navigate", "fileInfo", "setFileInfo", "printOptions", "setPrintOptions", "priceBreakdown", "setPriceBreakdown", "file", "localStorage", "getItem", "options", "price", "JSON", "parse", "formatPrice", "concat", "toFixed", "getFormatDisplayName", "format", "formats", "getPaperDisplayName", "paperType", "papers", "getFinishDisplayName", "finish", "finishes", "getComplexityDisplayName", "complexity", "complexities", "handleContinue", "handleEditOptions", "children", "className", "type", "startsWith", "fill", "viewBox", "fillRule", "d", "clipRule", "name", "size", "copies", "hasColor", "notes", "variant", "onClick", "basePrice", "paperCost", "finishCost", "complexityCost", "colorCost", "total"], "sources": ["/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/OrderSummary.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { Layout, Button } from '../components';\n\ninterface FileInfo {\n  name: string;\n  size: string;\n  type: string;\n}\n\ninterface PrintOptions {\n  format: string;\n  paperType: string;\n  finish: string;\n  copies: number;\n  hasColor: boolean;\n  complexity: 'low' | 'medium' | 'high';\n  notes: string;\n}\n\ninterface PriceBreakdown {\n  basePrice: number;\n  paperCost: number;\n  finishCost: number;\n  complexityCost: number;\n  colorCost: number;\n  total: number;\n}\n\nconst OrderSummary: React.FC = () => {\n  const navigate = useNavigate();\n  const [fileInfo, setFileInfo] = useState<FileInfo | null>(null);\n  const [printOptions, setPrintOptions] = useState<PrintOptions | null>(null);\n  const [priceBreakdown, setPriceBreakdown] = useState<PriceBreakdown | null>(null);\n\n  useEffect(() => {\n    // Get data from localStorage\n    const file = localStorage.getItem('uploadedFile');\n    const options = localStorage.getItem('printOptions');\n    const price = localStorage.getItem('priceBreakdown');\n\n    if (!file || !options || !price) {\n      // Redirect to upload if missing data\n      navigate('/upload');\n      return;\n    }\n\n    setFileInfo(JSON.parse(file));\n    setPrintOptions(JSON.parse(options));\n    setPriceBreakdown(JSON.parse(price));\n  }, [navigate]);\n\n  const formatPrice = (price: number) => {\n    return `${price.toFixed(2)} AOA`;\n  };\n\n  const getFormatDisplayName = (format: string) => {\n    const formats: { [key: string]: string } = {\n      'A4': 'A4 (210 × 297 mm)',\n      'A3': 'A3 (297 × 420 mm)',\n      'A5': 'A5 (148 × 210 mm)',\n      'Letter': 'Letter (216 × 279 mm)',\n    };\n    return formats[format] || format;\n  };\n\n  const getPaperDisplayName = (paperType: string) => {\n    const papers: { [key: string]: string } = {\n      'standard': 'Papel Standard (75g/m²)',\n      'premium': 'Papel Premium (90g/m²)',\n      'photo': 'Papel Fotográfico (200g/m²)',\n      'cardstock': 'Cartolina (250g/m²)',\n    };\n    return papers[paperType] || paperType;\n  };\n\n  const getFinishDisplayName = (finish: string) => {\n    const finishes: { [key: string]: string } = {\n      'none': 'Sem Acabamento',\n      'glossy': 'Brilhante',\n      'matte': 'Fosco',\n      'laminated': 'Plastificado',\n    };\n    return finishes[finish] || finish;\n  };\n\n  const getComplexityDisplayName = (complexity: string) => {\n    const complexities: { [key: string]: string } = {\n      'low': 'Baixa (Texto simples)',\n      'medium': 'Média (Texto + Imagens)',\n      'high': 'Alta (Design complexo)',\n    };\n    return complexities[complexity] || complexity;\n  };\n\n  const handleContinue = () => {\n    navigate('/checkout');\n  };\n\n  const handleEditOptions = () => {\n    navigate('/options');\n  };\n\n  if (!fileInfo || !printOptions || !priceBreakdown) {\n    return (\n      <Layout>\n        <div className=\"flex items-center justify-center min-h-screen\">\n          <div className=\"text-center\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n            <p className=\"text-gray-600\">Carregando resumo...</p>\n          </div>\n        </div>\n      </Layout>\n    );\n  }\n\n  return (\n    <Layout>\n      <div className=\"max-w-4xl mx-auto py-8\">\n        <div className=\"text-center mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-4\">\n            Resumo do Pedido\n          </h1>\n          <p className=\"text-lg text-gray-600\">\n            Revise os detalhes antes de finalizar\n          </p>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n          {/* File Information */}\n          <div className=\"bg-white rounded-lg shadow-lg p-6\">\n            <h2 className=\"text-xl font-semibold text-gray-900 mb-6\">\n              Arquivo\n            </h2>\n\n            <div className=\"flex items-center space-x-4 mb-6\">\n              <div className=\"w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center\">\n                {fileInfo.type.startsWith('image/') ? (\n                  <svg className=\"w-8 h-8 text-blue-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z\" clipRule=\"evenodd\" />\n                  </svg>\n                ) : (\n                  <svg className=\"w-8 h-8 text-blue-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z\" clipRule=\"evenodd\" />\n                  </svg>\n                )}\n              </div>\n\n              <div className=\"flex-1\">\n                <p className=\"font-medium text-gray-900\">{fileInfo.name}</p>\n                <p className=\"text-sm text-gray-600\">{fileInfo.size}</p>\n              </div>\n            </div>\n\n            <div className=\"space-y-3\">\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600\">Formato:</span>\n                <span className=\"font-medium\">{getFormatDisplayName(printOptions.format)}</span>\n              </div>\n\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600\">Papel:</span>\n                <span className=\"font-medium\">{getPaperDisplayName(printOptions.paperType)}</span>\n              </div>\n\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600\">Acabamento:</span>\n                <span className=\"font-medium\">{getFinishDisplayName(printOptions.finish)}</span>\n              </div>\n\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600\">Cópias:</span>\n                <span className=\"font-medium\">{printOptions.copies}</span>\n              </div>\n\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600\">Cores:</span>\n                <span className=\"font-medium\">\n                  {printOptions.hasColor ? 'Colorido' : 'Preto e Branco'}\n                </span>\n              </div>\n\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600\">Complexidade:</span>\n                <span className=\"font-medium\">{getComplexityDisplayName(printOptions.complexity)}</span>\n              </div>\n\n              {printOptions.notes && (\n                <div className=\"pt-3 border-t\">\n                  <span className=\"text-gray-600 block mb-1\">Observações:</span>\n                  <p className=\"text-sm text-gray-800 bg-gray-50 p-3 rounded\">\n                    {printOptions.notes}\n                  </p>\n                </div>\n              )}\n            </div>\n\n            <Button\n              variant=\"outline\"\n              onClick={handleEditOptions}\n              className=\"w-full mt-6\"\n            >\n              Editar Opções\n            </Button>\n          </div>\n\n          {/* Price Breakdown */}\n          <div className=\"bg-white rounded-lg shadow-lg p-6\">\n            <h2 className=\"text-xl font-semibold text-gray-900 mb-6\">\n              Detalhes do Preço\n            </h2>\n\n            <div className=\"space-y-4\">\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600\">Preço base ({printOptions.format}):</span>\n                <span>{formatPrice(priceBreakdown.basePrice)}</span>\n              </div>\n\n              {priceBreakdown.paperCost > 0 && (\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-600\">Papel premium:</span>\n                  <span className=\"text-green-600\">+{formatPrice(priceBreakdown.paperCost)}</span>\n                </div>\n              )}\n\n              {priceBreakdown.finishCost > 0 && (\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-600\">Acabamento:</span>\n                  <span className=\"text-green-600\">+{formatPrice(priceBreakdown.finishCost)}</span>\n                </div>\n              )}\n\n              {priceBreakdown.complexityCost > 0 && (\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-600\">Complexidade:</span>\n                  <span className=\"text-green-600\">+{formatPrice(priceBreakdown.complexityCost)}</span>\n                </div>\n              )}\n\n              {priceBreakdown.colorCost > 0 && (\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-600\">Impressão a cores:</span>\n                  <span className=\"text-green-600\">+{formatPrice(priceBreakdown.colorCost)}</span>\n                </div>\n              )}\n\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600\">Quantidade:</span>\n                <span>{printOptions.copies} cópia(s)</span>\n              </div>\n\n              <hr className=\"my-4\" />\n\n              <div className=\"flex justify-between text-xl font-bold\">\n                <span>Total:</span>\n                <span className=\"text-blue-600\">{formatPrice(priceBreakdown.total)}</span>\n              </div>\n            </div>\n\n            <div className=\"mt-6 p-4 bg-green-50 rounded-lg\">\n              <h3 className=\"font-medium text-green-900 mb-2\">✅ Incluído no Preço</h3>\n              <ul className=\"text-sm text-green-800 space-y-1\">\n                <li>• Entrega grátis em Luanda</li>\n                <li>• Embalagem protectora</li>\n                <li>• Garantia de qualidade</li>\n                <li>• Suporte técnico</li>\n              </ul>\n            </div>\n\n            <div className=\"mt-4 p-4 bg-blue-50 rounded-lg\">\n              <h3 className=\"font-medium text-blue-900 mb-2\">📅 Prazo de Entrega</h3>\n              <p className=\"text-sm text-blue-800\">\n                {printOptions.complexity === 'high' ? '4-5 dias úteis' :\n                 printOptions.complexity === 'medium' ? '2-3 dias úteis' :\n                 '1-2 dias úteis'}\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"flex justify-between mt-8\">\n          <Button\n            variant=\"outline\"\n            onClick={() => navigate('/options')}\n          >\n            Voltar às Opções\n          </Button>\n\n          <Button\n            onClick={handleContinue}\n            size=\"lg\"\n          >\n            Finalizar Pedido\n          </Button>\n        </div>\n      </div>\n    </Layout>\n  );\n};\n\nexport default OrderSummary;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,OAASC,MAAM,CAAEC,MAAM,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBA2B/C,KAAM,CAAAC,YAAsB,CAAGA,CAAA,GAAM,CACnC,KAAM,CAAAC,QAAQ,CAAGR,WAAW,CAAC,CAAC,CAC9B,KAAM,CAACS,QAAQ,CAAEC,WAAW,CAAC,CAAGZ,QAAQ,CAAkB,IAAI,CAAC,CAC/D,KAAM,CAACa,YAAY,CAAEC,eAAe,CAAC,CAAGd,QAAQ,CAAsB,IAAI,CAAC,CAC3E,KAAM,CAACe,cAAc,CAAEC,iBAAiB,CAAC,CAAGhB,QAAQ,CAAwB,IAAI,CAAC,CAEjFC,SAAS,CAAC,IAAM,CACd;AACA,KAAM,CAAAgB,IAAI,CAAGC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC,CACjD,KAAM,CAAAC,OAAO,CAAGF,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC,CACpD,KAAM,CAAAE,KAAK,CAAGH,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC,CAEpD,GAAI,CAACF,IAAI,EAAI,CAACG,OAAO,EAAI,CAACC,KAAK,CAAE,CAC/B;AACAX,QAAQ,CAAC,SAAS,CAAC,CACnB,OACF,CAEAE,WAAW,CAACU,IAAI,CAACC,KAAK,CAACN,IAAI,CAAC,CAAC,CAC7BH,eAAe,CAACQ,IAAI,CAACC,KAAK,CAACH,OAAO,CAAC,CAAC,CACpCJ,iBAAiB,CAACM,IAAI,CAACC,KAAK,CAACF,KAAK,CAAC,CAAC,CACtC,CAAC,CAAE,CAACX,QAAQ,CAAC,CAAC,CAEd,KAAM,CAAAc,WAAW,CAAIH,KAAa,EAAK,CACrC,SAAAI,MAAA,CAAUJ,KAAK,CAACK,OAAO,CAAC,CAAC,CAAC,SAC5B,CAAC,CAED,KAAM,CAAAC,oBAAoB,CAAIC,MAAc,EAAK,CAC/C,KAAM,CAAAC,OAAkC,CAAG,CACzC,IAAI,CAAE,mBAAmB,CACzB,IAAI,CAAE,mBAAmB,CACzB,IAAI,CAAE,mBAAmB,CACzB,QAAQ,CAAE,uBACZ,CAAC,CACD,MAAO,CAAAA,OAAO,CAACD,MAAM,CAAC,EAAIA,MAAM,CAClC,CAAC,CAED,KAAM,CAAAE,mBAAmB,CAAIC,SAAiB,EAAK,CACjD,KAAM,CAAAC,MAAiC,CAAG,CACxC,UAAU,CAAE,yBAAyB,CACrC,SAAS,CAAE,wBAAwB,CACnC,OAAO,CAAE,6BAA6B,CACtC,WAAW,CAAE,qBACf,CAAC,CACD,MAAO,CAAAA,MAAM,CAACD,SAAS,CAAC,EAAIA,SAAS,CACvC,CAAC,CAED,KAAM,CAAAE,oBAAoB,CAAIC,MAAc,EAAK,CAC/C,KAAM,CAAAC,QAAmC,CAAG,CAC1C,MAAM,CAAE,gBAAgB,CACxB,QAAQ,CAAE,WAAW,CACrB,OAAO,CAAE,OAAO,CAChB,WAAW,CAAE,cACf,CAAC,CACD,MAAO,CAAAA,QAAQ,CAACD,MAAM,CAAC,EAAIA,MAAM,CACnC,CAAC,CAED,KAAM,CAAAE,wBAAwB,CAAIC,UAAkB,EAAK,CACvD,KAAM,CAAAC,YAAuC,CAAG,CAC9C,KAAK,CAAE,uBAAuB,CAC9B,QAAQ,CAAE,yBAAyB,CACnC,MAAM,CAAE,wBACV,CAAC,CACD,MAAO,CAAAA,YAAY,CAACD,UAAU,CAAC,EAAIA,UAAU,CAC/C,CAAC,CAED,KAAM,CAAAE,cAAc,CAAGA,CAAA,GAAM,CAC3B7B,QAAQ,CAAC,WAAW,CAAC,CACvB,CAAC,CAED,KAAM,CAAA8B,iBAAiB,CAAGA,CAAA,GAAM,CAC9B9B,QAAQ,CAAC,UAAU,CAAC,CACtB,CAAC,CAED,GAAI,CAACC,QAAQ,EAAI,CAACE,YAAY,EAAI,CAACE,cAAc,CAAE,CACjD,mBACET,IAAA,CAACH,MAAM,EAAAsC,QAAA,cACLnC,IAAA,QAAKoC,SAAS,CAAC,+CAA+C,CAAAD,QAAA,cAC5DjC,KAAA,QAAKkC,SAAS,CAAC,aAAa,CAAAD,QAAA,eAC1BnC,IAAA,QAAKoC,SAAS,CAAC,6EAA6E,CAAM,CAAC,cACnGpC,IAAA,MAAGoC,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,sBAAoB,CAAG,CAAC,EAClD,CAAC,CACH,CAAC,CACA,CAAC,CAEb,CAEA,mBACEnC,IAAA,CAACH,MAAM,EAAAsC,QAAA,cACLjC,KAAA,QAAKkC,SAAS,CAAC,wBAAwB,CAAAD,QAAA,eACrCjC,KAAA,QAAKkC,SAAS,CAAC,kBAAkB,CAAAD,QAAA,eAC/BnC,IAAA,OAAIoC,SAAS,CAAC,uCAAuC,CAAAD,QAAA,CAAC,kBAEtD,CAAI,CAAC,cACLnC,IAAA,MAAGoC,SAAS,CAAC,uBAAuB,CAAAD,QAAA,CAAC,uCAErC,CAAG,CAAC,EACD,CAAC,cAENjC,KAAA,QAAKkC,SAAS,CAAC,uCAAuC,CAAAD,QAAA,eAEpDjC,KAAA,QAAKkC,SAAS,CAAC,mCAAmC,CAAAD,QAAA,eAChDnC,IAAA,OAAIoC,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,SAEzD,CAAI,CAAC,cAELjC,KAAA,QAAKkC,SAAS,CAAC,kCAAkC,CAAAD,QAAA,eAC/CnC,IAAA,QAAKoC,SAAS,CAAC,mEAAmE,CAAAD,QAAA,CAC/E9B,QAAQ,CAACgC,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,cACjCtC,IAAA,QAAKoC,SAAS,CAAC,uBAAuB,CAACG,IAAI,CAAC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAAL,QAAA,cAC5EnC,IAAA,SAAMyC,QAAQ,CAAC,SAAS,CAACC,CAAC,CAAC,4FAA4F,CAACC,QAAQ,CAAC,SAAS,CAAE,CAAC,CAC1I,CAAC,cAEN3C,IAAA,QAAKoC,SAAS,CAAC,uBAAuB,CAACG,IAAI,CAAC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAAL,QAAA,cAC5EnC,IAAA,SAAMyC,QAAQ,CAAC,SAAS,CAACC,CAAC,CAAC,oLAAoL,CAACC,QAAQ,CAAC,SAAS,CAAE,CAAC,CAClO,CACN,CACE,CAAC,cAENzC,KAAA,QAAKkC,SAAS,CAAC,QAAQ,CAAAD,QAAA,eACrBnC,IAAA,MAAGoC,SAAS,CAAC,2BAA2B,CAAAD,QAAA,CAAE9B,QAAQ,CAACuC,IAAI,CAAI,CAAC,cAC5D5C,IAAA,MAAGoC,SAAS,CAAC,uBAAuB,CAAAD,QAAA,CAAE9B,QAAQ,CAACwC,IAAI,CAAI,CAAC,EACrD,CAAC,EACH,CAAC,cAEN3C,KAAA,QAAKkC,SAAS,CAAC,WAAW,CAAAD,QAAA,eACxBjC,KAAA,QAAKkC,SAAS,CAAC,sBAAsB,CAAAD,QAAA,eACnCnC,IAAA,SAAMoC,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,UAAQ,CAAM,CAAC,cAC/CnC,IAAA,SAAMoC,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAEd,oBAAoB,CAACd,YAAY,CAACe,MAAM,CAAC,CAAO,CAAC,EAC7E,CAAC,cAENpB,KAAA,QAAKkC,SAAS,CAAC,sBAAsB,CAAAD,QAAA,eACnCnC,IAAA,SAAMoC,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,QAAM,CAAM,CAAC,cAC7CnC,IAAA,SAAMoC,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAEX,mBAAmB,CAACjB,YAAY,CAACkB,SAAS,CAAC,CAAO,CAAC,EAC/E,CAAC,cAENvB,KAAA,QAAKkC,SAAS,CAAC,sBAAsB,CAAAD,QAAA,eACnCnC,IAAA,SAAMoC,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,aAAW,CAAM,CAAC,cAClDnC,IAAA,SAAMoC,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAER,oBAAoB,CAACpB,YAAY,CAACqB,MAAM,CAAC,CAAO,CAAC,EAC7E,CAAC,cAEN1B,KAAA,QAAKkC,SAAS,CAAC,sBAAsB,CAAAD,QAAA,eACnCnC,IAAA,SAAMoC,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,YAAO,CAAM,CAAC,cAC9CnC,IAAA,SAAMoC,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAE5B,YAAY,CAACuC,MAAM,CAAO,CAAC,EACvD,CAAC,cAEN5C,KAAA,QAAKkC,SAAS,CAAC,sBAAsB,CAAAD,QAAA,eACnCnC,IAAA,SAAMoC,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,QAAM,CAAM,CAAC,cAC7CnC,IAAA,SAAMoC,SAAS,CAAC,aAAa,CAAAD,QAAA,CAC1B5B,YAAY,CAACwC,QAAQ,CAAG,UAAU,CAAG,gBAAgB,CAClD,CAAC,EACJ,CAAC,cAEN7C,KAAA,QAAKkC,SAAS,CAAC,sBAAsB,CAAAD,QAAA,eACnCnC,IAAA,SAAMoC,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,eAAa,CAAM,CAAC,cACpDnC,IAAA,SAAMoC,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAEL,wBAAwB,CAACvB,YAAY,CAACwB,UAAU,CAAC,CAAO,CAAC,EACrF,CAAC,CAELxB,YAAY,CAACyC,KAAK,eACjB9C,KAAA,QAAKkC,SAAS,CAAC,eAAe,CAAAD,QAAA,eAC5BnC,IAAA,SAAMoC,SAAS,CAAC,0BAA0B,CAAAD,QAAA,CAAC,oBAAY,CAAM,CAAC,cAC9DnC,IAAA,MAAGoC,SAAS,CAAC,8CAA8C,CAAAD,QAAA,CACxD5B,YAAY,CAACyC,KAAK,CAClB,CAAC,EACD,CACN,EACE,CAAC,cAENhD,IAAA,CAACF,MAAM,EACLmD,OAAO,CAAC,SAAS,CACjBC,OAAO,CAAEhB,iBAAkB,CAC3BE,SAAS,CAAC,aAAa,CAAAD,QAAA,CACxB,qBAED,CAAQ,CAAC,EACN,CAAC,cAGNjC,KAAA,QAAKkC,SAAS,CAAC,mCAAmC,CAAAD,QAAA,eAChDnC,IAAA,OAAIoC,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,sBAEzD,CAAI,CAAC,cAELjC,KAAA,QAAKkC,SAAS,CAAC,WAAW,CAAAD,QAAA,eACxBjC,KAAA,QAAKkC,SAAS,CAAC,sBAAsB,CAAAD,QAAA,eACnCjC,KAAA,SAAMkC,SAAS,CAAC,eAAe,CAAAD,QAAA,EAAC,iBAAY,CAAC5B,YAAY,CAACe,MAAM,CAAC,IAAE,EAAM,CAAC,cAC1EtB,IAAA,SAAAmC,QAAA,CAAOjB,WAAW,CAACT,cAAc,CAAC0C,SAAS,CAAC,CAAO,CAAC,EACjD,CAAC,CAEL1C,cAAc,CAAC2C,SAAS,CAAG,CAAC,eAC3BlD,KAAA,QAAKkC,SAAS,CAAC,sBAAsB,CAAAD,QAAA,eACnCnC,IAAA,SAAMoC,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,gBAAc,CAAM,CAAC,cACrDjC,KAAA,SAAMkC,SAAS,CAAC,gBAAgB,CAAAD,QAAA,EAAC,GAAC,CAACjB,WAAW,CAACT,cAAc,CAAC2C,SAAS,CAAC,EAAO,CAAC,EAC7E,CACN,CAEA3C,cAAc,CAAC4C,UAAU,CAAG,CAAC,eAC5BnD,KAAA,QAAKkC,SAAS,CAAC,sBAAsB,CAAAD,QAAA,eACnCnC,IAAA,SAAMoC,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,aAAW,CAAM,CAAC,cAClDjC,KAAA,SAAMkC,SAAS,CAAC,gBAAgB,CAAAD,QAAA,EAAC,GAAC,CAACjB,WAAW,CAACT,cAAc,CAAC4C,UAAU,CAAC,EAAO,CAAC,EAC9E,CACN,CAEA5C,cAAc,CAAC6C,cAAc,CAAG,CAAC,eAChCpD,KAAA,QAAKkC,SAAS,CAAC,sBAAsB,CAAAD,QAAA,eACnCnC,IAAA,SAAMoC,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,eAAa,CAAM,CAAC,cACpDjC,KAAA,SAAMkC,SAAS,CAAC,gBAAgB,CAAAD,QAAA,EAAC,GAAC,CAACjB,WAAW,CAACT,cAAc,CAAC6C,cAAc,CAAC,EAAO,CAAC,EAClF,CACN,CAEA7C,cAAc,CAAC8C,SAAS,CAAG,CAAC,eAC3BrD,KAAA,QAAKkC,SAAS,CAAC,sBAAsB,CAAAD,QAAA,eACnCnC,IAAA,SAAMoC,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,uBAAkB,CAAM,CAAC,cACzDjC,KAAA,SAAMkC,SAAS,CAAC,gBAAgB,CAAAD,QAAA,EAAC,GAAC,CAACjB,WAAW,CAACT,cAAc,CAAC8C,SAAS,CAAC,EAAO,CAAC,EAC7E,CACN,cAEDrD,KAAA,QAAKkC,SAAS,CAAC,sBAAsB,CAAAD,QAAA,eACnCnC,IAAA,SAAMoC,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,aAAW,CAAM,CAAC,cAClDjC,KAAA,SAAAiC,QAAA,EAAO5B,YAAY,CAACuC,MAAM,CAAC,cAAS,EAAM,CAAC,EACxC,CAAC,cAEN9C,IAAA,OAAIoC,SAAS,CAAC,MAAM,CAAE,CAAC,cAEvBlC,KAAA,QAAKkC,SAAS,CAAC,wCAAwC,CAAAD,QAAA,eACrDnC,IAAA,SAAAmC,QAAA,CAAM,QAAM,CAAM,CAAC,cACnBnC,IAAA,SAAMoC,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAEjB,WAAW,CAACT,cAAc,CAAC+C,KAAK,CAAC,CAAO,CAAC,EACvE,CAAC,EACH,CAAC,cAENtD,KAAA,QAAKkC,SAAS,CAAC,iCAAiC,CAAAD,QAAA,eAC9CnC,IAAA,OAAIoC,SAAS,CAAC,iCAAiC,CAAAD,QAAA,CAAC,gCAAmB,CAAI,CAAC,cACxEjC,KAAA,OAAIkC,SAAS,CAAC,kCAAkC,CAAAD,QAAA,eAC9CnC,IAAA,OAAAmC,QAAA,CAAI,oCAA0B,CAAI,CAAC,cACnCnC,IAAA,OAAAmC,QAAA,CAAI,6BAAsB,CAAI,CAAC,cAC/BnC,IAAA,OAAAmC,QAAA,CAAI,8BAAuB,CAAI,CAAC,cAChCnC,IAAA,OAAAmC,QAAA,CAAI,2BAAiB,CAAI,CAAC,EACxB,CAAC,EACF,CAAC,cAENjC,KAAA,QAAKkC,SAAS,CAAC,gCAAgC,CAAAD,QAAA,eAC7CnC,IAAA,OAAIoC,SAAS,CAAC,gCAAgC,CAAAD,QAAA,CAAC,+BAAmB,CAAI,CAAC,cACvEnC,IAAA,MAAGoC,SAAS,CAAC,uBAAuB,CAAAD,QAAA,CACjC5B,YAAY,CAACwB,UAAU,GAAK,MAAM,CAAG,gBAAgB,CACrDxB,YAAY,CAACwB,UAAU,GAAK,QAAQ,CAAG,gBAAgB,CACvD,gBAAgB,CAChB,CAAC,EACD,CAAC,EACH,CAAC,EACH,CAAC,cAEN7B,KAAA,QAAKkC,SAAS,CAAC,2BAA2B,CAAAD,QAAA,eACxCnC,IAAA,CAACF,MAAM,EACLmD,OAAO,CAAC,SAAS,CACjBC,OAAO,CAAEA,CAAA,GAAM9C,QAAQ,CAAC,UAAU,CAAE,CAAA+B,QAAA,CACrC,2BAED,CAAQ,CAAC,cAETnC,IAAA,CAACF,MAAM,EACLoD,OAAO,CAAEjB,cAAe,CACxBY,IAAI,CAAC,IAAI,CAAAV,QAAA,CACV,kBAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CACA,CAAC,CAEb,CAAC,CAED,cAAe,CAAAhC,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}