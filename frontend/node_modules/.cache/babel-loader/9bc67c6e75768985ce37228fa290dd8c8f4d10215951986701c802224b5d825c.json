{"ast": null, "code": "import React from'react';import{useNavigate}from'react-router-dom';import{Layout,Button}from'../components';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const LandingPage=()=>{const navigate=useNavigate();const handleStartOrder=()=>{navigate('/upload');};const handleLogin=()=>{navigate('/painel');};return/*#__PURE__*/_jsx(Layout,{children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col items-center justify-center min-h-[60vh] text-center\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"max-w-4xl mx-auto\",children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-5xl font-bold text-gray-900 mb-6\",children:\"WePrint AI\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xl text-gray-600 mb-8 max-w-2xl mx-auto\",children:\"A gr\\xE1fica inteligente que vai at\\xE9 ti. Impress\\xE3o profissional com tecnologia AI para otimizar qualidade e custos.\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col sm:flex-row gap-4 justify-center items-center\",children:[/*#__PURE__*/_jsx(Button,{onClick:handleStartOrder,size:\"lg\",className:\"w-full sm:w-auto\",children:\"Come\\xE7ar Encomenda\"}),/*#__PURE__*/_jsx(Button,{onClick:handleLogin,variant:\"outline\",size:\"lg\",className:\"w-full sm:w-auto\",children:\"Login/Cadastro\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mt-16 grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4\",children:/*#__PURE__*/_jsx(\"span\",{className:\"text-2xl\",children:\"\\uD83D\\uDE80\"})}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold mb-2\",children:\"R\\xE1pido e F\\xE1cil\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600\",children:\"Upload, configure e receba em casa\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4\",children:/*#__PURE__*/_jsx(\"span\",{className:\"text-2xl\",children:\"\\uD83E\\uDD16\"})}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold mb-2\",children:\"Tecnologia AI\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600\",children:\"Otimiza\\xE7\\xE3o autom\\xE1tica de qualidade\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-purple-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4\",children:/*#__PURE__*/_jsx(\"span\",{className:\"text-2xl\",children:\"\\uD83D\\uDCE6\"})}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold mb-2\",children:\"Entrega Gr\\xE1tis\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600\",children:\"Levamos at\\xE9 \\xE0 sua porta\"})]})]})]})});};export default LandingPage;", "map": {"version": 3, "names": ["React", "useNavigate", "Layout", "<PERSON><PERSON>", "jsx", "_jsx", "jsxs", "_jsxs", "LandingPage", "navigate", "handleStartOrder", "handleLogin", "children", "className", "onClick", "size", "variant"], "sources": ["/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/LandingPage.tsx"], "sourcesContent": ["import React from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { Layout, Button } from '../components';\n\nconst LandingPage: React.FC = () => {\n  const navigate = useNavigate();\n\n  const handleStartOrder = () => {\n    navigate('/upload');\n  };\n\n  const handleLogin = () => {\n    navigate('/painel');\n  };\n\n  return (\n    <Layout>\n      <div className=\"flex flex-col items-center justify-center min-h-[60vh] text-center\">\n        <div className=\"max-w-4xl mx-auto\">\n          <h1 className=\"text-5xl font-bold text-gray-900 mb-6\">\n            WePrint AI\n          </h1>\n          <p className=\"text-xl text-gray-600 mb-8 max-w-2xl mx-auto\">\n            A gráfica inteligente que vai até ti. Impressão profissional com tecnologia AI para otimizar qualidade e custos.\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center\">\n            <Button\n              onClick={handleStartOrder}\n              size=\"lg\"\n              className=\"w-full sm:w-auto\"\n            >\n              Começar Encomenda\n            </Button>\n            <Button\n              onClick={handleLogin}\n              variant=\"outline\"\n              size=\"lg\"\n              className=\"w-full sm:w-auto\"\n            >\n              Login/Cadastro\n            </Button>\n          </div>\n        </div>\n\n        <div className=\"mt-16 grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto\">\n          <div className=\"text-center\">\n            <div className=\"bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4\">\n              <span className=\"text-2xl\">🚀</span>\n            </div>\n            <h3 className=\"text-lg font-semibold mb-2\">Rápido e Fácil</h3>\n            <p className=\"text-gray-600\">Upload, configure e receba em casa</p>\n          </div>\n\n          <div className=\"text-center\">\n            <div className=\"bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4\">\n              <span className=\"text-2xl\">🤖</span>\n            </div>\n            <h3 className=\"text-lg font-semibold mb-2\">Tecnologia AI</h3>\n            <p className=\"text-gray-600\">Otimização automática de qualidade</p>\n          </div>\n\n          <div className=\"text-center\">\n            <div className=\"bg-purple-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4\">\n              <span className=\"text-2xl\">📦</span>\n            </div>\n            <h3 className=\"text-lg font-semibold mb-2\">Entrega Grátis</h3>\n            <p className=\"text-gray-600\">Levamos até à sua porta</p>\n          </div>\n        </div>\n      </div>\n    </Layout>\n  );\n};\n\nexport default LandingPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,OAASC,MAAM,CAAEC,MAAM,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE/C,KAAM,CAAAC,WAAqB,CAAGA,CAAA,GAAM,CAClC,KAAM,CAAAC,QAAQ,CAAGR,WAAW,CAAC,CAAC,CAE9B,KAAM,CAAAS,gBAAgB,CAAGA,CAAA,GAAM,CAC7BD,QAAQ,CAAC,SAAS,CAAC,CACrB,CAAC,CAED,KAAM,CAAAE,WAAW,CAAGA,CAAA,GAAM,CACxBF,QAAQ,CAAC,SAAS,CAAC,CACrB,CAAC,CAED,mBACEJ,IAAA,CAACH,MAAM,EAAAU,QAAA,cACLL,KAAA,QAAKM,SAAS,CAAC,oEAAoE,CAAAD,QAAA,eACjFL,KAAA,QAAKM,SAAS,CAAC,mBAAmB,CAAAD,QAAA,eAChCP,IAAA,OAAIQ,SAAS,CAAC,uCAAuC,CAAAD,QAAA,CAAC,YAEtD,CAAI,CAAC,cACLP,IAAA,MAAGQ,SAAS,CAAC,8CAA8C,CAAAD,QAAA,CAAC,2HAE5D,CAAG,CAAC,cACJL,KAAA,QAAKM,SAAS,CAAC,6DAA6D,CAAAD,QAAA,eAC1EP,IAAA,CAACF,MAAM,EACLW,OAAO,CAAEJ,gBAAiB,CAC1BK,IAAI,CAAC,IAAI,CACTF,SAAS,CAAC,kBAAkB,CAAAD,QAAA,CAC7B,sBAED,CAAQ,CAAC,cACTP,IAAA,CAACF,MAAM,EACLW,OAAO,CAAEH,WAAY,CACrBK,OAAO,CAAC,SAAS,CACjBD,IAAI,CAAC,IAAI,CACTF,SAAS,CAAC,kBAAkB,CAAAD,QAAA,CAC7B,gBAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,cAENL,KAAA,QAAKM,SAAS,CAAC,+DAA+D,CAAAD,QAAA,eAC5EL,KAAA,QAAKM,SAAS,CAAC,aAAa,CAAAD,QAAA,eAC1BP,IAAA,QAAKQ,SAAS,CAAC,kFAAkF,CAAAD,QAAA,cAC/FP,IAAA,SAAMQ,SAAS,CAAC,UAAU,CAAAD,QAAA,CAAC,cAAE,CAAM,CAAC,CACjC,CAAC,cACNP,IAAA,OAAIQ,SAAS,CAAC,4BAA4B,CAAAD,QAAA,CAAC,sBAAc,CAAI,CAAC,cAC9DP,IAAA,MAAGQ,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,oCAAkC,CAAG,CAAC,EAChE,CAAC,cAENL,KAAA,QAAKM,SAAS,CAAC,aAAa,CAAAD,QAAA,eAC1BP,IAAA,QAAKQ,SAAS,CAAC,mFAAmF,CAAAD,QAAA,cAChGP,IAAA,SAAMQ,SAAS,CAAC,UAAU,CAAAD,QAAA,CAAC,cAAE,CAAM,CAAC,CACjC,CAAC,cACNP,IAAA,OAAIQ,SAAS,CAAC,4BAA4B,CAAAD,QAAA,CAAC,eAAa,CAAI,CAAC,cAC7DP,IAAA,MAAGQ,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,6CAAkC,CAAG,CAAC,EAChE,CAAC,cAENL,KAAA,QAAKM,SAAS,CAAC,aAAa,CAAAD,QAAA,eAC1BP,IAAA,QAAKQ,SAAS,CAAC,oFAAoF,CAAAD,QAAA,cACjGP,IAAA,SAAMQ,SAAS,CAAC,UAAU,CAAAD,QAAA,CAAC,cAAE,CAAM,CAAC,CACjC,CAAC,cACNP,IAAA,OAAIQ,SAAS,CAAC,4BAA4B,CAAAD,QAAA,CAAC,mBAAc,CAAI,CAAC,cAC9DP,IAAA,MAAGQ,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,+BAAuB,CAAG,CAAC,EACrD,CAAC,EACH,CAAC,EACH,CAAC,CACA,CAAC,CAEb,CAAC,CAED,cAAe,CAAAJ,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}