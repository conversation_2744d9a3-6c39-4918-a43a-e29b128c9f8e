{"ast": null, "code": "import React from'react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const OptionsForm=()=>/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col items-center justify-center min-h-screen\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-semibold mb-4\",children:\"Op\\xE7\\xF5es de Impress\\xE3o\"}),/*#__PURE__*/_jsxs(\"form\",{className:\"space-y-4\",children:[/*#__PURE__*/_jsxs(\"select\",{className:\"block w-64 p-2 border rounded\",children:[/*#__PURE__*/_jsx(\"option\",{children:\"Formato\"}),/*#__PURE__*/_jsx(\"option\",{children:\"A4\"}),/*#__PURE__*/_jsx(\"option\",{children:\"A3\"}),/*#__PURE__*/_jsx(\"option\",{children:\"Cart\\xE3o\"})]}),/*#__PURE__*/_jsxs(\"select\",{className:\"block w-64 p-2 border rounded\",children:[/*#__PURE__*/_jsx(\"option\",{children:\"Papel\"}),/*#__PURE__*/_jsx(\"option\",{children:\"Offset\"}),/*#__PURE__*/_jsx(\"option\",{children:\"Couch\\xEA\"})]}),/*#__PURE__*/_jsxs(\"select\",{className:\"block w-64 p-2 border rounded\",children:[/*#__PURE__*/_jsx(\"option\",{children:\"Acabamento\"}),/*#__PURE__*/_jsx(\"option\",{children:\"Brilho\"}),/*#__PURE__*/_jsx(\"option\",{children:\"Fosco\"})]}),/*#__PURE__*/_jsx(\"button\",{className:\"px-4 py-2 bg-blue-600 text-white rounded\",children:\"Ver Resumo\"})]})]});export default OptionsForm;", "map": {"version": 3, "names": ["React", "jsx", "_jsx", "jsxs", "_jsxs", "OptionsForm", "className", "children"], "sources": ["/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/OptionsForm.tsx"], "sourcesContent": ["import React from 'react';\n\nconst OptionsForm: React.FC = () => (\n  <div className=\"flex flex-col items-center justify-center min-h-screen\">\n    <h2 className=\"text-2xl font-semibold mb-4\">Opções de Impressão</h2>\n    <form className=\"space-y-4\">\n      <select className=\"block w-64 p-2 border rounded\">\n        <option>Formato</option>\n        <option>A4</option>\n        <option>A3</option>\n        <option>Cartão</option>\n      </select>\n      <select className=\"block w-64 p-2 border rounded\">\n        <option>Papel</option>\n        <option>Offset</option>\n        <option>Couchê</option>\n      </select>\n      <select className=\"block w-64 p-2 border rounded\">\n        <option>Acabamento</option>\n        <option>Brilho</option>\n        <option>Fosco</option>\n      </select>\n      <button className=\"px-4 py-2 bg-blue-600 text-white rounded\">Ver Resumo</button>\n    </form>\n  </div>\n);\n\nexport default OptionsForm;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE1B,KAAM,CAAAC,WAAqB,CAAGA,CAAA,gBAC5BD,KAAA,QAAKE,SAAS,CAAC,wDAAwD,CAAAC,QAAA,eACrEL,IAAA,OAAII,SAAS,CAAC,6BAA6B,CAAAC,QAAA,CAAC,8BAAmB,CAAI,CAAC,cACpEH,KAAA,SAAME,SAAS,CAAC,WAAW,CAAAC,QAAA,eACzBH,KAAA,WAAQE,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC/CL,IAAA,WAAAK,QAAA,CAAQ,SAAO,CAAQ,CAAC,cACxBL,IAAA,WAAAK,QAAA,CAAQ,IAAE,CAAQ,CAAC,cACnBL,IAAA,WAAAK,QAAA,CAAQ,IAAE,CAAQ,CAAC,cACnBL,IAAA,WAAAK,QAAA,CAAQ,WAAM,CAAQ,CAAC,EACjB,CAAC,cACTH,KAAA,WAAQE,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC/CL,IAAA,WAAAK,QAAA,CAAQ,OAAK,CAAQ,CAAC,cACtBL,IAAA,WAAAK,QAAA,CAAQ,QAAM,CAAQ,CAAC,cACvBL,IAAA,WAAAK,QAAA,CAAQ,WAAM,CAAQ,CAAC,EACjB,CAAC,cACTH,KAAA,WAAQE,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC/CL,IAAA,WAAAK,QAAA,CAAQ,YAAU,CAAQ,CAAC,cAC3BL,IAAA,WAAAK,QAAA,CAAQ,QAAM,CAAQ,CAAC,cACvBL,IAAA,WAAAK,QAAA,CAAQ,OAAK,CAAQ,CAAC,EAChB,CAAC,cACTL,IAAA,WAAQI,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,YAAU,CAAQ,CAAC,EAC5E,CAAC,EACJ,CACN,CAED,cAAe,CAAAF,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}