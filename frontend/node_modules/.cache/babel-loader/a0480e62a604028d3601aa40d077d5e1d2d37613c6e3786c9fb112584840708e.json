{"ast": null, "code": "// Export all components from this file for easier imports\nexport { default as Layout } from './Layout';\nexport { default as Button } from './Button';", "map": {"version": 3, "names": ["default", "Layout", "<PERSON><PERSON>"], "sources": ["/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/index.ts"], "sourcesContent": ["// Export all components from this file for easier imports\nexport { default as Layout } from './Layout';\nexport { default as Button } from './Button';\n"], "mappings": "AAAA;AACA,SAASA,OAAO,IAAIC,MAAM,QAAQ,UAAU;AAC5C,SAASD,OAAO,IAAIE,MAAM,QAAQ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}