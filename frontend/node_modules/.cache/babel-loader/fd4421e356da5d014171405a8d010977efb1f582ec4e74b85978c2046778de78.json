{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/Layout.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Layout = ({\n  children,\n  title\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: [title && /*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"bg-white shadow-sm border-b\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between items-center py-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-2xl font-bold text-gray-900\",\n            children: title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 15,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm text-gray-500\",\n              children: \"WePrint AI\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 17,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 16,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 14,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 13,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 10,\n    columnNumber: 5\n  }, this);\n};\n_c = Layout;\nexport default Layout;\nvar _c;\n$RefreshReg$(_c, \"Layout\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Layout", "children", "title", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/Layout.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface LayoutProps {\n  children: React.ReactNode;\n  title?: string;\n}\n\nconst Layout: React.FC<LayoutProps> = ({ children, title }) => {\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {title && (\n        <header className=\"bg-white shadow-sm border-b\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <div className=\"flex justify-between items-center py-4\">\n              <h1 className=\"text-2xl font-bold text-gray-900\">{title}</h1>\n              <div className=\"flex items-center space-x-4\">\n                <span className=\"text-sm text-gray-500\">WePrint AI</span>\n              </div>\n            </div>\n          </div>\n        </header>\n      )}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {children}\n      </main>\n    </div>\n  );\n};\n\nexport default Layout;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAO1B,MAAMC,MAA6B,GAAGA,CAAC;EAAEC,QAAQ;EAAEC;AAAM,CAAC,KAAK;EAC7D,oBACEH,OAAA;IAAKI,SAAS,EAAC,yBAAyB;IAAAF,QAAA,GACrCC,KAAK,iBACJH,OAAA;MAAQI,SAAS,EAAC,6BAA6B;MAAAF,QAAA,eAC7CF,OAAA;QAAKI,SAAS,EAAC,wCAAwC;QAAAF,QAAA,eACrDF,OAAA;UAAKI,SAAS,EAAC,wCAAwC;UAAAF,QAAA,gBACrDF,OAAA;YAAII,SAAS,EAAC,kCAAkC;YAAAF,QAAA,EAAEC;UAAK;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC7DR,OAAA;YAAKI,SAAS,EAAC,6BAA6B;YAAAF,QAAA,eAC1CF,OAAA;cAAMI,SAAS,EAAC,uBAAuB;cAAAF,QAAA,EAAC;YAAU;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CACT,eACDR,OAAA;MAAMI,SAAS,EAAC,6CAA6C;MAAAF,QAAA,EAC1DA;IAAQ;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACC,EAAA,GApBIR,MAA6B;AAsBnC,eAAeA,MAAM;AAAC,IAAAQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}