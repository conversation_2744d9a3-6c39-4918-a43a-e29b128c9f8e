{"ast": null, "code": "import React,{useState,useEffect}from'react';import{useNavigate}from'react-router-dom';import{Layout,Button}from'../components';import{apiService}from'../services/api';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ClientPanel=()=>{const navigate=useNavigate();const[customer,setCustomer]=useState(null);const[orders,setOrders]=useState([]);const[isLoading,setIsLoading]=useState(true);const[activeTab,setActiveTab]=useState('orders');useEffect(()=>{const loadCustomerData=async()=>{// Get customer email from localStorage or URL params\n// In a real app, this would come from authentication\nconst urlParams=new URLSearchParams(window.location.search);const customerEmail=urlParams.get('email')||localStorage.getItem('customerEmail')||'<EMAIL>';try{// Load customer orders from backend\nconst ordersResponse=await apiService.get(\"/orders/customer/\".concat(customerEmail));if(ordersResponse.success&&ordersResponse.data){var _backendOrders$,_backendOrders$2;const backendOrders=ordersResponse.data;// Transform backend orders to frontend format\nconst transformedOrders=backendOrders.map(order=>{var _order$file;return{id:order.id.toString(),orderNumber:order.orderNumber,fileName:((_order$file=order.file)===null||_order$file===void 0?void 0:_order$file.originalName)||'Arquivo não encontrado',status:order.status,total:order.price||0,createdAt:new Date(order.createdAt).toLocaleDateString('pt-BR'),estimatedDelivery:order.estimatedCompletionDate?new Date(order.estimatedCompletionDate).toLocaleDateString('pt-BR'):undefined};});setOrders(transformedOrders);// Calculate customer stats\nconst totalOrders=transformedOrders.length;const totalSpent=transformedOrders.reduce((sum,order)=>sum+order.total,0);setCustomer({name:((_backendOrders$=backendOrders[0])===null||_backendOrders$===void 0?void 0:_backendOrders$.customerName)||'Cliente',email:customerEmail,phone:((_backendOrders$2=backendOrders[0])===null||_backendOrders$2===void 0?void 0:_backendOrders$2.customerPhone)||'',totalOrders,totalSpent});}else{// No orders found, set empty customer\nsetCustomer({name:'Cliente',email:customerEmail,phone:'',totalOrders:0,totalSpent:0});setOrders([]);}}catch(error){console.error('Error loading customer data:',error);// Fallback to empty state\nsetCustomer({name:'Cliente',email:customerEmail,phone:'',totalOrders:0,totalSpent:0});setOrders([]);}finally{setIsLoading(false);}};loadCustomerData();},[]);const getStatusColor=status=>{const colors={pending:'bg-yellow-100 text-yellow-800',processing:'bg-blue-100 text-blue-800',printing:'bg-purple-100 text-purple-800',shipped:'bg-indigo-100 text-indigo-800',delivered:'bg-green-100 text-green-800',cancelled:'bg-red-100 text-red-800'};return colors[status];};const getStatusText=status=>{const texts={pending:'Pendente',processing:'Em Processamento',printing:'Imprimindo',shipped:'Enviado',delivered:'Entregue',cancelled:'Cancelado'};return texts[status];};const formatPrice=price=>{return\"\".concat(price.toFixed(2),\" AOA\");};const formatDate=dateString=>{return new Date(dateString).toLocaleDateString('pt-AO');};const handleNewOrder=()=>{navigate('/upload');};const handleViewOrder=orderId=>{// In a real app, this would navigate to order details\nalert(\"Ver detalhes do pedido \".concat(orderId));};const handleReorderOrder=orderId=>{// In a real app, this would duplicate the order\nalert(\"Repetir pedido \".concat(orderId));};if(isLoading){return/*#__PURE__*/_jsx(Layout,{children:/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center justify-center min-h-screen\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600\",children:\"Carregando painel...\"})]})})});}return/*#__PURE__*/_jsx(Layout,{children:/*#__PURE__*/_jsxs(\"div\",{className:\"max-w-6xl mx-auto py-8\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-center mb-8\",children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-3xl font-bold text-gray-900 mb-4\",children:\"Painel do Cliente\"}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-lg text-gray-600\",children:[\"Bem-vindo de volta, \",customer===null||customer===void 0?void 0:customer.name,\"!\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-lg shadow-lg p-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"p-3 rounded-full bg-blue-100\",children:/*#__PURE__*/_jsxs(\"svg\",{className:\"w-6 h-6 text-blue-600\",fill:\"currentColor\",viewBox:\"0 0 20 20\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M9 2a1 1 0 000 2h2a1 1 0 100-2H9z\"}),/*#__PURE__*/_jsx(\"path\",{fillRule:\"evenodd\",d:\"M4 5a2 2 0 012-2v1a1 1 0 001 1h6a1 1 0 001-1V3a2 2 0 012 2v6.5a1.5 1.5 0 01-1.5 1.5h-9A1.5 1.5 0 014 11.5V5zM7 7a1 1 0 012 0v3a1 1 0 11-2 0V7zm5 0a1 1 0 10-2 0v3a1 1 0 102 0V7z\",clipRule:\"evenodd\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"ml-4\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm font-medium text-gray-600\",children:\"Total de Pedidos\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-2xl font-bold text-gray-900\",children:customer===null||customer===void 0?void 0:customer.totalOrders})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-lg shadow-lg p-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"p-3 rounded-full bg-green-100\",children:/*#__PURE__*/_jsxs(\"svg\",{className:\"w-6 h-6 text-green-600\",fill:\"currentColor\",viewBox:\"0 0 20 20\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z\"}),/*#__PURE__*/_jsx(\"path\",{fillRule:\"evenodd\",d:\"M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z\",clipRule:\"evenodd\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"ml-4\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm font-medium text-gray-600\",children:\"Total Gasto\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-2xl font-bold text-gray-900\",children:formatPrice((customer===null||customer===void 0?void 0:customer.totalSpent)||0)})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-lg shadow-lg p-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"p-3 rounded-full bg-purple-100\",children:/*#__PURE__*/_jsx(\"svg\",{className:\"w-6 h-6 text-purple-600\",fill:\"currentColor\",viewBox:\"0 0 20 20\",children:/*#__PURE__*/_jsx(\"path\",{fillRule:\"evenodd\",d:\"M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",clipRule:\"evenodd\"})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"ml-4\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm font-medium text-gray-600\",children:\"Status\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-2xl font-bold text-gray-900\",children:\"Premium\"})]})]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-lg shadow-lg\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"border-b border-gray-200\",children:/*#__PURE__*/_jsxs(\"nav\",{className:\"-mb-px flex\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setActiveTab('orders'),className:\"py-4 px-6 text-sm font-medium border-b-2 \".concat(activeTab==='orders'?'border-blue-500 text-blue-600':'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'),children:\"Meus Pedidos\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setActiveTab('profile'),className:\"py-4 px-6 text-sm font-medium border-b-2 \".concat(activeTab==='profile'?'border-blue-500 text-blue-600':'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'),children:\"Perfil\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"p-6\",children:[activeTab==='orders'&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between items-center mb-6\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-xl font-semibold text-gray-900\",children:\"Hist\\xF3rico de Pedidos\"}),/*#__PURE__*/_jsx(Button,{onClick:handleNewOrder,children:\"Novo Pedido\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"space-y-4\",children:orders.map(order=>/*#__PURE__*/_jsxs(\"div\",{className:\"border border-gray-200 rounded-lg p-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between mb-3\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-3\",children:[/*#__PURE__*/_jsxs(\"h3\",{className:\"font-medium text-gray-900\",children:[\"Pedido #\",order.orderNumber]}),/*#__PURE__*/_jsx(\"span\",{className:\"px-2 py-1 text-xs font-medium rounded-full \".concat(getStatusColor(order.status)),children:getStatusText(order.status)})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-right\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"font-semibold text-gray-900\",children:formatPrice(order.total)}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-600\",children:formatDate(order.createdAt)})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"p\",{className:\"text-sm text-gray-600 mb-1\",children:[\"Arquivo: \",order.fileName]}),order.estimatedDelivery&&/*#__PURE__*/_jsxs(\"p\",{className:\"text-sm text-gray-600\",children:[\"Entrega estimada: \",formatDate(order.estimatedDelivery)]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex space-x-2\",children:[/*#__PURE__*/_jsx(Button,{variant:\"outline\",size:\"sm\",onClick:()=>handleViewOrder(order.id),children:\"Ver Detalhes\"}),order.status==='delivered'&&/*#__PURE__*/_jsx(Button,{variant:\"outline\",size:\"sm\",onClick:()=>handleReorderOrder(order.id),children:\"Repetir\"})]})]})]},order.id))})]}),activeTab==='profile'&&customer&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-xl font-semibold text-gray-900 mb-6\",children:\"Informa\\xE7\\xF5es do Perfil\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 gap-6\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"Nome Completo\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:customer.name,className:\"w-full p-3 border border-gray-300 rounded-md bg-gray-50\",readOnly:true})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"Email\"}),/*#__PURE__*/_jsx(\"input\",{type:\"email\",value:customer.email,className:\"w-full p-3 border border-gray-300 rounded-md bg-gray-50\",readOnly:true})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"Telefone\"}),/*#__PURE__*/_jsx(\"input\",{type:\"tel\",value:customer.phone,className:\"w-full p-3 border border-gray-300 rounded-md bg-gray-50\",readOnly:true})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"Status da Conta\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"px-3 py-1 bg-purple-100 text-purple-800 text-sm font-medium rounded-full\",children:\"Premium\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-600\",children:\"Desde Janeiro 2024\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mt-6 p-4 bg-blue-50 rounded-lg\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"font-medium text-blue-900 mb-2\",children:\"Benef\\xEDcios Premium\"}),/*#__PURE__*/_jsxs(\"ul\",{className:\"text-sm text-blue-800 space-y-1\",children:[/*#__PURE__*/_jsx(\"li\",{children:\"\\u2022 Entrega priorit\\xE1ria\"}),/*#__PURE__*/_jsx(\"li\",{children:\"\\u2022 Desconto de 10% em todos os pedidos\"}),/*#__PURE__*/_jsx(\"li\",{children:\"\\u2022 Suporte t\\xE9cnico dedicado\"}),/*#__PURE__*/_jsx(\"li\",{children:\"\\u2022 Acesso antecipado a novos servi\\xE7os\"})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"mt-6\",children:/*#__PURE__*/_jsx(Button,{variant:\"outline\",children:\"Editar Perfil\"})})]})]})]})]})});};export default ClientPanel;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "Layout", "<PERSON><PERSON>", "apiService", "jsx", "_jsx", "jsxs", "_jsxs", "ClientPanel", "navigate", "customer", "setCustomer", "orders", "setOrders", "isLoading", "setIsLoading", "activeTab", "setActiveTab", "loadCustomerData", "urlParams", "URLSearchParams", "window", "location", "search", "customerEmail", "get", "localStorage", "getItem", "ordersResponse", "concat", "success", "data", "_backendOrders$", "_backendOrders$2", "backendOrders", "transformedOrders", "map", "order", "_order$file", "id", "toString", "orderNumber", "fileName", "file", "originalName", "status", "total", "price", "createdAt", "Date", "toLocaleDateString", "estimatedDelivery", "estimatedCompletionDate", "undefined", "totalOrders", "length", "totalSpent", "reduce", "sum", "name", "customerName", "email", "phone", "customerPhone", "error", "console", "getStatusColor", "colors", "pending", "processing", "printing", "shipped", "delivered", "cancelled", "getStatusText", "texts", "formatPrice", "toFixed", "formatDate", "dateString", "handleNewOrder", "handleViewOrder", "orderId", "alert", "handleReorderOrder", "children", "className", "fill", "viewBox", "d", "fillRule", "clipRule", "onClick", "variant", "size", "type", "value", "readOnly"], "sources": ["/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/ClientPanel.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { Layout, Button } from '../components';\nimport { apiService } from '../services/api';\n\ninterface Order {\n  id: string;\n  orderNumber: string;\n  fileName: string;\n  status: 'pending' | 'processing' | 'printing' | 'shipped' | 'delivered' | 'cancelled';\n  total: number;\n  createdAt: string;\n  estimatedDelivery?: string;\n}\n\ninterface Customer {\n  name: string;\n  email: string;\n  phone: string;\n  totalOrders: number;\n  totalSpent: number;\n}\n\nconst ClientPanel: React.FC = () => {\n  const navigate = useNavigate();\n  const [customer, setCustomer] = useState<Customer | null>(null);\n  const [orders, setOrders] = useState<Order[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [activeTab, setActiveTab] = useState<'orders' | 'profile'>('orders');\n\n  useEffect(() => {\n    const loadCustomerData = async () => {\n      // Get customer email from localStorage or URL params\n      // In a real app, this would come from authentication\n      const urlParams = new URLSearchParams(window.location.search);\n      const customerEmail = urlParams.get('email') || localStorage.getItem('customerEmail') || '<EMAIL>';\n\n      try {\n        // Load customer orders from backend\n        const ordersResponse = await apiService.get(`/orders/customer/${customerEmail}`);\n\n        if (ordersResponse.success && ordersResponse.data) {\n          const backendOrders = ordersResponse.data as any[];\n\n          // Transform backend orders to frontend format\n          const transformedOrders: Order[] = backendOrders.map(order => ({\n            id: order.id.toString(),\n            orderNumber: order.orderNumber,\n            fileName: order.file?.originalName || 'Arquivo não encontrado',\n            status: order.status,\n            total: order.price || 0,\n            createdAt: new Date(order.createdAt).toLocaleDateString('pt-BR'),\n            estimatedDelivery: order.estimatedCompletionDate ?\n              new Date(order.estimatedCompletionDate).toLocaleDateString('pt-BR') : undefined\n          }));\n\n          setOrders(transformedOrders);\n\n          // Calculate customer stats\n          const totalOrders = transformedOrders.length;\n          const totalSpent = transformedOrders.reduce((sum, order) => sum + order.total, 0);\n\n          setCustomer({\n            name: backendOrders[0]?.customerName || 'Cliente',\n            email: customerEmail,\n            phone: backendOrders[0]?.customerPhone || '',\n            totalOrders,\n            totalSpent\n          });\n        } else {\n          // No orders found, set empty customer\n          setCustomer({\n            name: 'Cliente',\n            email: customerEmail,\n            phone: '',\n            totalOrders: 0,\n            totalSpent: 0\n          });\n          setOrders([]);\n        }\n      } catch (error) {\n        console.error('Error loading customer data:', error);\n        // Fallback to empty state\n        setCustomer({\n          name: 'Cliente',\n          email: customerEmail,\n          phone: '',\n          totalOrders: 0,\n          totalSpent: 0\n        });\n        setOrders([]);\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    loadCustomerData();\n  }, []);\n\n  const getStatusColor = (status: Order['status']) => {\n    const colors = {\n      pending: 'bg-yellow-100 text-yellow-800',\n      processing: 'bg-blue-100 text-blue-800',\n      printing: 'bg-purple-100 text-purple-800',\n      shipped: 'bg-indigo-100 text-indigo-800',\n      delivered: 'bg-green-100 text-green-800',\n      cancelled: 'bg-red-100 text-red-800',\n    };\n    return colors[status];\n  };\n\n  const getStatusText = (status: Order['status']) => {\n    const texts = {\n      pending: 'Pendente',\n      processing: 'Em Processamento',\n      printing: 'Imprimindo',\n      shipped: 'Enviado',\n      delivered: 'Entregue',\n      cancelled: 'Cancelado',\n    };\n    return texts[status];\n  };\n\n  const formatPrice = (price: number) => {\n    return `${price.toFixed(2)} AOA`;\n  };\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('pt-AO');\n  };\n\n  const handleNewOrder = () => {\n    navigate('/upload');\n  };\n\n  const handleViewOrder = (orderId: string) => {\n    // In a real app, this would navigate to order details\n    alert(`Ver detalhes do pedido ${orderId}`);\n  };\n\n  const handleReorderOrder = (orderId: string) => {\n    // In a real app, this would duplicate the order\n    alert(`Repetir pedido ${orderId}`);\n  };\n\n  if (isLoading) {\n    return (\n      <Layout>\n        <div className=\"flex items-center justify-center min-h-screen\">\n          <div className=\"text-center\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n            <p className=\"text-gray-600\">Carregando painel...</p>\n          </div>\n        </div>\n      </Layout>\n    );\n  }\n\n  return (\n    <Layout>\n      <div className=\"max-w-6xl mx-auto py-8\">\n        <div className=\"text-center mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-4\">\n            Painel do Cliente\n          </h1>\n          <p className=\"text-lg text-gray-600\">\n            Bem-vindo de volta, {customer?.name}!\n          </p>\n        </div>\n\n        {/* Stats Cards */}\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\">\n          <div className=\"bg-white rounded-lg shadow-lg p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-3 rounded-full bg-blue-100\">\n                <svg className=\"w-6 h-6 text-blue-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path d=\"M9 2a1 1 0 000 2h2a1 1 0 100-2H9z\" />\n                  <path fillRule=\"evenodd\" d=\"M4 5a2 2 0 012-2v1a1 1 0 001 1h6a1 1 0 001-1V3a2 2 0 012 2v6.5a1.5 1.5 0 01-1.5 1.5h-9A1.5 1.5 0 014 11.5V5zM7 7a1 1 0 012 0v3a1 1 0 11-2 0V7zm5 0a1 1 0 10-2 0v3a1 1 0 102 0V7z\" clipRule=\"evenodd\" />\n                </svg>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">Total de Pedidos</p>\n                <p className=\"text-2xl font-bold text-gray-900\">{customer?.totalOrders}</p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow-lg p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-3 rounded-full bg-green-100\">\n                <svg className=\"w-6 h-6 text-green-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path d=\"M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z\" />\n                  <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z\" clipRule=\"evenodd\" />\n                </svg>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">Total Gasto</p>\n                <p className=\"text-2xl font-bold text-gray-900\">{formatPrice(customer?.totalSpent || 0)}</p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow-lg p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-3 rounded-full bg-purple-100\">\n                <svg className=\"w-6 h-6 text-purple-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path fillRule=\"evenodd\" d=\"M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n                </svg>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">Status</p>\n                <p className=\"text-2xl font-bold text-gray-900\">Premium</p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Tabs */}\n        <div className=\"bg-white rounded-lg shadow-lg\">\n          <div className=\"border-b border-gray-200\">\n            <nav className=\"-mb-px flex\">\n              <button\n                onClick={() => setActiveTab('orders')}\n                className={`py-4 px-6 text-sm font-medium border-b-2 ${\n                  activeTab === 'orders'\n                    ? 'border-blue-500 text-blue-600'\n                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                }`}\n              >\n                Meus Pedidos\n              </button>\n              <button\n                onClick={() => setActiveTab('profile')}\n                className={`py-4 px-6 text-sm font-medium border-b-2 ${\n                  activeTab === 'profile'\n                    ? 'border-blue-500 text-blue-600'\n                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                }`}\n              >\n                Perfil\n              </button>\n            </nav>\n          </div>\n\n          <div className=\"p-6\">\n            {activeTab === 'orders' && (\n              <div>\n                <div className=\"flex justify-between items-center mb-6\">\n                  <h2 className=\"text-xl font-semibold text-gray-900\">\n                    Histórico de Pedidos\n                  </h2>\n                  <Button onClick={handleNewOrder}>\n                    Novo Pedido\n                  </Button>\n                </div>\n\n                <div className=\"space-y-4\">\n                  {orders.map((order) => (\n                    <div key={order.id} className=\"border border-gray-200 rounded-lg p-4\">\n                      <div className=\"flex items-center justify-between mb-3\">\n                        <div className=\"flex items-center space-x-3\">\n                          <h3 className=\"font-medium text-gray-900\">\n                            Pedido #{order.orderNumber}\n                          </h3>\n                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(order.status)}`}>\n                            {getStatusText(order.status)}\n                          </span>\n                        </div>\n                        <div className=\"text-right\">\n                          <p className=\"font-semibold text-gray-900\">{formatPrice(order.total)}</p>\n                          <p className=\"text-sm text-gray-600\">{formatDate(order.createdAt)}</p>\n                        </div>\n                      </div>\n\n                      <div className=\"flex items-center justify-between\">\n                        <div>\n                          <p className=\"text-sm text-gray-600 mb-1\">\n                            Arquivo: {order.fileName}\n                          </p>\n                          {order.estimatedDelivery && (\n                            <p className=\"text-sm text-gray-600\">\n                              Entrega estimada: {formatDate(order.estimatedDelivery)}\n                            </p>\n                          )}\n                        </div>\n\n                        <div className=\"flex space-x-2\">\n                          <Button\n                            variant=\"outline\"\n                            size=\"sm\"\n                            onClick={() => handleViewOrder(order.id)}\n                          >\n                            Ver Detalhes\n                          </Button>\n                          {order.status === 'delivered' && (\n                            <Button\n                              variant=\"outline\"\n                              size=\"sm\"\n                              onClick={() => handleReorderOrder(order.id)}\n                            >\n                              Repetir\n                            </Button>\n                          )}\n                        </div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            )}\n\n            {activeTab === 'profile' && customer && (\n              <div>\n                <h2 className=\"text-xl font-semibold text-gray-900 mb-6\">\n                  Informações do Perfil\n                </h2>\n\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Nome Completo\n                    </label>\n                    <input\n                      type=\"text\"\n                      value={customer.name}\n                      className=\"w-full p-3 border border-gray-300 rounded-md bg-gray-50\"\n                      readOnly\n                    />\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Email\n                    </label>\n                    <input\n                      type=\"email\"\n                      value={customer.email}\n                      className=\"w-full p-3 border border-gray-300 rounded-md bg-gray-50\"\n                      readOnly\n                    />\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Telefone\n                    </label>\n                    <input\n                      type=\"tel\"\n                      value={customer.phone}\n                      className=\"w-full p-3 border border-gray-300 rounded-md bg-gray-50\"\n                      readOnly\n                    />\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Status da Conta\n                    </label>\n                    <div className=\"flex items-center space-x-2\">\n                      <span className=\"px-3 py-1 bg-purple-100 text-purple-800 text-sm font-medium rounded-full\">\n                        Premium\n                      </span>\n                      <span className=\"text-sm text-gray-600\">\n                        Desde Janeiro 2024\n                      </span>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"mt-6 p-4 bg-blue-50 rounded-lg\">\n                  <h3 className=\"font-medium text-blue-900 mb-2\">Benefícios Premium</h3>\n                  <ul className=\"text-sm text-blue-800 space-y-1\">\n                    <li>• Entrega prioritária</li>\n                    <li>• Desconto de 10% em todos os pedidos</li>\n                    <li>• Suporte técnico dedicado</li>\n                    <li>• Acesso antecipado a novos serviços</li>\n                  </ul>\n                </div>\n\n                <div className=\"mt-6\">\n                  <Button variant=\"outline\">\n                    Editar Perfil\n                  </Button>\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    </Layout>\n  );\n};\n\nexport default ClientPanel;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,OAASC,MAAM,CAAEC,MAAM,KAAQ,eAAe,CAC9C,OAASC,UAAU,KAAQ,iBAAiB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAoB7C,KAAM,CAAAC,WAAqB,CAAGA,CAAA,GAAM,CAClC,KAAM,CAAAC,QAAQ,CAAGT,WAAW,CAAC,CAAC,CAC9B,KAAM,CAACU,QAAQ,CAAEC,WAAW,CAAC,CAAGb,QAAQ,CAAkB,IAAI,CAAC,CAC/D,KAAM,CAACc,MAAM,CAAEC,SAAS,CAAC,CAAGf,QAAQ,CAAU,EAAE,CAAC,CACjD,KAAM,CAACgB,SAAS,CAAEC,YAAY,CAAC,CAAGjB,QAAQ,CAAC,IAAI,CAAC,CAChD,KAAM,CAACkB,SAAS,CAAEC,YAAY,CAAC,CAAGnB,QAAQ,CAAuB,QAAQ,CAAC,CAE1EC,SAAS,CAAC,IAAM,CACd,KAAM,CAAAmB,gBAAgB,CAAG,KAAAA,CAAA,GAAY,CACnC;AACA;AACA,KAAM,CAAAC,SAAS,CAAG,GAAI,CAAAC,eAAe,CAACC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAC7D,KAAM,CAAAC,aAAa,CAAGL,SAAS,CAACM,GAAG,CAAC,OAAO,CAAC,EAAIC,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC,EAAI,oBAAoB,CAE7G,GAAI,CACF;AACA,KAAM,CAAAC,cAAc,CAAG,KAAM,CAAAzB,UAAU,CAACsB,GAAG,qBAAAI,MAAA,CAAqBL,aAAa,CAAE,CAAC,CAEhF,GAAII,cAAc,CAACE,OAAO,EAAIF,cAAc,CAACG,IAAI,CAAE,KAAAC,eAAA,CAAAC,gBAAA,CACjD,KAAM,CAAAC,aAAa,CAAGN,cAAc,CAACG,IAAa,CAElD;AACA,KAAM,CAAAI,iBAA0B,CAAGD,aAAa,CAACE,GAAG,CAACC,KAAK,OAAAC,WAAA,OAAK,CAC7DC,EAAE,CAAEF,KAAK,CAACE,EAAE,CAACC,QAAQ,CAAC,CAAC,CACvBC,WAAW,CAAEJ,KAAK,CAACI,WAAW,CAC9BC,QAAQ,CAAE,EAAAJ,WAAA,CAAAD,KAAK,CAACM,IAAI,UAAAL,WAAA,iBAAVA,WAAA,CAAYM,YAAY,GAAI,wBAAwB,CAC9DC,MAAM,CAAER,KAAK,CAACQ,MAAM,CACpBC,KAAK,CAAET,KAAK,CAACU,KAAK,EAAI,CAAC,CACvBC,SAAS,CAAE,GAAI,CAAAC,IAAI,CAACZ,KAAK,CAACW,SAAS,CAAC,CAACE,kBAAkB,CAAC,OAAO,CAAC,CAChEC,iBAAiB,CAAEd,KAAK,CAACe,uBAAuB,CAC9C,GAAI,CAAAH,IAAI,CAACZ,KAAK,CAACe,uBAAuB,CAAC,CAACF,kBAAkB,CAAC,OAAO,CAAC,CAAGG,SAC1E,CAAC,EAAC,CAAC,CAEHxC,SAAS,CAACsB,iBAAiB,CAAC,CAE5B;AACA,KAAM,CAAAmB,WAAW,CAAGnB,iBAAiB,CAACoB,MAAM,CAC5C,KAAM,CAAAC,UAAU,CAAGrB,iBAAiB,CAACsB,MAAM,CAAC,CAACC,GAAG,CAAErB,KAAK,GAAKqB,GAAG,CAAGrB,KAAK,CAACS,KAAK,CAAE,CAAC,CAAC,CAEjFnC,WAAW,CAAC,CACVgD,IAAI,CAAE,EAAA3B,eAAA,CAAAE,aAAa,CAAC,CAAC,CAAC,UAAAF,eAAA,iBAAhBA,eAAA,CAAkB4B,YAAY,GAAI,SAAS,CACjDC,KAAK,CAAErC,aAAa,CACpBsC,KAAK,CAAE,EAAA7B,gBAAA,CAAAC,aAAa,CAAC,CAAC,CAAC,UAAAD,gBAAA,iBAAhBA,gBAAA,CAAkB8B,aAAa,GAAI,EAAE,CAC5CT,WAAW,CACXE,UACF,CAAC,CAAC,CACJ,CAAC,IAAM,CACL;AACA7C,WAAW,CAAC,CACVgD,IAAI,CAAE,SAAS,CACfE,KAAK,CAAErC,aAAa,CACpBsC,KAAK,CAAE,EAAE,CACTR,WAAW,CAAE,CAAC,CACdE,UAAU,CAAE,CACd,CAAC,CAAC,CACF3C,SAAS,CAAC,EAAE,CAAC,CACf,CACF,CAAE,MAAOmD,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,CAAEA,KAAK,CAAC,CACpD;AACArD,WAAW,CAAC,CACVgD,IAAI,CAAE,SAAS,CACfE,KAAK,CAAErC,aAAa,CACpBsC,KAAK,CAAE,EAAE,CACTR,WAAW,CAAE,CAAC,CACdE,UAAU,CAAE,CACd,CAAC,CAAC,CACF3C,SAAS,CAAC,EAAE,CAAC,CACf,CAAC,OAAS,CACRE,YAAY,CAAC,KAAK,CAAC,CACrB,CACF,CAAC,CAEDG,gBAAgB,CAAC,CAAC,CACpB,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAgD,cAAc,CAAIrB,MAAuB,EAAK,CAClD,KAAM,CAAAsB,MAAM,CAAG,CACbC,OAAO,CAAE,+BAA+B,CACxCC,UAAU,CAAE,2BAA2B,CACvCC,QAAQ,CAAE,+BAA+B,CACzCC,OAAO,CAAE,+BAA+B,CACxCC,SAAS,CAAE,6BAA6B,CACxCC,SAAS,CAAE,yBACb,CAAC,CACD,MAAO,CAAAN,MAAM,CAACtB,MAAM,CAAC,CACvB,CAAC,CAED,KAAM,CAAA6B,aAAa,CAAI7B,MAAuB,EAAK,CACjD,KAAM,CAAA8B,KAAK,CAAG,CACZP,OAAO,CAAE,UAAU,CACnBC,UAAU,CAAE,kBAAkB,CAC9BC,QAAQ,CAAE,YAAY,CACtBC,OAAO,CAAE,SAAS,CAClBC,SAAS,CAAE,UAAU,CACrBC,SAAS,CAAE,WACb,CAAC,CACD,MAAO,CAAAE,KAAK,CAAC9B,MAAM,CAAC,CACtB,CAAC,CAED,KAAM,CAAA+B,WAAW,CAAI7B,KAAa,EAAK,CACrC,SAAAlB,MAAA,CAAUkB,KAAK,CAAC8B,OAAO,CAAC,CAAC,CAAC,SAC5B,CAAC,CAED,KAAM,CAAAC,UAAU,CAAIC,UAAkB,EAAK,CACzC,MAAO,IAAI,CAAA9B,IAAI,CAAC8B,UAAU,CAAC,CAAC7B,kBAAkB,CAAC,OAAO,CAAC,CACzD,CAAC,CAED,KAAM,CAAA8B,cAAc,CAAGA,CAAA,GAAM,CAC3BvE,QAAQ,CAAC,SAAS,CAAC,CACrB,CAAC,CAED,KAAM,CAAAwE,eAAe,CAAIC,OAAe,EAAK,CAC3C;AACAC,KAAK,2BAAAtD,MAAA,CAA2BqD,OAAO,CAAE,CAAC,CAC5C,CAAC,CAED,KAAM,CAAAE,kBAAkB,CAAIF,OAAe,EAAK,CAC9C;AACAC,KAAK,mBAAAtD,MAAA,CAAmBqD,OAAO,CAAE,CAAC,CACpC,CAAC,CAED,GAAIpE,SAAS,CAAE,CACb,mBACET,IAAA,CAACJ,MAAM,EAAAoF,QAAA,cACLhF,IAAA,QAAKiF,SAAS,CAAC,+CAA+C,CAAAD,QAAA,cAC5D9E,KAAA,QAAK+E,SAAS,CAAC,aAAa,CAAAD,QAAA,eAC1BhF,IAAA,QAAKiF,SAAS,CAAC,6EAA6E,CAAM,CAAC,cACnGjF,IAAA,MAAGiF,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,sBAAoB,CAAG,CAAC,EAClD,CAAC,CACH,CAAC,CACA,CAAC,CAEb,CAEA,mBACEhF,IAAA,CAACJ,MAAM,EAAAoF,QAAA,cACL9E,KAAA,QAAK+E,SAAS,CAAC,wBAAwB,CAAAD,QAAA,eACrC9E,KAAA,QAAK+E,SAAS,CAAC,kBAAkB,CAAAD,QAAA,eAC/BhF,IAAA,OAAIiF,SAAS,CAAC,uCAAuC,CAAAD,QAAA,CAAC,mBAEtD,CAAI,CAAC,cACL9E,KAAA,MAAG+E,SAAS,CAAC,uBAAuB,CAAAD,QAAA,EAAC,sBACf,CAAC3E,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAEiD,IAAI,CAAC,GACtC,EAAG,CAAC,EACD,CAAC,cAGNpD,KAAA,QAAK+E,SAAS,CAAC,4CAA4C,CAAAD,QAAA,eACzDhF,IAAA,QAAKiF,SAAS,CAAC,mCAAmC,CAAAD,QAAA,cAChD9E,KAAA,QAAK+E,SAAS,CAAC,mBAAmB,CAAAD,QAAA,eAChChF,IAAA,QAAKiF,SAAS,CAAC,8BAA8B,CAAAD,QAAA,cAC3C9E,KAAA,QAAK+E,SAAS,CAAC,uBAAuB,CAACC,IAAI,CAAC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAAH,QAAA,eAC5EhF,IAAA,SAAMoF,CAAC,CAAC,mCAAmC,CAAE,CAAC,cAC9CpF,IAAA,SAAMqF,QAAQ,CAAC,SAAS,CAACD,CAAC,CAAC,kLAAkL,CAACE,QAAQ,CAAC,SAAS,CAAE,CAAC,EAChO,CAAC,CACH,CAAC,cACNpF,KAAA,QAAK+E,SAAS,CAAC,MAAM,CAAAD,QAAA,eACnBhF,IAAA,MAAGiF,SAAS,CAAC,mCAAmC,CAAAD,QAAA,CAAC,kBAAgB,CAAG,CAAC,cACrEhF,IAAA,MAAGiF,SAAS,CAAC,kCAAkC,CAAAD,QAAA,CAAE3E,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAE4C,WAAW,CAAI,CAAC,EACxE,CAAC,EACH,CAAC,CACH,CAAC,cAENjD,IAAA,QAAKiF,SAAS,CAAC,mCAAmC,CAAAD,QAAA,cAChD9E,KAAA,QAAK+E,SAAS,CAAC,mBAAmB,CAAAD,QAAA,eAChChF,IAAA,QAAKiF,SAAS,CAAC,+BAA+B,CAAAD,QAAA,cAC5C9E,KAAA,QAAK+E,SAAS,CAAC,wBAAwB,CAACC,IAAI,CAAC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAAH,QAAA,eAC7EhF,IAAA,SAAMoF,CAAC,CAAC,4OAA4O,CAAE,CAAC,cACvPpF,IAAA,SAAMqF,QAAQ,CAAC,SAAS,CAACD,CAAC,CAAC,sdAAsd,CAACE,QAAQ,CAAC,SAAS,CAAE,CAAC,EACpgB,CAAC,CACH,CAAC,cACNpF,KAAA,QAAK+E,SAAS,CAAC,MAAM,CAAAD,QAAA,eACnBhF,IAAA,MAAGiF,SAAS,CAAC,mCAAmC,CAAAD,QAAA,CAAC,aAAW,CAAG,CAAC,cAChEhF,IAAA,MAAGiF,SAAS,CAAC,kCAAkC,CAAAD,QAAA,CAAET,WAAW,CAAC,CAAAlE,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAE8C,UAAU,GAAI,CAAC,CAAC,CAAI,CAAC,EACzF,CAAC,EACH,CAAC,CACH,CAAC,cAENnD,IAAA,QAAKiF,SAAS,CAAC,mCAAmC,CAAAD,QAAA,cAChD9E,KAAA,QAAK+E,SAAS,CAAC,mBAAmB,CAAAD,QAAA,eAChChF,IAAA,QAAKiF,SAAS,CAAC,gCAAgC,CAAAD,QAAA,cAC7ChF,IAAA,QAAKiF,SAAS,CAAC,yBAAyB,CAACC,IAAI,CAAC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAAH,QAAA,cAC9EhF,IAAA,SAAMqF,QAAQ,CAAC,SAAS,CAACD,CAAC,CAAC,iiBAAiiB,CAACE,QAAQ,CAAC,SAAS,CAAE,CAAC,CAC/kB,CAAC,CACH,CAAC,cACNpF,KAAA,QAAK+E,SAAS,CAAC,MAAM,CAAAD,QAAA,eACnBhF,IAAA,MAAGiF,SAAS,CAAC,mCAAmC,CAAAD,QAAA,CAAC,QAAM,CAAG,CAAC,cAC3DhF,IAAA,MAAGiF,SAAS,CAAC,kCAAkC,CAAAD,QAAA,CAAC,SAAO,CAAG,CAAC,EACxD,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,cAGN9E,KAAA,QAAK+E,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5ChF,IAAA,QAAKiF,SAAS,CAAC,0BAA0B,CAAAD,QAAA,cACvC9E,KAAA,QAAK+E,SAAS,CAAC,aAAa,CAAAD,QAAA,eAC1BhF,IAAA,WACEuF,OAAO,CAAEA,CAAA,GAAM3E,YAAY,CAAC,QAAQ,CAAE,CACtCqE,SAAS,6CAAAzD,MAAA,CACPb,SAAS,GAAK,QAAQ,CAClB,+BAA+B,CAC/B,4EAA4E,CAC/E,CAAAqE,QAAA,CACJ,cAED,CAAQ,CAAC,cACThF,IAAA,WACEuF,OAAO,CAAEA,CAAA,GAAM3E,YAAY,CAAC,SAAS,CAAE,CACvCqE,SAAS,6CAAAzD,MAAA,CACPb,SAAS,GAAK,SAAS,CACnB,+BAA+B,CAC/B,4EAA4E,CAC/E,CAAAqE,QAAA,CACJ,QAED,CAAQ,CAAC,EACN,CAAC,CACH,CAAC,cAEN9E,KAAA,QAAK+E,SAAS,CAAC,KAAK,CAAAD,QAAA,EACjBrE,SAAS,GAAK,QAAQ,eACrBT,KAAA,QAAA8E,QAAA,eACE9E,KAAA,QAAK+E,SAAS,CAAC,wCAAwC,CAAAD,QAAA,eACrDhF,IAAA,OAAIiF,SAAS,CAAC,qCAAqC,CAAAD,QAAA,CAAC,yBAEpD,CAAI,CAAC,cACLhF,IAAA,CAACH,MAAM,EAAC0F,OAAO,CAAEZ,cAAe,CAAAK,QAAA,CAAC,aAEjC,CAAQ,CAAC,EACN,CAAC,cAENhF,IAAA,QAAKiF,SAAS,CAAC,WAAW,CAAAD,QAAA,CACvBzE,MAAM,CAACwB,GAAG,CAAEC,KAAK,eAChB9B,KAAA,QAAoB+E,SAAS,CAAC,uCAAuC,CAAAD,QAAA,eACnE9E,KAAA,QAAK+E,SAAS,CAAC,wCAAwC,CAAAD,QAAA,eACrD9E,KAAA,QAAK+E,SAAS,CAAC,6BAA6B,CAAAD,QAAA,eAC1C9E,KAAA,OAAI+E,SAAS,CAAC,2BAA2B,CAAAD,QAAA,EAAC,UAChC,CAAChD,KAAK,CAACI,WAAW,EACxB,CAAC,cACLpC,IAAA,SAAMiF,SAAS,+CAAAzD,MAAA,CAAgDqC,cAAc,CAAC7B,KAAK,CAACQ,MAAM,CAAC,CAAG,CAAAwC,QAAA,CAC3FX,aAAa,CAACrC,KAAK,CAACQ,MAAM,CAAC,CACxB,CAAC,EACJ,CAAC,cACNtC,KAAA,QAAK+E,SAAS,CAAC,YAAY,CAAAD,QAAA,eACzBhF,IAAA,MAAGiF,SAAS,CAAC,6BAA6B,CAAAD,QAAA,CAAET,WAAW,CAACvC,KAAK,CAACS,KAAK,CAAC,CAAI,CAAC,cACzEzC,IAAA,MAAGiF,SAAS,CAAC,uBAAuB,CAAAD,QAAA,CAAEP,UAAU,CAACzC,KAAK,CAACW,SAAS,CAAC,CAAI,CAAC,EACnE,CAAC,EACH,CAAC,cAENzC,KAAA,QAAK+E,SAAS,CAAC,mCAAmC,CAAAD,QAAA,eAChD9E,KAAA,QAAA8E,QAAA,eACE9E,KAAA,MAAG+E,SAAS,CAAC,4BAA4B,CAAAD,QAAA,EAAC,WAC/B,CAAChD,KAAK,CAACK,QAAQ,EACvB,CAAC,CACHL,KAAK,CAACc,iBAAiB,eACtB5C,KAAA,MAAG+E,SAAS,CAAC,uBAAuB,CAAAD,QAAA,EAAC,oBACjB,CAACP,UAAU,CAACzC,KAAK,CAACc,iBAAiB,CAAC,EACrD,CACJ,EACE,CAAC,cAEN5C,KAAA,QAAK+E,SAAS,CAAC,gBAAgB,CAAAD,QAAA,eAC7BhF,IAAA,CAACH,MAAM,EACL2F,OAAO,CAAC,SAAS,CACjBC,IAAI,CAAC,IAAI,CACTF,OAAO,CAAEA,CAAA,GAAMX,eAAe,CAAC5C,KAAK,CAACE,EAAE,CAAE,CAAA8C,QAAA,CAC1C,cAED,CAAQ,CAAC,CACRhD,KAAK,CAACQ,MAAM,GAAK,WAAW,eAC3BxC,IAAA,CAACH,MAAM,EACL2F,OAAO,CAAC,SAAS,CACjBC,IAAI,CAAC,IAAI,CACTF,OAAO,CAAEA,CAAA,GAAMR,kBAAkB,CAAC/C,KAAK,CAACE,EAAE,CAAE,CAAA8C,QAAA,CAC7C,SAED,CAAQ,CACT,EACE,CAAC,EACH,CAAC,GA9CEhD,KAAK,CAACE,EA+CX,CACN,CAAC,CACC,CAAC,EACH,CACN,CAEAvB,SAAS,GAAK,SAAS,EAAIN,QAAQ,eAClCH,KAAA,QAAA8E,QAAA,eACEhF,IAAA,OAAIiF,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,6BAEzD,CAAI,CAAC,cAEL9E,KAAA,QAAK+E,SAAS,CAAC,uCAAuC,CAAAD,QAAA,eACpD9E,KAAA,QAAA8E,QAAA,eACEhF,IAAA,UAAOiF,SAAS,CAAC,8CAA8C,CAAAD,QAAA,CAAC,eAEhE,CAAO,CAAC,cACRhF,IAAA,UACE0F,IAAI,CAAC,MAAM,CACXC,KAAK,CAAEtF,QAAQ,CAACiD,IAAK,CACrB2B,SAAS,CAAC,yDAAyD,CACnEW,QAAQ,MACT,CAAC,EACC,CAAC,cAEN1F,KAAA,QAAA8E,QAAA,eACEhF,IAAA,UAAOiF,SAAS,CAAC,8CAA8C,CAAAD,QAAA,CAAC,OAEhE,CAAO,CAAC,cACRhF,IAAA,UACE0F,IAAI,CAAC,OAAO,CACZC,KAAK,CAAEtF,QAAQ,CAACmD,KAAM,CACtByB,SAAS,CAAC,yDAAyD,CACnEW,QAAQ,MACT,CAAC,EACC,CAAC,cAEN1F,KAAA,QAAA8E,QAAA,eACEhF,IAAA,UAAOiF,SAAS,CAAC,8CAA8C,CAAAD,QAAA,CAAC,UAEhE,CAAO,CAAC,cACRhF,IAAA,UACE0F,IAAI,CAAC,KAAK,CACVC,KAAK,CAAEtF,QAAQ,CAACoD,KAAM,CACtBwB,SAAS,CAAC,yDAAyD,CACnEW,QAAQ,MACT,CAAC,EACC,CAAC,cAEN1F,KAAA,QAAA8E,QAAA,eACEhF,IAAA,UAAOiF,SAAS,CAAC,8CAA8C,CAAAD,QAAA,CAAC,iBAEhE,CAAO,CAAC,cACR9E,KAAA,QAAK+E,SAAS,CAAC,6BAA6B,CAAAD,QAAA,eAC1ChF,IAAA,SAAMiF,SAAS,CAAC,0EAA0E,CAAAD,QAAA,CAAC,SAE3F,CAAM,CAAC,cACPhF,IAAA,SAAMiF,SAAS,CAAC,uBAAuB,CAAAD,QAAA,CAAC,oBAExC,CAAM,CAAC,EACJ,CAAC,EACH,CAAC,EACH,CAAC,cAEN9E,KAAA,QAAK+E,SAAS,CAAC,gCAAgC,CAAAD,QAAA,eAC7ChF,IAAA,OAAIiF,SAAS,CAAC,gCAAgC,CAAAD,QAAA,CAAC,uBAAkB,CAAI,CAAC,cACtE9E,KAAA,OAAI+E,SAAS,CAAC,iCAAiC,CAAAD,QAAA,eAC7ChF,IAAA,OAAAgF,QAAA,CAAI,+BAAqB,CAAI,CAAC,cAC9BhF,IAAA,OAAAgF,QAAA,CAAI,4CAAqC,CAAI,CAAC,cAC9ChF,IAAA,OAAAgF,QAAA,CAAI,oCAA0B,CAAI,CAAC,cACnChF,IAAA,OAAAgF,QAAA,CAAI,8CAAoC,CAAI,CAAC,EAC3C,CAAC,EACF,CAAC,cAENhF,IAAA,QAAKiF,SAAS,CAAC,MAAM,CAAAD,QAAA,cACnBhF,IAAA,CAACH,MAAM,EAAC2F,OAAO,CAAC,SAAS,CAAAR,QAAA,CAAC,eAE1B,CAAQ,CAAC,CACN,CAAC,EACH,CACN,EACE,CAAC,EACH,CAAC,EACH,CAAC,CACA,CAAC,CAEb,CAAC,CAED,cAAe,CAAA7E,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}