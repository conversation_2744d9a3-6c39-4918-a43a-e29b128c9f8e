{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/CheckoutPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { PaymentFlow } from '../components';\nimport { MulticaixaService } from '../services/multicaixa';\nimport { apiService } from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CheckoutPage = () => {\n  _s();\n  const navigate = useNavigate();\n  const [step, setStep] = useState('form');\n  const [formData, setFormData] = useState({\n    name: '',\n    deliveryAddress: '',\n    phone: '',\n    email: '',\n    paymentMethod: 'MULTICAIXA_EXPRESS'\n  });\n  const [orderId, setOrderId] = useState('');\n  const [orderAmount] = useState(2500); // Example amount in AOA\n\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const handleSubmitForm = async e => {\n    e.preventDefault();\n\n    // Validate form\n    if (!formData.name || !formData.phone || !formData.email) {\n      alert('Por favor, preencha todos os campos obrigatórios.');\n      return;\n    }\n    try {\n      // Get file and print options from localStorage\n      const backendFileInfo = localStorage.getItem('backendFileInfo');\n      const printOptions = localStorage.getItem('printOptions');\n      if (!backendFileInfo || !printOptions) {\n        alert('Dados do arquivo ou opções de impressão não encontrados. Reinicie o processo.');\n        navigate('/upload');\n        return;\n      }\n      const fileInfo = JSON.parse(backendFileInfo);\n      const options = JSON.parse(printOptions);\n\n      // Create order through backend API\n      const orderData = {\n        fileId: fileInfo.id,\n        customerName: formData.name,\n        customerEmail: formData.email,\n        customerPhone: formData.phone,\n        format: options.format,\n        paperType: options.paperType,\n        finish: options.finish,\n        copies: options.copies,\n        notes: `Endereço de entrega: ${formData.deliveryAddress}`\n      };\n      const orderResponse = await apiService.post('/orders', orderData);\n      if (!orderResponse.success) {\n        throw new Error(orderResponse.error || 'Erro ao criar pedido');\n      }\n      const createdOrder = orderResponse.data;\n      setOrderId(createdOrder.id.toString());\n\n      // Store order info for payment\n      localStorage.setItem('currentOrder', JSON.stringify(createdOrder));\n\n      // Move to payment step if Multicaixa is selected\n      if (formData.paymentMethod === 'MULTICAIXA_EXPRESS') {\n        setStep('payment');\n      } else {\n        // Handle other payment methods\n        alert('Método de pagamento não implementado ainda.');\n      }\n    } catch (error) {\n      console.error('Error creating order:', error);\n      alert(`Erro ao criar pedido: ${error instanceof Error ? error.message : 'Tente novamente.'}`);\n    }\n  };\n  const handlePaymentComplete = paymentId => {\n    console.log('Payment completed:', paymentId);\n    setStep('success');\n  };\n  const handlePaymentFailed = error => {\n    console.error('Payment failed:', error);\n    alert(`Erro no pagamento: ${error}`);\n    setStep('form');\n  };\n  const handleBackToForm = () => {\n    setStep('form');\n  };\n  const handleGoHome = () => {\n    navigate('/');\n  };\n\n  // Form Step\n  if (step === 'form') {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50 py-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-md mx-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-lg p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-2xl font-semibold text-gray-900 mb-6 text-center\",\n            children: \"Finalizar Pedido\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-6 p-4 bg-blue-50 rounded-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"font-medium text-blue-900 mb-2\",\n              children: \"Resumo do Pedido\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between text-sm text-blue-800\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Total:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 127,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-semibold\",\n                children: MulticaixaService.formatAoaAmount(orderAmount)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 128,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleSubmitForm,\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Nome Completo *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 136,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"name\",\n                value: formData.name,\n                onChange: handleInputChange,\n                className: \"w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                placeholder: \"Seu nome completo\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 139,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Email *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 151,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"email\",\n                name: \"email\",\n                value: formData.email,\n                onChange: handleInputChange,\n                className: \"w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                placeholder: \"<EMAIL>\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 154,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Telefone *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 166,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"tel\",\n                name: \"phone\",\n                value: formData.phone,\n                onChange: handleInputChange,\n                className: \"w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                placeholder: \"+244 900 000 000\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Endere\\xE7o de Entrega\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"deliveryAddress\",\n                value: formData.deliveryAddress,\n                onChange: handleInputChange,\n                className: \"w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                placeholder: \"Endere\\xE7o para entrega (opcional)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"M\\xE9todo de Pagamento\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                name: \"paymentMethod\",\n                value: formData.paymentMethod,\n                onChange: handleInputChange,\n                className: \"w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"MULTICAIXA_EXPRESS\",\n                  children: \"Multicaixa Express\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 204,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"CASH\",\n                  children: \"Pagamento em Dinheiro\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 205,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"BANK_TRANSFER\",\n                  children: \"Transfer\\xEAncia Banc\\xE1ria\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 206,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"w-full bg-blue-600 text-white py-3 px-4 rounded-md font-medium hover:bg-blue-700 transition-colors\",\n              children: \"Continuar para Pagamento\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 116,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Payment Step\n  if (step === 'payment') {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50 py-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-md mx-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-4\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleBackToForm,\n            className: \"flex items-center gap-2 text-blue-600 hover:text-blue-700\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-4 h-4\",\n              fill: \"currentColor\",\n              viewBox: \"0 0 20 20\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                fillRule: \"evenodd\",\n                d: \"M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z\",\n                clipRule: \"evenodd\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 234,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 15\n            }, this), \"Voltar\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(PaymentFlow, {\n          orderId: orderId,\n          amount: orderAmount,\n          currency: \"AOA\",\n          description: \"Servi\\xE7os de Impress\\xE3o WePrint AI\",\n          customerEmail: formData.email,\n          customerPhone: formData.phone,\n          onPaymentComplete: handlePaymentComplete,\n          onPaymentFailed: handlePaymentFailed,\n          onCancel: handleBackToForm\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 227,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 226,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Success Step\n  if (step === 'success') {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50 py-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-md mx-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-lg p-6 text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-20 h-20 text-green-500 mx-auto mb-4\",\n            fill: \"currentColor\",\n            viewBox: \"0 0 20 20\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              fillRule: \"evenodd\",\n              d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n              clipRule: \"evenodd\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-2xl font-semibold text-gray-900 mb-2\",\n            children: \"Pedido Confirmado!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 mb-2\",\n            children: [\"Obrigado, \", formData.name, \"!\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 270,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-500 mb-6\",\n            children: \"O seu pedido foi processado com sucesso. Receber\\xE1 uma confirma\\xE7\\xE3o por email em breve.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleGoHome,\n              className: \"w-full bg-blue-600 text-white py-3 px-4 rounded-md font-medium hover:bg-blue-700 transition-colors\",\n              children: \"Voltar ao In\\xEDcio\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => navigate('/orders'),\n              className: \"w-full border border-gray-300 text-gray-700 py-3 px-4 rounded-md font-medium hover:bg-gray-50 transition-colors\",\n              children: \"Ver Meus Pedidos\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 260,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 259,\n      columnNumber: 7\n    }, this);\n  }\n  return null;\n};\n_s(CheckoutPage, \"SO4gqIAP1ue0X3iHt1JH6NGWteM=\", false, function () {\n  return [useNavigate];\n});\n_c = CheckoutPage;\nexport default CheckoutPage;\nvar _c;\n$RefreshReg$(_c, \"CheckoutPage\");", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "PaymentFlow", "MulticaixaService", "apiService", "jsxDEV", "_jsxDEV", "CheckoutPage", "_s", "navigate", "step", "setStep", "formData", "setFormData", "name", "deliveryAddress", "phone", "email", "paymentMethod", "orderId", "setOrderId", "orderAmount", "handleInputChange", "e", "value", "target", "prev", "handleSubmitForm", "preventDefault", "alert", "backendFileInfo", "localStorage", "getItem", "printOptions", "fileInfo", "JSON", "parse", "options", "orderData", "fileId", "id", "customerName", "customerEmail", "customerPhone", "format", "paperType", "finish", "copies", "notes", "orderResponse", "post", "success", "Error", "error", "createdOrder", "data", "toString", "setItem", "stringify", "console", "message", "handlePaymentComplete", "paymentId", "log", "handlePaymentFailed", "handleBackToForm", "handleGoHome", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "formatAoaAmount", "onSubmit", "type", "onChange", "placeholder", "required", "onClick", "fill", "viewBox", "fillRule", "d", "clipRule", "amount", "currency", "description", "onPaymentComplete", "onPaymentFailed", "onCancel", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/CheckoutPage.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { PaymentFlow } from '../components';\nimport { MulticaixaService } from '../services/multicaixa';\nimport { apiService } from '../services/api';\n\ninterface CheckoutFormData {\n  name: string;\n  deliveryAddress: string;\n  phone: string;\n  email: string;\n  paymentMethod: 'MULTICAIXA_EXPRESS' | 'CASH' | 'BANK_TRANSFER';\n}\n\nconst CheckoutPage: React.FC = () => {\n  const navigate = useNavigate();\n  const [step, setStep] = useState<'form' | 'payment' | 'success'>('form');\n  const [formData, setFormData] = useState<CheckoutFormData>({\n    name: '',\n    deliveryAddress: '',\n    phone: '',\n    email: '',\n    paymentMethod: 'MULTICAIXA_EXPRESS',\n  });\n  const [orderId, setOrderId] = useState<string>('');\n  const [orderAmount] = useState<number>(2500); // Example amount in AOA\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({ ...prev, [name]: value }));\n  };\n\n  const handleSubmitForm = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    // Validate form\n    if (!formData.name || !formData.phone || !formData.email) {\n      alert('Por favor, preencha todos os campos obrigatórios.');\n      return;\n    }\n\n    try {\n      // Get file and print options from localStorage\n      const backendFileInfo = localStorage.getItem('backendFileInfo');\n      const printOptions = localStorage.getItem('printOptions');\n\n      if (!backendFileInfo || !printOptions) {\n        alert('Dados do arquivo ou opções de impressão não encontrados. Reinicie o processo.');\n        navigate('/upload');\n        return;\n      }\n\n      const fileInfo = JSON.parse(backendFileInfo);\n      const options = JSON.parse(printOptions);\n\n      // Create order through backend API\n      const orderData = {\n        fileId: fileInfo.id,\n        customerName: formData.name,\n        customerEmail: formData.email,\n        customerPhone: formData.phone,\n        format: options.format,\n        paperType: options.paperType,\n        finish: options.finish,\n        copies: options.copies,\n        notes: `Endereço de entrega: ${formData.deliveryAddress}`\n      };\n\n      const orderResponse = await apiService.post('/orders', orderData);\n\n      if (!orderResponse.success) {\n        throw new Error(orderResponse.error || 'Erro ao criar pedido');\n      }\n\n      const createdOrder = orderResponse.data;\n      setOrderId(createdOrder.id.toString());\n\n      // Store order info for payment\n      localStorage.setItem('currentOrder', JSON.stringify(createdOrder));\n\n      // Move to payment step if Multicaixa is selected\n      if (formData.paymentMethod === 'MULTICAIXA_EXPRESS') {\n        setStep('payment');\n      } else {\n        // Handle other payment methods\n        alert('Método de pagamento não implementado ainda.');\n      }\n    } catch (error) {\n      console.error('Error creating order:', error);\n      alert(`Erro ao criar pedido: ${error instanceof Error ? error.message : 'Tente novamente.'}`);\n    }\n  };\n\n  const handlePaymentComplete = (paymentId: string) => {\n    console.log('Payment completed:', paymentId);\n    setStep('success');\n  };\n\n  const handlePaymentFailed = (error: string) => {\n    console.error('Payment failed:', error);\n    alert(`Erro no pagamento: ${error}`);\n    setStep('form');\n  };\n\n  const handleBackToForm = () => {\n    setStep('form');\n  };\n\n  const handleGoHome = () => {\n    navigate('/');\n  };\n\n  // Form Step\n  if (step === 'form') {\n    return (\n      <div className=\"min-h-screen bg-gray-50 py-8\">\n        <div className=\"max-w-md mx-auto\">\n          <div className=\"bg-white rounded-lg shadow-lg p-6\">\n            <h2 className=\"text-2xl font-semibold text-gray-900 mb-6 text-center\">\n              Finalizar Pedido\n            </h2>\n\n            {/* Order Summary */}\n            <div className=\"mb-6 p-4 bg-blue-50 rounded-lg\">\n              <h3 className=\"font-medium text-blue-900 mb-2\">Resumo do Pedido</h3>\n              <div className=\"flex justify-between text-sm text-blue-800\">\n                <span>Total:</span>\n                <span className=\"font-semibold\">\n                  {MulticaixaService.formatAoaAmount(orderAmount)}\n                </span>\n              </div>\n            </div>\n\n            <form onSubmit={handleSubmitForm} className=\"space-y-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Nome Completo *\n                </label>\n                <input\n                  type=\"text\"\n                  name=\"name\"\n                  value={formData.name}\n                  onChange={handleInputChange}\n                  className=\"w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                  placeholder=\"Seu nome completo\"\n                  required\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Email *\n                </label>\n                <input\n                  type=\"email\"\n                  name=\"email\"\n                  value={formData.email}\n                  onChange={handleInputChange}\n                  className=\"w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                  placeholder=\"<EMAIL>\"\n                  required\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Telefone *\n                </label>\n                <input\n                  type=\"tel\"\n                  name=\"phone\"\n                  value={formData.phone}\n                  onChange={handleInputChange}\n                  className=\"w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                  placeholder=\"+244 900 000 000\"\n                  required\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Endereço de Entrega\n                </label>\n                <input\n                  type=\"text\"\n                  name=\"deliveryAddress\"\n                  value={formData.deliveryAddress}\n                  onChange={handleInputChange}\n                  className=\"w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                  placeholder=\"Endereço para entrega (opcional)\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Método de Pagamento\n                </label>\n                <select\n                  name=\"paymentMethod\"\n                  value={formData.paymentMethod}\n                  onChange={handleInputChange}\n                  className=\"w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                >\n                  <option value=\"MULTICAIXA_EXPRESS\">Multicaixa Express</option>\n                  <option value=\"CASH\">Pagamento em Dinheiro</option>\n                  <option value=\"BANK_TRANSFER\">Transferência Bancária</option>\n                </select>\n              </div>\n\n              <button\n                type=\"submit\"\n                className=\"w-full bg-blue-600 text-white py-3 px-4 rounded-md font-medium hover:bg-blue-700 transition-colors\"\n              >\n                Continuar para Pagamento\n              </button>\n            </form>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  // Payment Step\n  if (step === 'payment') {\n    return (\n      <div className=\"min-h-screen bg-gray-50 py-8\">\n        <div className=\"max-w-md mx-auto\">\n          <div className=\"mb-4\">\n            <button\n              onClick={handleBackToForm}\n              className=\"flex items-center gap-2 text-blue-600 hover:text-blue-700\"\n            >\n              <svg className=\"w-4 h-4\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                <path fillRule=\"evenodd\" d=\"M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z\" clipRule=\"evenodd\" />\n              </svg>\n              Voltar\n            </button>\n          </div>\n\n          <PaymentFlow\n            orderId={orderId}\n            amount={orderAmount}\n            currency=\"AOA\"\n            description=\"Serviços de Impressão WePrint AI\"\n            customerEmail={formData.email}\n            customerPhone={formData.phone}\n            onPaymentComplete={handlePaymentComplete}\n            onPaymentFailed={handlePaymentFailed}\n            onCancel={handleBackToForm}\n          />\n        </div>\n      </div>\n    );\n  }\n\n  // Success Step\n  if (step === 'success') {\n    return (\n      <div className=\"min-h-screen bg-gray-50 py-8\">\n        <div className=\"max-w-md mx-auto\">\n          <div className=\"bg-white rounded-lg shadow-lg p-6 text-center\">\n            <svg className=\"w-20 h-20 text-green-500 mx-auto mb-4\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n            </svg>\n\n            <h2 className=\"text-2xl font-semibold text-gray-900 mb-2\">\n              Pedido Confirmado!\n            </h2>\n\n            <p className=\"text-gray-600 mb-2\">\n              Obrigado, {formData.name}!\n            </p>\n\n            <p className=\"text-sm text-gray-500 mb-6\">\n              O seu pedido foi processado com sucesso. Receberá uma confirmação por email em breve.\n            </p>\n\n            <div className=\"space-y-3\">\n              <button\n                onClick={handleGoHome}\n                className=\"w-full bg-blue-600 text-white py-3 px-4 rounded-md font-medium hover:bg-blue-700 transition-colors\"\n              >\n                Voltar ao Início\n              </button>\n\n              <button\n                onClick={() => navigate('/orders')}\n                className=\"w-full border border-gray-300 text-gray-700 py-3 px-4 rounded-md font-medium hover:bg-gray-50 transition-colors\"\n              >\n                Ver Meus Pedidos\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return null;\n};\n\nexport default CheckoutPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,iBAAiB,QAAQ,wBAAwB;AAC1D,SAASC,UAAU,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAU7C,MAAMC,YAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnC,MAAMC,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACS,IAAI,EAAEC,OAAO,CAAC,GAAGX,QAAQ,CAAiC,MAAM,CAAC;EACxE,MAAM,CAACY,QAAQ,EAAEC,WAAW,CAAC,GAAGb,QAAQ,CAAmB;IACzDc,IAAI,EAAE,EAAE;IACRC,eAAe,EAAE,EAAE;IACnBC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE,EAAE;IACTC,aAAa,EAAE;EACjB,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGpB,QAAQ,CAAS,EAAE,CAAC;EAClD,MAAM,CAACqB,WAAW,CAAC,GAAGrB,QAAQ,CAAS,IAAI,CAAC,CAAC,CAAC;;EAE9C,MAAMsB,iBAAiB,GAAIC,CAA0D,IAAK;IACxF,MAAM;MAAET,IAAI;MAAEU;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAChCZ,WAAW,CAACa,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACZ,IAAI,GAAGU;IAAM,CAAC,CAAC,CAAC;EACnD,CAAC;EAED,MAAMG,gBAAgB,GAAG,MAAOJ,CAAkB,IAAK;IACrDA,CAAC,CAACK,cAAc,CAAC,CAAC;;IAElB;IACA,IAAI,CAAChB,QAAQ,CAACE,IAAI,IAAI,CAACF,QAAQ,CAACI,KAAK,IAAI,CAACJ,QAAQ,CAACK,KAAK,EAAE;MACxDY,KAAK,CAAC,mDAAmD,CAAC;MAC1D;IACF;IAEA,IAAI;MACF;MACA,MAAMC,eAAe,GAAGC,YAAY,CAACC,OAAO,CAAC,iBAAiB,CAAC;MAC/D,MAAMC,YAAY,GAAGF,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;MAEzD,IAAI,CAACF,eAAe,IAAI,CAACG,YAAY,EAAE;QACrCJ,KAAK,CAAC,+EAA+E,CAAC;QACtFpB,QAAQ,CAAC,SAAS,CAAC;QACnB;MACF;MAEA,MAAMyB,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACN,eAAe,CAAC;MAC5C,MAAMO,OAAO,GAAGF,IAAI,CAACC,KAAK,CAACH,YAAY,CAAC;;MAExC;MACA,MAAMK,SAAS,GAAG;QAChBC,MAAM,EAAEL,QAAQ,CAACM,EAAE;QACnBC,YAAY,EAAE7B,QAAQ,CAACE,IAAI;QAC3B4B,aAAa,EAAE9B,QAAQ,CAACK,KAAK;QAC7B0B,aAAa,EAAE/B,QAAQ,CAACI,KAAK;QAC7B4B,MAAM,EAAEP,OAAO,CAACO,MAAM;QACtBC,SAAS,EAAER,OAAO,CAACQ,SAAS;QAC5BC,MAAM,EAAET,OAAO,CAACS,MAAM;QACtBC,MAAM,EAAEV,OAAO,CAACU,MAAM;QACtBC,KAAK,EAAE,wBAAwBpC,QAAQ,CAACG,eAAe;MACzD,CAAC;MAED,MAAMkC,aAAa,GAAG,MAAM7C,UAAU,CAAC8C,IAAI,CAAC,SAAS,EAAEZ,SAAS,CAAC;MAEjE,IAAI,CAACW,aAAa,CAACE,OAAO,EAAE;QAC1B,MAAM,IAAIC,KAAK,CAACH,aAAa,CAACI,KAAK,IAAI,sBAAsB,CAAC;MAChE;MAEA,MAAMC,YAAY,GAAGL,aAAa,CAACM,IAAI;MACvCnC,UAAU,CAACkC,YAAY,CAACd,EAAE,CAACgB,QAAQ,CAAC,CAAC,CAAC;;MAEtC;MACAzB,YAAY,CAAC0B,OAAO,CAAC,cAAc,EAAEtB,IAAI,CAACuB,SAAS,CAACJ,YAAY,CAAC,CAAC;;MAElE;MACA,IAAI1C,QAAQ,CAACM,aAAa,KAAK,oBAAoB,EAAE;QACnDP,OAAO,CAAC,SAAS,CAAC;MACpB,CAAC,MAAM;QACL;QACAkB,KAAK,CAAC,6CAA6C,CAAC;MACtD;IACF,CAAC,CAAC,OAAOwB,KAAK,EAAE;MACdM,OAAO,CAACN,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CxB,KAAK,CAAC,yBAAyBwB,KAAK,YAAYD,KAAK,GAAGC,KAAK,CAACO,OAAO,GAAG,kBAAkB,EAAE,CAAC;IAC/F;EACF,CAAC;EAED,MAAMC,qBAAqB,GAAIC,SAAiB,IAAK;IACnDH,OAAO,CAACI,GAAG,CAAC,oBAAoB,EAAED,SAAS,CAAC;IAC5CnD,OAAO,CAAC,SAAS,CAAC;EACpB,CAAC;EAED,MAAMqD,mBAAmB,GAAIX,KAAa,IAAK;IAC7CM,OAAO,CAACN,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;IACvCxB,KAAK,CAAC,sBAAsBwB,KAAK,EAAE,CAAC;IACpC1C,OAAO,CAAC,MAAM,CAAC;EACjB,CAAC;EAED,MAAMsD,gBAAgB,GAAGA,CAAA,KAAM;IAC7BtD,OAAO,CAAC,MAAM,CAAC;EACjB,CAAC;EAED,MAAMuD,YAAY,GAAGA,CAAA,KAAM;IACzBzD,QAAQ,CAAC,GAAG,CAAC;EACf,CAAC;;EAED;EACA,IAAIC,IAAI,KAAK,MAAM,EAAE;IACnB,oBACEJ,OAAA;MAAK6D,SAAS,EAAC,8BAA8B;MAAAC,QAAA,eAC3C9D,OAAA;QAAK6D,SAAS,EAAC,kBAAkB;QAAAC,QAAA,eAC/B9D,OAAA;UAAK6D,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChD9D,OAAA;YAAI6D,SAAS,EAAC,uDAAuD;YAAAC,QAAA,EAAC;UAEtE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAGLlE,OAAA;YAAK6D,SAAS,EAAC,gCAAgC;YAAAC,QAAA,gBAC7C9D,OAAA;cAAI6D,SAAS,EAAC,gCAAgC;cAAAC,QAAA,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpElE,OAAA;cAAK6D,SAAS,EAAC,4CAA4C;cAAAC,QAAA,gBACzD9D,OAAA;gBAAA8D,QAAA,EAAM;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACnBlE,OAAA;gBAAM6D,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAC5BjE,iBAAiB,CAACsE,eAAe,CAACpD,WAAW;cAAC;gBAAAgD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENlE,OAAA;YAAMoE,QAAQ,EAAE/C,gBAAiB;YAACwC,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACrD9D,OAAA;cAAA8D,QAAA,gBACE9D,OAAA;gBAAO6D,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRlE,OAAA;gBACEqE,IAAI,EAAC,MAAM;gBACX7D,IAAI,EAAC,MAAM;gBACXU,KAAK,EAAEZ,QAAQ,CAACE,IAAK;gBACrB8D,QAAQ,EAAEtD,iBAAkB;gBAC5B6C,SAAS,EAAC,qGAAqG;gBAC/GU,WAAW,EAAC,mBAAmB;gBAC/BC,QAAQ;cAAA;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENlE,OAAA;cAAA8D,QAAA,gBACE9D,OAAA;gBAAO6D,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRlE,OAAA;gBACEqE,IAAI,EAAC,OAAO;gBACZ7D,IAAI,EAAC,OAAO;gBACZU,KAAK,EAAEZ,QAAQ,CAACK,KAAM;gBACtB2D,QAAQ,EAAEtD,iBAAkB;gBAC5B6C,SAAS,EAAC,qGAAqG;gBAC/GU,WAAW,EAAC,eAAe;gBAC3BC,QAAQ;cAAA;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENlE,OAAA;cAAA8D,QAAA,gBACE9D,OAAA;gBAAO6D,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRlE,OAAA;gBACEqE,IAAI,EAAC,KAAK;gBACV7D,IAAI,EAAC,OAAO;gBACZU,KAAK,EAAEZ,QAAQ,CAACI,KAAM;gBACtB4D,QAAQ,EAAEtD,iBAAkB;gBAC5B6C,SAAS,EAAC,qGAAqG;gBAC/GU,WAAW,EAAC,kBAAkB;gBAC9BC,QAAQ;cAAA;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENlE,OAAA;cAAA8D,QAAA,gBACE9D,OAAA;gBAAO6D,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRlE,OAAA;gBACEqE,IAAI,EAAC,MAAM;gBACX7D,IAAI,EAAC,iBAAiB;gBACtBU,KAAK,EAAEZ,QAAQ,CAACG,eAAgB;gBAChC6D,QAAQ,EAAEtD,iBAAkB;gBAC5B6C,SAAS,EAAC,qGAAqG;gBAC/GU,WAAW,EAAC;cAAkC;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENlE,OAAA;cAAA8D,QAAA,gBACE9D,OAAA;gBAAO6D,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRlE,OAAA;gBACEQ,IAAI,EAAC,eAAe;gBACpBU,KAAK,EAAEZ,QAAQ,CAACM,aAAc;gBAC9B0D,QAAQ,EAAEtD,iBAAkB;gBAC5B6C,SAAS,EAAC,qGAAqG;gBAAAC,QAAA,gBAE/G9D,OAAA;kBAAQkB,KAAK,EAAC,oBAAoB;kBAAA4C,QAAA,EAAC;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC9DlE,OAAA;kBAAQkB,KAAK,EAAC,MAAM;kBAAA4C,QAAA,EAAC;gBAAqB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACnDlE,OAAA;kBAAQkB,KAAK,EAAC,eAAe;kBAAA4C,QAAA,EAAC;gBAAsB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENlE,OAAA;cACEqE,IAAI,EAAC,QAAQ;cACbR,SAAS,EAAC,oGAAoG;cAAAC,QAAA,EAC/G;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,IAAI9D,IAAI,KAAK,SAAS,EAAE;IACtB,oBACEJ,OAAA;MAAK6D,SAAS,EAAC,8BAA8B;MAAAC,QAAA,eAC3C9D,OAAA;QAAK6D,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/B9D,OAAA;UAAK6D,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnB9D,OAAA;YACEyE,OAAO,EAAEd,gBAAiB;YAC1BE,SAAS,EAAC,2DAA2D;YAAAC,QAAA,gBAErE9D,OAAA;cAAK6D,SAAS,EAAC,SAAS;cAACa,IAAI,EAAC,cAAc;cAACC,OAAO,EAAC,WAAW;cAAAb,QAAA,eAC9D9D,OAAA;gBAAM4E,QAAQ,EAAC,SAAS;gBAACC,CAAC,EAAC,uIAAuI;gBAACC,QAAQ,EAAC;cAAS;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrL,CAAC,UAER;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENlE,OAAA,CAACJ,WAAW;UACViB,OAAO,EAAEA,OAAQ;UACjBkE,MAAM,EAAEhE,WAAY;UACpBiE,QAAQ,EAAC,KAAK;UACdC,WAAW,EAAC,wCAAkC;UAC9C7C,aAAa,EAAE9B,QAAQ,CAACK,KAAM;UAC9B0B,aAAa,EAAE/B,QAAQ,CAACI,KAAM;UAC9BwE,iBAAiB,EAAE3B,qBAAsB;UACzC4B,eAAe,EAAEzB,mBAAoB;UACrC0B,QAAQ,EAAEzB;QAAiB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,IAAI9D,IAAI,KAAK,SAAS,EAAE;IACtB,oBACEJ,OAAA;MAAK6D,SAAS,EAAC,8BAA8B;MAAAC,QAAA,eAC3C9D,OAAA;QAAK6D,SAAS,EAAC,kBAAkB;QAAAC,QAAA,eAC/B9D,OAAA;UAAK6D,SAAS,EAAC,+CAA+C;UAAAC,QAAA,gBAC5D9D,OAAA;YAAK6D,SAAS,EAAC,uCAAuC;YAACa,IAAI,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAAb,QAAA,eAC5F9D,OAAA;cAAM4E,QAAQ,EAAC,SAAS;cAACC,CAAC,EAAC,uIAAuI;cAACC,QAAQ,EAAC;YAAS;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrL,CAAC,eAENlE,OAAA;YAAI6D,SAAS,EAAC,2CAA2C;YAAAC,QAAA,EAAC;UAE1D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAELlE,OAAA;YAAG6D,SAAS,EAAC,oBAAoB;YAAAC,QAAA,GAAC,YACtB,EAACxD,QAAQ,CAACE,IAAI,EAAC,GAC3B;UAAA;YAAAuD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAEJlE,OAAA;YAAG6D,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAE1C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAEJlE,OAAA;YAAK6D,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB9D,OAAA;cACEyE,OAAO,EAAEb,YAAa;cACtBC,SAAS,EAAC,oGAAoG;cAAAC,QAAA,EAC/G;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAETlE,OAAA;cACEyE,OAAO,EAAEA,CAAA,KAAMtE,QAAQ,CAAC,SAAS,CAAE;cACnC0D,SAAS,EAAC,iHAAiH;cAAAC,QAAA,EAC5H;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,OAAO,IAAI;AACb,CAAC;AAAChE,EAAA,CA7RID,YAAsB;EAAA,QACTN,WAAW;AAAA;AAAA0F,EAAA,GADxBpF,YAAsB;AA+R5B,eAAeA,YAAY;AAAC,IAAAoF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}