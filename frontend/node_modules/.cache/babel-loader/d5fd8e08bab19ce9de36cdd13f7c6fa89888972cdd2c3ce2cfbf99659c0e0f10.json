{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/ClientPanel.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { Layout, Button } from '../components';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ClientPanel = () => {\n  _s();\n  const navigate = useNavigate();\n  const [customer, setCustomer] = useState(null);\n  const [orders, setOrders] = useState([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [activeTab, setActiveTab] = useState('orders');\n  useEffect(() => {\n    // Simulate loading customer data\n    setTimeout(() => {\n      setCustomer({\n        name: '<PERSON>',\n        email: '<EMAIL>',\n        phone: '+244 900 123 456',\n        totalOrders: 8,\n        totalSpent: 12450.00\n      });\n      setOrders([{\n        id: '1',\n        orderNumber: 'WP67504853469',\n        fileName: 'documento-teste.pdf',\n        status: 'delivered',\n        total: 2500.00,\n        createdAt: '2024-01-15',\n        estimatedDelivery: '2024-01-17'\n      }, {\n        id: '2',\n        orderNumber: 'WP67504853470',\n        fileName: 'apresentacao-empresa.pdf',\n        status: 'processing',\n        total: 4200.00,\n        createdAt: '2024-01-20',\n        estimatedDelivery: '2024-01-23'\n      }, {\n        id: '3',\n        orderNumber: 'WP67504853471',\n        fileName: 'relatorio-mensal.pdf',\n        status: 'printing',\n        total: 1800.00,\n        createdAt: '2024-01-22',\n        estimatedDelivery: '2024-01-24'\n      }]);\n      setIsLoading(false);\n    }, 1000);\n  }, []);\n  const getStatusColor = status => {\n    const colors = {\n      pending: 'bg-yellow-100 text-yellow-800',\n      processing: 'bg-blue-100 text-blue-800',\n      printing: 'bg-purple-100 text-purple-800',\n      shipped: 'bg-indigo-100 text-indigo-800',\n      delivered: 'bg-green-100 text-green-800',\n      cancelled: 'bg-red-100 text-red-800'\n    };\n    return colors[status];\n  };\n  const getStatusText = status => {\n    const texts = {\n      pending: 'Pendente',\n      processing: 'Em Processamento',\n      printing: 'Imprimindo',\n      shipped: 'Enviado',\n      delivered: 'Entregue',\n      cancelled: 'Cancelado'\n    };\n    return texts[status];\n  };\n  const formatPrice = price => {\n    return `${price.toFixed(2)} AOA`;\n  };\n  const formatDate = dateString => {\n    return new Date(dateString).toLocaleDateString('pt-AO');\n  };\n  const handleNewOrder = () => {\n    navigate('/upload');\n  };\n  const handleViewOrder = orderId => {\n    // In a real app, this would navigate to order details\n    alert(`Ver detalhes do pedido ${orderId}`);\n  };\n  const handleReorderOrder = orderId => {\n    // In a real app, this would duplicate the order\n    alert(`Repetir pedido ${orderId}`);\n  };\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(Layout, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-center min-h-screen\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: \"Carregando painel...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 124,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Layout, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-6xl mx-auto py-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold text-gray-900 mb-4\",\n          children: \"Painel do Cliente\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-gray-600\",\n          children: [\"Bem-vindo de volta, \", customer === null || customer === void 0 ? void 0 : customer.name, \"!\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-lg p-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-3 rounded-full bg-blue-100\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-6 h-6 text-blue-600\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 20 20\",\n                children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M9 2a1 1 0 000 2h2a1 1 0 100-2H9z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 153,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  fillRule: \"evenodd\",\n                  d: \"M4 5a2 2 0 012-2v1a1 1 0 001 1h6a1 1 0 001-1V3a2 2 0 012 2v6.5a1.5 1.5 0 01-1.5 1.5h-9A1.5 1.5 0 014 11.5V5zM7 7a1 1 0 012 0v3a1 1 0 11-2 0V7zm5 0a1 1 0 10-2 0v3a1 1 0 102 0V7z\",\n                  clipRule: \"evenodd\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 154,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 152,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm font-medium text-gray-600\",\n                children: \"Total de Pedidos\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 158,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-2xl font-bold text-gray-900\",\n                children: customer === null || customer === void 0 ? void 0 : customer.totalOrders\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-lg p-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-3 rounded-full bg-green-100\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-6 h-6 text-green-600\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 20 20\",\n                children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 168,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  fillRule: \"evenodd\",\n                  d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z\",\n                  clipRule: \"evenodd\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 169,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm font-medium text-gray-600\",\n                children: \"Total Gasto\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-2xl font-bold text-gray-900\",\n                children: formatPrice((customer === null || customer === void 0 ? void 0 : customer.totalSpent) || 0)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-lg p-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-3 rounded-full bg-purple-100\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-6 h-6 text-purple-600\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 20 20\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  fillRule: \"evenodd\",\n                  d: \"M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                  clipRule: \"evenodd\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 183,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm font-medium text-gray-600\",\n                children: \"Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-2xl font-bold text-gray-900\",\n                children: \"Premium\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-lg\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"border-b border-gray-200\",\n          children: /*#__PURE__*/_jsxDEV(\"nav\", {\n            className: \"-mb-px flex\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setActiveTab('orders'),\n              className: `py-4 px-6 text-sm font-medium border-b-2 ${activeTab === 'orders' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`,\n              children: \"Meus Pedidos\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setActiveTab('profile'),\n              className: `py-4 px-6 text-sm font-medium border-b-2 ${activeTab === 'profile' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`,\n              children: \"Perfil\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6\",\n          children: [activeTab === 'orders' && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between items-center mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-xl font-semibold text-gray-900\",\n                children: \"Hist\\xF3rico de Pedidos\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                onClick: handleNewOrder,\n                children: \"Novo Pedido\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: orders.map(order => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"border border-gray-200 rounded-lg p-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"font-medium text-gray-900\",\n                      children: [\"Pedido #\", order.orderNumber]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 238,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(order.status)}`,\n                      children: getStatusText(order.status)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 241,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 237,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-right\",\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"font-semibold text-gray-900\",\n                      children: formatPrice(order.total)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 246,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-600\",\n                      children: formatDate(order.createdAt)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 247,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 245,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 236,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-600 mb-1\",\n                      children: [\"Arquivo: \", order.fileName]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 253,\n                      columnNumber: 27\n                    }, this), order.estimatedDelivery && /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-600\",\n                      children: [\"Entrega estimada: \", formatDate(order.estimatedDelivery)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 257,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 252,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex space-x-2\",\n                    children: [/*#__PURE__*/_jsxDEV(Button, {\n                      variant: \"outline\",\n                      size: \"sm\",\n                      onClick: () => handleViewOrder(order.id),\n                      children: \"Ver Detalhes\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 264,\n                      columnNumber: 27\n                    }, this), order.status === 'delivered' && /*#__PURE__*/_jsxDEV(Button, {\n                      variant: \"outline\",\n                      size: \"sm\",\n                      onClick: () => handleReorderOrder(order.id),\n                      children: \"Repetir\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 272,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 263,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 251,\n                  columnNumber: 23\n                }, this)]\n              }, order.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 15\n          }, this), activeTab === 'profile' && customer && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl font-semibold text-gray-900 mb-6\",\n              children: \"Informa\\xE7\\xF5es do Perfil\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Nome Completo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 296,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: customer.name,\n                  className: \"w-full p-3 border border-gray-300 rounded-md bg-gray-50\",\n                  readOnly: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 299,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 295,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Email\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 308,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"email\",\n                  value: customer.email,\n                  className: \"w-full p-3 border border-gray-300 rounded-md bg-gray-50\",\n                  readOnly: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 311,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Telefone\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 320,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"tel\",\n                  value: customer.phone,\n                  className: \"w-full p-3 border border-gray-300 rounded-md bg-gray-50\",\n                  readOnly: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 323,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 319,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Status da Conta\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 332,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"px-3 py-1 bg-purple-100 text-purple-800 text-sm font-medium rounded-full\",\n                    children: \"Premium\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 336,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm text-gray-600\",\n                    children: \"Desde Janeiro 2024\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 339,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 335,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 331,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-6 p-4 bg-blue-50 rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"font-medium text-blue-900 mb-2\",\n                children: \"Benef\\xEDcios Premium\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 347,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                className: \"text-sm text-blue-800 space-y-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"\\u2022 Entrega priorit\\xE1ria\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 349,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"\\u2022 Desconto de 10% em todos os pedidos\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 350,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"\\u2022 Suporte t\\xE9cnico dedicado\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 351,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"\\u2022 Acesso antecipado a novos servi\\xE7os\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 352,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 348,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 346,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-6\",\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outline\",\n                children: \"Editar Perfil\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 357,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 356,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 137,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 136,\n    columnNumber: 5\n  }, this);\n};\n_s(ClientPanel, \"vMPvTy8N9Fd1gWMdzuEDwbRq8VQ=\", false, function () {\n  return [useNavigate];\n});\n_c = ClientPanel;\nexport default ClientPanel;\nvar _c;\n$RefreshReg$(_c, \"ClientPanel\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "Layout", "<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "ClientPanel", "_s", "navigate", "customer", "setCustomer", "orders", "setOrders", "isLoading", "setIsLoading", "activeTab", "setActiveTab", "setTimeout", "name", "email", "phone", "totalOrders", "totalSpent", "id", "orderNumber", "fileName", "status", "total", "createdAt", "estimatedDelivery", "getStatusColor", "colors", "pending", "processing", "printing", "shipped", "delivered", "cancelled", "getStatusText", "texts", "formatPrice", "price", "toFixed", "formatDate", "dateString", "Date", "toLocaleDateString", "handleNewOrder", "handleViewOrder", "orderId", "alert", "handleReorderOrder", "children", "className", "_jsxFileName", "lineNumber", "columnNumber", "fill", "viewBox", "d", "fillRule", "clipRule", "onClick", "map", "order", "variant", "size", "type", "value", "readOnly", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/ClientPanel.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { Layout, Button } from '../components';\nimport { apiService } from '../services/api';\n\ninterface Order {\n  id: string;\n  orderNumber: string;\n  fileName: string;\n  status: 'pending' | 'processing' | 'printing' | 'shipped' | 'delivered' | 'cancelled';\n  total: number;\n  createdAt: string;\n  estimatedDelivery?: string;\n}\n\ninterface Customer {\n  name: string;\n  email: string;\n  phone: string;\n  totalOrders: number;\n  totalSpent: number;\n}\n\nconst ClientPanel: React.FC = () => {\n  const navigate = useNavigate();\n  const [customer, setCustomer] = useState<Customer | null>(null);\n  const [orders, setOrders] = useState<Order[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [activeTab, setActiveTab] = useState<'orders' | 'profile'>('orders');\n\n  useEffect(() => {\n    // Simulate loading customer data\n    setTimeout(() => {\n      setCustomer({\n        name: '<PERSON>',\n        email: '<EMAIL>',\n        phone: '+244 900 123 456',\n        totalOrders: 8,\n        totalSpent: 12450.00,\n      });\n\n      setOrders([\n        {\n          id: '1',\n          orderNumber: 'WP67504853469',\n          fileName: 'documento-teste.pdf',\n          status: 'delivered',\n          total: 2500.00,\n          createdAt: '2024-01-15',\n          estimatedDelivery: '2024-01-17',\n        },\n        {\n          id: '2',\n          orderNumber: 'WP67504853470',\n          fileName: 'apresentacao-empresa.pdf',\n          status: 'processing',\n          total: 4200.00,\n          createdAt: '2024-01-20',\n          estimatedDelivery: '2024-01-23',\n        },\n        {\n          id: '3',\n          orderNumber: 'WP67504853471',\n          fileName: 'relatorio-mensal.pdf',\n          status: 'printing',\n          total: 1800.00,\n          createdAt: '2024-01-22',\n          estimatedDelivery: '2024-01-24',\n        },\n      ]);\n\n      setIsLoading(false);\n    }, 1000);\n  }, []);\n\n  const getStatusColor = (status: Order['status']) => {\n    const colors = {\n      pending: 'bg-yellow-100 text-yellow-800',\n      processing: 'bg-blue-100 text-blue-800',\n      printing: 'bg-purple-100 text-purple-800',\n      shipped: 'bg-indigo-100 text-indigo-800',\n      delivered: 'bg-green-100 text-green-800',\n      cancelled: 'bg-red-100 text-red-800',\n    };\n    return colors[status];\n  };\n\n  const getStatusText = (status: Order['status']) => {\n    const texts = {\n      pending: 'Pendente',\n      processing: 'Em Processamento',\n      printing: 'Imprimindo',\n      shipped: 'Enviado',\n      delivered: 'Entregue',\n      cancelled: 'Cancelado',\n    };\n    return texts[status];\n  };\n\n  const formatPrice = (price: number) => {\n    return `${price.toFixed(2)} AOA`;\n  };\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('pt-AO');\n  };\n\n  const handleNewOrder = () => {\n    navigate('/upload');\n  };\n\n  const handleViewOrder = (orderId: string) => {\n    // In a real app, this would navigate to order details\n    alert(`Ver detalhes do pedido ${orderId}`);\n  };\n\n  const handleReorderOrder = (orderId: string) => {\n    // In a real app, this would duplicate the order\n    alert(`Repetir pedido ${orderId}`);\n  };\n\n  if (isLoading) {\n    return (\n      <Layout>\n        <div className=\"flex items-center justify-center min-h-screen\">\n          <div className=\"text-center\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n            <p className=\"text-gray-600\">Carregando painel...</p>\n          </div>\n        </div>\n      </Layout>\n    );\n  }\n\n  return (\n    <Layout>\n      <div className=\"max-w-6xl mx-auto py-8\">\n        <div className=\"text-center mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-4\">\n            Painel do Cliente\n          </h1>\n          <p className=\"text-lg text-gray-600\">\n            Bem-vindo de volta, {customer?.name}!\n          </p>\n        </div>\n\n        {/* Stats Cards */}\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\">\n          <div className=\"bg-white rounded-lg shadow-lg p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-3 rounded-full bg-blue-100\">\n                <svg className=\"w-6 h-6 text-blue-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path d=\"M9 2a1 1 0 000 2h2a1 1 0 100-2H9z\" />\n                  <path fillRule=\"evenodd\" d=\"M4 5a2 2 0 012-2v1a1 1 0 001 1h6a1 1 0 001-1V3a2 2 0 012 2v6.5a1.5 1.5 0 01-1.5 1.5h-9A1.5 1.5 0 014 11.5V5zM7 7a1 1 0 012 0v3a1 1 0 11-2 0V7zm5 0a1 1 0 10-2 0v3a1 1 0 102 0V7z\" clipRule=\"evenodd\" />\n                </svg>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">Total de Pedidos</p>\n                <p className=\"text-2xl font-bold text-gray-900\">{customer?.totalOrders}</p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow-lg p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-3 rounded-full bg-green-100\">\n                <svg className=\"w-6 h-6 text-green-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path d=\"M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z\" />\n                  <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z\" clipRule=\"evenodd\" />\n                </svg>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">Total Gasto</p>\n                <p className=\"text-2xl font-bold text-gray-900\">{formatPrice(customer?.totalSpent || 0)}</p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow-lg p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-3 rounded-full bg-purple-100\">\n                <svg className=\"w-6 h-6 text-purple-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path fillRule=\"evenodd\" d=\"M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n                </svg>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">Status</p>\n                <p className=\"text-2xl font-bold text-gray-900\">Premium</p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Tabs */}\n        <div className=\"bg-white rounded-lg shadow-lg\">\n          <div className=\"border-b border-gray-200\">\n            <nav className=\"-mb-px flex\">\n              <button\n                onClick={() => setActiveTab('orders')}\n                className={`py-4 px-6 text-sm font-medium border-b-2 ${\n                  activeTab === 'orders'\n                    ? 'border-blue-500 text-blue-600'\n                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                }`}\n              >\n                Meus Pedidos\n              </button>\n              <button\n                onClick={() => setActiveTab('profile')}\n                className={`py-4 px-6 text-sm font-medium border-b-2 ${\n                  activeTab === 'profile'\n                    ? 'border-blue-500 text-blue-600'\n                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                }`}\n              >\n                Perfil\n              </button>\n            </nav>\n          </div>\n\n          <div className=\"p-6\">\n            {activeTab === 'orders' && (\n              <div>\n                <div className=\"flex justify-between items-center mb-6\">\n                  <h2 className=\"text-xl font-semibold text-gray-900\">\n                    Histórico de Pedidos\n                  </h2>\n                  <Button onClick={handleNewOrder}>\n                    Novo Pedido\n                  </Button>\n                </div>\n\n                <div className=\"space-y-4\">\n                  {orders.map((order) => (\n                    <div key={order.id} className=\"border border-gray-200 rounded-lg p-4\">\n                      <div className=\"flex items-center justify-between mb-3\">\n                        <div className=\"flex items-center space-x-3\">\n                          <h3 className=\"font-medium text-gray-900\">\n                            Pedido #{order.orderNumber}\n                          </h3>\n                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(order.status)}`}>\n                            {getStatusText(order.status)}\n                          </span>\n                        </div>\n                        <div className=\"text-right\">\n                          <p className=\"font-semibold text-gray-900\">{formatPrice(order.total)}</p>\n                          <p className=\"text-sm text-gray-600\">{formatDate(order.createdAt)}</p>\n                        </div>\n                      </div>\n\n                      <div className=\"flex items-center justify-between\">\n                        <div>\n                          <p className=\"text-sm text-gray-600 mb-1\">\n                            Arquivo: {order.fileName}\n                          </p>\n                          {order.estimatedDelivery && (\n                            <p className=\"text-sm text-gray-600\">\n                              Entrega estimada: {formatDate(order.estimatedDelivery)}\n                            </p>\n                          )}\n                        </div>\n\n                        <div className=\"flex space-x-2\">\n                          <Button\n                            variant=\"outline\"\n                            size=\"sm\"\n                            onClick={() => handleViewOrder(order.id)}\n                          >\n                            Ver Detalhes\n                          </Button>\n                          {order.status === 'delivered' && (\n                            <Button\n                              variant=\"outline\"\n                              size=\"sm\"\n                              onClick={() => handleReorderOrder(order.id)}\n                            >\n                              Repetir\n                            </Button>\n                          )}\n                        </div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            )}\n\n            {activeTab === 'profile' && customer && (\n              <div>\n                <h2 className=\"text-xl font-semibold text-gray-900 mb-6\">\n                  Informações do Perfil\n                </h2>\n\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Nome Completo\n                    </label>\n                    <input\n                      type=\"text\"\n                      value={customer.name}\n                      className=\"w-full p-3 border border-gray-300 rounded-md bg-gray-50\"\n                      readOnly\n                    />\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Email\n                    </label>\n                    <input\n                      type=\"email\"\n                      value={customer.email}\n                      className=\"w-full p-3 border border-gray-300 rounded-md bg-gray-50\"\n                      readOnly\n                    />\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Telefone\n                    </label>\n                    <input\n                      type=\"tel\"\n                      value={customer.phone}\n                      className=\"w-full p-3 border border-gray-300 rounded-md bg-gray-50\"\n                      readOnly\n                    />\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Status da Conta\n                    </label>\n                    <div className=\"flex items-center space-x-2\">\n                      <span className=\"px-3 py-1 bg-purple-100 text-purple-800 text-sm font-medium rounded-full\">\n                        Premium\n                      </span>\n                      <span className=\"text-sm text-gray-600\">\n                        Desde Janeiro 2024\n                      </span>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"mt-6 p-4 bg-blue-50 rounded-lg\">\n                  <h3 className=\"font-medium text-blue-900 mb-2\">Benefícios Premium</h3>\n                  <ul className=\"text-sm text-blue-800 space-y-1\">\n                    <li>• Entrega prioritária</li>\n                    <li>• Desconto de 10% em todos os pedidos</li>\n                    <li>• Suporte técnico dedicado</li>\n                    <li>• Acesso antecipado a novos serviços</li>\n                  </ul>\n                </div>\n\n                <div className=\"mt-6\">\n                  <Button variant=\"outline\">\n                    Editar Perfil\n                  </Button>\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    </Layout>\n  );\n};\n\nexport default ClientPanel;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,MAAM,EAAEC,MAAM,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAqB/C,MAAMC,WAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClC,MAAMC,QAAQ,GAAGP,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACQ,QAAQ,EAAEC,WAAW,CAAC,GAAGX,QAAQ,CAAkB,IAAI,CAAC;EAC/D,MAAM,CAACY,MAAM,EAAEC,SAAS,CAAC,GAAGb,QAAQ,CAAU,EAAE,CAAC;EACjD,MAAM,CAACc,SAAS,EAAEC,YAAY,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACgB,SAAS,EAAEC,YAAY,CAAC,GAAGjB,QAAQ,CAAuB,QAAQ,CAAC;EAE1EC,SAAS,CAAC,MAAM;IACd;IACAiB,UAAU,CAAC,MAAM;MACfP,WAAW,CAAC;QACVQ,IAAI,EAAE,YAAY;QAClBC,KAAK,EAAE,sBAAsB;QAC7BC,KAAK,EAAE,kBAAkB;QACzBC,WAAW,EAAE,CAAC;QACdC,UAAU,EAAE;MACd,CAAC,CAAC;MAEFV,SAAS,CAAC,CACR;QACEW,EAAE,EAAE,GAAG;QACPC,WAAW,EAAE,eAAe;QAC5BC,QAAQ,EAAE,qBAAqB;QAC/BC,MAAM,EAAE,WAAW;QACnBC,KAAK,EAAE,OAAO;QACdC,SAAS,EAAE,YAAY;QACvBC,iBAAiB,EAAE;MACrB,CAAC,EACD;QACEN,EAAE,EAAE,GAAG;QACPC,WAAW,EAAE,eAAe;QAC5BC,QAAQ,EAAE,0BAA0B;QACpCC,MAAM,EAAE,YAAY;QACpBC,KAAK,EAAE,OAAO;QACdC,SAAS,EAAE,YAAY;QACvBC,iBAAiB,EAAE;MACrB,CAAC,EACD;QACEN,EAAE,EAAE,GAAG;QACPC,WAAW,EAAE,eAAe;QAC5BC,QAAQ,EAAE,sBAAsB;QAChCC,MAAM,EAAE,UAAU;QAClBC,KAAK,EAAE,OAAO;QACdC,SAAS,EAAE,YAAY;QACvBC,iBAAiB,EAAE;MACrB,CAAC,CACF,CAAC;MAEFf,YAAY,CAAC,KAAK,CAAC;IACrB,CAAC,EAAE,IAAI,CAAC;EACV,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMgB,cAAc,GAAIJ,MAAuB,IAAK;IAClD,MAAMK,MAAM,GAAG;MACbC,OAAO,EAAE,+BAA+B;MACxCC,UAAU,EAAE,2BAA2B;MACvCC,QAAQ,EAAE,+BAA+B;MACzCC,OAAO,EAAE,+BAA+B;MACxCC,SAAS,EAAE,6BAA6B;MACxCC,SAAS,EAAE;IACb,CAAC;IACD,OAAON,MAAM,CAACL,MAAM,CAAC;EACvB,CAAC;EAED,MAAMY,aAAa,GAAIZ,MAAuB,IAAK;IACjD,MAAMa,KAAK,GAAG;MACZP,OAAO,EAAE,UAAU;MACnBC,UAAU,EAAE,kBAAkB;MAC9BC,QAAQ,EAAE,YAAY;MACtBC,OAAO,EAAE,SAAS;MAClBC,SAAS,EAAE,UAAU;MACrBC,SAAS,EAAE;IACb,CAAC;IACD,OAAOE,KAAK,CAACb,MAAM,CAAC;EACtB,CAAC;EAED,MAAMc,WAAW,GAAIC,KAAa,IAAK;IACrC,OAAO,GAAGA,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC,MAAM;EAClC,CAAC;EAED,MAAMC,UAAU,GAAIC,UAAkB,IAAK;IACzC,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,OAAO,CAAC;EACzD,CAAC;EAED,MAAMC,cAAc,GAAGA,CAAA,KAAM;IAC3BvC,QAAQ,CAAC,SAAS,CAAC;EACrB,CAAC;EAED,MAAMwC,eAAe,GAAIC,OAAe,IAAK;IAC3C;IACAC,KAAK,CAAC,0BAA0BD,OAAO,EAAE,CAAC;EAC5C,CAAC;EAED,MAAME,kBAAkB,GAAIF,OAAe,IAAK;IAC9C;IACAC,KAAK,CAAC,kBAAkBD,OAAO,EAAE,CAAC;EACpC,CAAC;EAED,IAAIpC,SAAS,EAAE;IACb,oBACER,OAAA,CAACH,MAAM;MAAAkD,QAAA,eACL/C,OAAA;QAAKgD,SAAS,EAAC,+CAA+C;QAAAD,QAAA,eAC5D/C,OAAA;UAAKgD,SAAS,EAAC,aAAa;UAAAD,QAAA,gBAC1B/C,OAAA;YAAKgD,SAAS,EAAC;UAA6E;YAAA5B,QAAA,EAAA6B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACnGnD,OAAA;YAAGgD,SAAS,EAAC,eAAe;YAAAD,QAAA,EAAC;UAAoB;YAAA3B,QAAA,EAAA6B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAA/B,QAAA,EAAA6B,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD;MAAC;QAAA/B,QAAA,EAAA6B,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAA/B,QAAA,EAAA6B,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAEb;EAEA,oBACEnD,OAAA,CAACH,MAAM;IAAAkD,QAAA,eACL/C,OAAA;MAAKgD,SAAS,EAAC,wBAAwB;MAAAD,QAAA,gBACrC/C,OAAA;QAAKgD,SAAS,EAAC,kBAAkB;QAAAD,QAAA,gBAC/B/C,OAAA;UAAIgD,SAAS,EAAC,uCAAuC;UAAAD,QAAA,EAAC;QAEtD;UAAA3B,QAAA,EAAA6B,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLnD,OAAA;UAAGgD,SAAS,EAAC,uBAAuB;UAAAD,QAAA,GAAC,sBACf,EAAC3C,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAES,IAAI,EAAC,GACtC;QAAA;UAAAO,QAAA,EAAA6B,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAA/B,QAAA,EAAA6B,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGNnD,OAAA;QAAKgD,SAAS,EAAC,4CAA4C;QAAAD,QAAA,gBACzD/C,OAAA;UAAKgD,SAAS,EAAC,mCAAmC;UAAAD,QAAA,eAChD/C,OAAA;YAAKgD,SAAS,EAAC,mBAAmB;YAAAD,QAAA,gBAChC/C,OAAA;cAAKgD,SAAS,EAAC,8BAA8B;cAAAD,QAAA,eAC3C/C,OAAA;gBAAKgD,SAAS,EAAC,uBAAuB;gBAACI,IAAI,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAAN,QAAA,gBAC5E/C,OAAA;kBAAMsD,CAAC,EAAC;gBAAmC;kBAAAlC,QAAA,EAAA6B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC9CnD,OAAA;kBAAMuD,QAAQ,EAAC,SAAS;kBAACD,CAAC,EAAC,kLAAkL;kBAACE,QAAQ,EAAC;gBAAS;kBAAApC,QAAA,EAAA6B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAA/B,QAAA,EAAA6B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChO;YAAC;cAAA/B,QAAA,EAAA6B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNnD,OAAA;cAAKgD,SAAS,EAAC,MAAM;cAAAD,QAAA,gBACnB/C,OAAA;gBAAGgD,SAAS,EAAC,mCAAmC;gBAAAD,QAAA,EAAC;cAAgB;gBAAA3B,QAAA,EAAA6B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACrEnD,OAAA;gBAAGgD,SAAS,EAAC,kCAAkC;gBAAAD,QAAA,EAAE3C,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEY;cAAW;gBAAAI,QAAA,EAAA6B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAA/B,QAAA,EAAA6B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxE,CAAC;UAAA;YAAA/B,QAAA,EAAA6B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAA/B,QAAA,EAAA6B,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENnD,OAAA;UAAKgD,SAAS,EAAC,mCAAmC;UAAAD,QAAA,eAChD/C,OAAA;YAAKgD,SAAS,EAAC,mBAAmB;YAAAD,QAAA,gBAChC/C,OAAA;cAAKgD,SAAS,EAAC,+BAA+B;cAAAD,QAAA,eAC5C/C,OAAA;gBAAKgD,SAAS,EAAC,wBAAwB;gBAACI,IAAI,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAAN,QAAA,gBAC7E/C,OAAA;kBAAMsD,CAAC,EAAC;gBAA4O;kBAAAlC,QAAA,EAAA6B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACvPnD,OAAA;kBAAMuD,QAAQ,EAAC,SAAS;kBAACD,CAAC,EAAC,sdAAsd;kBAACE,QAAQ,EAAC;gBAAS;kBAAApC,QAAA,EAAA6B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAA/B,QAAA,EAAA6B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpgB;YAAC;cAAA/B,QAAA,EAAA6B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNnD,OAAA;cAAKgD,SAAS,EAAC,MAAM;cAAAD,QAAA,gBACnB/C,OAAA;gBAAGgD,SAAS,EAAC,mCAAmC;gBAAAD,QAAA,EAAC;cAAW;gBAAA3B,QAAA,EAAA6B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAChEnD,OAAA;gBAAGgD,SAAS,EAAC,kCAAkC;gBAAAD,QAAA,EAAEZ,WAAW,CAAC,CAAA/B,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEa,UAAU,KAAI,CAAC;cAAC;gBAAAG,QAAA,EAAA6B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAA/B,QAAA,EAAA6B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzF,CAAC;UAAA;YAAA/B,QAAA,EAAA6B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAA/B,QAAA,EAAA6B,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENnD,OAAA;UAAKgD,SAAS,EAAC,mCAAmC;UAAAD,QAAA,eAChD/C,OAAA;YAAKgD,SAAS,EAAC,mBAAmB;YAAAD,QAAA,gBAChC/C,OAAA;cAAKgD,SAAS,EAAC,gCAAgC;cAAAD,QAAA,eAC7C/C,OAAA;gBAAKgD,SAAS,EAAC,yBAAyB;gBAACI,IAAI,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAAN,QAAA,eAC9E/C,OAAA;kBAAMuD,QAAQ,EAAC,SAAS;kBAACD,CAAC,EAAC,iiBAAiiB;kBAACE,QAAQ,EAAC;gBAAS;kBAAApC,QAAA,EAAA6B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAA/B,QAAA,EAAA6B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/kB;YAAC;cAAA/B,QAAA,EAAA6B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNnD,OAAA;cAAKgD,SAAS,EAAC,MAAM;cAAAD,QAAA,gBACnB/C,OAAA;gBAAGgD,SAAS,EAAC,mCAAmC;gBAAAD,QAAA,EAAC;cAAM;gBAAA3B,QAAA,EAAA6B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC3DnD,OAAA;gBAAGgD,SAAS,EAAC,kCAAkC;gBAAAD,QAAA,EAAC;cAAO;gBAAA3B,QAAA,EAAA6B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAA/B,QAAA,EAAA6B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CAAC;UAAA;YAAA/B,QAAA,EAAA6B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAA/B,QAAA,EAAA6B,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAA/B,QAAA,EAAA6B,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNnD,OAAA;QAAKgD,SAAS,EAAC,+BAA+B;QAAAD,QAAA,gBAC5C/C,OAAA;UAAKgD,SAAS,EAAC,0BAA0B;UAAAD,QAAA,eACvC/C,OAAA;YAAKgD,SAAS,EAAC,aAAa;YAAAD,QAAA,gBAC1B/C,OAAA;cACEyD,OAAO,EAAEA,CAAA,KAAM9C,YAAY,CAAC,QAAQ,CAAE;cACtCqC,SAAS,EAAE,4CACTtC,SAAS,KAAK,QAAQ,GAClB,+BAA+B,GAC/B,4EAA4E,EAC/E;cAAAqC,QAAA,EACJ;YAED;cAAA3B,QAAA,EAAA6B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTnD,OAAA;cACEyD,OAAO,EAAEA,CAAA,KAAM9C,YAAY,CAAC,SAAS,CAAE;cACvCqC,SAAS,EAAE,4CACTtC,SAAS,KAAK,SAAS,GACnB,+BAA+B,GAC/B,4EAA4E,EAC/E;cAAAqC,QAAA,EACJ;YAED;cAAA3B,QAAA,EAAA6B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAA/B,QAAA,EAAA6B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAA/B,QAAA,EAAA6B,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENnD,OAAA;UAAKgD,SAAS,EAAC,KAAK;UAAAD,QAAA,GACjBrC,SAAS,KAAK,QAAQ,iBACrBV,OAAA;YAAA+C,QAAA,gBACE/C,OAAA;cAAKgD,SAAS,EAAC,wCAAwC;cAAAD,QAAA,gBACrD/C,OAAA;gBAAIgD,SAAS,EAAC,qCAAqC;gBAAAD,QAAA,EAAC;cAEpD;gBAAA3B,QAAA,EAAA6B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLnD,OAAA,CAACF,MAAM;gBAAC2D,OAAO,EAAEf,cAAe;gBAAAK,QAAA,EAAC;cAEjC;gBAAA3B,QAAA,EAAA6B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAA/B,QAAA,EAAA6B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENnD,OAAA;cAAKgD,SAAS,EAAC,WAAW;cAAAD,QAAA,EACvBzC,MAAM,CAACoD,GAAG,CAAEC,KAAK,iBAChB3D,OAAA;gBAAoBgD,SAAS,EAAC,uCAAuC;gBAAAD,QAAA,gBACnE/C,OAAA;kBAAKgD,SAAS,EAAC,wCAAwC;kBAAAD,QAAA,gBACrD/C,OAAA;oBAAKgD,SAAS,EAAC,6BAA6B;oBAAAD,QAAA,gBAC1C/C,OAAA;sBAAIgD,SAAS,EAAC,2BAA2B;sBAAAD,QAAA,GAAC,UAChC,EAACY,KAAK,CAACxC,WAAW;oBAAA;sBAAAC,QAAA,EAAA6B,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxB,CAAC,eACLnD,OAAA;sBAAMgD,SAAS,EAAE,8CAA8CvB,cAAc,CAACkC,KAAK,CAACtC,MAAM,CAAC,EAAG;sBAAA0B,QAAA,EAC3Fd,aAAa,CAAC0B,KAAK,CAACtC,MAAM;oBAAC;sBAAAD,QAAA,EAAA6B,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxB,CAAC;kBAAA;oBAAA/B,QAAA,EAAA6B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACNnD,OAAA;oBAAKgD,SAAS,EAAC,YAAY;oBAAAD,QAAA,gBACzB/C,OAAA;sBAAGgD,SAAS,EAAC,6BAA6B;sBAAAD,QAAA,EAAEZ,WAAW,CAACwB,KAAK,CAACrC,KAAK;oBAAC;sBAAAF,QAAA,EAAA6B,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACzEnD,OAAA;sBAAGgD,SAAS,EAAC,uBAAuB;sBAAAD,QAAA,EAAET,UAAU,CAACqB,KAAK,CAACpC,SAAS;oBAAC;sBAAAH,QAAA,EAAA6B,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAA/B,QAAA,EAAA6B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnE,CAAC;gBAAA;kBAAA/B,QAAA,EAAA6B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENnD,OAAA;kBAAKgD,SAAS,EAAC,mCAAmC;kBAAAD,QAAA,gBAChD/C,OAAA;oBAAA+C,QAAA,gBACE/C,OAAA;sBAAGgD,SAAS,EAAC,4BAA4B;sBAAAD,QAAA,GAAC,WAC/B,EAACY,KAAK,CAACvC,QAAQ;oBAAA;sBAAAA,QAAA,EAAA6B,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvB,CAAC,EACHQ,KAAK,CAACnC,iBAAiB,iBACtBxB,OAAA;sBAAGgD,SAAS,EAAC,uBAAuB;sBAAAD,QAAA,GAAC,oBACjB,EAACT,UAAU,CAACqB,KAAK,CAACnC,iBAAiB,CAAC;oBAAA;sBAAAJ,QAAA,EAAA6B,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrD,CACJ;kBAAA;oBAAA/B,QAAA,EAAA6B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eAENnD,OAAA;oBAAKgD,SAAS,EAAC,gBAAgB;oBAAAD,QAAA,gBAC7B/C,OAAA,CAACF,MAAM;sBACL8D,OAAO,EAAC,SAAS;sBACjBC,IAAI,EAAC,IAAI;sBACTJ,OAAO,EAAEA,CAAA,KAAMd,eAAe,CAACgB,KAAK,CAACzC,EAAE,CAAE;sBAAA6B,QAAA,EAC1C;oBAED;sBAAA3B,QAAA,EAAA6B,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,EACRQ,KAAK,CAACtC,MAAM,KAAK,WAAW,iBAC3BrB,OAAA,CAACF,MAAM;sBACL8D,OAAO,EAAC,SAAS;sBACjBC,IAAI,EAAC,IAAI;sBACTJ,OAAO,EAAEA,CAAA,KAAMX,kBAAkB,CAACa,KAAK,CAACzC,EAAE,CAAE;sBAAA6B,QAAA,EAC7C;oBAED;sBAAA3B,QAAA,EAAA6B,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CACT;kBAAA;oBAAA/B,QAAA,EAAA6B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAA/B,QAAA,EAAA6B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA,GA9CEQ,KAAK,CAACzC,EAAE;gBAAAE,QAAA,EAAA6B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA+Cb,CACN;YAAC;cAAA/B,QAAA,EAAA6B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAA/B,QAAA,EAAA6B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAEAzC,SAAS,KAAK,SAAS,IAAIN,QAAQ,iBAClCJ,OAAA;YAAA+C,QAAA,gBACE/C,OAAA;cAAIgD,SAAS,EAAC,0CAA0C;cAAAD,QAAA,EAAC;YAEzD;cAAA3B,QAAA,EAAA6B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAELnD,OAAA;cAAKgD,SAAS,EAAC,uCAAuC;cAAAD,QAAA,gBACpD/C,OAAA;gBAAA+C,QAAA,gBACE/C,OAAA;kBAAOgD,SAAS,EAAC,8CAA8C;kBAAAD,QAAA,EAAC;gBAEhE;kBAAA3B,QAAA,EAAA6B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRnD,OAAA;kBACE8D,IAAI,EAAC,MAAM;kBACXC,KAAK,EAAE3D,QAAQ,CAACS,IAAK;kBACrBmC,SAAS,EAAC,yDAAyD;kBACnEgB,QAAQ;gBAAA;kBAAA5C,QAAA,EAAA6B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAA/B,QAAA,EAAA6B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENnD,OAAA;gBAAA+C,QAAA,gBACE/C,OAAA;kBAAOgD,SAAS,EAAC,8CAA8C;kBAAAD,QAAA,EAAC;gBAEhE;kBAAA3B,QAAA,EAAA6B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRnD,OAAA;kBACE8D,IAAI,EAAC,OAAO;kBACZC,KAAK,EAAE3D,QAAQ,CAACU,KAAM;kBACtBkC,SAAS,EAAC,yDAAyD;kBACnEgB,QAAQ;gBAAA;kBAAA5C,QAAA,EAAA6B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAA/B,QAAA,EAAA6B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENnD,OAAA;gBAAA+C,QAAA,gBACE/C,OAAA;kBAAOgD,SAAS,EAAC,8CAA8C;kBAAAD,QAAA,EAAC;gBAEhE;kBAAA3B,QAAA,EAAA6B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRnD,OAAA;kBACE8D,IAAI,EAAC,KAAK;kBACVC,KAAK,EAAE3D,QAAQ,CAACW,KAAM;kBACtBiC,SAAS,EAAC,yDAAyD;kBACnEgB,QAAQ;gBAAA;kBAAA5C,QAAA,EAAA6B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAA/B,QAAA,EAAA6B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENnD,OAAA;gBAAA+C,QAAA,gBACE/C,OAAA;kBAAOgD,SAAS,EAAC,8CAA8C;kBAAAD,QAAA,EAAC;gBAEhE;kBAAA3B,QAAA,EAAA6B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRnD,OAAA;kBAAKgD,SAAS,EAAC,6BAA6B;kBAAAD,QAAA,gBAC1C/C,OAAA;oBAAMgD,SAAS,EAAC,0EAA0E;oBAAAD,QAAA,EAAC;kBAE3F;oBAAA3B,QAAA,EAAA6B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACPnD,OAAA;oBAAMgD,SAAS,EAAC,uBAAuB;oBAAAD,QAAA,EAAC;kBAExC;oBAAA3B,QAAA,EAAA6B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAA/B,QAAA,EAAA6B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAA/B,QAAA,EAAA6B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAA/B,QAAA,EAAA6B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENnD,OAAA;cAAKgD,SAAS,EAAC,gCAAgC;cAAAD,QAAA,gBAC7C/C,OAAA;gBAAIgD,SAAS,EAAC,gCAAgC;gBAAAD,QAAA,EAAC;cAAkB;gBAAA3B,QAAA,EAAA6B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtEnD,OAAA;gBAAIgD,SAAS,EAAC,iCAAiC;gBAAAD,QAAA,gBAC7C/C,OAAA;kBAAA+C,QAAA,EAAI;gBAAqB;kBAAA3B,QAAA,EAAA6B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC9BnD,OAAA;kBAAA+C,QAAA,EAAI;gBAAqC;kBAAA3B,QAAA,EAAA6B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC9CnD,OAAA;kBAAA+C,QAAA,EAAI;gBAA0B;kBAAA3B,QAAA,EAAA6B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACnCnD,OAAA;kBAAA+C,QAAA,EAAI;gBAAoC;kBAAA3B,QAAA,EAAA6B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAA/B,QAAA,EAAA6B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC;YAAA;cAAA/B,QAAA,EAAA6B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAENnD,OAAA;cAAKgD,SAAS,EAAC,MAAM;cAAAD,QAAA,eACnB/C,OAAA,CAACF,MAAM;gBAAC8D,OAAO,EAAC,SAAS;gBAAAb,QAAA,EAAC;cAE1B;gBAAA3B,QAAA,EAAA6B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAA/B,QAAA,EAAA6B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAA/B,QAAA,EAAA6B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAA/B,QAAA,EAAA6B,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAA/B,QAAA,EAAA6B,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAA/B,QAAA,EAAA6B,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAA/B,QAAA,EAAA6B,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAACjD,EAAA,CAxVID,WAAqB;EAAA,QACRL,WAAW;AAAA;AAAAqE,EAAA,GADxBhE,WAAqB;AA0V3B,eAAeA,WAAW;AAAC,IAAAgE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}