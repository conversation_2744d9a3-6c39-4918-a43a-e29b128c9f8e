{"ast": null, "code": "import React from'react';import{BrowserRouter as Router,Routes,Route}from'react-router-dom';import LandingPage from'./pages/LandingPage';import UploadPage from'./pages/UploadPage';import PreviewPage from'./pages/PreviewPage';import OptionsForm from'./pages/OptionsForm';import OrderSummary from'./pages/OrderSummary';import CheckoutPage from'./pages/CheckoutPage';import ClientPanel from'./pages/ClientPanel';import AdminPanel from'./pages/AdminPanel';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const App=()=>/*#__PURE__*/_jsx(Router,{children:/*#__PURE__*/_jsxs(Routes,{children:[/*#__PURE__*/_jsx(Route,{path:\"/\",element:/*#__PURE__*/_jsx(LandingPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"/upload\",element:/*#__PURE__*/_jsx(UploadPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"/preview\",element:/*#__PURE__*/_jsx(PreviewPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"/options\",element:/*#__PURE__*/_jsx(OptionsForm,{})}),/*#__PURE__*/_jsx(Route,{path:\"/summary\",element:/*#__PURE__*/_jsx(OrderSummary,{})}),/*#__PURE__*/_jsx(Route,{path:\"/checkout\",element:/*#__PURE__*/_jsx(CheckoutPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"/painel\",element:/*#__PURE__*/_jsx(ClientPanel,{})}),/*#__PURE__*/_jsx(Route,{path:\"/admin\",element:/*#__PURE__*/_jsx(AdminPanel,{})})]})});export default App;", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "LandingPage", "UploadPage", "PreviewPage", "OptionsForm", "OrderSummary", "CheckoutPage", "ClientPanel", "AdminPanel", "jsx", "_jsx", "jsxs", "_jsxs", "App", "children", "path", "element"], "sources": ["/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport LandingPage from './pages/LandingPage';\nimport UploadPage from './pages/UploadPage';\nimport PreviewPage from './pages/PreviewPage';\nimport OptionsForm from './pages/OptionsForm';\nimport OrderSummary from './pages/OrderSummary';\nimport CheckoutPage from './pages/CheckoutPage';\nimport ClientPanel from './pages/ClientPanel';\nimport AdminPanel from './pages/AdminPanel';\n\nconst App: React.FC = () => (\n  <Router>\n    <Routes>\n      <Route path=\"/\" element={<LandingPage />} />\n      <Route path=\"/upload\" element={<UploadPage />} />\n      <Route path=\"/preview\" element={<PreviewPage />} />\n      <Route path=\"/options\" element={<OptionsForm />} />\n      <Route path=\"/summary\" element={<OrderSummary />} />\n      <Route path=\"/checkout\" element={<CheckoutPage />} />\n      <Route path=\"/painel\" element={<ClientPanel />} />\n      <Route path=\"/admin\" element={<AdminPanel />} />\n    </Routes>\n  </Router>\n);\n\nexport default App;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,aAAa,GAAI,CAAAC,MAAM,CAAEC,MAAM,CAAEC,KAAK,KAAQ,kBAAkB,CACzE,MAAO,CAAAC,WAAW,KAAM,qBAAqB,CAC7C,MAAO,CAAAC,UAAU,KAAM,oBAAoB,CAC3C,MAAO,CAAAC,WAAW,KAAM,qBAAqB,CAC7C,MAAO,CAAAC,WAAW,KAAM,qBAAqB,CAC7C,MAAO,CAAAC,YAAY,KAAM,sBAAsB,CAC/C,MAAO,CAAAC,YAAY,KAAM,sBAAsB,CAC/C,MAAO,CAAAC,WAAW,KAAM,qBAAqB,CAC7C,MAAO,CAAAC,UAAU,KAAM,oBAAoB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE5C,KAAM,CAAAC,GAAa,CAAGA,CAAA,gBACpBH,IAAA,CAACZ,MAAM,EAAAgB,QAAA,cACLF,KAAA,CAACb,MAAM,EAAAe,QAAA,eACLJ,IAAA,CAACV,KAAK,EAACe,IAAI,CAAC,GAAG,CAACC,OAAO,cAAEN,IAAA,CAACT,WAAW,GAAE,CAAE,CAAE,CAAC,cAC5CS,IAAA,CAACV,KAAK,EAACe,IAAI,CAAC,SAAS,CAACC,OAAO,cAAEN,IAAA,CAACR,UAAU,GAAE,CAAE,CAAE,CAAC,cACjDQ,IAAA,CAACV,KAAK,EAACe,IAAI,CAAC,UAAU,CAACC,OAAO,cAAEN,IAAA,CAACP,WAAW,GAAE,CAAE,CAAE,CAAC,cACnDO,IAAA,CAACV,KAAK,EAACe,IAAI,CAAC,UAAU,CAACC,OAAO,cAAEN,IAAA,CAACN,WAAW,GAAE,CAAE,CAAE,CAAC,cACnDM,IAAA,CAACV,KAAK,EAACe,IAAI,CAAC,UAAU,CAACC,OAAO,cAAEN,IAAA,CAACL,YAAY,GAAE,CAAE,CAAE,CAAC,cACpDK,IAAA,CAACV,KAAK,EAACe,IAAI,CAAC,WAAW,CAACC,OAAO,cAAEN,IAAA,CAACJ,YAAY,GAAE,CAAE,CAAE,CAAC,cACrDI,IAAA,CAACV,KAAK,EAACe,IAAI,CAAC,SAAS,CAACC,OAAO,cAAEN,IAAA,CAACH,WAAW,GAAE,CAAE,CAAE,CAAC,cAClDG,IAAA,CAACV,KAAK,EAACe,IAAI,CAAC,QAAQ,CAACC,OAAO,cAAEN,IAAA,CAACF,UAAU,GAAE,CAAE,CAAE,CAAC,EAC1C,CAAC,CACH,CACT,CAED,cAAe,CAAAK,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}