{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/OptionsForm.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { Layout, Button } from '../components';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst OptionsForm = () => {\n  _s();\n  const navigate = useNavigate();\n  const [uploadedFile, setUploadedFile] = useState(null);\n  const [options, setOptions] = useState({\n    format: 'A4',\n    paperType: 'standard',\n    finish: 'none',\n    copies: 1,\n    hasColor: false,\n    complexity: 'low',\n    notes: ''\n  });\n  const [priceBreakdown, setPriceBreakdown] = useState({\n    basePrice: 0,\n    paperCost: 0,\n    finishCost: 0,\n    complexityCost: 0,\n    colorCost: 0,\n    total: 0\n  });\n  useEffect(() => {\n    // Get uploaded file info from localStorage\n    const fileInfo = localStorage.getItem('uploadedFile');\n    if (fileInfo) {\n      setUploadedFile(JSON.parse(fileInfo));\n    } else {\n      // Redirect back to upload if no file\n      navigate('/upload');\n    }\n  }, [navigate]);\n  const calculatePrice = () => {\n    let basePrice = 2.5; // Base price per page in AOA\n\n    // Format pricing\n    const formatPrices = {\n      'A4': 1.0,\n      'A3': 2.0,\n      'A5': 0.8,\n      'Letter': 1.0\n    };\n\n    // Paper type pricing\n    const paperPrices = {\n      'standard': 1.0,\n      'premium': 1.5,\n      'photo': 2.0,\n      'cardstock': 1.8\n    };\n\n    // Finish pricing\n    const finishPrices = {\n      'none': 1.0,\n      'glossy': 1.3,\n      'matte': 1.2,\n      'laminated': 1.8\n    };\n\n    // Complexity pricing\n    const complexityPrices = {\n      'low': 1.0,\n      'medium': 1.3,\n      'high': 1.6\n    };\n    const formatMultiplier = formatPrices[options.format] || 1.0;\n    const paperMultiplier = paperPrices[options.paperType] || 1.0;\n    const finishMultiplier = finishPrices[options.finish] || 1.0;\n    const complexityMultiplier = complexityPrices[options.complexity] || 1.0;\n    const colorMultiplier = options.hasColor ? 1.5 : 1.0;\n    const paperCost = basePrice * formatMultiplier * (paperMultiplier - 1);\n    const finishCost = basePrice * formatMultiplier * (finishMultiplier - 1);\n    const complexityCost = basePrice * formatMultiplier * (complexityMultiplier - 1);\n    const colorCost = options.hasColor ? basePrice * formatMultiplier * 0.5 : 0;\n    const unitPrice = basePrice * formatMultiplier * paperMultiplier * finishMultiplier * complexityMultiplier * colorMultiplier;\n    const total = unitPrice * options.copies;\n    setPriceBreakdown({\n      basePrice: basePrice * formatMultiplier,\n      paperCost,\n      finishCost,\n      complexityCost,\n      colorCost,\n      total\n    });\n  };\n  const handleOptionChange = (field, value) => {\n    setOptions(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n  const handleContinue = () => {\n    // Store options in localStorage\n    localStorage.setItem('printOptions', JSON.stringify(options));\n    localStorage.setItem('priceBreakdown', JSON.stringify(priceBreakdown));\n    navigate('/summary');\n  };\n  const formatPrice = price => {\n    return `${price.toFixed(2)} AOA`;\n  };\n  if (!uploadedFile) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: \"Carregando...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 130,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Layout, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-4xl mx-auto py-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold text-gray-900 mb-4\",\n          children: \"Op\\xE7\\xF5es de Impress\\xE3o\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-gray-600\",\n          children: [\"Configure as op\\xE7\\xF5es para o seu documento: \", uploadedFile.name]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-lg p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-semibold text-gray-900 mb-6\",\n            children: \"Configura\\xE7\\xF5es\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Formato do Papel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 155,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: options.format,\n                onChange: e => handleOptionChange('format', e.target.value),\n                className: \"w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"A4\",\n                  children: \"A4 (210 \\xD7 297 mm)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 163,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"A3\",\n                  children: \"A3 (297 \\xD7 420 mm)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 164,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"A5\",\n                  children: \"A5 (148 \\xD7 210 mm)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 165,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Letter\",\n                  children: \"Letter (216 \\xD7 279 mm)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 166,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 158,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Tipo de Papel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: options.paperType,\n                onChange: e => handleOptionChange('paperType', e.target.value),\n                className: \"w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"standard\",\n                  children: \"Papel Standard (75g/m\\xB2)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 180,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"premium\",\n                  children: \"Papel Premium (90g/m\\xB2)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 181,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"photo\",\n                  children: \"Papel Fotogr\\xE1fico (200g/m\\xB2)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 182,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"cardstock\",\n                  children: \"Cartolina (250g/m\\xB2)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 183,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 175,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Acabamento\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: options.finish,\n                onChange: e => handleOptionChange('finish', e.target.value),\n                className: \"w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"none\",\n                  children: \"Sem Acabamento\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 197,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"glossy\",\n                  children: \"Brilhante\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 198,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"matte\",\n                  children: \"Fosco\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 199,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"laminated\",\n                  children: \"Plastificado\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 200,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"N\\xFAmero de C\\xF3pias\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                min: \"1\",\n                max: \"1000\",\n                value: options.copies,\n                onChange: e => handleOptionChange('copies', parseInt(e.target.value) || 1),\n                className: \"w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"flex items-center space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  checked: options.hasColor,\n                  onChange: e => handleOptionChange('hasColor', e.target.checked),\n                  className: \"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 222,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm font-medium text-gray-700\",\n                  children: \"Impress\\xE3o a Cores\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 228,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Complexidade do Documento\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: options.complexity,\n                onChange: e => handleOptionChange('complexity', e.target.value),\n                className: \"w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"low\",\n                  children: \"Baixa (Texto simples)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 244,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"medium\",\n                  children: \"M\\xE9dia (Texto + Imagens)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 245,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"high\",\n                  children: \"Alta (Design complexo)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 246,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 235,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Observa\\xE7\\xF5es (Opcional)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                value: options.notes,\n                onChange: e => handleOptionChange('notes', e.target.value),\n                rows: 3,\n                className: \"w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                placeholder: \"Instru\\xE7\\xF5es especiais para a impress\\xE3o...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 255,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-lg p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-semibold text-gray-900 mb-6\",\n            children: \"Resumo de Pre\\xE7os\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between text-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-gray-600\",\n                children: [\"Pre\\xE7o base (\", options.format, \"):\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 274,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: formatPrice(priceBreakdown.basePrice)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 15\n            }, this), priceBreakdown.paperCost > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between text-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-gray-600\",\n                children: \"Papel premium:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"+\", formatPrice(priceBreakdown.paperCost)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 17\n            }, this), priceBreakdown.finishCost > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between text-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-gray-600\",\n                children: \"Acabamento:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 287,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"+\", formatPrice(priceBreakdown.finishCost)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 17\n            }, this), priceBreakdown.complexityCost > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between text-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-gray-600\",\n                children: \"Complexidade:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 294,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"+\", formatPrice(priceBreakdown.complexityCost)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 295,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 293,\n              columnNumber: 17\n            }, this), priceBreakdown.colorCost > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between text-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-gray-600\",\n                children: \"Impress\\xE3o a cores:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"+\", formatPrice(priceBreakdown.colorCost)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between text-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-gray-600\",\n                children: \"Quantidade:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [options.copies, \" c\\xF3pia(s)\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 308,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n              className: \"my-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between text-lg font-semibold\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Total:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 314,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-blue-600\",\n                children: formatPrice(priceBreakdown.total)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 313,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-6 p-4 bg-blue-50 rounded-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"font-medium text-blue-900 mb-2\",\n              children: \"Informa\\xE7\\xF5es\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 320,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              className: \"text-sm text-blue-800 space-y-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\u2022 Entrega gr\\xE1tis em Luanda\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 322,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\u2022 Prazo: 2-5 dias \\xFAteis\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 323,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\u2022 Garantia de qualidade\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 324,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\u2022 Suporte t\\xE9cnico inclu\\xEDdo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 325,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 321,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 319,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between mt-8\",\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outline\",\n          onClick: () => navigate('/upload'),\n          children: \"Voltar\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 332,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleContinue,\n          children: \"Ver Resumo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 339,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 331,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 134,\n    columnNumber: 5\n  }, this);\n};\n_s(OptionsForm, \"zBb8sxi/6KQOOFluY/P9+EkW3mw=\", false, function () {\n  return [useNavigate];\n});\n_c = OptionsForm;\nexport default OptionsForm;\nvar _c;\n$RefreshReg$(_c, \"OptionsForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "Layout", "<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "OptionsForm", "_s", "navigate", "uploadedFile", "setUploadedFile", "options", "setOptions", "format", "paperType", "finish", "copies", "hasColor", "complexity", "notes", "priceBreakdown", "setPriceBreakdown", "basePrice", "paperCost", "finishCost", "complexityCost", "colorCost", "total", "fileInfo", "localStorage", "getItem", "JSON", "parse", "calculatePrice", "formatPrices", "paperPrices", "finishPrices", "complexityPrices", "formatMultiplier", "paperMultiplier", "finishMultiplier", "complexityMultiplier", "colorMultiplier", "unitPrice", "handleOptionChange", "field", "value", "prev", "handleContinue", "setItem", "stringify", "formatPrice", "price", "toFixed", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "name", "onChange", "e", "target", "type", "min", "max", "parseInt", "checked", "rows", "placeholder", "variant", "onClick", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/OptionsForm.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { Layout, Button } from '../components';\n\ninterface PrintOptions {\n  format: string;\n  paperType: string;\n  finish: string;\n  copies: number;\n  hasColor: boolean;\n  complexity: 'low' | 'medium' | 'high';\n  notes: string;\n}\n\ninterface PriceBreakdown {\n  basePrice: number;\n  paperCost: number;\n  finishCost: number;\n  complexityCost: number;\n  colorCost: number;\n  total: number;\n}\n\nconst OptionsForm: React.FC = () => {\n  const navigate = useNavigate();\n  const [uploadedFile, setUploadedFile] = useState<any>(null);\n  const [options, setOptions] = useState<PrintOptions>({\n    format: 'A4',\n    paperType: 'standard',\n    finish: 'none',\n    copies: 1,\n    hasColor: false,\n    complexity: 'low',\n    notes: '',\n  });\n  const [priceBreakdown, setPriceBreakdown] = useState<PriceBreakdown>({\n    basePrice: 0,\n    paperCost: 0,\n    finishCost: 0,\n    complexityCost: 0,\n    colorCost: 0,\n    total: 0,\n  });\n\n  useEffect(() => {\n    // Get uploaded file info from localStorage\n    const fileInfo = localStorage.getItem('uploadedFile');\n    if (fileInfo) {\n      setUploadedFile(JSON.parse(fileInfo));\n    } else {\n      // Redirect back to upload if no file\n      navigate('/upload');\n    }\n  }, [navigate]);\n\n  const calculatePrice = () => {\n    let basePrice = 2.5; // Base price per page in AOA\n\n    // Format pricing\n    const formatPrices: { [key: string]: number } = {\n      'A4': 1.0,\n      'A3': 2.0,\n      'A5': 0.8,\n      'Letter': 1.0,\n    };\n\n    // Paper type pricing\n    const paperPrices: { [key: string]: number } = {\n      'standard': 1.0,\n      'premium': 1.5,\n      'photo': 2.0,\n      'cardstock': 1.8,\n    };\n\n    // Finish pricing\n    const finishPrices: { [key: string]: number } = {\n      'none': 1.0,\n      'glossy': 1.3,\n      'matte': 1.2,\n      'laminated': 1.8,\n    };\n\n    // Complexity pricing\n    const complexityPrices: { [key: string]: number } = {\n      'low': 1.0,\n      'medium': 1.3,\n      'high': 1.6,\n    };\n\n    const formatMultiplier = formatPrices[options.format] || 1.0;\n    const paperMultiplier = paperPrices[options.paperType] || 1.0;\n    const finishMultiplier = finishPrices[options.finish] || 1.0;\n    const complexityMultiplier = complexityPrices[options.complexity] || 1.0;\n    const colorMultiplier = options.hasColor ? 1.5 : 1.0;\n\n    const paperCost = basePrice * formatMultiplier * (paperMultiplier - 1);\n    const finishCost = basePrice * formatMultiplier * (finishMultiplier - 1);\n    const complexityCost = basePrice * formatMultiplier * (complexityMultiplier - 1);\n    const colorCost = options.hasColor ? basePrice * formatMultiplier * 0.5 : 0;\n\n    const unitPrice = basePrice * formatMultiplier * paperMultiplier * finishMultiplier * complexityMultiplier * colorMultiplier;\n    const total = unitPrice * options.copies;\n\n    setPriceBreakdown({\n      basePrice: basePrice * formatMultiplier,\n      paperCost,\n      finishCost,\n      complexityCost,\n      colorCost,\n      total,\n    });\n  };\n\n  const handleOptionChange = (field: keyof PrintOptions, value: any) => {\n    setOptions(prev => ({ ...prev, [field]: value }));\n  };\n\n  const handleContinue = () => {\n    // Store options in localStorage\n    localStorage.setItem('printOptions', JSON.stringify(options));\n    localStorage.setItem('priceBreakdown', JSON.stringify(priceBreakdown));\n    navigate('/summary');\n  };\n\n  const formatPrice = (price: number) => {\n    return `${price.toFixed(2)} AOA`;\n  };\n\n  if (!uploadedFile) {\n    return <div>Carregando...</div>;\n  }\n\n  return (\n    <Layout>\n      <div className=\"max-w-4xl mx-auto py-8\">\n        <div className=\"text-center mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-4\">\n            Opções de Impressão\n          </h1>\n          <p className=\"text-lg text-gray-600\">\n            Configure as opções para o seu documento: {uploadedFile.name}\n          </p>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n          {/* Options Form */}\n          <div className=\"bg-white rounded-lg shadow-lg p-6\">\n            <h2 className=\"text-xl font-semibold text-gray-900 mb-6\">\n              Configurações\n            </h2>\n\n            <div className=\"space-y-6\">\n              {/* Format */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Formato do Papel\n                </label>\n                <select\n                  value={options.format}\n                  onChange={(e) => handleOptionChange('format', e.target.value)}\n                  className=\"w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                >\n                  <option value=\"A4\">A4 (210 × 297 mm)</option>\n                  <option value=\"A3\">A3 (297 × 420 mm)</option>\n                  <option value=\"A5\">A5 (148 × 210 mm)</option>\n                  <option value=\"Letter\">Letter (216 × 279 mm)</option>\n                </select>\n              </div>\n\n              {/* Paper Type */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Tipo de Papel\n                </label>\n                <select\n                  value={options.paperType}\n                  onChange={(e) => handleOptionChange('paperType', e.target.value)}\n                  className=\"w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                >\n                  <option value=\"standard\">Papel Standard (75g/m²)</option>\n                  <option value=\"premium\">Papel Premium (90g/m²)</option>\n                  <option value=\"photo\">Papel Fotográfico (200g/m²)</option>\n                  <option value=\"cardstock\">Cartolina (250g/m²)</option>\n                </select>\n              </div>\n\n              {/* Finish */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Acabamento\n                </label>\n                <select\n                  value={options.finish}\n                  onChange={(e) => handleOptionChange('finish', e.target.value)}\n                  className=\"w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                >\n                  <option value=\"none\">Sem Acabamento</option>\n                  <option value=\"glossy\">Brilhante</option>\n                  <option value=\"matte\">Fosco</option>\n                  <option value=\"laminated\">Plastificado</option>\n                </select>\n              </div>\n\n              {/* Copies */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Número de Cópias\n                </label>\n                <input\n                  type=\"number\"\n                  min=\"1\"\n                  max=\"1000\"\n                  value={options.copies}\n                  onChange={(e) => handleOptionChange('copies', parseInt(e.target.value) || 1)}\n                  className=\"w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                />\n              </div>\n\n              {/* Color */}\n              <div>\n                <label className=\"flex items-center space-x-3\">\n                  <input\n                    type=\"checkbox\"\n                    checked={options.hasColor}\n                    onChange={(e) => handleOptionChange('hasColor', e.target.checked)}\n                    className=\"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500\"\n                  />\n                  <span className=\"text-sm font-medium text-gray-700\">\n                    Impressão a Cores\n                  </span>\n                </label>\n              </div>\n\n              {/* Complexity */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Complexidade do Documento\n                </label>\n                <select\n                  value={options.complexity}\n                  onChange={(e) => handleOptionChange('complexity', e.target.value as 'low' | 'medium' | 'high')}\n                  className=\"w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                >\n                  <option value=\"low\">Baixa (Texto simples)</option>\n                  <option value=\"medium\">Média (Texto + Imagens)</option>\n                  <option value=\"high\">Alta (Design complexo)</option>\n                </select>\n              </div>\n\n              {/* Notes */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Observações (Opcional)\n                </label>\n                <textarea\n                  value={options.notes}\n                  onChange={(e) => handleOptionChange('notes', e.target.value)}\n                  rows={3}\n                  className=\"w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                  placeholder=\"Instruções especiais para a impressão...\"\n                />\n              </div>\n            </div>\n          </div>\n\n          {/* Price Breakdown */}\n          <div className=\"bg-white rounded-lg shadow-lg p-6\">\n            <h2 className=\"text-xl font-semibold text-gray-900 mb-6\">\n              Resumo de Preços\n            </h2>\n\n            <div className=\"space-y-4\">\n              <div className=\"flex justify-between text-sm\">\n                <span className=\"text-gray-600\">Preço base ({options.format}):</span>\n                <span>{formatPrice(priceBreakdown.basePrice)}</span>\n              </div>\n\n              {priceBreakdown.paperCost > 0 && (\n                <div className=\"flex justify-between text-sm\">\n                  <span className=\"text-gray-600\">Papel premium:</span>\n                  <span>+{formatPrice(priceBreakdown.paperCost)}</span>\n                </div>\n              )}\n\n              {priceBreakdown.finishCost > 0 && (\n                <div className=\"flex justify-between text-sm\">\n                  <span className=\"text-gray-600\">Acabamento:</span>\n                  <span>+{formatPrice(priceBreakdown.finishCost)}</span>\n                </div>\n              )}\n\n              {priceBreakdown.complexityCost > 0 && (\n                <div className=\"flex justify-between text-sm\">\n                  <span className=\"text-gray-600\">Complexidade:</span>\n                  <span>+{formatPrice(priceBreakdown.complexityCost)}</span>\n                </div>\n              )}\n\n              {priceBreakdown.colorCost > 0 && (\n                <div className=\"flex justify-between text-sm\">\n                  <span className=\"text-gray-600\">Impressão a cores:</span>\n                  <span>+{formatPrice(priceBreakdown.colorCost)}</span>\n                </div>\n              )}\n\n              <div className=\"flex justify-between text-sm\">\n                <span className=\"text-gray-600\">Quantidade:</span>\n                <span>{options.copies} cópia(s)</span>\n              </div>\n\n              <hr className=\"my-4\" />\n\n              <div className=\"flex justify-between text-lg font-semibold\">\n                <span>Total:</span>\n                <span className=\"text-blue-600\">{formatPrice(priceBreakdown.total)}</span>\n              </div>\n            </div>\n\n            <div className=\"mt-6 p-4 bg-blue-50 rounded-lg\">\n              <h3 className=\"font-medium text-blue-900 mb-2\">Informações</h3>\n              <ul className=\"text-sm text-blue-800 space-y-1\">\n                <li>• Entrega grátis em Luanda</li>\n                <li>• Prazo: 2-5 dias úteis</li>\n                <li>• Garantia de qualidade</li>\n                <li>• Suporte técnico incluído</li>\n              </ul>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"flex justify-between mt-8\">\n          <Button\n            variant=\"outline\"\n            onClick={() => navigate('/upload')}\n          >\n            Voltar\n          </Button>\n\n          <Button\n            onClick={handleContinue}\n          >\n            Ver Resumo\n          </Button>\n        </div>\n      </div>\n    </Layout>\n  );\n};\n\nexport default OptionsForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,MAAM,EAAEC,MAAM,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAqB/C,MAAMC,WAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClC,MAAMC,QAAQ,GAAGP,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACQ,YAAY,EAAEC,eAAe,CAAC,GAAGX,QAAQ,CAAM,IAAI,CAAC;EAC3D,MAAM,CAACY,OAAO,EAAEC,UAAU,CAAC,GAAGb,QAAQ,CAAe;IACnDc,MAAM,EAAE,IAAI;IACZC,SAAS,EAAE,UAAU;IACrBC,MAAM,EAAE,MAAM;IACdC,MAAM,EAAE,CAAC;IACTC,QAAQ,EAAE,KAAK;IACfC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGtB,QAAQ,CAAiB;IACnEuB,SAAS,EAAE,CAAC;IACZC,SAAS,EAAE,CAAC;IACZC,UAAU,EAAE,CAAC;IACbC,cAAc,EAAE,CAAC;IACjBC,SAAS,EAAE,CAAC;IACZC,KAAK,EAAE;EACT,CAAC,CAAC;EAEF3B,SAAS,CAAC,MAAM;IACd;IACA,MAAM4B,QAAQ,GAAGC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;IACrD,IAAIF,QAAQ,EAAE;MACZlB,eAAe,CAACqB,IAAI,CAACC,KAAK,CAACJ,QAAQ,CAAC,CAAC;IACvC,CAAC,MAAM;MACL;MACApB,QAAQ,CAAC,SAAS,CAAC;IACrB;EACF,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;EAEd,MAAMyB,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAIX,SAAS,GAAG,GAAG,CAAC,CAAC;;IAErB;IACA,MAAMY,YAAuC,GAAG;MAC9C,IAAI,EAAE,GAAG;MACT,IAAI,EAAE,GAAG;MACT,IAAI,EAAE,GAAG;MACT,QAAQ,EAAE;IACZ,CAAC;;IAED;IACA,MAAMC,WAAsC,GAAG;MAC7C,UAAU,EAAE,GAAG;MACf,SAAS,EAAE,GAAG;MACd,OAAO,EAAE,GAAG;MACZ,WAAW,EAAE;IACf,CAAC;;IAED;IACA,MAAMC,YAAuC,GAAG;MAC9C,MAAM,EAAE,GAAG;MACX,QAAQ,EAAE,GAAG;MACb,OAAO,EAAE,GAAG;MACZ,WAAW,EAAE;IACf,CAAC;;IAED;IACA,MAAMC,gBAA2C,GAAG;MAClD,KAAK,EAAE,GAAG;MACV,QAAQ,EAAE,GAAG;MACb,MAAM,EAAE;IACV,CAAC;IAED,MAAMC,gBAAgB,GAAGJ,YAAY,CAACvB,OAAO,CAACE,MAAM,CAAC,IAAI,GAAG;IAC5D,MAAM0B,eAAe,GAAGJ,WAAW,CAACxB,OAAO,CAACG,SAAS,CAAC,IAAI,GAAG;IAC7D,MAAM0B,gBAAgB,GAAGJ,YAAY,CAACzB,OAAO,CAACI,MAAM,CAAC,IAAI,GAAG;IAC5D,MAAM0B,oBAAoB,GAAGJ,gBAAgB,CAAC1B,OAAO,CAACO,UAAU,CAAC,IAAI,GAAG;IACxE,MAAMwB,eAAe,GAAG/B,OAAO,CAACM,QAAQ,GAAG,GAAG,GAAG,GAAG;IAEpD,MAAMM,SAAS,GAAGD,SAAS,GAAGgB,gBAAgB,IAAIC,eAAe,GAAG,CAAC,CAAC;IACtE,MAAMf,UAAU,GAAGF,SAAS,GAAGgB,gBAAgB,IAAIE,gBAAgB,GAAG,CAAC,CAAC;IACxE,MAAMf,cAAc,GAAGH,SAAS,GAAGgB,gBAAgB,IAAIG,oBAAoB,GAAG,CAAC,CAAC;IAChF,MAAMf,SAAS,GAAGf,OAAO,CAACM,QAAQ,GAAGK,SAAS,GAAGgB,gBAAgB,GAAG,GAAG,GAAG,CAAC;IAE3E,MAAMK,SAAS,GAAGrB,SAAS,GAAGgB,gBAAgB,GAAGC,eAAe,GAAGC,gBAAgB,GAAGC,oBAAoB,GAAGC,eAAe;IAC5H,MAAMf,KAAK,GAAGgB,SAAS,GAAGhC,OAAO,CAACK,MAAM;IAExCK,iBAAiB,CAAC;MAChBC,SAAS,EAAEA,SAAS,GAAGgB,gBAAgB;MACvCf,SAAS;MACTC,UAAU;MACVC,cAAc;MACdC,SAAS;MACTC;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMiB,kBAAkB,GAAGA,CAACC,KAAyB,EAAEC,KAAU,KAAK;IACpElC,UAAU,CAACmC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACF,KAAK,GAAGC;IAAM,CAAC,CAAC,CAAC;EACnD,CAAC;EAED,MAAME,cAAc,GAAGA,CAAA,KAAM;IAC3B;IACAnB,YAAY,CAACoB,OAAO,CAAC,cAAc,EAAElB,IAAI,CAACmB,SAAS,CAACvC,OAAO,CAAC,CAAC;IAC7DkB,YAAY,CAACoB,OAAO,CAAC,gBAAgB,EAAElB,IAAI,CAACmB,SAAS,CAAC9B,cAAc,CAAC,CAAC;IACtEZ,QAAQ,CAAC,UAAU,CAAC;EACtB,CAAC;EAED,MAAM2C,WAAW,GAAIC,KAAa,IAAK;IACrC,OAAO,GAAGA,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC,MAAM;EAClC,CAAC;EAED,IAAI,CAAC5C,YAAY,EAAE;IACjB,oBAAOJ,OAAA;MAAAiD,QAAA,EAAK;IAAa;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EACjC;EAEA,oBACErD,OAAA,CAACH,MAAM;IAAAoD,QAAA,eACLjD,OAAA;MAAKsD,SAAS,EAAC,wBAAwB;MAAAL,QAAA,gBACrCjD,OAAA;QAAKsD,SAAS,EAAC,kBAAkB;QAAAL,QAAA,gBAC/BjD,OAAA;UAAIsD,SAAS,EAAC,uCAAuC;UAAAL,QAAA,EAAC;QAEtD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLrD,OAAA;UAAGsD,SAAS,EAAC,uBAAuB;UAAAL,QAAA,GAAC,kDACO,EAAC7C,YAAY,CAACmD,IAAI;QAAA;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENrD,OAAA;QAAKsD,SAAS,EAAC,uCAAuC;QAAAL,QAAA,gBAEpDjD,OAAA;UAAKsD,SAAS,EAAC,mCAAmC;UAAAL,QAAA,gBAChDjD,OAAA;YAAIsD,SAAS,EAAC,0CAA0C;YAAAL,QAAA,EAAC;UAEzD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAELrD,OAAA;YAAKsD,SAAS,EAAC,WAAW;YAAAL,QAAA,gBAExBjD,OAAA;cAAAiD,QAAA,gBACEjD,OAAA;gBAAOsD,SAAS,EAAC,8CAA8C;gBAAAL,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRrD,OAAA;gBACEyC,KAAK,EAAEnC,OAAO,CAACE,MAAO;gBACtBgD,QAAQ,EAAGC,CAAC,IAAKlB,kBAAkB,CAAC,QAAQ,EAAEkB,CAAC,CAACC,MAAM,CAACjB,KAAK,CAAE;gBAC9Da,SAAS,EAAC,qGAAqG;gBAAAL,QAAA,gBAE/GjD,OAAA;kBAAQyC,KAAK,EAAC,IAAI;kBAAAQ,QAAA,EAAC;gBAAiB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC7CrD,OAAA;kBAAQyC,KAAK,EAAC,IAAI;kBAAAQ,QAAA,EAAC;gBAAiB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC7CrD,OAAA;kBAAQyC,KAAK,EAAC,IAAI;kBAAAQ,QAAA,EAAC;gBAAiB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC7CrD,OAAA;kBAAQyC,KAAK,EAAC,QAAQ;kBAAAQ,QAAA,EAAC;gBAAqB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAGNrD,OAAA;cAAAiD,QAAA,gBACEjD,OAAA;gBAAOsD,SAAS,EAAC,8CAA8C;gBAAAL,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRrD,OAAA;gBACEyC,KAAK,EAAEnC,OAAO,CAACG,SAAU;gBACzB+C,QAAQ,EAAGC,CAAC,IAAKlB,kBAAkB,CAAC,WAAW,EAAEkB,CAAC,CAACC,MAAM,CAACjB,KAAK,CAAE;gBACjEa,SAAS,EAAC,qGAAqG;gBAAAL,QAAA,gBAE/GjD,OAAA;kBAAQyC,KAAK,EAAC,UAAU;kBAAAQ,QAAA,EAAC;gBAAuB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACzDrD,OAAA;kBAAQyC,KAAK,EAAC,SAAS;kBAAAQ,QAAA,EAAC;gBAAsB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACvDrD,OAAA;kBAAQyC,KAAK,EAAC,OAAO;kBAAAQ,QAAA,EAAC;gBAA2B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC1DrD,OAAA;kBAAQyC,KAAK,EAAC,WAAW;kBAAAQ,QAAA,EAAC;gBAAmB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAGNrD,OAAA;cAAAiD,QAAA,gBACEjD,OAAA;gBAAOsD,SAAS,EAAC,8CAA8C;gBAAAL,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRrD,OAAA;gBACEyC,KAAK,EAAEnC,OAAO,CAACI,MAAO;gBACtB8C,QAAQ,EAAGC,CAAC,IAAKlB,kBAAkB,CAAC,QAAQ,EAAEkB,CAAC,CAACC,MAAM,CAACjB,KAAK,CAAE;gBAC9Da,SAAS,EAAC,qGAAqG;gBAAAL,QAAA,gBAE/GjD,OAAA;kBAAQyC,KAAK,EAAC,MAAM;kBAAAQ,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC5CrD,OAAA;kBAAQyC,KAAK,EAAC,QAAQ;kBAAAQ,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACzCrD,OAAA;kBAAQyC,KAAK,EAAC,OAAO;kBAAAQ,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpCrD,OAAA;kBAAQyC,KAAK,EAAC,WAAW;kBAAAQ,QAAA,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAGNrD,OAAA;cAAAiD,QAAA,gBACEjD,OAAA;gBAAOsD,SAAS,EAAC,8CAA8C;gBAAAL,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRrD,OAAA;gBACE2D,IAAI,EAAC,QAAQ;gBACbC,GAAG,EAAC,GAAG;gBACPC,GAAG,EAAC,MAAM;gBACVpB,KAAK,EAAEnC,OAAO,CAACK,MAAO;gBACtB6C,QAAQ,EAAGC,CAAC,IAAKlB,kBAAkB,CAAC,QAAQ,EAAEuB,QAAQ,CAACL,CAAC,CAACC,MAAM,CAACjB,KAAK,CAAC,IAAI,CAAC,CAAE;gBAC7Ea,SAAS,EAAC;cAAqG;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGNrD,OAAA;cAAAiD,QAAA,eACEjD,OAAA;gBAAOsD,SAAS,EAAC,6BAA6B;gBAAAL,QAAA,gBAC5CjD,OAAA;kBACE2D,IAAI,EAAC,UAAU;kBACfI,OAAO,EAAEzD,OAAO,CAACM,QAAS;kBAC1B4C,QAAQ,EAAGC,CAAC,IAAKlB,kBAAkB,CAAC,UAAU,EAAEkB,CAAC,CAACC,MAAM,CAACK,OAAO,CAAE;kBAClET,SAAS,EAAC;gBAAmE;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9E,CAAC,eACFrD,OAAA;kBAAMsD,SAAS,EAAC,mCAAmC;kBAAAL,QAAA,EAAC;gBAEpD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAGNrD,OAAA;cAAAiD,QAAA,gBACEjD,OAAA;gBAAOsD,SAAS,EAAC,8CAA8C;gBAAAL,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRrD,OAAA;gBACEyC,KAAK,EAAEnC,OAAO,CAACO,UAAW;gBAC1B2C,QAAQ,EAAGC,CAAC,IAAKlB,kBAAkB,CAAC,YAAY,EAAEkB,CAAC,CAACC,MAAM,CAACjB,KAAkC,CAAE;gBAC/Fa,SAAS,EAAC,qGAAqG;gBAAAL,QAAA,gBAE/GjD,OAAA;kBAAQyC,KAAK,EAAC,KAAK;kBAAAQ,QAAA,EAAC;gBAAqB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClDrD,OAAA;kBAAQyC,KAAK,EAAC,QAAQ;kBAAAQ,QAAA,EAAC;gBAAuB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACvDrD,OAAA;kBAAQyC,KAAK,EAAC,MAAM;kBAAAQ,QAAA,EAAC;gBAAsB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAGNrD,OAAA;cAAAiD,QAAA,gBACEjD,OAAA;gBAAOsD,SAAS,EAAC,8CAA8C;gBAAAL,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRrD,OAAA;gBACEyC,KAAK,EAAEnC,OAAO,CAACQ,KAAM;gBACrB0C,QAAQ,EAAGC,CAAC,IAAKlB,kBAAkB,CAAC,OAAO,EAAEkB,CAAC,CAACC,MAAM,CAACjB,KAAK,CAAE;gBAC7DuB,IAAI,EAAE,CAAE;gBACRV,SAAS,EAAC,qGAAqG;gBAC/GW,WAAW,EAAC;cAA0C;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNrD,OAAA;UAAKsD,SAAS,EAAC,mCAAmC;UAAAL,QAAA,gBAChDjD,OAAA;YAAIsD,SAAS,EAAC,0CAA0C;YAAAL,QAAA,EAAC;UAEzD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAELrD,OAAA;YAAKsD,SAAS,EAAC,WAAW;YAAAL,QAAA,gBACxBjD,OAAA;cAAKsD,SAAS,EAAC,8BAA8B;cAAAL,QAAA,gBAC3CjD,OAAA;gBAAMsD,SAAS,EAAC,eAAe;gBAAAL,QAAA,GAAC,iBAAY,EAAC3C,OAAO,CAACE,MAAM,EAAC,IAAE;cAAA;gBAAA0C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACrErD,OAAA;gBAAAiD,QAAA,EAAOH,WAAW,CAAC/B,cAAc,CAACE,SAAS;cAAC;gBAAAiC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD,CAAC,EAELtC,cAAc,CAACG,SAAS,GAAG,CAAC,iBAC3BlB,OAAA;cAAKsD,SAAS,EAAC,8BAA8B;cAAAL,QAAA,gBAC3CjD,OAAA;gBAAMsD,SAAS,EAAC,eAAe;gBAAAL,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACrDrD,OAAA;gBAAAiD,QAAA,GAAM,GAAC,EAACH,WAAW,CAAC/B,cAAc,CAACG,SAAS,CAAC;cAAA;gBAAAgC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CACN,EAEAtC,cAAc,CAACI,UAAU,GAAG,CAAC,iBAC5BnB,OAAA;cAAKsD,SAAS,EAAC,8BAA8B;cAAAL,QAAA,gBAC3CjD,OAAA;gBAAMsD,SAAS,EAAC,eAAe;gBAAAL,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAClDrD,OAAA;gBAAAiD,QAAA,GAAM,GAAC,EAACH,WAAW,CAAC/B,cAAc,CAACI,UAAU,CAAC;cAAA;gBAAA+B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CACN,EAEAtC,cAAc,CAACK,cAAc,GAAG,CAAC,iBAChCpB,OAAA;cAAKsD,SAAS,EAAC,8BAA8B;cAAAL,QAAA,gBAC3CjD,OAAA;gBAAMsD,SAAS,EAAC,eAAe;gBAAAL,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACpDrD,OAAA;gBAAAiD,QAAA,GAAM,GAAC,EAACH,WAAW,CAAC/B,cAAc,CAACK,cAAc,CAAC;cAAA;gBAAA8B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CACN,EAEAtC,cAAc,CAACM,SAAS,GAAG,CAAC,iBAC3BrB,OAAA;cAAKsD,SAAS,EAAC,8BAA8B;cAAAL,QAAA,gBAC3CjD,OAAA;gBAAMsD,SAAS,EAAC,eAAe;gBAAAL,QAAA,EAAC;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzDrD,OAAA;gBAAAiD,QAAA,GAAM,GAAC,EAACH,WAAW,CAAC/B,cAAc,CAACM,SAAS,CAAC;cAAA;gBAAA6B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CACN,eAEDrD,OAAA;cAAKsD,SAAS,EAAC,8BAA8B;cAAAL,QAAA,gBAC3CjD,OAAA;gBAAMsD,SAAS,EAAC,eAAe;gBAAAL,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAClDrD,OAAA;gBAAAiD,QAAA,GAAO3C,OAAO,CAACK,MAAM,EAAC,cAAS;cAAA;gBAAAuC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC,eAENrD,OAAA;cAAIsD,SAAS,EAAC;YAAM;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAEvBrD,OAAA;cAAKsD,SAAS,EAAC,4CAA4C;cAAAL,QAAA,gBACzDjD,OAAA;gBAAAiD,QAAA,EAAM;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACnBrD,OAAA;gBAAMsD,SAAS,EAAC,eAAe;gBAAAL,QAAA,EAAEH,WAAW,CAAC/B,cAAc,CAACO,KAAK;cAAC;gBAAA4B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENrD,OAAA;YAAKsD,SAAS,EAAC,gCAAgC;YAAAL,QAAA,gBAC7CjD,OAAA;cAAIsD,SAAS,EAAC,gCAAgC;cAAAL,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/DrD,OAAA;cAAIsD,SAAS,EAAC,iCAAiC;cAAAL,QAAA,gBAC7CjD,OAAA;gBAAAiD,QAAA,EAAI;cAA0B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnCrD,OAAA;gBAAAiD,QAAA,EAAI;cAAuB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChCrD,OAAA;gBAAAiD,QAAA,EAAI;cAAuB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChCrD,OAAA;gBAAAiD,QAAA,EAAI;cAA0B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENrD,OAAA;QAAKsD,SAAS,EAAC,2BAA2B;QAAAL,QAAA,gBACxCjD,OAAA,CAACF,MAAM;UACLoE,OAAO,EAAC,SAAS;UACjBC,OAAO,EAAEA,CAAA,KAAMhE,QAAQ,CAAC,SAAS,CAAE;UAAA8C,QAAA,EACpC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAETrD,OAAA,CAACF,MAAM;UACLqE,OAAO,EAAExB,cAAe;UAAAM,QAAA,EACzB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAACnD,EAAA,CApUID,WAAqB;EAAA,QACRL,WAAW;AAAA;AAAAwE,EAAA,GADxBnE,WAAqB;AAsU3B,eAAeA,WAAW;AAAC,IAAAmE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}