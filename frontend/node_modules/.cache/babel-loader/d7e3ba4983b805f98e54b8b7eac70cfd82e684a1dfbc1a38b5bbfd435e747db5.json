{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/UploadPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { Layout, Button } from '../components';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UploadPage = () => {\n  _s();\n  const navigate = useNavigate();\n  const fileInputRef = useRef(null);\n  const [uploadedFile, setUploadedFile] = useState(null);\n  const [isDragging, setIsDragging] = useState(false);\n  const [isUploading, setIsUploading] = useState(false);\n  const [uploadProgress, setUploadProgress] = useState(0);\n  const formatFileSize = bytes => {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  };\n  const validateFile = file => {\n    const allowedTypes = ['application/pdf', 'image/jpeg', 'image/png', 'image/jpg'];\n    const maxSize = 10 * 1024 * 1024; // 10MB\n\n    if (!allowedTypes.includes(file.type)) {\n      return 'Tipo de arquivo não suportado. Use PDF, JPG ou PNG.';\n    }\n    if (file.size > maxSize) {\n      return 'Arquivo muito grande. Máximo 10MB.';\n    }\n    return null;\n  };\n  const handleFileSelect = async file => {\n    const error = validateFile(file);\n    if (error) {\n      alert(error);\n      return;\n    }\n    setIsUploading(true);\n    setUploadProgress(0);\n    try {\n      // Simulate upload progress\n      const progressInterval = setInterval(() => {\n        setUploadProgress(prev => {\n          if (prev >= 90) {\n            clearInterval(progressInterval);\n            return 90;\n          }\n          return prev + 10;\n        });\n      }, 200);\n\n      // Create file info\n      const fileInfo = {\n        file,\n        size: formatFileSize(file.size)\n      };\n\n      // If it's an image, create preview\n      if (file.type.startsWith('image/')) {\n        const reader = new FileReader();\n        reader.onload = e => {\n          var _e$target;\n          fileInfo.preview = (_e$target = e.target) === null || _e$target === void 0 ? void 0 : _e$target.result;\n          setUploadedFile(fileInfo);\n        };\n        reader.readAsDataURL(file);\n      } else {\n        setUploadedFile(fileInfo);\n      }\n\n      // Complete upload\n      setTimeout(() => {\n        setUploadProgress(100);\n        setIsUploading(false);\n      }, 1000);\n    } catch (error) {\n      console.error('Upload error:', error);\n      alert('Erro no upload. Tente novamente.');\n      setIsUploading(false);\n      setUploadProgress(0);\n    }\n  };\n  const handleDrop = e => {\n    e.preventDefault();\n    setIsDragging(false);\n    const files = Array.from(e.dataTransfer.files);\n    if (files.length > 0) {\n      handleFileSelect(files[0]);\n    }\n  };\n  const handleDragOver = e => {\n    e.preventDefault();\n    setIsDragging(true);\n  };\n  const handleDragLeave = e => {\n    e.preventDefault();\n    setIsDragging(false);\n  };\n  const handleFileInputChange = e => {\n    const files = e.target.files;\n    if (files && files.length > 0) {\n      handleFileSelect(files[0]);\n    }\n  };\n  const handleContinue = () => {\n    if (uploadedFile) {\n      // Store file info in localStorage for next step\n      localStorage.setItem('uploadedFile', JSON.stringify({\n        name: uploadedFile.file.name,\n        size: uploadedFile.size,\n        type: uploadedFile.file.type\n      }));\n      navigate('/options');\n    }\n  };\n  const handleRemoveFile = () => {\n    setUploadedFile(null);\n    setUploadProgress(0);\n    if (fileInputRef.current) {\n      fileInputRef.current.value = '';\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Layout, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-4xl mx-auto py-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold text-gray-900 mb-4\",\n          children: \"Upload do Seu Documento\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-gray-600\",\n          children: \"Fa\\xE7a upload do arquivo que deseja imprimir\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 9\n      }, this), !uploadedFile ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `border-2 border-dashed rounded-lg p-8 text-center transition-colors ${isDragging ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-gray-400'}`,\n          onDrop: handleDrop,\n          onDragOver: handleDragOver,\n          onDragLeave: handleDragLeave,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"mx-auto h-12 w-12 text-gray-400\",\n              stroke: \"currentColor\",\n              fill: \"none\",\n              viewBox: \"0 0 48 48\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02\",\n                strokeWidth: 2,\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 175,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-lg font-medium text-gray-900 mb-2\",\n              children: \"Arraste e solte o seu arquivo aqui\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"ou\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: () => {\n              var _fileInputRef$current;\n              return (_fileInputRef$current = fileInputRef.current) === null || _fileInputRef$current === void 0 ? void 0 : _fileInputRef$current.click();\n            },\n            className: \"mb-4\",\n            children: \"Escolher Arquivo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            ref: fileInputRef,\n            type: \"file\",\n            className: \"hidden\",\n            accept: \".pdf,.jpg,.jpeg,.png\",\n            onChange: handleFileInputChange\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-gray-500\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Formatos suportados: PDF, JPG, PNG\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Tamanho m\\xE1ximo: 10MB\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-lg p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start justify-between mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900\",\n              children: \"Arquivo Carregado\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleRemoveFile,\n              className: \"text-red-600 hover:text-red-700\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-5 h-5\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 20 20\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  fillRule: \"evenodd\",\n                  d: \"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\",\n                  clipRule: \"evenodd\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 224,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 223,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [uploadedFile.preview ? /*#__PURE__*/_jsxDEV(\"img\", {\n              src: uploadedFile.preview,\n              alt: \"Preview\",\n              className: \"w-16 h-16 object-cover rounded\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-16 h-16 bg-red-100 rounded flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-8 h-8 text-red-600\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 20 20\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  fillRule: \"evenodd\",\n                  d: \"M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z\",\n                  clipRule: \"evenodd\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 239,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"font-medium text-gray-900\",\n                children: uploadedFile.file.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600\",\n                children: uploadedFile.size\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 246,\n                columnNumber: 19\n              }, this), uploadedFile.pages && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600\",\n                children: [uploadedFile.pages, \" p\\xE1ginas\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-green-600\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-6 h-6\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 20 20\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  fillRule: \"evenodd\",\n                  d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                  clipRule: \"evenodd\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 254,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 253,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 213,\n        columnNumber: 11\n      }, this), isUploading && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow p-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between mb-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-medium text-gray-700\",\n              children: \"Fazendo upload...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm text-gray-600\",\n              children: [uploadProgress, \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-full bg-gray-200 rounded-full h-2\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-blue-600 h-2 rounded-full transition-all duration-300\",\n              style: {\n                width: `${uploadProgress}%`\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 263,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outline\",\n          onClick: () => navigate('/'),\n          children: \"Voltar\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 282,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleContinue,\n          disabled: !uploadedFile || isUploading,\n          children: \"Continuar\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 289,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 281,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 146,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 145,\n    columnNumber: 5\n  }, this);\n};\n_s(UploadPage, \"Kf45d+nYI5J/iSo7G7whZ9FYsqg=\", false, function () {\n  return [useNavigate];\n});\n_c = UploadPage;\nexport default UploadPage;\nvar _c;\n$RefreshReg$(_c, \"UploadPage\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useNavigate", "Layout", "<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "UploadPage", "_s", "navigate", "fileInputRef", "uploadedFile", "setUploadedFile", "isDragging", "setIsDragging", "isUploading", "setIsUploading", "uploadProgress", "setUploadProgress", "formatFileSize", "bytes", "k", "sizes", "i", "Math", "floor", "log", "parseFloat", "pow", "toFixed", "validateFile", "file", "allowedTypes", "maxSize", "includes", "type", "size", "handleFileSelect", "error", "alert", "progressInterval", "setInterval", "prev", "clearInterval", "fileInfo", "startsWith", "reader", "FileReader", "onload", "e", "_e$target", "preview", "target", "result", "readAsDataURL", "setTimeout", "console", "handleDrop", "preventDefault", "files", "Array", "from", "dataTransfer", "length", "handleDragOver", "handleDragLeave", "handleFileInputChange", "handleContinue", "localStorage", "setItem", "JSON", "stringify", "name", "handleRemoveFile", "current", "value", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onDrop", "onDragOver", "onDragLeave", "stroke", "fill", "viewBox", "d", "strokeWidth", "strokeLinecap", "strokeLinejoin", "onClick", "_fileInputRef$current", "click", "ref", "accept", "onChange", "fillRule", "clipRule", "src", "alt", "pages", "style", "width", "variant", "disabled", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/UploadPage.tsx"], "sourcesContent": ["import React, { useState, useRef } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { Layout, Button } from '../components';\n\ninterface FileInfo {\n  file: File;\n  preview?: string;\n  pages?: number;\n  size: string;\n}\n\nconst UploadPage: React.FC = () => {\n  const navigate = useNavigate();\n  const fileInputRef = useRef<HTMLInputElement>(null);\n  const [uploadedFile, setUploadedFile] = useState<FileInfo | null>(null);\n  const [isDragging, setIsDragging] = useState(false);\n  const [isUploading, setIsUploading] = useState(false);\n  const [uploadProgress, setUploadProgress] = useState(0);\n\n  const formatFileSize = (bytes: number): string => {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  };\n\n  const validateFile = (file: File): string | null => {\n    const allowedTypes = ['application/pdf', 'image/jpeg', 'image/png', 'image/jpg'];\n    const maxSize = 10 * 1024 * 1024; // 10MB\n\n    if (!allowedTypes.includes(file.type)) {\n      return 'Tipo de arquivo não suportado. Use PDF, JPG ou PNG.';\n    }\n\n    if (file.size > maxSize) {\n      return 'Arquivo muito grande. Máximo 10MB.';\n    }\n\n    return null;\n  };\n\n  const handleFileSelect = async (file: File) => {\n    const error = validateFile(file);\n    if (error) {\n      alert(error);\n      return;\n    }\n\n    setIsUploading(true);\n    setUploadProgress(0);\n\n    try {\n      // Simulate upload progress\n      const progressInterval = setInterval(() => {\n        setUploadProgress(prev => {\n          if (prev >= 90) {\n            clearInterval(progressInterval);\n            return 90;\n          }\n          return prev + 10;\n        });\n      }, 200);\n\n      // Create file info\n      const fileInfo: FileInfo = {\n        file,\n        size: formatFileSize(file.size),\n      };\n\n      // If it's an image, create preview\n      if (file.type.startsWith('image/')) {\n        const reader = new FileReader();\n        reader.onload = (e) => {\n          fileInfo.preview = e.target?.result as string;\n          setUploadedFile(fileInfo);\n        };\n        reader.readAsDataURL(file);\n      } else {\n        setUploadedFile(fileInfo);\n      }\n\n      // Complete upload\n      setTimeout(() => {\n        setUploadProgress(100);\n        setIsUploading(false);\n      }, 1000);\n\n    } catch (error) {\n      console.error('Upload error:', error);\n      alert('Erro no upload. Tente novamente.');\n      setIsUploading(false);\n      setUploadProgress(0);\n    }\n  };\n\n  const handleDrop = (e: React.DragEvent) => {\n    e.preventDefault();\n    setIsDragging(false);\n\n    const files = Array.from(e.dataTransfer.files);\n    if (files.length > 0) {\n      handleFileSelect(files[0]);\n    }\n  };\n\n  const handleDragOver = (e: React.DragEvent) => {\n    e.preventDefault();\n    setIsDragging(true);\n  };\n\n  const handleDragLeave = (e: React.DragEvent) => {\n    e.preventDefault();\n    setIsDragging(false);\n  };\n\n  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const files = e.target.files;\n    if (files && files.length > 0) {\n      handleFileSelect(files[0]);\n    }\n  };\n\n  const handleContinue = () => {\n    if (uploadedFile) {\n      // Store file info in localStorage for next step\n      localStorage.setItem('uploadedFile', JSON.stringify({\n        name: uploadedFile.file.name,\n        size: uploadedFile.size,\n        type: uploadedFile.file.type,\n      }));\n      navigate('/options');\n    }\n  };\n\n  const handleRemoveFile = () => {\n    setUploadedFile(null);\n    setUploadProgress(0);\n    if (fileInputRef.current) {\n      fileInputRef.current.value = '';\n    }\n  };\n\n  return (\n    <Layout>\n      <div className=\"max-w-4xl mx-auto py-8\">\n        <div className=\"text-center mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-4\">\n            Upload do Seu Documento\n          </h1>\n          <p className=\"text-lg text-gray-600\">\n            Faça upload do arquivo que deseja imprimir\n          </p>\n        </div>\n\n        {!uploadedFile ? (\n          <div className=\"mb-8\">\n            <div\n              className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${\n                isDragging\n                  ? 'border-blue-500 bg-blue-50'\n                  : 'border-gray-300 hover:border-gray-400'\n              }`}\n              onDrop={handleDrop}\n              onDragOver={handleDragOver}\n              onDragLeave={handleDragLeave}\n            >\n              <div className=\"mb-4\">\n                <svg\n                  className=\"mx-auto h-12 w-12 text-gray-400\"\n                  stroke=\"currentColor\"\n                  fill=\"none\"\n                  viewBox=\"0 0 48 48\"\n                >\n                  <path\n                    d=\"M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02\"\n                    strokeWidth={2}\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                  />\n                </svg>\n              </div>\n\n              <div className=\"mb-4\">\n                <p className=\"text-lg font-medium text-gray-900 mb-2\">\n                  Arraste e solte o seu arquivo aqui\n                </p>\n                <p className=\"text-gray-600\">ou</p>\n              </div>\n\n              <Button\n                onClick={() => fileInputRef.current?.click()}\n                className=\"mb-4\"\n              >\n                Escolher Arquivo\n              </Button>\n\n              <input\n                ref={fileInputRef}\n                type=\"file\"\n                className=\"hidden\"\n                accept=\".pdf,.jpg,.jpeg,.png\"\n                onChange={handleFileInputChange}\n              />\n\n              <div className=\"text-sm text-gray-500\">\n                <p>Formatos suportados: PDF, JPG, PNG</p>\n                <p>Tamanho máximo: 10MB</p>\n              </div>\n            </div>\n          </div>\n        ) : (\n          <div className=\"mb-8\">\n            <div className=\"bg-white rounded-lg shadow-lg p-6\">\n              <div className=\"flex items-start justify-between mb-4\">\n                <h3 className=\"text-lg font-semibold text-gray-900\">\n                  Arquivo Carregado\n                </h3>\n                <button\n                  onClick={handleRemoveFile}\n                  className=\"text-red-600 hover:text-red-700\"\n                >\n                  <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\" clipRule=\"evenodd\" />\n                  </svg>\n                </button>\n              </div>\n\n              <div className=\"flex items-center space-x-4\">\n                {uploadedFile.preview ? (\n                  <img\n                    src={uploadedFile.preview}\n                    alt=\"Preview\"\n                    className=\"w-16 h-16 object-cover rounded\"\n                  />\n                ) : (\n                  <div className=\"w-16 h-16 bg-red-100 rounded flex items-center justify-center\">\n                    <svg className=\"w-8 h-8 text-red-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                      <path fillRule=\"evenodd\" d=\"M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z\" clipRule=\"evenodd\" />\n                    </svg>\n                  </div>\n                )}\n\n                <div className=\"flex-1\">\n                  <p className=\"font-medium text-gray-900\">{uploadedFile.file.name}</p>\n                  <p className=\"text-sm text-gray-600\">{uploadedFile.size}</p>\n                  {uploadedFile.pages && (\n                    <p className=\"text-sm text-gray-600\">{uploadedFile.pages} páginas</p>\n                  )}\n                </div>\n\n                <div className=\"text-green-600\">\n                  <svg className=\"w-6 h-6\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n                  </svg>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {isUploading && (\n          <div className=\"mb-8\">\n            <div className=\"bg-white rounded-lg shadow p-4\">\n              <div className=\"flex items-center justify-between mb-2\">\n                <span className=\"text-sm font-medium text-gray-700\">\n                  Fazendo upload...\n                </span>\n                <span className=\"text-sm text-gray-600\">{uploadProgress}%</span>\n              </div>\n              <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                <div\n                  className=\"bg-blue-600 h-2 rounded-full transition-all duration-300\"\n                  style={{ width: `${uploadProgress}%` }}\n                />\n              </div>\n            </div>\n          </div>\n        )}\n\n        <div className=\"flex justify-between\">\n          <Button\n            variant=\"outline\"\n            onClick={() => navigate('/')}\n          >\n            Voltar\n          </Button>\n\n          <Button\n            onClick={handleContinue}\n            disabled={!uploadedFile || isUploading}\n          >\n            Continuar\n          </Button>\n        </div>\n      </div>\n    </Layout>\n  );\n};\n\nexport default UploadPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC/C,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,MAAM,EAAEC,MAAM,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAS/C,MAAMC,UAAoB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjC,MAAMC,QAAQ,GAAGP,WAAW,CAAC,CAAC;EAC9B,MAAMQ,YAAY,GAAGT,MAAM,CAAmB,IAAI,CAAC;EACnD,MAAM,CAACU,YAAY,EAAEC,eAAe,CAAC,GAAGZ,QAAQ,CAAkB,IAAI,CAAC;EACvE,MAAM,CAACa,UAAU,EAAEC,aAAa,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACe,WAAW,EAAEC,cAAc,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACiB,cAAc,EAAEC,iBAAiB,CAAC,GAAGlB,QAAQ,CAAC,CAAC,CAAC;EAEvD,MAAMmB,cAAc,GAAIC,KAAa,IAAa;IAChD,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,SAAS;IACjC,MAAMC,CAAC,GAAG,IAAI;IACd,MAAMC,KAAK,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACzC,MAAMC,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAACN,KAAK,CAAC,GAAGI,IAAI,CAACE,GAAG,CAACL,CAAC,CAAC,CAAC;IACnD,OAAOM,UAAU,CAAC,CAACP,KAAK,GAAGI,IAAI,CAACI,GAAG,CAACP,CAAC,EAAEE,CAAC,CAAC,EAAEM,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGP,KAAK,CAACC,CAAC,CAAC;EACzE,CAAC;EAED,MAAMO,YAAY,GAAIC,IAAU,IAAoB;IAClD,MAAMC,YAAY,GAAG,CAAC,iBAAiB,EAAE,YAAY,EAAE,WAAW,EAAE,WAAW,CAAC;IAChF,MAAMC,OAAO,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;;IAElC,IAAI,CAACD,YAAY,CAACE,QAAQ,CAACH,IAAI,CAACI,IAAI,CAAC,EAAE;MACrC,OAAO,qDAAqD;IAC9D;IAEA,IAAIJ,IAAI,CAACK,IAAI,GAAGH,OAAO,EAAE;MACvB,OAAO,oCAAoC;IAC7C;IAEA,OAAO,IAAI;EACb,CAAC;EAED,MAAMI,gBAAgB,GAAG,MAAON,IAAU,IAAK;IAC7C,MAAMO,KAAK,GAAGR,YAAY,CAACC,IAAI,CAAC;IAChC,IAAIO,KAAK,EAAE;MACTC,KAAK,CAACD,KAAK,CAAC;MACZ;IACF;IAEAtB,cAAc,CAAC,IAAI,CAAC;IACpBE,iBAAiB,CAAC,CAAC,CAAC;IAEpB,IAAI;MACF;MACA,MAAMsB,gBAAgB,GAAGC,WAAW,CAAC,MAAM;QACzCvB,iBAAiB,CAACwB,IAAI,IAAI;UACxB,IAAIA,IAAI,IAAI,EAAE,EAAE;YACdC,aAAa,CAACH,gBAAgB,CAAC;YAC/B,OAAO,EAAE;UACX;UACA,OAAOE,IAAI,GAAG,EAAE;QAClB,CAAC,CAAC;MACJ,CAAC,EAAE,GAAG,CAAC;;MAEP;MACA,MAAME,QAAkB,GAAG;QACzBb,IAAI;QACJK,IAAI,EAAEjB,cAAc,CAACY,IAAI,CAACK,IAAI;MAChC,CAAC;;MAED;MACA,IAAIL,IAAI,CAACI,IAAI,CAACU,UAAU,CAAC,QAAQ,CAAC,EAAE;QAClC,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;QAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAC,IAAK;UAAA,IAAAC,SAAA;UACrBN,QAAQ,CAACO,OAAO,IAAAD,SAAA,GAAGD,CAAC,CAACG,MAAM,cAAAF,SAAA,uBAARA,SAAA,CAAUG,MAAgB;UAC7CzC,eAAe,CAACgC,QAAQ,CAAC;QAC3B,CAAC;QACDE,MAAM,CAACQ,aAAa,CAACvB,IAAI,CAAC;MAC5B,CAAC,MAAM;QACLnB,eAAe,CAACgC,QAAQ,CAAC;MAC3B;;MAEA;MACAW,UAAU,CAAC,MAAM;QACfrC,iBAAiB,CAAC,GAAG,CAAC;QACtBF,cAAc,CAAC,KAAK,CAAC;MACvB,CAAC,EAAE,IAAI,CAAC;IAEV,CAAC,CAAC,OAAOsB,KAAK,EAAE;MACdkB,OAAO,CAAClB,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrCC,KAAK,CAAC,kCAAkC,CAAC;MACzCvB,cAAc,CAAC,KAAK,CAAC;MACrBE,iBAAiB,CAAC,CAAC,CAAC;IACtB;EACF,CAAC;EAED,MAAMuC,UAAU,GAAIR,CAAkB,IAAK;IACzCA,CAAC,CAACS,cAAc,CAAC,CAAC;IAClB5C,aAAa,CAAC,KAAK,CAAC;IAEpB,MAAM6C,KAAK,GAAGC,KAAK,CAACC,IAAI,CAACZ,CAAC,CAACa,YAAY,CAACH,KAAK,CAAC;IAC9C,IAAIA,KAAK,CAACI,MAAM,GAAG,CAAC,EAAE;MACpB1B,gBAAgB,CAACsB,KAAK,CAAC,CAAC,CAAC,CAAC;IAC5B;EACF,CAAC;EAED,MAAMK,cAAc,GAAIf,CAAkB,IAAK;IAC7CA,CAAC,CAACS,cAAc,CAAC,CAAC;IAClB5C,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAMmD,eAAe,GAAIhB,CAAkB,IAAK;IAC9CA,CAAC,CAACS,cAAc,CAAC,CAAC;IAClB5C,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC;EAED,MAAMoD,qBAAqB,GAAIjB,CAAsC,IAAK;IACxE,MAAMU,KAAK,GAAGV,CAAC,CAACG,MAAM,CAACO,KAAK;IAC5B,IAAIA,KAAK,IAAIA,KAAK,CAACI,MAAM,GAAG,CAAC,EAAE;MAC7B1B,gBAAgB,CAACsB,KAAK,CAAC,CAAC,CAAC,CAAC;IAC5B;EACF,CAAC;EAED,MAAMQ,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAIxD,YAAY,EAAE;MAChB;MACAyD,YAAY,CAACC,OAAO,CAAC,cAAc,EAAEC,IAAI,CAACC,SAAS,CAAC;QAClDC,IAAI,EAAE7D,YAAY,CAACoB,IAAI,CAACyC,IAAI;QAC5BpC,IAAI,EAAEzB,YAAY,CAACyB,IAAI;QACvBD,IAAI,EAAExB,YAAY,CAACoB,IAAI,CAACI;MAC1B,CAAC,CAAC,CAAC;MACH1B,QAAQ,CAAC,UAAU,CAAC;IACtB;EACF,CAAC;EAED,MAAMgE,gBAAgB,GAAGA,CAAA,KAAM;IAC7B7D,eAAe,CAAC,IAAI,CAAC;IACrBM,iBAAiB,CAAC,CAAC,CAAC;IACpB,IAAIR,YAAY,CAACgE,OAAO,EAAE;MACxBhE,YAAY,CAACgE,OAAO,CAACC,KAAK,GAAG,EAAE;IACjC;EACF,CAAC;EAED,oBACErE,OAAA,CAACH,MAAM;IAAAyE,QAAA,eACLtE,OAAA;MAAKuE,SAAS,EAAC,wBAAwB;MAAAD,QAAA,gBACrCtE,OAAA;QAAKuE,SAAS,EAAC,kBAAkB;QAAAD,QAAA,gBAC/BtE,OAAA;UAAIuE,SAAS,EAAC,uCAAuC;UAAAD,QAAA,EAAC;QAEtD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL3E,OAAA;UAAGuE,SAAS,EAAC,uBAAuB;UAAAD,QAAA,EAAC;QAErC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,EAEL,CAACtE,YAAY,gBACZL,OAAA;QAAKuE,SAAS,EAAC,MAAM;QAAAD,QAAA,eACnBtE,OAAA;UACEuE,SAAS,EAAE,uEACThE,UAAU,GACN,4BAA4B,GAC5B,uCAAuC,EAC1C;UACHqE,MAAM,EAAEzB,UAAW;UACnB0B,UAAU,EAAEnB,cAAe;UAC3BoB,WAAW,EAAEnB,eAAgB;UAAAW,QAAA,gBAE7BtE,OAAA;YAAKuE,SAAS,EAAC,MAAM;YAAAD,QAAA,eACnBtE,OAAA;cACEuE,SAAS,EAAC,iCAAiC;cAC3CQ,MAAM,EAAC,cAAc;cACrBC,IAAI,EAAC,MAAM;cACXC,OAAO,EAAC,WAAW;cAAAX,QAAA,eAEnBtE,OAAA;gBACEkF,CAAC,EAAC,wLAAwL;gBAC1LC,WAAW,EAAE,CAAE;gBACfC,aAAa,EAAC,OAAO;gBACrBC,cAAc,EAAC;cAAO;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN3E,OAAA;YAAKuE,SAAS,EAAC,MAAM;YAAAD,QAAA,gBACnBtE,OAAA;cAAGuE,SAAS,EAAC,wCAAwC;cAAAD,QAAA,EAAC;YAEtD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJ3E,OAAA;cAAGuE,SAAS,EAAC,eAAe;cAAAD,QAAA,EAAC;YAAE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC,eAEN3E,OAAA,CAACF,MAAM;YACLwF,OAAO,EAAEA,CAAA;cAAA,IAAAC,qBAAA;cAAA,QAAAA,qBAAA,GAAMnF,YAAY,CAACgE,OAAO,cAAAmB,qBAAA,uBAApBA,qBAAA,CAAsBC,KAAK,CAAC,CAAC;YAAA,CAAC;YAC7CjB,SAAS,EAAC,MAAM;YAAAD,QAAA,EACjB;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAET3E,OAAA;YACEyF,GAAG,EAAErF,YAAa;YAClByB,IAAI,EAAC,MAAM;YACX0C,SAAS,EAAC,QAAQ;YAClBmB,MAAM,EAAC,sBAAsB;YAC7BC,QAAQ,EAAE/B;UAAsB;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,eAEF3E,OAAA;YAAKuE,SAAS,EAAC,uBAAuB;YAAAD,QAAA,gBACpCtE,OAAA;cAAAsE,QAAA,EAAG;YAAkC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACzC3E,OAAA;cAAAsE,QAAA,EAAG;YAAoB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,gBAEN3E,OAAA;QAAKuE,SAAS,EAAC,MAAM;QAAAD,QAAA,eACnBtE,OAAA;UAAKuE,SAAS,EAAC,mCAAmC;UAAAD,QAAA,gBAChDtE,OAAA;YAAKuE,SAAS,EAAC,uCAAuC;YAAAD,QAAA,gBACpDtE,OAAA;cAAIuE,SAAS,EAAC,qCAAqC;cAAAD,QAAA,EAAC;YAEpD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL3E,OAAA;cACEsF,OAAO,EAAEnB,gBAAiB;cAC1BI,SAAS,EAAC,iCAAiC;cAAAD,QAAA,eAE3CtE,OAAA;gBAAKuE,SAAS,EAAC,SAAS;gBAACS,IAAI,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAAX,QAAA,eAC9DtE,OAAA;kBAAM4F,QAAQ,EAAC,SAAS;kBAACV,CAAC,EAAC,oMAAoM;kBAACW,QAAQ,EAAC;gBAAS;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAEN3E,OAAA;YAAKuE,SAAS,EAAC,6BAA6B;YAAAD,QAAA,GACzCjE,YAAY,CAACwC,OAAO,gBACnB7C,OAAA;cACE8F,GAAG,EAAEzF,YAAY,CAACwC,OAAQ;cAC1BkD,GAAG,EAAC,SAAS;cACbxB,SAAS,EAAC;YAAgC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC,gBAEF3E,OAAA;cAAKuE,SAAS,EAAC,+DAA+D;cAAAD,QAAA,eAC5EtE,OAAA;gBAAKuE,SAAS,EAAC,sBAAsB;gBAACS,IAAI,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAAX,QAAA,eAC3EtE,OAAA;kBAAM4F,QAAQ,EAAC,SAAS;kBAACV,CAAC,EAAC,oLAAoL;kBAACW,QAAQ,EAAC;gBAAS;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN,eAED3E,OAAA;cAAKuE,SAAS,EAAC,QAAQ;cAAAD,QAAA,gBACrBtE,OAAA;gBAAGuE,SAAS,EAAC,2BAA2B;gBAAAD,QAAA,EAAEjE,YAAY,CAACoB,IAAI,CAACyC;cAAI;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrE3E,OAAA;gBAAGuE,SAAS,EAAC,uBAAuB;gBAAAD,QAAA,EAAEjE,YAAY,CAACyB;cAAI;gBAAA0C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,EAC3DtE,YAAY,CAAC2F,KAAK,iBACjBhG,OAAA;gBAAGuE,SAAS,EAAC,uBAAuB;gBAAAD,QAAA,GAAEjE,YAAY,CAAC2F,KAAK,EAAC,aAAQ;cAAA;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CACrE;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAEN3E,OAAA;cAAKuE,SAAS,EAAC,gBAAgB;cAAAD,QAAA,eAC7BtE,OAAA;gBAAKuE,SAAS,EAAC,SAAS;gBAACS,IAAI,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAAX,QAAA,eAC9DtE,OAAA;kBAAM4F,QAAQ,EAAC,SAAS;kBAACV,CAAC,EAAC,uIAAuI;kBAACW,QAAQ,EAAC;gBAAS;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAEAlE,WAAW,iBACVT,OAAA;QAAKuE,SAAS,EAAC,MAAM;QAAAD,QAAA,eACnBtE,OAAA;UAAKuE,SAAS,EAAC,gCAAgC;UAAAD,QAAA,gBAC7CtE,OAAA;YAAKuE,SAAS,EAAC,wCAAwC;YAAAD,QAAA,gBACrDtE,OAAA;cAAMuE,SAAS,EAAC,mCAAmC;cAAAD,QAAA,EAAC;YAEpD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACP3E,OAAA;cAAMuE,SAAS,EAAC,uBAAuB;cAAAD,QAAA,GAAE3D,cAAc,EAAC,GAAC;YAAA;cAAA6D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D,CAAC,eACN3E,OAAA;YAAKuE,SAAS,EAAC,qCAAqC;YAAAD,QAAA,eAClDtE,OAAA;cACEuE,SAAS,EAAC,0DAA0D;cACpE0B,KAAK,EAAE;gBAAEC,KAAK,EAAE,GAAGvF,cAAc;cAAI;YAAE;cAAA6D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAED3E,OAAA;QAAKuE,SAAS,EAAC,sBAAsB;QAAAD,QAAA,gBACnCtE,OAAA,CAACF,MAAM;UACLqG,OAAO,EAAC,SAAS;UACjBb,OAAO,EAAEA,CAAA,KAAMnF,QAAQ,CAAC,GAAG,CAAE;UAAAmE,QAAA,EAC9B;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAET3E,OAAA,CAACF,MAAM;UACLwF,OAAO,EAAEzB,cAAe;UACxBuC,QAAQ,EAAE,CAAC/F,YAAY,IAAII,WAAY;UAAA6D,QAAA,EACxC;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAACzE,EAAA,CA/RID,UAAoB;EAAA,QACPL,WAAW;AAAA;AAAAyG,EAAA,GADxBpG,UAAoB;AAiS1B,eAAeA,UAAU;AAAC,IAAAoG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}