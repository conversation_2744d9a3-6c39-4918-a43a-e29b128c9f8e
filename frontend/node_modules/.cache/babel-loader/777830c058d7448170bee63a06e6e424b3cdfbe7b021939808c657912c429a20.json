{"ast": null, "code": "import _objectSpread from\"/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState}from'react';import{useNavigate}from'react-router-dom';import{PaymentFlow}from'../components';import{MulticaixaService}from'../services/multicaixa';import{apiService}from'../services/api';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const CheckoutPage=()=>{const navigate=useNavigate();const[step,setStep]=useState('form');const[formData,setFormData]=useState({name:'',deliveryAddress:'',phone:'',email:'',paymentMethod:'CASH_ON_DELIVERY'});const[orderId,setOrderId]=useState('');const[orderAmount,setOrderAmount]=useState(0);// Load order amount from localStorage on component mount\nReact.useEffect(()=>{const storedOptions=localStorage.getItem('printOptions');if(storedOptions){try{const options=JSON.parse(storedOptions);// Calculate total based on stored options\nconst basePrice=50;// Base price per page in AOA\nconst pages=options.pages||1;const copies=options.copies||1;const colorMultiplier=options.color==='color'?2:1;const qualityMultiplier=options.quality==='high'?1.5:1;const total=Math.round(basePrice*pages*copies*colorMultiplier*qualityMultiplier);setOrderAmount(total);}catch(error){console.error('Error parsing stored options:',error);setOrderAmount(100);// Fallback amount\n}}else{setOrderAmount(100);// Default amount if no options stored\n}},[]);const handleInputChange=e=>{const{name,value}=e.target;setFormData(prev=>_objectSpread(_objectSpread({},prev),{},{[name]:value}));};const handleSubmitForm=async e=>{e.preventDefault();// Validate form\nif(!formData.name||!formData.phone||!formData.email){alert('Por favor, preencha todos os campos obrigatórios.');return;}try{// Get file and print options from localStorage\nconst backendFileInfo=localStorage.getItem('backendFileInfo');const printOptions=localStorage.getItem('printOptions');if(!backendFileInfo||!printOptions){alert('Dados do arquivo ou opções de impressão não encontrados. Reinicie o processo.');navigate('/upload');return;}const fileInfo=JSON.parse(backendFileInfo);const options=JSON.parse(printOptions);// Create order through backend API\nconst orderData={fileId:fileInfo.id,customerName:formData.name,customerEmail:formData.email,customerPhone:formData.phone,format:options.format,paperType:options.paperType,finish:options.finish,copies:options.copies,notes:\"Endere\\xE7o de entrega: \".concat(formData.deliveryAddress)};const orderResponse=await apiService.post('/orders',orderData);if(!orderResponse.success){throw new Error(orderResponse.error||'Erro ao criar pedido');}const createdOrder=orderResponse.data;setOrderId(createdOrder.id.toString());// Store order info for payment\nlocalStorage.setItem('currentOrder',JSON.stringify(createdOrder));// Handle payment method\nif(formData.paymentMethod==='MULTICAIXA_EXPRESS'){setStep('payment');}else if(formData.paymentMethod==='CASH_ON_DELIVERY'){// For cash on delivery, go directly to success\nsetStep('success');}else{// Handle other payment methods\nalert('Método de pagamento não implementado ainda.');}}catch(error){console.error('Error creating order:',error);alert(\"Erro ao criar pedido: \".concat(error instanceof Error?error.message:'Tente novamente.'));}};const handlePaymentComplete=paymentId=>{console.log('Payment completed:',paymentId);setStep('success');};const handlePaymentFailed=error=>{console.error('Payment failed:',error);alert(\"Erro no pagamento: \".concat(error));setStep('form');};const handleBackToForm=()=>{setStep('form');};const handleGoHome=()=>{navigate('/');};// Form Step\nif(step==='form'){return/*#__PURE__*/_jsx(\"div\",{className:\"min-h-screen bg-gray-50 py-8\",children:/*#__PURE__*/_jsx(\"div\",{className:\"max-w-md mx-auto\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-lg shadow-lg p-6\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-semibold text-gray-900 mb-6 text-center\",children:\"Finalizar Pedido\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"mb-6 p-4 bg-blue-50 rounded-lg\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"font-medium text-blue-900 mb-2\",children:\"Resumo do Pedido\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between text-sm text-blue-800\",children:[/*#__PURE__*/_jsx(\"span\",{children:\"Total:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"font-semibold\",children:MulticaixaService.formatAoaAmount(orderAmount)})]})]}),/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleSubmitForm,className:\"space-y-4\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-1\",children:\"Nome Completo *\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",name:\"name\",value:formData.name,onChange:handleInputChange,className:\"w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",placeholder:\"Seu nome completo\",required:true})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-1\",children:\"Email *\"}),/*#__PURE__*/_jsx(\"input\",{type:\"email\",name:\"email\",value:formData.email,onChange:handleInputChange,className:\"w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",placeholder:\"<EMAIL>\",required:true})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-1\",children:\"Telefone *\"}),/*#__PURE__*/_jsx(\"input\",{type:\"tel\",name:\"phone\",value:formData.phone,onChange:handleInputChange,className:\"w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",placeholder:\"+244 900 000 000\",required:true})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-1\",children:\"Endere\\xE7o de Entrega\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",name:\"deliveryAddress\",value:formData.deliveryAddress,onChange:handleInputChange,className:\"w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",placeholder:\"Endere\\xE7o para entrega (opcional)\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-1\",children:\"M\\xE9todo de Pagamento\"}),/*#__PURE__*/_jsxs(\"select\",{name:\"paymentMethod\",value:formData.paymentMethod,onChange:handleInputChange,className:\"w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"CASH_ON_DELIVERY\",children:\"\\uD83D\\uDCB0 Pagamento na Entrega (Recomendado)\"}),/*#__PURE__*/_jsx(\"option\",{value:\"MULTICAIXA_EXPRESS\",children:\"\\uD83D\\uDCF1 Multicaixa Express\"}),/*#__PURE__*/_jsx(\"option\",{value:\"BANK_TRANSFER\",children:\"\\uD83C\\uDFE6 Transfer\\xEAncia Banc\\xE1ria\"})]})]}),/*#__PURE__*/_jsx(\"button\",{type:\"submit\",className:\"w-full bg-blue-600 text-white py-3 px-4 rounded-md font-medium hover:bg-blue-700 transition-colors\",children:formData.paymentMethod==='CASH_ON_DELIVERY'?'Finalizar Pedido':'Continuar para Pagamento'})]})]})})});}// Payment Step\nif(step==='payment'){return/*#__PURE__*/_jsx(\"div\",{className:\"min-h-screen bg-gray-50 py-8\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"max-w-md mx-auto\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"mb-4\",children:/*#__PURE__*/_jsxs(\"button\",{onClick:handleBackToForm,className:\"flex items-center gap-2 text-blue-600 hover:text-blue-700\",children:[/*#__PURE__*/_jsx(\"svg\",{className:\"w-4 h-4\",fill:\"currentColor\",viewBox:\"0 0 20 20\",children:/*#__PURE__*/_jsx(\"path\",{fillRule:\"evenodd\",d:\"M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z\",clipRule:\"evenodd\"})}),\"Voltar\"]})}),/*#__PURE__*/_jsx(PaymentFlow,{orderId:orderId,amount:orderAmount,currency:\"AOA\",description:\"Servi\\xE7os de Impress\\xE3o WePrint AI\",customerEmail:formData.email,customerPhone:formData.phone,onPaymentComplete:handlePaymentComplete,onPaymentFailed:handlePaymentFailed,onCancel:handleBackToForm})]})});}// Success Step\nif(step==='success'){return/*#__PURE__*/_jsx(\"div\",{className:\"min-h-screen bg-gray-50 py-8\",children:/*#__PURE__*/_jsx(\"div\",{className:\"max-w-md mx-auto\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-lg shadow-lg p-6 text-center\",children:[/*#__PURE__*/_jsx(\"svg\",{className:\"w-20 h-20 text-green-500 mx-auto mb-4\",fill:\"currentColor\",viewBox:\"0 0 20 20\",children:/*#__PURE__*/_jsx(\"path\",{fillRule:\"evenodd\",d:\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",clipRule:\"evenodd\"})}),/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-semibold text-gray-900 mb-2\",children:\"Pedido Confirmado!\"}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-gray-600 mb-2\",children:[\"Obrigado, \",formData.name,\"!\"]}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-500 mb-6\",children:formData.paymentMethod==='CASH_ON_DELIVERY'?'O seu pedido foi registado com sucesso. Prepare o pagamento para a entrega. Receberá uma confirmação por email em breve.':'O seu pedido foi processado com sucesso. Receberá uma confirmação por email em breve.'}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-3\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:handleGoHome,className:\"w-full bg-blue-600 text-white py-3 px-4 rounded-md font-medium hover:bg-blue-700 transition-colors\",children:\"Voltar ao In\\xEDcio\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>navigate('/orders'),className:\"w-full border border-gray-300 text-gray-700 py-3 px-4 rounded-md font-medium hover:bg-gray-50 transition-colors\",children:\"Ver Meus Pedidos\"})]})]})})});}return null;};export default CheckoutPage;", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "PaymentFlow", "MulticaixaService", "apiService", "jsx", "_jsx", "jsxs", "_jsxs", "CheckoutPage", "navigate", "step", "setStep", "formData", "setFormData", "name", "deliveryAddress", "phone", "email", "paymentMethod", "orderId", "setOrderId", "orderAmount", "setOrderAmount", "useEffect", "storedOptions", "localStorage", "getItem", "options", "JSON", "parse", "basePrice", "pages", "copies", "colorMultiplier", "color", "qualityMultiplier", "quality", "total", "Math", "round", "error", "console", "handleInputChange", "e", "value", "target", "prev", "_objectSpread", "handleSubmitForm", "preventDefault", "alert", "backendFileInfo", "printOptions", "fileInfo", "orderData", "fileId", "id", "customerName", "customerEmail", "customerPhone", "format", "paperType", "finish", "notes", "concat", "orderResponse", "post", "success", "Error", "createdOrder", "data", "toString", "setItem", "stringify", "message", "handlePaymentComplete", "paymentId", "log", "handlePaymentFailed", "handleBackToForm", "handleGoHome", "className", "children", "formatAoaAmount", "onSubmit", "type", "onChange", "placeholder", "required", "onClick", "fill", "viewBox", "fillRule", "d", "clipRule", "amount", "currency", "description", "onPaymentComplete", "onPaymentFailed", "onCancel"], "sources": ["/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/CheckoutPage.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { PaymentFlow } from '../components';\nimport { MulticaixaService } from '../services/multicaixa';\nimport { apiService } from '../services/api';\n\ninterface CheckoutFormData {\n  name: string;\n  deliveryAddress: string;\n  phone: string;\n  email: string;\n  paymentMethod: 'MULTICAIXA_EXPRESS' | 'CASH_ON_DELIVERY' | 'BANK_TRANSFER';\n}\n\nconst CheckoutPage: React.FC = () => {\n  const navigate = useNavigate();\n  const [step, setStep] = useState<'form' | 'payment' | 'success'>('form');\n  const [formData, setFormData] = useState<CheckoutFormData>({\n    name: '',\n    deliveryAddress: '',\n    phone: '',\n    email: '',\n    paymentMethod: 'CASH_ON_DELIVERY',\n  });\n  const [orderId, setOrderId] = useState<string>('');\n  const [orderAmount, setOrderAmount] = useState<number>(0);\n\n  // Load order amount from localStorage on component mount\n  React.useEffect(() => {\n    const storedOptions = localStorage.getItem('printOptions');\n    if (storedOptions) {\n      try {\n        const options = JSON.parse(storedOptions);\n        // Calculate total based on stored options\n        const basePrice = 50; // Base price per page in AOA\n        const pages = options.pages || 1;\n        const copies = options.copies || 1;\n        const colorMultiplier = options.color === 'color' ? 2 : 1;\n        const qualityMultiplier = options.quality === 'high' ? 1.5 : 1;\n\n        const total = Math.round(basePrice * pages * copies * colorMultiplier * qualityMultiplier);\n        setOrderAmount(total);\n      } catch (error) {\n        console.error('Error parsing stored options:', error);\n        setOrderAmount(100); // Fallback amount\n      }\n    } else {\n      setOrderAmount(100); // Default amount if no options stored\n    }\n  }, []);\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({ ...prev, [name]: value }));\n  };\n\n  const handleSubmitForm = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    // Validate form\n    if (!formData.name || !formData.phone || !formData.email) {\n      alert('Por favor, preencha todos os campos obrigatórios.');\n      return;\n    }\n\n    try {\n      // Get file and print options from localStorage\n      const backendFileInfo = localStorage.getItem('backendFileInfo');\n      const printOptions = localStorage.getItem('printOptions');\n\n      if (!backendFileInfo || !printOptions) {\n        alert('Dados do arquivo ou opções de impressão não encontrados. Reinicie o processo.');\n        navigate('/upload');\n        return;\n      }\n\n      const fileInfo = JSON.parse(backendFileInfo);\n      const options = JSON.parse(printOptions);\n\n      // Create order through backend API\n      const orderData = {\n        fileId: fileInfo.id,\n        customerName: formData.name,\n        customerEmail: formData.email,\n        customerPhone: formData.phone,\n        format: options.format,\n        paperType: options.paperType,\n        finish: options.finish,\n        copies: options.copies,\n        notes: `Endereço de entrega: ${formData.deliveryAddress}`\n      };\n\n      const orderResponse = await apiService.post('/orders', orderData);\n\n      if (!orderResponse.success) {\n        throw new Error(orderResponse.error || 'Erro ao criar pedido');\n      }\n\n      const createdOrder = orderResponse.data as any;\n      setOrderId(createdOrder.id.toString());\n\n      // Store order info for payment\n      localStorage.setItem('currentOrder', JSON.stringify(createdOrder));\n\n      // Handle payment method\n      if (formData.paymentMethod === 'MULTICAIXA_EXPRESS') {\n        setStep('payment');\n      } else if (formData.paymentMethod === 'CASH_ON_DELIVERY') {\n        // For cash on delivery, go directly to success\n        setStep('success');\n      } else {\n        // Handle other payment methods\n        alert('Método de pagamento não implementado ainda.');\n      }\n    } catch (error) {\n      console.error('Error creating order:', error);\n      alert(`Erro ao criar pedido: ${error instanceof Error ? error.message : 'Tente novamente.'}`);\n    }\n  };\n\n  const handlePaymentComplete = (paymentId: string) => {\n    console.log('Payment completed:', paymentId);\n    setStep('success');\n  };\n\n  const handlePaymentFailed = (error: string) => {\n    console.error('Payment failed:', error);\n    alert(`Erro no pagamento: ${error}`);\n    setStep('form');\n  };\n\n  const handleBackToForm = () => {\n    setStep('form');\n  };\n\n  const handleGoHome = () => {\n    navigate('/');\n  };\n\n  // Form Step\n  if (step === 'form') {\n    return (\n      <div className=\"min-h-screen bg-gray-50 py-8\">\n        <div className=\"max-w-md mx-auto\">\n          <div className=\"bg-white rounded-lg shadow-lg p-6\">\n            <h2 className=\"text-2xl font-semibold text-gray-900 mb-6 text-center\">\n              Finalizar Pedido\n            </h2>\n\n            {/* Order Summary */}\n            <div className=\"mb-6 p-4 bg-blue-50 rounded-lg\">\n              <h3 className=\"font-medium text-blue-900 mb-2\">Resumo do Pedido</h3>\n              <div className=\"flex justify-between text-sm text-blue-800\">\n                <span>Total:</span>\n                <span className=\"font-semibold\">\n                  {MulticaixaService.formatAoaAmount(orderAmount)}\n                </span>\n              </div>\n            </div>\n\n            <form onSubmit={handleSubmitForm} className=\"space-y-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Nome Completo *\n                </label>\n                <input\n                  type=\"text\"\n                  name=\"name\"\n                  value={formData.name}\n                  onChange={handleInputChange}\n                  className=\"w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                  placeholder=\"Seu nome completo\"\n                  required\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Email *\n                </label>\n                <input\n                  type=\"email\"\n                  name=\"email\"\n                  value={formData.email}\n                  onChange={handleInputChange}\n                  className=\"w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                  placeholder=\"<EMAIL>\"\n                  required\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Telefone *\n                </label>\n                <input\n                  type=\"tel\"\n                  name=\"phone\"\n                  value={formData.phone}\n                  onChange={handleInputChange}\n                  className=\"w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                  placeholder=\"+244 900 000 000\"\n                  required\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Endereço de Entrega\n                </label>\n                <input\n                  type=\"text\"\n                  name=\"deliveryAddress\"\n                  value={formData.deliveryAddress}\n                  onChange={handleInputChange}\n                  className=\"w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                  placeholder=\"Endereço para entrega (opcional)\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Método de Pagamento\n                </label>\n                <select\n                  name=\"paymentMethod\"\n                  value={formData.paymentMethod}\n                  onChange={handleInputChange}\n                  className=\"w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                >\n                  <option value=\"CASH_ON_DELIVERY\">💰 Pagamento na Entrega (Recomendado)</option>\n                  <option value=\"MULTICAIXA_EXPRESS\">📱 Multicaixa Express</option>\n                  <option value=\"BANK_TRANSFER\">🏦 Transferência Bancária</option>\n                </select>\n              </div>\n\n              <button\n                type=\"submit\"\n                className=\"w-full bg-blue-600 text-white py-3 px-4 rounded-md font-medium hover:bg-blue-700 transition-colors\"\n              >\n                {formData.paymentMethod === 'CASH_ON_DELIVERY' ? 'Finalizar Pedido' : 'Continuar para Pagamento'}\n              </button>\n            </form>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  // Payment Step\n  if (step === 'payment') {\n    return (\n      <div className=\"min-h-screen bg-gray-50 py-8\">\n        <div className=\"max-w-md mx-auto\">\n          <div className=\"mb-4\">\n            <button\n              onClick={handleBackToForm}\n              className=\"flex items-center gap-2 text-blue-600 hover:text-blue-700\"\n            >\n              <svg className=\"w-4 h-4\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                <path fillRule=\"evenodd\" d=\"M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z\" clipRule=\"evenodd\" />\n              </svg>\n              Voltar\n            </button>\n          </div>\n\n          <PaymentFlow\n            orderId={orderId}\n            amount={orderAmount}\n            currency=\"AOA\"\n            description=\"Serviços de Impressão WePrint AI\"\n            customerEmail={formData.email}\n            customerPhone={formData.phone}\n            onPaymentComplete={handlePaymentComplete}\n            onPaymentFailed={handlePaymentFailed}\n            onCancel={handleBackToForm}\n          />\n        </div>\n      </div>\n    );\n  }\n\n  // Success Step\n  if (step === 'success') {\n    return (\n      <div className=\"min-h-screen bg-gray-50 py-8\">\n        <div className=\"max-w-md mx-auto\">\n          <div className=\"bg-white rounded-lg shadow-lg p-6 text-center\">\n            <svg className=\"w-20 h-20 text-green-500 mx-auto mb-4\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n            </svg>\n\n            <h2 className=\"text-2xl font-semibold text-gray-900 mb-2\">\n              Pedido Confirmado!\n            </h2>\n\n            <p className=\"text-gray-600 mb-2\">\n              Obrigado, {formData.name}!\n            </p>\n\n            <p className=\"text-sm text-gray-500 mb-6\">\n              {formData.paymentMethod === 'CASH_ON_DELIVERY'\n                ? 'O seu pedido foi registado com sucesso. Prepare o pagamento para a entrega. Receberá uma confirmação por email em breve.'\n                : 'O seu pedido foi processado com sucesso. Receberá uma confirmação por email em breve.'\n              }\n            </p>\n\n            <div className=\"space-y-3\">\n              <button\n                onClick={handleGoHome}\n                className=\"w-full bg-blue-600 text-white py-3 px-4 rounded-md font-medium hover:bg-blue-700 transition-colors\"\n              >\n                Voltar ao Início\n              </button>\n\n              <button\n                onClick={() => navigate('/orders')}\n                className=\"w-full border border-gray-300 text-gray-700 py-3 px-4 rounded-md font-medium hover:bg-gray-50 transition-colors\"\n              >\n                Ver Meus Pedidos\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return null;\n};\n\nexport default CheckoutPage;\n"], "mappings": "sIAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,OAASC,WAAW,KAAQ,eAAe,CAC3C,OAASC,iBAAiB,KAAQ,wBAAwB,CAC1D,OAASC,UAAU,KAAQ,iBAAiB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAU7C,KAAM,CAAAC,YAAsB,CAAGA,CAAA,GAAM,CACnC,KAAM,CAAAC,QAAQ,CAAGT,WAAW,CAAC,CAAC,CAC9B,KAAM,CAACU,IAAI,CAAEC,OAAO,CAAC,CAAGZ,QAAQ,CAAiC,MAAM,CAAC,CACxE,KAAM,CAACa,QAAQ,CAAEC,WAAW,CAAC,CAAGd,QAAQ,CAAmB,CACzDe,IAAI,CAAE,EAAE,CACRC,eAAe,CAAE,EAAE,CACnBC,KAAK,CAAE,EAAE,CACTC,KAAK,CAAE,EAAE,CACTC,aAAa,CAAE,kBACjB,CAAC,CAAC,CACF,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAGrB,QAAQ,CAAS,EAAE,CAAC,CAClD,KAAM,CAACsB,WAAW,CAAEC,cAAc,CAAC,CAAGvB,QAAQ,CAAS,CAAC,CAAC,CAEzD;AACAD,KAAK,CAACyB,SAAS,CAAC,IAAM,CACpB,KAAM,CAAAC,aAAa,CAAGC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC,CAC1D,GAAIF,aAAa,CAAE,CACjB,GAAI,CACF,KAAM,CAAAG,OAAO,CAAGC,IAAI,CAACC,KAAK,CAACL,aAAa,CAAC,CACzC;AACA,KAAM,CAAAM,SAAS,CAAG,EAAE,CAAE;AACtB,KAAM,CAAAC,KAAK,CAAGJ,OAAO,CAACI,KAAK,EAAI,CAAC,CAChC,KAAM,CAAAC,MAAM,CAAGL,OAAO,CAACK,MAAM,EAAI,CAAC,CAClC,KAAM,CAAAC,eAAe,CAAGN,OAAO,CAACO,KAAK,GAAK,OAAO,CAAG,CAAC,CAAG,CAAC,CACzD,KAAM,CAAAC,iBAAiB,CAAGR,OAAO,CAACS,OAAO,GAAK,MAAM,CAAG,GAAG,CAAG,CAAC,CAE9D,KAAM,CAAAC,KAAK,CAAGC,IAAI,CAACC,KAAK,CAACT,SAAS,CAAGC,KAAK,CAAGC,MAAM,CAAGC,eAAe,CAAGE,iBAAiB,CAAC,CAC1Fb,cAAc,CAACe,KAAK,CAAC,CACvB,CAAE,MAAOG,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,CAAEA,KAAK,CAAC,CACrDlB,cAAc,CAAC,GAAG,CAAC,CAAE;AACvB,CACF,CAAC,IAAM,CACLA,cAAc,CAAC,GAAG,CAAC,CAAE;AACvB,CACF,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAoB,iBAAiB,CAAIC,CAA0D,EAAK,CACxF,KAAM,CAAE7B,IAAI,CAAE8B,KAAM,CAAC,CAAGD,CAAC,CAACE,MAAM,CAChChC,WAAW,CAACiC,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAE,CAAChC,IAAI,EAAG8B,KAAK,EAAG,CAAC,CACnD,CAAC,CAED,KAAM,CAAAI,gBAAgB,CAAG,KAAO,CAAAL,CAAkB,EAAK,CACrDA,CAAC,CAACM,cAAc,CAAC,CAAC,CAElB;AACA,GAAI,CAACrC,QAAQ,CAACE,IAAI,EAAI,CAACF,QAAQ,CAACI,KAAK,EAAI,CAACJ,QAAQ,CAACK,KAAK,CAAE,CACxDiC,KAAK,CAAC,mDAAmD,CAAC,CAC1D,OACF,CAEA,GAAI,CACF;AACA,KAAM,CAAAC,eAAe,CAAG1B,YAAY,CAACC,OAAO,CAAC,iBAAiB,CAAC,CAC/D,KAAM,CAAA0B,YAAY,CAAG3B,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC,CAEzD,GAAI,CAACyB,eAAe,EAAI,CAACC,YAAY,CAAE,CACrCF,KAAK,CAAC,+EAA+E,CAAC,CACtFzC,QAAQ,CAAC,SAAS,CAAC,CACnB,OACF,CAEA,KAAM,CAAA4C,QAAQ,CAAGzB,IAAI,CAACC,KAAK,CAACsB,eAAe,CAAC,CAC5C,KAAM,CAAAxB,OAAO,CAAGC,IAAI,CAACC,KAAK,CAACuB,YAAY,CAAC,CAExC;AACA,KAAM,CAAAE,SAAS,CAAG,CAChBC,MAAM,CAAEF,QAAQ,CAACG,EAAE,CACnBC,YAAY,CAAE7C,QAAQ,CAACE,IAAI,CAC3B4C,aAAa,CAAE9C,QAAQ,CAACK,KAAK,CAC7B0C,aAAa,CAAE/C,QAAQ,CAACI,KAAK,CAC7B4C,MAAM,CAAEjC,OAAO,CAACiC,MAAM,CACtBC,SAAS,CAAElC,OAAO,CAACkC,SAAS,CAC5BC,MAAM,CAAEnC,OAAO,CAACmC,MAAM,CACtB9B,MAAM,CAAEL,OAAO,CAACK,MAAM,CACtB+B,KAAK,4BAAAC,MAAA,CAA0BpD,QAAQ,CAACG,eAAe,CACzD,CAAC,CAED,KAAM,CAAAkD,aAAa,CAAG,KAAM,CAAA9D,UAAU,CAAC+D,IAAI,CAAC,SAAS,CAAEZ,SAAS,CAAC,CAEjE,GAAI,CAACW,aAAa,CAACE,OAAO,CAAE,CAC1B,KAAM,IAAI,CAAAC,KAAK,CAACH,aAAa,CAACzB,KAAK,EAAI,sBAAsB,CAAC,CAChE,CAEA,KAAM,CAAA6B,YAAY,CAAGJ,aAAa,CAACK,IAAW,CAC9ClD,UAAU,CAACiD,YAAY,CAACb,EAAE,CAACe,QAAQ,CAAC,CAAC,CAAC,CAEtC;AACA9C,YAAY,CAAC+C,OAAO,CAAC,cAAc,CAAE5C,IAAI,CAAC6C,SAAS,CAACJ,YAAY,CAAC,CAAC,CAElE;AACA,GAAIzD,QAAQ,CAACM,aAAa,GAAK,oBAAoB,CAAE,CACnDP,OAAO,CAAC,SAAS,CAAC,CACpB,CAAC,IAAM,IAAIC,QAAQ,CAACM,aAAa,GAAK,kBAAkB,CAAE,CACxD;AACAP,OAAO,CAAC,SAAS,CAAC,CACpB,CAAC,IAAM,CACL;AACAuC,KAAK,CAAC,6CAA6C,CAAC,CACtD,CACF,CAAE,MAAOV,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,CAAEA,KAAK,CAAC,CAC7CU,KAAK,0BAAAc,MAAA,CAA0BxB,KAAK,WAAY,CAAA4B,KAAK,CAAG5B,KAAK,CAACkC,OAAO,CAAG,kBAAkB,CAAE,CAAC,CAC/F,CACF,CAAC,CAED,KAAM,CAAAC,qBAAqB,CAAIC,SAAiB,EAAK,CACnDnC,OAAO,CAACoC,GAAG,CAAC,oBAAoB,CAAED,SAAS,CAAC,CAC5CjE,OAAO,CAAC,SAAS,CAAC,CACpB,CAAC,CAED,KAAM,CAAAmE,mBAAmB,CAAItC,KAAa,EAAK,CAC7CC,OAAO,CAACD,KAAK,CAAC,iBAAiB,CAAEA,KAAK,CAAC,CACvCU,KAAK,uBAAAc,MAAA,CAAuBxB,KAAK,CAAE,CAAC,CACpC7B,OAAO,CAAC,MAAM,CAAC,CACjB,CAAC,CAED,KAAM,CAAAoE,gBAAgB,CAAGA,CAAA,GAAM,CAC7BpE,OAAO,CAAC,MAAM,CAAC,CACjB,CAAC,CAED,KAAM,CAAAqE,YAAY,CAAGA,CAAA,GAAM,CACzBvE,QAAQ,CAAC,GAAG,CAAC,CACf,CAAC,CAED;AACA,GAAIC,IAAI,GAAK,MAAM,CAAE,CACnB,mBACEL,IAAA,QAAK4E,SAAS,CAAC,8BAA8B,CAAAC,QAAA,cAC3C7E,IAAA,QAAK4E,SAAS,CAAC,kBAAkB,CAAAC,QAAA,cAC/B3E,KAAA,QAAK0E,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChD7E,IAAA,OAAI4E,SAAS,CAAC,uDAAuD,CAAAC,QAAA,CAAC,kBAEtE,CAAI,CAAC,cAGL3E,KAAA,QAAK0E,SAAS,CAAC,gCAAgC,CAAAC,QAAA,eAC7C7E,IAAA,OAAI4E,SAAS,CAAC,gCAAgC,CAAAC,QAAA,CAAC,kBAAgB,CAAI,CAAC,cACpE3E,KAAA,QAAK0E,SAAS,CAAC,4CAA4C,CAAAC,QAAA,eACzD7E,IAAA,SAAA6E,QAAA,CAAM,QAAM,CAAM,CAAC,cACnB7E,IAAA,SAAM4E,SAAS,CAAC,eAAe,CAAAC,QAAA,CAC5BhF,iBAAiB,CAACiF,eAAe,CAAC9D,WAAW,CAAC,CAC3C,CAAC,EACJ,CAAC,EACH,CAAC,cAENd,KAAA,SAAM6E,QAAQ,CAAEpC,gBAAiB,CAACiC,SAAS,CAAC,WAAW,CAAAC,QAAA,eACrD3E,KAAA,QAAA2E,QAAA,eACE7E,IAAA,UAAO4E,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,iBAEhE,CAAO,CAAC,cACR7E,IAAA,UACEgF,IAAI,CAAC,MAAM,CACXvE,IAAI,CAAC,MAAM,CACX8B,KAAK,CAAEhC,QAAQ,CAACE,IAAK,CACrBwE,QAAQ,CAAE5C,iBAAkB,CAC5BuC,SAAS,CAAC,qGAAqG,CAC/GM,WAAW,CAAC,mBAAmB,CAC/BC,QAAQ,MACT,CAAC,EACC,CAAC,cAENjF,KAAA,QAAA2E,QAAA,eACE7E,IAAA,UAAO4E,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,SAEhE,CAAO,CAAC,cACR7E,IAAA,UACEgF,IAAI,CAAC,OAAO,CACZvE,IAAI,CAAC,OAAO,CACZ8B,KAAK,CAAEhC,QAAQ,CAACK,KAAM,CACtBqE,QAAQ,CAAE5C,iBAAkB,CAC5BuC,SAAS,CAAC,qGAAqG,CAC/GM,WAAW,CAAC,eAAe,CAC3BC,QAAQ,MACT,CAAC,EACC,CAAC,cAENjF,KAAA,QAAA2E,QAAA,eACE7E,IAAA,UAAO4E,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,YAEhE,CAAO,CAAC,cACR7E,IAAA,UACEgF,IAAI,CAAC,KAAK,CACVvE,IAAI,CAAC,OAAO,CACZ8B,KAAK,CAAEhC,QAAQ,CAACI,KAAM,CACtBsE,QAAQ,CAAE5C,iBAAkB,CAC5BuC,SAAS,CAAC,qGAAqG,CAC/GM,WAAW,CAAC,kBAAkB,CAC9BC,QAAQ,MACT,CAAC,EACC,CAAC,cAENjF,KAAA,QAAA2E,QAAA,eACE7E,IAAA,UAAO4E,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,wBAEhE,CAAO,CAAC,cACR7E,IAAA,UACEgF,IAAI,CAAC,MAAM,CACXvE,IAAI,CAAC,iBAAiB,CACtB8B,KAAK,CAAEhC,QAAQ,CAACG,eAAgB,CAChCuE,QAAQ,CAAE5C,iBAAkB,CAC5BuC,SAAS,CAAC,qGAAqG,CAC/GM,WAAW,CAAC,qCAAkC,CAC/C,CAAC,EACC,CAAC,cAENhF,KAAA,QAAA2E,QAAA,eACE7E,IAAA,UAAO4E,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,wBAEhE,CAAO,CAAC,cACR3E,KAAA,WACEO,IAAI,CAAC,eAAe,CACpB8B,KAAK,CAAEhC,QAAQ,CAACM,aAAc,CAC9BoE,QAAQ,CAAE5C,iBAAkB,CAC5BuC,SAAS,CAAC,qGAAqG,CAAAC,QAAA,eAE/G7E,IAAA,WAAQuC,KAAK,CAAC,kBAAkB,CAAAsC,QAAA,CAAC,iDAAqC,CAAQ,CAAC,cAC/E7E,IAAA,WAAQuC,KAAK,CAAC,oBAAoB,CAAAsC,QAAA,CAAC,iCAAqB,CAAQ,CAAC,cACjE7E,IAAA,WAAQuC,KAAK,CAAC,eAAe,CAAAsC,QAAA,CAAC,2CAAyB,CAAQ,CAAC,EAC1D,CAAC,EACN,CAAC,cAEN7E,IAAA,WACEgF,IAAI,CAAC,QAAQ,CACbJ,SAAS,CAAC,oGAAoG,CAAAC,QAAA,CAE7GtE,QAAQ,CAACM,aAAa,GAAK,kBAAkB,CAAG,kBAAkB,CAAG,0BAA0B,CAC1F,CAAC,EACL,CAAC,EACJ,CAAC,CACH,CAAC,CACH,CAAC,CAEV,CAEA;AACA,GAAIR,IAAI,GAAK,SAAS,CAAE,CACtB,mBACEL,IAAA,QAAK4E,SAAS,CAAC,8BAA8B,CAAAC,QAAA,cAC3C3E,KAAA,QAAK0E,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/B7E,IAAA,QAAK4E,SAAS,CAAC,MAAM,CAAAC,QAAA,cACnB3E,KAAA,WACEkF,OAAO,CAAEV,gBAAiB,CAC1BE,SAAS,CAAC,2DAA2D,CAAAC,QAAA,eAErE7E,IAAA,QAAK4E,SAAS,CAAC,SAAS,CAACS,IAAI,CAAC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAAT,QAAA,cAC9D7E,IAAA,SAAMuF,QAAQ,CAAC,SAAS,CAACC,CAAC,CAAC,uIAAuI,CAACC,QAAQ,CAAC,SAAS,CAAE,CAAC,CACrL,CAAC,SAER,EAAQ,CAAC,CACN,CAAC,cAENzF,IAAA,CAACJ,WAAW,EACVkB,OAAO,CAAEA,OAAQ,CACjB4E,MAAM,CAAE1E,WAAY,CACpB2E,QAAQ,CAAC,KAAK,CACdC,WAAW,CAAC,wCAAkC,CAC9CvC,aAAa,CAAE9C,QAAQ,CAACK,KAAM,CAC9B0C,aAAa,CAAE/C,QAAQ,CAACI,KAAM,CAC9BkF,iBAAiB,CAAEvB,qBAAsB,CACzCwB,eAAe,CAAErB,mBAAoB,CACrCsB,QAAQ,CAAErB,gBAAiB,CAC5B,CAAC,EACC,CAAC,CACH,CAAC,CAEV,CAEA;AACA,GAAIrE,IAAI,GAAK,SAAS,CAAE,CACtB,mBACEL,IAAA,QAAK4E,SAAS,CAAC,8BAA8B,CAAAC,QAAA,cAC3C7E,IAAA,QAAK4E,SAAS,CAAC,kBAAkB,CAAAC,QAAA,cAC/B3E,KAAA,QAAK0E,SAAS,CAAC,+CAA+C,CAAAC,QAAA,eAC5D7E,IAAA,QAAK4E,SAAS,CAAC,uCAAuC,CAACS,IAAI,CAAC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAAT,QAAA,cAC5F7E,IAAA,SAAMuF,QAAQ,CAAC,SAAS,CAACC,CAAC,CAAC,uIAAuI,CAACC,QAAQ,CAAC,SAAS,CAAE,CAAC,CACrL,CAAC,cAENzF,IAAA,OAAI4E,SAAS,CAAC,2CAA2C,CAAAC,QAAA,CAAC,oBAE1D,CAAI,CAAC,cAEL3E,KAAA,MAAG0E,SAAS,CAAC,oBAAoB,CAAAC,QAAA,EAAC,YACtB,CAACtE,QAAQ,CAACE,IAAI,CAAC,GAC3B,EAAG,CAAC,cAEJT,IAAA,MAAG4E,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CACtCtE,QAAQ,CAACM,aAAa,GAAK,kBAAkB,CAC1C,0HAA0H,CAC1H,uFAAuF,CAE1F,CAAC,cAEJX,KAAA,QAAK0E,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxB7E,IAAA,WACEoF,OAAO,CAAET,YAAa,CACtBC,SAAS,CAAC,oGAAoG,CAAAC,QAAA,CAC/G,qBAED,CAAQ,CAAC,cAET7E,IAAA,WACEoF,OAAO,CAAEA,CAAA,GAAMhF,QAAQ,CAAC,SAAS,CAAE,CACnCwE,SAAS,CAAC,iHAAiH,CAAAC,QAAA,CAC5H,kBAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CACH,CAAC,CACH,CAAC,CAEV,CAEA,MAAO,KAAI,CACb,CAAC,CAED,cAAe,CAAA1E,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}