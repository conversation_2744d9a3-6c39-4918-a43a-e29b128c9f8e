{"ast": null, "code": "import _objectSpread from\"/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";const API_BASE_URL=process.env.REACT_APP_API_URL||'http://localhost:8000/api';class ApiService{constructor(){let baseURL=arguments.length>0&&arguments[0]!==undefined?arguments[0]:API_BASE_URL;this.baseURL=void 0;this.baseURL=baseURL;}async request(endpoint){let options=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{};const url=\"\".concat(this.baseURL).concat(endpoint);const defaultHeaders={'Content-Type':'application/json'};const config=_objectSpread({headers:_objectSpread(_objectSpread({},defaultHeaders),options.headers)},options);try{const response=await fetch(url,config);const data=await response.json();if(!response.ok){return{success:false,error:data.message||'An error occurred'};}return{success:true,data};}catch(error){return{success:false,error:error instanceof Error?error.message:'Network error'};}}// GET request\nasync get(endpoint){return this.request(endpoint,{method:'GET'});}// POST request\nasync post(endpoint,data){return this.request(endpoint,{method:'POST',body:data?JSON.stringify(data):undefined});}// PUT request\nasync put(endpoint,data){return this.request(endpoint,{method:'PUT',body:data?JSON.stringify(data):undefined});}// DELETE request\nasync delete(endpoint){return this.request(endpoint,{method:'DELETE'});}// File upload\nasync uploadFile(endpoint,file){const formData=new FormData();formData.append('file',file);return this.request(endpoint,{method:'POST',body:formData,headers:{}// Let browser set Content-Type for FormData\n});}}export const apiService=new ApiService();export default ApiService;", "map": {"version": 3, "names": ["API_BASE_URL", "process", "env", "REACT_APP_API_URL", "ApiService", "constructor", "baseURL", "arguments", "length", "undefined", "request", "endpoint", "options", "url", "concat", "defaultHeaders", "config", "_objectSpread", "headers", "response", "fetch", "data", "json", "ok", "success", "error", "message", "Error", "get", "method", "post", "body", "JSON", "stringify", "put", "delete", "uploadFile", "file", "formData", "FormData", "append", "apiService"], "sources": ["/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/services/api.ts"], "sourcesContent": ["import { ApiResponse } from '../types';\n\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000/api';\n\nclass ApiService {\n  private baseURL: string;\n\n  constructor(baseURL: string = API_BASE_URL) {\n    this.baseURL = baseURL;\n  }\n\n  private async request<T>(\n    endpoint: string,\n    options: RequestInit = {}\n  ): Promise<ApiResponse<T>> {\n    const url = `${this.baseURL}${endpoint}`;\n    \n    const defaultHeaders = {\n      'Content-Type': 'application/json',\n    };\n\n    const config: RequestInit = {\n      headers: { ...defaultHeaders, ...options.headers },\n      ...options,\n    };\n\n    try {\n      const response = await fetch(url, config);\n      const data = await response.json();\n\n      if (!response.ok) {\n        return {\n          success: false,\n          error: data.message || 'An error occurred',\n        };\n      }\n\n      return {\n        success: true,\n        data,\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : 'Network error',\n      };\n    }\n  }\n\n  // GET request\n  async get<T>(endpoint: string): Promise<ApiResponse<T>> {\n    return this.request<T>(endpoint, { method: 'GET' });\n  }\n\n  // POST request\n  async post<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {\n    return this.request<T>(endpoint, {\n      method: 'POST',\n      body: data ? JSON.stringify(data) : undefined,\n    });\n  }\n\n  // PUT request\n  async put<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {\n    return this.request<T>(endpoint, {\n      method: 'PUT',\n      body: data ? JSON.stringify(data) : undefined,\n    });\n  }\n\n  // DELETE request\n  async delete<T>(endpoint: string): Promise<ApiResponse<T>> {\n    return this.request<T>(endpoint, { method: 'DELETE' });\n  }\n\n  // File upload\n  async uploadFile<T>(endpoint: string, file: File): Promise<ApiResponse<T>> {\n    const formData = new FormData();\n    formData.append('file', file);\n\n    return this.request<T>(endpoint, {\n      method: 'POST',\n      body: formData,\n      headers: {}, // Let browser set Content-Type for FormData\n    });\n  }\n}\n\nexport const apiService = new ApiService();\nexport default ApiService;\n"], "mappings": "sIAEA,KAAM,CAAAA,YAAY,CAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,EAAI,2BAA2B,CAEjF,KAAM,CAAAC,UAAW,CAGfC,WAAWA,CAAA,CAAiC,IAAhC,CAAAC,OAAe,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAGP,YAAY,MAFlCM,OAAO,QAGb,IAAI,CAACA,OAAO,CAAGA,OAAO,CACxB,CAEA,KAAc,CAAAI,OAAOA,CACnBC,QAAgB,CAES,IADzB,CAAAC,OAAoB,CAAAL,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAAC,CAEzB,KAAM,CAAAM,GAAG,IAAAC,MAAA,CAAM,IAAI,CAACR,OAAO,EAAAQ,MAAA,CAAGH,QAAQ,CAAE,CAExC,KAAM,CAAAI,cAAc,CAAG,CACrB,cAAc,CAAE,kBAClB,CAAC,CAED,KAAM,CAAAC,MAAmB,CAAAC,aAAA,EACvBC,OAAO,CAAAD,aAAA,CAAAA,aAAA,IAAOF,cAAc,EAAKH,OAAO,CAACM,OAAO,CAAE,EAC/CN,OAAO,CACX,CAED,GAAI,CACF,KAAM,CAAAO,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAACP,GAAG,CAAEG,MAAM,CAAC,CACzC,KAAM,CAAAK,IAAI,CAAG,KAAM,CAAAF,QAAQ,CAACG,IAAI,CAAC,CAAC,CAElC,GAAI,CAACH,QAAQ,CAACI,EAAE,CAAE,CAChB,MAAO,CACLC,OAAO,CAAE,KAAK,CACdC,KAAK,CAAEJ,IAAI,CAACK,OAAO,EAAI,mBACzB,CAAC,CACH,CAEA,MAAO,CACLF,OAAO,CAAE,IAAI,CACbH,IACF,CAAC,CACH,CAAE,MAAOI,KAAK,CAAE,CACd,MAAO,CACLD,OAAO,CAAE,KAAK,CACdC,KAAK,CAAEA,KAAK,WAAY,CAAAE,KAAK,CAAGF,KAAK,CAACC,OAAO,CAAG,eAClD,CAAC,CACH,CACF,CAEA;AACA,KAAM,CAAAE,GAAGA,CAAIjB,QAAgB,CAA2B,CACtD,MAAO,KAAI,CAACD,OAAO,CAAIC,QAAQ,CAAE,CAAEkB,MAAM,CAAE,KAAM,CAAC,CAAC,CACrD,CAEA;AACA,KAAM,CAAAC,IAAIA,CAAInB,QAAgB,CAAEU,IAAU,CAA2B,CACnE,MAAO,KAAI,CAACX,OAAO,CAAIC,QAAQ,CAAE,CAC/BkB,MAAM,CAAE,MAAM,CACdE,IAAI,CAAEV,IAAI,CAAGW,IAAI,CAACC,SAAS,CAACZ,IAAI,CAAC,CAAGZ,SACtC,CAAC,CAAC,CACJ,CAEA;AACA,KAAM,CAAAyB,GAAGA,CAAIvB,QAAgB,CAAEU,IAAU,CAA2B,CAClE,MAAO,KAAI,CAACX,OAAO,CAAIC,QAAQ,CAAE,CAC/BkB,MAAM,CAAE,KAAK,CACbE,IAAI,CAAEV,IAAI,CAAGW,IAAI,CAACC,SAAS,CAACZ,IAAI,CAAC,CAAGZ,SACtC,CAAC,CAAC,CACJ,CAEA;AACA,KAAM,CAAA0B,MAAMA,CAAIxB,QAAgB,CAA2B,CACzD,MAAO,KAAI,CAACD,OAAO,CAAIC,QAAQ,CAAE,CAAEkB,MAAM,CAAE,QAAS,CAAC,CAAC,CACxD,CAEA;AACA,KAAM,CAAAO,UAAUA,CAAIzB,QAAgB,CAAE0B,IAAU,CAA2B,CACzE,KAAM,CAAAC,QAAQ,CAAG,GAAI,CAAAC,QAAQ,CAAC,CAAC,CAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,CAAEH,IAAI,CAAC,CAE7B,MAAO,KAAI,CAAC3B,OAAO,CAAIC,QAAQ,CAAE,CAC/BkB,MAAM,CAAE,MAAM,CACdE,IAAI,CAAEO,QAAQ,CACdpB,OAAO,CAAE,CAAC,CAAG;AACf,CAAC,CAAC,CACJ,CACF,CAEA,MAAO,MAAM,CAAAuB,UAAU,CAAG,GAAI,CAAArC,UAAU,CAAC,CAAC,CAC1C,cAAe,CAAAA,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}