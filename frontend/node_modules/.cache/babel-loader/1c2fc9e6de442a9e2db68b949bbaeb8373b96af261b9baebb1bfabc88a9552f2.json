{"ast": null, "code": "import _objectSpread from\"/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React from'react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ServiceCard=_ref=>{let{icon,title,description,features,price,popular}=_ref;return/*#__PURE__*/_jsxs(\"div\",{className:\"relative bg-white rounded-2xl shadow-xl p-8 transform hover:scale-105 transition-all duration-300 \".concat(popular?'ring-4 ring-weprint-magenta':''),children:[popular&&/*#__PURE__*/_jsx(\"div\",{className:\"absolute -top-4 left-1/2 transform -translate-x-1/2\",children:/*#__PURE__*/_jsx(\"span\",{className:\"bg-weprint-magenta text-white px-6 py-2 rounded-full text-sm font-bold\",children:\"MAIS POPULAR\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-center mb-6\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-6xl mb-4\",children:icon}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-2xl font-bold text-weprint-black mb-2\",children:title}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600\",children:description})]}),/*#__PURE__*/_jsx(\"ul\",{className:\"space-y-3 mb-8\",children:features.map((feature,index)=>/*#__PURE__*/_jsxs(\"li\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-2 h-2 bg-weprint-cyan rounded-full mr-3\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-700\",children:feature})]},index))}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-3xl font-bold text-weprint-magenta mb-4\",children:price}),/*#__PURE__*/_jsx(\"button\",{className:\"w-full bg-weprint-gradient text-white font-bold py-3 rounded-full hover:shadow-lg transition-all duration-300\",children:\"Escolher Plano\"})]})]});};const ServicesSection=()=>{const services=[{icon:\"📄\",title:\"Documentos\",description:\"Impressão de documentos profissionais\",features:[\"PDF, Word, Excel\",\"Preto e branco ou colorido\",\"Papel A4, A3, Carta\",\"Acabamento profissional\"],price:\"A partir de 50 AOA\"},{icon:\"📊\",title:\"Apresentações\",description:\"Impressões para reuniões e eventos\",features:[\"PowerPoint, PDF\",\"Papel premium\",\"Cores vibrantes\",\"Encadernação disponível\"],price:\"A partir de 100 AOA\",popular:true},{icon:\"📸\",title:\"Fotografias\",description:\"Impressão fotográfica de alta qualidade\",features:[\"Papel fotográfico\",\"Vários tamanhos\",\"Cores profissionais\",\"Acabamento brilhante/fosco\"],price:\"A partir de 200 AOA\"},{icon:\"📚\",title:\"Livros & Revistas\",description:\"Impressão de publicações completas\",features:[\"Encadernação profissional\",\"Capa personalizada\",\"Múltiplas páginas\",\"Papel de qualidade\"],price:\"A partir de 500 AOA\"}];return/*#__PURE__*/_jsx(\"section\",{id:\"services\",className:\"py-20 bg-weprint-gradient-subtle\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"container mx-auto px-6 lg:px-8\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-center mb-16\",children:[/*#__PURE__*/_jsxs(\"h2\",{className:\"text-4xl md:text-5xl font-black text-weprint-black mb-6\",children:[\"Nossos \",/*#__PURE__*/_jsx(\"span\",{className:\"text-weprint-magenta\",children:\"Servi\\xE7os\"})]}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xl text-gray-600 max-w-3xl mx-auto\",children:\"Oferecemos uma ampla gama de servi\\xE7os de impress\\xE3o para atender todas as suas necessidades, desde documentos simples at\\xE9 projetos complexos.\"}),/*#__PURE__*/_jsx(\"div\",{className:\"w-24 h-1 bg-weprint-gradient mx-auto mt-6 rounded-full\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",children:services.map((service,index)=>/*#__PURE__*/_jsx(ServiceCard,_objectSpread({},service),index))}),/*#__PURE__*/_jsx(\"div\",{className:\"mt-16 text-center\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-2xl shadow-xl p-8 max-w-4xl mx-auto\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-2xl font-bold text-weprint-black mb-4\",children:\"N\\xE3o encontrou o que procura?\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600 mb-6\",children:\"Temos solu\\xE7\\xF5es personalizadas para projetos especiais. Entre em contato conosco e vamos criar a solu\\xE7\\xE3o perfeita para voc\\xEA.\"}),/*#__PURE__*/_jsx(\"button\",{className:\"bg-weprint-magenta text-white font-bold px-8 py-3 rounded-full hover:bg-opacity-90 transition-all duration-300\",children:\"Falar com Especialista\"})]})})]})});};export default ServicesSection;", "map": {"version": 3, "names": ["React", "jsx", "_jsx", "jsxs", "_jsxs", "ServiceCard", "_ref", "icon", "title", "description", "features", "price", "popular", "className", "concat", "children", "map", "feature", "index", "ServicesSection", "services", "id", "service", "_objectSpread"], "sources": ["/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/landing/ServicesSection.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface ServiceCardProps {\n  icon: string;\n  title: string;\n  description: string;\n  features: string[];\n  price: string;\n  popular?: boolean;\n}\n\nconst ServiceCard: React.FC<ServiceCardProps> = ({ icon, title, description, features, price, popular }) => {\n  return (\n    <div className={`relative bg-white rounded-2xl shadow-xl p-8 transform hover:scale-105 transition-all duration-300 ${popular ? 'ring-4 ring-weprint-magenta' : ''}`}>\n      {popular && (\n        <div className=\"absolute -top-4 left-1/2 transform -translate-x-1/2\">\n          <span className=\"bg-weprint-magenta text-white px-6 py-2 rounded-full text-sm font-bold\">\n            MAIS POPULAR\n          </span>\n        </div>\n      )}\n      \n      <div className=\"text-center mb-6\">\n        <div className=\"text-6xl mb-4\">{icon}</div>\n        <h3 className=\"text-2xl font-bold text-weprint-black mb-2\">{title}</h3>\n        <p className=\"text-gray-600\">{description}</p>\n      </div>\n\n      <ul className=\"space-y-3 mb-8\">\n        {features.map((feature, index) => (\n          <li key={index} className=\"flex items-center\">\n            <div className=\"w-2 h-2 bg-weprint-cyan rounded-full mr-3\"></div>\n            <span className=\"text-gray-700\">{feature}</span>\n          </li>\n        ))}\n      </ul>\n\n      <div className=\"text-center\">\n        <div className=\"text-3xl font-bold text-weprint-magenta mb-4\">{price}</div>\n        <button className=\"w-full bg-weprint-gradient text-white font-bold py-3 rounded-full hover:shadow-lg transition-all duration-300\">\n          Escolher Plano\n        </button>\n      </div>\n    </div>\n  );\n};\n\nconst ServicesSection: React.FC = () => {\n  const services = [\n    {\n      icon: \"📄\",\n      title: \"Documentos\",\n      description: \"Impressão de documentos profissionais\",\n      features: [\n        \"PDF, Word, Excel\",\n        \"Preto e branco ou colorido\",\n        \"Papel A4, A3, Carta\",\n        \"Acabamento profissional\"\n      ],\n      price: \"A partir de 50 AOA\"\n    },\n    {\n      icon: \"📊\",\n      title: \"Apresentações\",\n      description: \"Impressões para reuniões e eventos\",\n      features: [\n        \"PowerPoint, PDF\",\n        \"Papel premium\",\n        \"Cores vibrantes\",\n        \"Encadernação disponível\"\n      ],\n      price: \"A partir de 100 AOA\",\n      popular: true\n    },\n    {\n      icon: \"📸\",\n      title: \"Fotografias\",\n      description: \"Impressão fotográfica de alta qualidade\",\n      features: [\n        \"Papel fotográfico\",\n        \"Vários tamanhos\",\n        \"Cores profissionais\",\n        \"Acabamento brilhante/fosco\"\n      ],\n      price: \"A partir de 200 AOA\"\n    },\n    {\n      icon: \"📚\",\n      title: \"Livros & Revistas\",\n      description: \"Impressão de publicações completas\",\n      features: [\n        \"Encadernação profissional\",\n        \"Capa personalizada\",\n        \"Múltiplas páginas\",\n        \"Papel de qualidade\"\n      ],\n      price: \"A partir de 500 AOA\"\n    }\n  ];\n\n  return (\n    <section id=\"services\" className=\"py-20 bg-weprint-gradient-subtle\">\n      <div className=\"container mx-auto px-6 lg:px-8\">\n        {/* Section Header */}\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-4xl md:text-5xl font-black text-weprint-black mb-6\">\n            Nossos <span className=\"text-weprint-magenta\">Serviços</span>\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            Oferecemos uma ampla gama de serviços de impressão para atender todas as suas necessidades, \n            desde documentos simples até projetos complexos.\n          </p>\n          <div className=\"w-24 h-1 bg-weprint-gradient mx-auto mt-6 rounded-full\"></div>\n        </div>\n\n        {/* Services Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {services.map((service, index) => (\n            <ServiceCard key={index} {...service} />\n          ))}\n        </div>\n\n        {/* Additional Info */}\n        <div className=\"mt-16 text-center\">\n          <div className=\"bg-white rounded-2xl shadow-xl p-8 max-w-4xl mx-auto\">\n            <h3 className=\"text-2xl font-bold text-weprint-black mb-4\">\n              Não encontrou o que procura?\n            </h3>\n            <p className=\"text-gray-600 mb-6\">\n              Temos soluções personalizadas para projetos especiais. Entre em contato conosco \n              e vamos criar a solução perfeita para você.\n            </p>\n            <button className=\"bg-weprint-magenta text-white font-bold px-8 py-3 rounded-full hover:bg-opacity-90 transition-all duration-300\">\n              Falar com Especialista\n            </button>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default ServicesSection;\n"], "mappings": "sIAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAW1B,KAAM,CAAAC,WAAuC,CAAGC,IAAA,EAA4D,IAA3D,CAAEC,IAAI,CAAEC,KAAK,CAAEC,WAAW,CAAEC,QAAQ,CAAEC,KAAK,CAAEC,OAAQ,CAAC,CAAAN,IAAA,CACrG,mBACEF,KAAA,QAAKS,SAAS,sGAAAC,MAAA,CAAuGF,OAAO,CAAG,6BAA6B,CAAG,EAAE,CAAG,CAAAG,QAAA,EACjKH,OAAO,eACNV,IAAA,QAAKW,SAAS,CAAC,qDAAqD,CAAAE,QAAA,cAClEb,IAAA,SAAMW,SAAS,CAAC,wEAAwE,CAAAE,QAAA,CAAC,cAEzF,CAAM,CAAC,CACJ,CACN,cAEDX,KAAA,QAAKS,SAAS,CAAC,kBAAkB,CAAAE,QAAA,eAC/Bb,IAAA,QAAKW,SAAS,CAAC,eAAe,CAAAE,QAAA,CAAER,IAAI,CAAM,CAAC,cAC3CL,IAAA,OAAIW,SAAS,CAAC,4CAA4C,CAAAE,QAAA,CAAEP,KAAK,CAAK,CAAC,cACvEN,IAAA,MAAGW,SAAS,CAAC,eAAe,CAAAE,QAAA,CAAEN,WAAW,CAAI,CAAC,EAC3C,CAAC,cAENP,IAAA,OAAIW,SAAS,CAAC,gBAAgB,CAAAE,QAAA,CAC3BL,QAAQ,CAACM,GAAG,CAAC,CAACC,OAAO,CAAEC,KAAK,gBAC3Bd,KAAA,OAAgBS,SAAS,CAAC,mBAAmB,CAAAE,QAAA,eAC3Cb,IAAA,QAAKW,SAAS,CAAC,2CAA2C,CAAM,CAAC,cACjEX,IAAA,SAAMW,SAAS,CAAC,eAAe,CAAAE,QAAA,CAAEE,OAAO,CAAO,CAAC,GAFzCC,KAGL,CACL,CAAC,CACA,CAAC,cAELd,KAAA,QAAKS,SAAS,CAAC,aAAa,CAAAE,QAAA,eAC1Bb,IAAA,QAAKW,SAAS,CAAC,8CAA8C,CAAAE,QAAA,CAAEJ,KAAK,CAAM,CAAC,cAC3ET,IAAA,WAAQW,SAAS,CAAC,+GAA+G,CAAAE,QAAA,CAAC,gBAElI,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CAEV,CAAC,CAED,KAAM,CAAAI,eAAyB,CAAGA,CAAA,GAAM,CACtC,KAAM,CAAAC,QAAQ,CAAG,CACf,CACEb,IAAI,CAAE,IAAI,CACVC,KAAK,CAAE,YAAY,CACnBC,WAAW,CAAE,uCAAuC,CACpDC,QAAQ,CAAE,CACR,kBAAkB,CAClB,4BAA4B,CAC5B,qBAAqB,CACrB,yBAAyB,CAC1B,CACDC,KAAK,CAAE,oBACT,CAAC,CACD,CACEJ,IAAI,CAAE,IAAI,CACVC,KAAK,CAAE,eAAe,CACtBC,WAAW,CAAE,oCAAoC,CACjDC,QAAQ,CAAE,CACR,iBAAiB,CACjB,eAAe,CACf,iBAAiB,CACjB,yBAAyB,CAC1B,CACDC,KAAK,CAAE,qBAAqB,CAC5BC,OAAO,CAAE,IACX,CAAC,CACD,CACEL,IAAI,CAAE,IAAI,CACVC,KAAK,CAAE,aAAa,CACpBC,WAAW,CAAE,yCAAyC,CACtDC,QAAQ,CAAE,CACR,mBAAmB,CACnB,iBAAiB,CACjB,qBAAqB,CACrB,4BAA4B,CAC7B,CACDC,KAAK,CAAE,qBACT,CAAC,CACD,CACEJ,IAAI,CAAE,IAAI,CACVC,KAAK,CAAE,mBAAmB,CAC1BC,WAAW,CAAE,oCAAoC,CACjDC,QAAQ,CAAE,CACR,2BAA2B,CAC3B,oBAAoB,CACpB,mBAAmB,CACnB,oBAAoB,CACrB,CACDC,KAAK,CAAE,qBACT,CAAC,CACF,CAED,mBACET,IAAA,YAASmB,EAAE,CAAC,UAAU,CAACR,SAAS,CAAC,kCAAkC,CAAAE,QAAA,cACjEX,KAAA,QAAKS,SAAS,CAAC,gCAAgC,CAAAE,QAAA,eAE7CX,KAAA,QAAKS,SAAS,CAAC,mBAAmB,CAAAE,QAAA,eAChCX,KAAA,OAAIS,SAAS,CAAC,yDAAyD,CAAAE,QAAA,EAAC,SAC/D,cAAAb,IAAA,SAAMW,SAAS,CAAC,sBAAsB,CAAAE,QAAA,CAAC,aAAQ,CAAM,CAAC,EAC3D,CAAC,cACLb,IAAA,MAAGW,SAAS,CAAC,yCAAyC,CAAAE,QAAA,CAAC,uJAGvD,CAAG,CAAC,cACJb,IAAA,QAAKW,SAAS,CAAC,wDAAwD,CAAM,CAAC,EAC3E,CAAC,cAGNX,IAAA,QAAKW,SAAS,CAAC,sDAAsD,CAAAE,QAAA,CAClEK,QAAQ,CAACJ,GAAG,CAAC,CAACM,OAAO,CAAEJ,KAAK,gBAC3BhB,IAAA,CAACG,WAAW,CAAAkB,aAAA,IAAiBD,OAAO,EAAlBJ,KAAqB,CACxC,CAAC,CACC,CAAC,cAGNhB,IAAA,QAAKW,SAAS,CAAC,mBAAmB,CAAAE,QAAA,cAChCX,KAAA,QAAKS,SAAS,CAAC,sDAAsD,CAAAE,QAAA,eACnEb,IAAA,OAAIW,SAAS,CAAC,4CAA4C,CAAAE,QAAA,CAAC,iCAE3D,CAAI,CAAC,cACLb,IAAA,MAAGW,SAAS,CAAC,oBAAoB,CAAAE,QAAA,CAAC,4IAGlC,CAAG,CAAC,cACJb,IAAA,WAAQW,SAAS,CAAC,gHAAgH,CAAAE,QAAA,CAAC,wBAEnI,CAAQ,CAAC,EACN,CAAC,CACH,CAAC,EACH,CAAC,CACC,CAAC,CAEd,CAAC,CAED,cAAe,CAAAI,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}