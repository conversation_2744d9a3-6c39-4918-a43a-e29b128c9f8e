{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/landing/BenefitsSection.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst BenefitCard = ({\n  icon,\n  title,\n  description,\n  gradient\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"group relative bg-white rounded-2xl shadow-xl p-8 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: `absolute inset-0 ${gradient} rounded-2xl opacity-0 group-hover:opacity-10 transition-opacity duration-300`\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative z-10\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-5xl mb-6 group-hover:scale-110 transition-transform duration-300\",\n        children: icon\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-xl font-bold text-weprint-black mb-4\",\n        children: title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600 leading-relaxed\",\n        children: description\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 12,\n    columnNumber: 5\n  }, this);\n};\n_c = BenefitCard;\nconst BenefitsSection = () => {\n  const benefits = [{\n    icon: \"🏆\",\n    title: \"Qualidade Premium\",\n    description: \"Utilizamos equipamentos de última geração e papéis de alta qualidade para garantir impressões profissionais que superam suas expectativas.\",\n    gradient: \"bg-gradient-to-br from-weprint-cyan to-blue-500\"\n  }, {\n    icon: \"⚡\",\n    title: \"Rapidez na Entrega\",\n    description: \"Processamos seus pedidos em até 24 horas e entregamos em toda Luanda. Urgência? Temos opções express para quando você precisa ainda mais rápido.\",\n    gradient: \"bg-gradient-to-br from-weprint-magenta to-pink-500\"\n  }, {\n    icon: \"💰\",\n    title: \"Preços Competitivos\",\n    description: \"Oferecemos os melhores preços do mercado sem comprometer a qualidade. Descontos especiais para grandes volumes e clientes frequentes.\",\n    gradient: \"bg-gradient-to-br from-weprint-yellow to-orange-500\"\n  }, {\n    icon: \"🛡️\",\n    title: \"Segurança Total\",\n    description: \"Seus documentos são tratados com máxima confidencialidade. Conexão segura, armazenamento criptografado e descarte seguro após impressão.\",\n    gradient: \"bg-gradient-to-br from-green-500 to-emerald-500\"\n  }, {\n    icon: \"📱\",\n    title: \"Facilidade de Uso\",\n    description: \"Interface intuitiva que funciona perfeitamente em computadores, tablets e smartphones. Upload, configure e peça em poucos cliques.\",\n    gradient: \"bg-gradient-to-br from-purple-500 to-indigo-500\"\n  }, {\n    icon: \"🎯\",\n    title: \"Atendimento Personalizado\",\n    description: \"Nossa equipe está sempre pronta para ajudar. Suporte via WhatsApp, email ou telefone. Soluções personalizadas para suas necessidades específicas.\",\n    gradient: \"bg-gradient-to-br from-red-500 to-rose-500\"\n  }];\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"py-20 bg-weprint-gradient-subtle\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-6 lg:px-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-16\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-4xl md:text-5xl font-black text-weprint-black mb-6\",\n          children: [\"Por que escolher a \", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-weprint-magenta\",\n            children: \"WePrint\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 32\n          }, this), \"?\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n          children: \"Somos mais que uma gr\\xE1fica. Somos seu parceiro para impress\\xF5es profissionais, oferecendo qualidade, rapidez e atendimento excepcional.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-24 h-1 bg-weprint-gradient mx-auto mt-6 rounded-full\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n        children: benefits.map((benefit, index) => /*#__PURE__*/_jsxDEV(BenefitCard, {\n          ...benefit\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-20\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-2xl shadow-xl p-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-2xl font-bold text-weprint-black text-center mb-8\",\n            children: \"N\\xFAmeros que Comprovam Nossa Excel\\xEAncia\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-2 md:grid-cols-4 gap-8 text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"group\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-3xl md:text-4xl font-black text-weprint-cyan mb-2 group-hover:scale-110 transition-transform duration-300\",\n                children: \"500+\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 97,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-gray-600 font-medium\",\n                children: \"Clientes Satisfeitos\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 100,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 96,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"group\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-3xl md:text-4xl font-black text-weprint-magenta mb-2 group-hover:scale-110 transition-transform duration-300\",\n                children: \"10K+\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 104,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-gray-600 font-medium\",\n                children: \"Impress\\xF5es Realizadas\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 107,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 103,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"group\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-3xl md:text-4xl font-black text-weprint-yellow mb-2 group-hover:scale-110 transition-transform duration-300\",\n                children: \"24h\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 111,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-gray-600 font-medium\",\n                children: \"Tempo M\\xE9dio\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 114,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 110,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"group\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-3xl md:text-4xl font-black text-weprint-black mb-2 group-hover:scale-110 transition-transform duration-300\",\n                children: \"4.9\\u2605\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 118,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-gray-600 font-medium\",\n                children: \"Avalia\\xE7\\xE3o M\\xE9dia\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 121,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-16 text-center\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-2xl shadow-xl p-8 max-w-4xl mx-auto\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-4xl text-weprint-magenta mb-4\",\n            children: \"\\\"\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-lg text-gray-700 italic mb-6\",\n            children: \"\\\"A WePrint transformou a forma como lidamos com impress\\xF5es na nossa empresa. Qualidade excepcional, entrega r\\xE1pida e pre\\xE7os justos. Recomendo a todos!\\\"\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 bg-weprint-gradient rounded-full mr-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-left\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"font-bold text-weprint-black\",\n                children: \"Maria Santos\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 138,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-gray-600 text-sm\",\n                children: \"Diretora, Empresa ABC\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 139,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 67,\n    columnNumber: 5\n  }, this);\n};\n_c2 = BenefitsSection;\nexport default BenefitsSection;\nvar _c, _c2;\n$RefreshReg$(_c, \"BenefitCard\");\n$RefreshReg$(_c2, \"BenefitsSection\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "BenefitCard", "icon", "title", "description", "gradient", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "BenefitsSection", "benefits", "map", "benefit", "index", "_c2", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/landing/BenefitsSection.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface BenefitCardProps {\n  icon: string;\n  title: string;\n  description: string;\n  gradient: string;\n}\n\nconst BenefitCard: React.FC<BenefitCardProps> = ({ icon, title, description, gradient }) => {\n  return (\n    <div className=\"group relative bg-white rounded-2xl shadow-xl p-8 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2\">\n      <div className={`absolute inset-0 ${gradient} rounded-2xl opacity-0 group-hover:opacity-10 transition-opacity duration-300`}></div>\n      \n      <div className=\"relative z-10\">\n        <div className=\"text-5xl mb-6 group-hover:scale-110 transition-transform duration-300\">\n          {icon}\n        </div>\n        <h3 className=\"text-xl font-bold text-weprint-black mb-4\">{title}</h3>\n        <p className=\"text-gray-600 leading-relaxed\">{description}</p>\n      </div>\n    </div>\n  );\n};\n\nconst BenefitsSection: React.FC = () => {\n  const benefits = [\n    {\n      icon: \"🏆\",\n      title: \"Qualidade Premium\",\n      description: \"Utilizamos equipamentos de última geração e papéis de alta qualidade para garantir impressões profissionais que superam suas expectativas.\",\n      gradient: \"bg-gradient-to-br from-weprint-cyan to-blue-500\"\n    },\n    {\n      icon: \"⚡\",\n      title: \"Rapidez na Entrega\",\n      description: \"Processamos seus pedidos em até 24 horas e entregamos em toda Luanda. Urgência? Temos opções express para quando você precisa ainda mais rápido.\",\n      gradient: \"bg-gradient-to-br from-weprint-magenta to-pink-500\"\n    },\n    {\n      icon: \"💰\",\n      title: \"Preços Competitivos\",\n      description: \"Oferecemos os melhores preços do mercado sem comprometer a qualidade. Descontos especiais para grandes volumes e clientes frequentes.\",\n      gradient: \"bg-gradient-to-br from-weprint-yellow to-orange-500\"\n    },\n    {\n      icon: \"🛡️\",\n      title: \"Segurança Total\",\n      description: \"Seus documentos são tratados com máxima confidencialidade. Conexão segura, armazenamento criptografado e descarte seguro após impressão.\",\n      gradient: \"bg-gradient-to-br from-green-500 to-emerald-500\"\n    },\n    {\n      icon: \"📱\",\n      title: \"Facilidade de Uso\",\n      description: \"Interface intuitiva que funciona perfeitamente em computadores, tablets e smartphones. Upload, configure e peça em poucos cliques.\",\n      gradient: \"bg-gradient-to-br from-purple-500 to-indigo-500\"\n    },\n    {\n      icon: \"🎯\",\n      title: \"Atendimento Personalizado\",\n      description: \"Nossa equipe está sempre pronta para ajudar. Suporte via WhatsApp, email ou telefone. Soluções personalizadas para suas necessidades específicas.\",\n      gradient: \"bg-gradient-to-br from-red-500 to-rose-500\"\n    }\n  ];\n\n  return (\n    <section className=\"py-20 bg-weprint-gradient-subtle\">\n      <div className=\"container mx-auto px-6 lg:px-8\">\n        {/* Section Header */}\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-4xl md:text-5xl font-black text-weprint-black mb-6\">\n            Por que escolher a <span className=\"text-weprint-magenta\">WePrint</span>?\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            Somos mais que uma gráfica. Somos seu parceiro para impressões profissionais, \n            oferecendo qualidade, rapidez e atendimento excepcional.\n          </p>\n          <div className=\"w-24 h-1 bg-weprint-gradient mx-auto mt-6 rounded-full\"></div>\n        </div>\n\n        {/* Benefits Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n          {benefits.map((benefit, index) => (\n            <BenefitCard key={index} {...benefit} />\n          ))}\n        </div>\n\n        {/* Stats Section */}\n        <div className=\"mt-20\">\n          <div className=\"bg-white rounded-2xl shadow-xl p-8\">\n            <h3 className=\"text-2xl font-bold text-weprint-black text-center mb-8\">\n              Números que Comprovam Nossa Excelência\n            </h3>\n            \n            <div className=\"grid grid-cols-2 md:grid-cols-4 gap-8 text-center\">\n              <div className=\"group\">\n                <div className=\"text-3xl md:text-4xl font-black text-weprint-cyan mb-2 group-hover:scale-110 transition-transform duration-300\">\n                  500+\n                </div>\n                <div className=\"text-gray-600 font-medium\">Clientes Satisfeitos</div>\n              </div>\n              \n              <div className=\"group\">\n                <div className=\"text-3xl md:text-4xl font-black text-weprint-magenta mb-2 group-hover:scale-110 transition-transform duration-300\">\n                  10K+\n                </div>\n                <div className=\"text-gray-600 font-medium\">Impressões Realizadas</div>\n              </div>\n              \n              <div className=\"group\">\n                <div className=\"text-3xl md:text-4xl font-black text-weprint-yellow mb-2 group-hover:scale-110 transition-transform duration-300\">\n                  24h\n                </div>\n                <div className=\"text-gray-600 font-medium\">Tempo Médio</div>\n              </div>\n              \n              <div className=\"group\">\n                <div className=\"text-3xl md:text-4xl font-black text-weprint-black mb-2 group-hover:scale-110 transition-transform duration-300\">\n                  4.9★\n                </div>\n                <div className=\"text-gray-600 font-medium\">Avaliação Média</div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Testimonial Preview */}\n        <div className=\"mt-16 text-center\">\n          <div className=\"bg-white rounded-2xl shadow-xl p-8 max-w-4xl mx-auto\">\n            <div className=\"text-4xl text-weprint-magenta mb-4\">\"</div>\n            <p className=\"text-lg text-gray-700 italic mb-6\">\n              \"A WePrint transformou a forma como lidamos com impressões na nossa empresa. \n              Qualidade excepcional, entrega rápida e preços justos. Recomendo a todos!\"\n            </p>\n            <div className=\"flex items-center justify-center\">\n              <div className=\"w-12 h-12 bg-weprint-gradient rounded-full mr-4\"></div>\n              <div className=\"text-left\">\n                <div className=\"font-bold text-weprint-black\">Maria Santos</div>\n                <div className=\"text-gray-600 text-sm\">Diretora, Empresa ABC</div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default BenefitsSection;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAS1B,MAAMC,WAAuC,GAAGA,CAAC;EAAEC,IAAI;EAAEC,KAAK;EAAEC,WAAW;EAAEC;AAAS,CAAC,KAAK;EAC1F,oBACEL,OAAA;IAAKM,SAAS,EAAC,+HAA+H;IAAAC,QAAA,gBAC5IP,OAAA;MAAKM,SAAS,EAAE,oBAAoBD,QAAQ;IAAgF;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAEnIX,OAAA;MAAKM,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5BP,OAAA;QAAKM,SAAS,EAAC,uEAAuE;QAAAC,QAAA,EACnFL;MAAI;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACNX,OAAA;QAAIM,SAAS,EAAC,2CAA2C;QAAAC,QAAA,EAAEJ;MAAK;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACtEX,OAAA;QAAGM,SAAS,EAAC,+BAA+B;QAAAC,QAAA,EAAEH;MAAW;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3D,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACC,EAAA,GAdIX,WAAuC;AAgB7C,MAAMY,eAAyB,GAAGA,CAAA,KAAM;EACtC,MAAMC,QAAQ,GAAG,CACf;IACEZ,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,mBAAmB;IAC1BC,WAAW,EAAE,4IAA4I;IACzJC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEH,IAAI,EAAE,GAAG;IACTC,KAAK,EAAE,oBAAoB;IAC3BC,WAAW,EAAE,kJAAkJ;IAC/JC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEH,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,qBAAqB;IAC5BC,WAAW,EAAE,uIAAuI;IACpJC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEH,IAAI,EAAE,KAAK;IACXC,KAAK,EAAE,iBAAiB;IACxBC,WAAW,EAAE,0IAA0I;IACvJC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEH,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,mBAAmB;IAC1BC,WAAW,EAAE,oIAAoI;IACjJC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEH,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,2BAA2B;IAClCC,WAAW,EAAE,mJAAmJ;IAChKC,QAAQ,EAAE;EACZ,CAAC,CACF;EAED,oBACEL,OAAA;IAASM,SAAS,EAAC,kCAAkC;IAAAC,QAAA,eACnDP,OAAA;MAAKM,SAAS,EAAC,gCAAgC;MAAAC,QAAA,gBAE7CP,OAAA;QAAKM,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCP,OAAA;UAAIM,SAAS,EAAC,yDAAyD;UAAAC,QAAA,GAAC,qBACnD,eAAAP,OAAA;YAAMM,SAAS,EAAC,sBAAsB;YAAAC,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,KAC1E;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLX,OAAA;UAAGM,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EAAC;QAGvD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJX,OAAA;UAAKM,SAAS,EAAC;QAAwD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3E,CAAC,eAGNX,OAAA;QAAKM,SAAS,EAAC,sDAAsD;QAAAC,QAAA,EAClEO,QAAQ,CAACC,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBAC3BjB,OAAA,CAACC,WAAW;UAAA,GAAiBe;QAAO,GAAlBC,KAAK;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAgB,CACxC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNX,OAAA;QAAKM,SAAS,EAAC,OAAO;QAAAC,QAAA,eACpBP,OAAA;UAAKM,SAAS,EAAC,oCAAoC;UAAAC,QAAA,gBACjDP,OAAA;YAAIM,SAAS,EAAC,wDAAwD;YAAAC,QAAA,EAAC;UAEvE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAELX,OAAA;YAAKM,SAAS,EAAC,mDAAmD;YAAAC,QAAA,gBAChEP,OAAA;cAAKM,SAAS,EAAC,OAAO;cAAAC,QAAA,gBACpBP,OAAA;gBAAKM,SAAS,EAAC,gHAAgH;gBAAAC,QAAA,EAAC;cAEhI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNX,OAAA;gBAAKM,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAC;cAAoB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClE,CAAC,eAENX,OAAA;cAAKM,SAAS,EAAC,OAAO;cAAAC,QAAA,gBACpBP,OAAA;gBAAKM,SAAS,EAAC,mHAAmH;gBAAAC,QAAA,EAAC;cAEnI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNX,OAAA;gBAAKM,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAC;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnE,CAAC,eAENX,OAAA;cAAKM,SAAS,EAAC,OAAO;cAAAC,QAAA,gBACpBP,OAAA;gBAAKM,SAAS,EAAC,kHAAkH;gBAAAC,QAAA,EAAC;cAElI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNX,OAAA;gBAAKM,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD,CAAC,eAENX,OAAA;cAAKM,SAAS,EAAC,OAAO;cAAAC,QAAA,gBACpBP,OAAA;gBAAKM,SAAS,EAAC,iHAAiH;gBAAAC,QAAA,EAAC;cAEjI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNX,OAAA;gBAAKM,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNX,OAAA;QAAKM,SAAS,EAAC,mBAAmB;QAAAC,QAAA,eAChCP,OAAA;UAAKM,SAAS,EAAC,sDAAsD;UAAAC,QAAA,gBACnEP,OAAA;YAAKM,SAAS,EAAC,oCAAoC;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC3DX,OAAA;YAAGM,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAGjD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJX,OAAA;YAAKM,SAAS,EAAC,kCAAkC;YAAAC,QAAA,gBAC/CP,OAAA;cAAKM,SAAS,EAAC;YAAiD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvEX,OAAA;cAAKM,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBP,OAAA;gBAAKM,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAChEX,OAAA;gBAAKM,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACO,GAAA,GAzHIL,eAAyB;AA2H/B,eAAeA,eAAe;AAAC,IAAAD,EAAA,EAAAM,GAAA;AAAAC,YAAA,CAAAP,EAAA;AAAAO,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}