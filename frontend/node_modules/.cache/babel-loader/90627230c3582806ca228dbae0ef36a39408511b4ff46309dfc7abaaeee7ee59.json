{"ast": null, "code": "import React from'react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Layout=_ref=>{let{children,title}=_ref;return/*#__PURE__*/_jsxs(\"div\",{className:\"min-h-screen bg-gray-50\",children:[title&&/*#__PURE__*/_jsx(\"header\",{className:\"bg-white shadow-sm border-b\",children:/*#__PURE__*/_jsx(\"div\",{className:\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between items-center py-4\",children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-2xl font-bold text-gray-900\",children:title}),/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center space-x-4\",children:/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-500\",children:\"WePrint AI\"})})]})})}),/*#__PURE__*/_jsx(\"main\",{className:\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",children:children})]});};export default Layout;", "map": {"version": 3, "names": ["React", "jsx", "_jsx", "jsxs", "_jsxs", "Layout", "_ref", "children", "title", "className"], "sources": ["/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/Layout.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface LayoutProps {\n  children: React.ReactNode;\n  title?: string;\n}\n\nconst Layout: React.FC<LayoutProps> = ({ children, title }) => {\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {title && (\n        <header className=\"bg-white shadow-sm border-b\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <div className=\"flex justify-between items-center py-4\">\n              <h1 className=\"text-2xl font-bold text-gray-900\">{title}</h1>\n              <div className=\"flex items-center space-x-4\">\n                <span className=\"text-sm text-gray-500\">WePrint AI</span>\n              </div>\n            </div>\n          </div>\n        </header>\n      )}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {children}\n      </main>\n    </div>\n  );\n};\n\nexport default Layout;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAO1B,KAAM,CAAAC,MAA6B,CAAGC,IAAA,EAAyB,IAAxB,CAAEC,QAAQ,CAAEC,KAAM,CAAC,CAAAF,IAAA,CACxD,mBACEF,KAAA,QAAKK,SAAS,CAAC,yBAAyB,CAAAF,QAAA,EACrCC,KAAK,eACJN,IAAA,WAAQO,SAAS,CAAC,6BAA6B,CAAAF,QAAA,cAC7CL,IAAA,QAAKO,SAAS,CAAC,wCAAwC,CAAAF,QAAA,cACrDH,KAAA,QAAKK,SAAS,CAAC,wCAAwC,CAAAF,QAAA,eACrDL,IAAA,OAAIO,SAAS,CAAC,kCAAkC,CAAAF,QAAA,CAAEC,KAAK,CAAK,CAAC,cAC7DN,IAAA,QAAKO,SAAS,CAAC,6BAA6B,CAAAF,QAAA,cAC1CL,IAAA,SAAMO,SAAS,CAAC,uBAAuB,CAAAF,QAAA,CAAC,YAAU,CAAM,CAAC,CACtD,CAAC,EACH,CAAC,CACH,CAAC,CACA,CACT,cACDL,IAAA,SAAMO,SAAS,CAAC,6CAA6C,CAAAF,QAAA,CAC1DA,QAAQ,CACL,CAAC,EACJ,CAAC,CAEV,CAAC,CAED,cAAe,CAAAF,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}