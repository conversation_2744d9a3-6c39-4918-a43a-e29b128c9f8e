{"ast": null, "code": "import React from'react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const UploadPage=()=>/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col items-center justify-center min-h-screen\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-semibold mb-4\",children:\"Upload de Ficheiro\"}),/*#__PURE__*/_jsx(\"input\",{type:\"file\",className:\"mb-4\"}),/*#__PURE__*/_jsx(\"button\",{className:\"px-4 py-2 bg-blue-600 text-white rounded\",children:\"Avan\\xE7ar\"})]});export default UploadPage;", "map": {"version": 3, "names": ["React", "jsx", "_jsx", "jsxs", "_jsxs", "UploadPage", "className", "children", "type"], "sources": ["/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/UploadPage.tsx"], "sourcesContent": ["import React from 'react';\n\nconst UploadPage: React.FC = () => (\n  <div className=\"flex flex-col items-center justify-center min-h-screen\">\n    <h2 className=\"text-2xl font-semibold mb-4\">Upload de Ficheiro</h2>\n    <input type=\"file\" className=\"mb-4\" />\n    <button className=\"px-4 py-2 bg-blue-600 text-white rounded\">Avançar</button>\n  </div>\n);\n\nexport default UploadPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE1B,KAAM,CAAAC,UAAoB,CAAGA,CAAA,gBAC3BD,KAAA,QAAKE,SAAS,CAAC,wDAAwD,CAAAC,QAAA,eACrEL,IAAA,OAAII,SAAS,CAAC,6BAA6B,CAAAC,QAAA,CAAC,oBAAkB,CAAI,CAAC,cACnEL,IAAA,UAAOM,IAAI,CAAC,MAAM,CAACF,SAAS,CAAC,MAAM,CAAE,CAAC,cACtCJ,IAAA,WAAQI,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,YAAO,CAAQ,CAAC,EAC1E,CACN,CAED,cAAe,CAAAF,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}