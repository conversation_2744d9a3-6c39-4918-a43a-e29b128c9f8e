{"ast": null, "code": "import React from'react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const CheckoutPage=()=>/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col items-center justify-center min-h-screen\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-semibold mb-4\",children:\"Checkout\"}),/*#__PURE__*/_jsxs(\"form\",{className:\"space-y-4\",children:[/*#__PURE__*/_jsx(\"input\",{className:\"block w-64 p-2 border rounded\",placeholder:\"Nome\"}),/*#__PURE__*/_jsx(\"input\",{className:\"block w-64 p-2 border rounded\",placeholder:\"Endere\\xE7o de Entrega\"}),/*#__PURE__*/_jsx(\"input\",{className:\"block w-64 p-2 border rounded\",placeholder:\"Telefone\"}),/*#__PURE__*/_jsxs(\"select\",{className:\"block w-64 p-2 border rounded\",children:[/*#__PURE__*/_jsx(\"option\",{children:\"M\\xE9todo de Pagamento\"}),/*#__PURE__*/_jsx(\"option\",{children:\"Multicaixa\"}),/*#__PURE__*/_jsx(\"option\",{children:\"Mobile Money\"}),/*#__PURE__*/_jsx(\"option\",{children:\"PayPal\"}),/*#__PURE__*/_jsx(\"option\",{children:\"Cart\\xE3o\"})]}),/*#__PURE__*/_jsx(\"button\",{className:\"px-4 py-2 bg-green-600 text-white rounded\",children:\"Confirmar\"})]})]});export default CheckoutPage;", "map": {"version": 3, "names": ["React", "jsx", "_jsx", "jsxs", "_jsxs", "CheckoutPage", "className", "children", "placeholder"], "sources": ["/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/CheckoutPage.tsx"], "sourcesContent": ["import React from 'react';\n\nconst CheckoutPage: React.FC = () => (\n  <div className=\"flex flex-col items-center justify-center min-h-screen\">\n    <h2 className=\"text-2xl font-semibold mb-4\">Checkout</h2>\n    <form className=\"space-y-4\">\n      <input className=\"block w-64 p-2 border rounded\" placeholder=\"Nome\" />\n      <input className=\"block w-64 p-2 border rounded\" placeholder=\"Endereço de Entrega\" />\n      <input className=\"block w-64 p-2 border rounded\" placeholder=\"Telefone\" />\n      <select className=\"block w-64 p-2 border rounded\">\n        <option>Método de Pagamento</option>\n        <option>Multicaixa</option>\n        <option>Mobile Money</option>\n        <option>PayPal</option>\n        <option>Cartão</option>\n      </select>\n      <button className=\"px-4 py-2 bg-green-600 text-white rounded\">Confirmar</button>\n    </form>\n  </div>\n);\n\nexport default CheckoutPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE1B,KAAM,CAAAC,YAAsB,CAAGA,CAAA,gBAC7BD,KAAA,QAAKE,SAAS,CAAC,wDAAwD,CAAAC,QAAA,eACrEL,IAAA,OAAII,SAAS,CAAC,6BAA6B,CAAAC,QAAA,CAAC,UAAQ,CAAI,CAAC,cACzDH,KAAA,SAAME,SAAS,CAAC,WAAW,CAAAC,QAAA,eACzBL,IAAA,UAAOI,SAAS,CAAC,+BAA+B,CAACE,WAAW,CAAC,MAAM,CAAE,CAAC,cACtEN,IAAA,UAAOI,SAAS,CAAC,+BAA+B,CAACE,WAAW,CAAC,wBAAqB,CAAE,CAAC,cACrFN,IAAA,UAAOI,SAAS,CAAC,+BAA+B,CAACE,WAAW,CAAC,UAAU,CAAE,CAAC,cAC1EJ,KAAA,WAAQE,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC/CL,IAAA,WAAAK,QAAA,CAAQ,wBAAmB,CAAQ,CAAC,cACpCL,IAAA,WAAAK,QAAA,CAAQ,YAAU,CAAQ,CAAC,cAC3BL,IAAA,WAAAK,QAAA,CAAQ,cAAY,CAAQ,CAAC,cAC7BL,IAAA,WAAAK,QAAA,CAAQ,QAAM,CAAQ,CAAC,cACvBL,IAAA,WAAAK,QAAA,CAAQ,WAAM,CAAQ,CAAC,EACjB,CAAC,cACTL,IAAA,WAAQI,SAAS,CAAC,2CAA2C,CAAAC,QAAA,CAAC,WAAS,CAAQ,CAAC,EAC5E,CAAC,EACJ,CACN,CAED,cAAe,CAAAF,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}