{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/AdminPanel.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { PaymentStatus } from '../components';\nimport { MulticaixaService } from '../services/multicaixa';\nimport { apiService } from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminPanel = () => {\n  _s();\n  const [activeTab, setActiveTab] = useState('overview');\n  const [stats, setStats] = useState({\n    totalRevenue: 0,\n    totalPayments: 0,\n    pendingPayments: 0,\n    completedPayments: 0,\n    failedPayments: 0\n  });\n  const [payments, setPayments] = useState([]);\n  const [orders, setOrders] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const loadDashboardData = useCallback(async () => {\n    setLoading(true);\n    try {\n      // Load payments\n      const paymentsResponse = await apiService.get('/payments');\n      if (paymentsResponse.success && paymentsResponse.data) {\n        setPayments(paymentsResponse.data);\n        calculateStats(paymentsResponse.data);\n      }\n\n      // Load orders\n      const ordersResponse = await apiService.get('/orders');\n      if (ordersResponse.success && ordersResponse.data) {\n        setOrders(ordersResponse.data);\n      }\n    } catch (error) {\n      console.error('Error loading dashboard data:', error);\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n  useEffect(() => {\n    loadDashboardData();\n  }, [loadDashboardData]);\n  const calculateStats = paymentsData => {\n    const stats = paymentsData.reduce((acc, payment) => {\n      acc.totalPayments++;\n      if (payment.status === 'COMPLETED') {\n        acc.completedPayments++;\n        acc.totalRevenue += payment.amount;\n      } else if (payment.status === 'PENDING' || payment.status === 'PROCESSING') {\n        acc.pendingPayments++;\n      } else if (payment.status === 'FAILED') {\n        acc.failedPayments++;\n      }\n      return acc;\n    }, {\n      totalRevenue: 0,\n      totalPayments: 0,\n      pendingPayments: 0,\n      completedPayments: 0,\n      failedPayments: 0\n    });\n    setStats(stats);\n  };\n  const handleRefundPayment = async paymentId => {\n    // eslint-disable-next-line no-restricted-globals\n    if (!confirm('Tem certeza que deseja processar este reembolso?')) {\n      return;\n    }\n    try {\n      const response = await MulticaixaService.requestRefund(paymentId);\n      if (response.success) {\n        alert('Reembolso processado com sucesso!');\n        loadDashboardData(); // Reload data\n      } else {\n        alert(`Erro ao processar reembolso: ${response.error}`);\n      }\n    } catch (error) {\n      alert('Erro ao processar reembolso');\n    }\n  };\n  const formatCurrency = amount => {\n    return MulticaixaService.formatAoaAmount(amount);\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 104,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold text-gray-900\",\n          children: \"Painel Administrativo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Gest\\xE3o de pagamentos e pedidos WePrint AI\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-8\",\n        children: /*#__PURE__*/_jsxDEV(\"nav\", {\n          className: \"flex space-x-8\",\n          children: [{\n            id: 'overview',\n            label: 'Visão Geral'\n          }, {\n            id: 'payments',\n            label: 'Pagamentos'\n          }, {\n            id: 'orders',\n            label: 'Pedidos'\n          }].map(tab => /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setActiveTab(tab.id),\n            className: `py-2 px-1 border-b-2 font-medium text-sm ${activeTab === tab.id ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`,\n            children: tab.label\n          }, tab.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 9\n      }, this), activeTab === 'overview' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow p-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-shrink-0\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-5 h-5 text-white\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 152,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 151,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 150,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 149,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"ml-5 w-0 flex-1\",\n                children: /*#__PURE__*/_jsxDEV(\"dl\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"dt\", {\n                    className: \"text-sm font-medium text-gray-500 truncate\",\n                    children: \"Receita Total\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 158,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"dd\", {\n                    className: \"text-lg font-medium text-gray-900\",\n                    children: formatCurrency(stats.totalRevenue)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 161,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 157,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 156,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow p-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-shrink-0\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-5 h-5 text-white\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      fillRule: \"evenodd\",\n                      d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                      clipRule: \"evenodd\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 174,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 173,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 172,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"ml-5 w-0 flex-1\",\n                children: /*#__PURE__*/_jsxDEV(\"dl\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"dt\", {\n                    className: \"text-sm font-medium text-gray-500 truncate\",\n                    children: \"Pagamentos Conclu\\xEDdos\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 180,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"dd\", {\n                    className: \"text-lg font-medium text-gray-900\",\n                    children: stats.completedPayments\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 183,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 179,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 178,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow p-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-shrink-0\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-5 h-5 text-white\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      fillRule: \"evenodd\",\n                      d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z\",\n                      clipRule: \"evenodd\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 196,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 195,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 194,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"ml-5 w-0 flex-1\",\n                children: /*#__PURE__*/_jsxDEV(\"dl\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"dt\", {\n                    className: \"text-sm font-medium text-gray-500 truncate\",\n                    children: \"Pagamentos Pendentes\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 202,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"dd\", {\n                    className: \"text-lg font-medium text-gray-900\",\n                    children: stats.pendingPayments\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 205,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 201,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow p-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-shrink-0\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-8 h-8 bg-red-500 rounded-md flex items-center justify-center\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-5 h-5 text-white\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      fillRule: \"evenodd\",\n                      d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n                      clipRule: \"evenodd\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 218,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 217,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 216,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 215,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"ml-5 w-0 flex-1\",\n                children: /*#__PURE__*/_jsxDEV(\"dl\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"dt\", {\n                    className: \"text-sm font-medium text-gray-500 truncate\",\n                    children: \"Pagamentos Falhados\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 224,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"dd\", {\n                    className: \"text-lg font-medium text-gray-900\",\n                    children: stats.failedPayments\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 227,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 223,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 222,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"px-6 py-4 border-b border-gray-200\",\n            children: /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-medium text-gray-900\",\n              children: \"Atividade Recente\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: payments.slice(0, 5).map(payment => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-3\",\n                  children: [/*#__PURE__*/_jsxDEV(PaymentStatus, {\n                    status: payment.status,\n                    size: \"sm\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 246,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm font-medium text-gray-900\",\n                      children: [\"Pagamento #\", payment.id.slice(-8)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 248,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-500\",\n                      children: formatCurrency(payment.amount)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 251,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 247,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 245,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-gray-500\",\n                  children: new Date(payment.createdAt).toLocaleDateString('pt-PT')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 256,\n                  columnNumber: 23\n                }, this)]\n              }, payment.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 144,\n        columnNumber: 11\n      }, this), activeTab === 'payments' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-6 py-4 border-b border-gray-200\",\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-medium text-gray-900\",\n            children: \"Gest\\xE3o de Pagamentos\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"overflow-x-auto\",\n          children: /*#__PURE__*/_jsxDEV(\"table\", {\n            className: \"min-w-full divide-y divide-gray-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              className: \"bg-gray-50\",\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"ID\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 277,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Valor\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 280,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 283,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"M\\xE9todo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 286,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Data\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 289,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"A\\xE7\\xF5es\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 292,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 275,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              className: \"bg-white divide-y divide-gray-200\",\n              children: payments.map(payment => /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                  children: [\"#\", payment.id.slice(-8)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 300,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                  children: formatCurrency(payment.amount)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 303,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap\",\n                  children: /*#__PURE__*/_jsxDEV(PaymentStatus, {\n                    status: payment.status,\n                    size: \"sm\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 307,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 306,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                  children: payment.paymentMethod\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 309,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                  children: new Date(payment.createdAt).toLocaleDateString('pt-PT')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 312,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                  children: MulticaixaService.canRefund(payment.status) && /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleRefundPayment(payment.id),\n                    className: \"text-red-600 hover:text-red-900\",\n                    children: \"Reembolsar\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 317,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 315,\n                  columnNumber: 23\n                }, this)]\n              }, payment.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 299,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 269,\n        columnNumber: 11\n      }, this), activeTab === 'orders' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-6 py-4 border-b border-gray-200\",\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-medium text-gray-900\",\n            children: \"Gest\\xE3o de Pedidos\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 337,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 336,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: orders.map(order => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"border border-gray-200 rounded-lg p-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"text-lg font-medium text-gray-900\",\n                  children: [\"Pedido #\", order.id.slice(-8)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 344,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `px-2 py-1 text-xs font-medium rounded-full ${order.status === 'delivered' ? 'bg-green-100 text-green-800' : order.status === 'in_progress' ? 'bg-blue-100 text-blue-800' : order.status === 'ready' ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800'}`,\n                  children: order.status\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 347,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 343,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-2 gap-4 text-sm\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-500\",\n                    children: \"Total:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 358,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"ml-2 font-medium\",\n                    children: formatCurrency(order.totalAmount)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 359,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 357,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-500\",\n                    children: \"Pagamento:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 362,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `ml-2 font-medium ${order.paymentStatus === 'paid' ? 'text-green-600' : order.paymentStatus === 'pending' ? 'text-yellow-600' : 'text-red-600'}`,\n                    children: order.paymentStatus\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 363,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 361,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-500\",\n                    children: \"Data:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 372,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"ml-2\",\n                    children: new Date(order.createdAt).toLocaleDateString('pt-PT')\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 373,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 371,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-500\",\n                    children: \"Itens:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 376,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"ml-2\",\n                    children: order.printJobs.length\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 377,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 375,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 356,\n                columnNumber: 21\n              }, this)]\n            }, order.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 342,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 340,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 339,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 335,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 112,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 111,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminPanel, \"zuWV3cJyXlYzKSW8tEbSqSWFcok=\");\n_c = AdminPanel;\nexport default AdminPanel;\nvar _c;\n$RefreshReg$(_c, \"AdminPanel\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "PaymentStatus", "MulticaixaService", "apiService", "jsxDEV", "_jsxDEV", "AdminPanel", "_s", "activeTab", "setActiveTab", "stats", "setStats", "totalRevenue", "totalPayments", "pendingPayments", "completedPayments", "failedPayments", "payments", "setPayments", "orders", "setOrders", "loading", "setLoading", "loadDashboardData", "paymentsResponse", "get", "success", "data", "calculateStats", "ordersResponse", "error", "console", "paymentsData", "reduce", "acc", "payment", "status", "amount", "handleRefundPayment", "paymentId", "confirm", "response", "requestRefund", "alert", "formatCurrency", "formatAoaAmount", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "id", "label", "map", "tab", "onClick", "fill", "viewBox", "d", "fillRule", "clipRule", "slice", "size", "Date", "createdAt", "toLocaleDateString", "paymentMethod", "canRefund", "order", "totalAmount", "paymentStatus", "printJobs", "length", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/AdminPanel.tsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport { PaymentStatus } from '../components';\nimport { MulticaixaService } from '../services/multicaixa';\nimport { apiService } from '../services/api';\nimport { Payment, Order } from '../types';\n\ninterface DashboardStats {\n  totalRevenue: number;\n  totalPayments: number;\n  pendingPayments: number;\n  completedPayments: number;\n  failedPayments: number;\n}\n\nconst AdminPanel: React.FC = () => {\n  const [activeTab, setActiveTab] = useState<'overview' | 'payments' | 'orders'>('overview');\n  const [stats, setStats] = useState<DashboardStats>({\n    totalRevenue: 0,\n    totalPayments: 0,\n    pendingPayments: 0,\n    completedPayments: 0,\n    failedPayments: 0,\n  });\n  const [payments, setPayments] = useState<Payment[]>([]);\n  const [orders, setOrders] = useState<Order[]>([]);\n  const [loading, setLoading] = useState(true);\n\n  const loadDashboardData = useCallback(async () => {\n    setLoading(true);\n    try {\n      // Load payments\n      const paymentsResponse = await apiService.get<Payment[]>('/payments');\n      if (paymentsResponse.success && paymentsResponse.data) {\n        setPayments(paymentsResponse.data);\n        calculateStats(paymentsResponse.data);\n      }\n\n      // Load orders\n      const ordersResponse = await apiService.get<Order[]>('/orders');\n      if (ordersResponse.success && ordersResponse.data) {\n        setOrders(ordersResponse.data);\n      }\n    } catch (error) {\n      console.error('Error loading dashboard data:', error);\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n\n  useEffect(() => {\n    loadDashboardData();\n  }, [loadDashboardData]);\n\n  const calculateStats = (paymentsData: Payment[]) => {\n    const stats = paymentsData.reduce((acc, payment) => {\n      acc.totalPayments++;\n\n      if (payment.status === 'COMPLETED') {\n        acc.completedPayments++;\n        acc.totalRevenue += payment.amount;\n      } else if (payment.status === 'PENDING' || payment.status === 'PROCESSING') {\n        acc.pendingPayments++;\n      } else if (payment.status === 'FAILED') {\n        acc.failedPayments++;\n      }\n\n      return acc;\n    }, {\n      totalRevenue: 0,\n      totalPayments: 0,\n      pendingPayments: 0,\n      completedPayments: 0,\n      failedPayments: 0,\n    });\n\n    setStats(stats);\n  };\n\n  const handleRefundPayment = async (paymentId: string) => {\n    // eslint-disable-next-line no-restricted-globals\n    if (!confirm('Tem certeza que deseja processar este reembolso?')) {\n      return;\n    }\n\n    try {\n      const response = await MulticaixaService.requestRefund(paymentId);\n      if (response.success) {\n        alert('Reembolso processado com sucesso!');\n        loadDashboardData(); // Reload data\n      } else {\n        alert(`Erro ao processar reembolso: ${response.error}`);\n      }\n    } catch (error) {\n      alert('Erro ao processar reembolso');\n    }\n  };\n\n  const formatCurrency = (amount: number) => {\n    return MulticaixaService.formatAoaAmount(amount);\n  };\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Header */}\n        <div className=\"mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900\">Painel Administrativo</h1>\n          <p className=\"text-gray-600\">Gestão de pagamentos e pedidos WePrint AI</p>\n        </div>\n\n        {/* Navigation Tabs */}\n        <div className=\"mb-8\">\n          <nav className=\"flex space-x-8\">\n            {[\n              { id: 'overview', label: 'Visão Geral' },\n              { id: 'payments', label: 'Pagamentos' },\n              { id: 'orders', label: 'Pedidos' },\n            ].map((tab) => (\n              <button\n                key={tab.id}\n                onClick={() => setActiveTab(tab.id as any)}\n                className={`py-2 px-1 border-b-2 font-medium text-sm ${\n                  activeTab === tab.id\n                    ? 'border-blue-500 text-blue-600'\n                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                }`}\n              >\n                {tab.label}\n              </button>\n            ))}\n          </nav>\n        </div>\n\n        {/* Overview Tab */}\n        {activeTab === 'overview' && (\n          <div className=\"space-y-6\">\n            {/* Stats Cards */}\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n              <div className=\"bg-white rounded-lg shadow p-6\">\n                <div className=\"flex items-center\">\n                  <div className=\"flex-shrink-0\">\n                    <div className=\"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center\">\n                      <svg className=\"w-5 h-5 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path d=\"M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z\" />\n                      </svg>\n                    </div>\n                  </div>\n                  <div className=\"ml-5 w-0 flex-1\">\n                    <dl>\n                      <dt className=\"text-sm font-medium text-gray-500 truncate\">\n                        Receita Total\n                      </dt>\n                      <dd className=\"text-lg font-medium text-gray-900\">\n                        {formatCurrency(stats.totalRevenue)}\n                      </dd>\n                    </dl>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"bg-white rounded-lg shadow p-6\">\n                <div className=\"flex items-center\">\n                  <div className=\"flex-shrink-0\">\n                    <div className=\"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center\">\n                      <svg className=\"w-5 h-5 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n                      </svg>\n                    </div>\n                  </div>\n                  <div className=\"ml-5 w-0 flex-1\">\n                    <dl>\n                      <dt className=\"text-sm font-medium text-gray-500 truncate\">\n                        Pagamentos Concluídos\n                      </dt>\n                      <dd className=\"text-lg font-medium text-gray-900\">\n                        {stats.completedPayments}\n                      </dd>\n                    </dl>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"bg-white rounded-lg shadow p-6\">\n                <div className=\"flex items-center\">\n                  <div className=\"flex-shrink-0\">\n                    <div className=\"w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center\">\n                      <svg className=\"w-5 h-5 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z\" clipRule=\"evenodd\" />\n                      </svg>\n                    </div>\n                  </div>\n                  <div className=\"ml-5 w-0 flex-1\">\n                    <dl>\n                      <dt className=\"text-sm font-medium text-gray-500 truncate\">\n                        Pagamentos Pendentes\n                      </dt>\n                      <dd className=\"text-lg font-medium text-gray-900\">\n                        {stats.pendingPayments}\n                      </dd>\n                    </dl>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"bg-white rounded-lg shadow p-6\">\n                <div className=\"flex items-center\">\n                  <div className=\"flex-shrink-0\">\n                    <div className=\"w-8 h-8 bg-red-500 rounded-md flex items-center justify-center\">\n                      <svg className=\"w-5 h-5 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\n                      </svg>\n                    </div>\n                  </div>\n                  <div className=\"ml-5 w-0 flex-1\">\n                    <dl>\n                      <dt className=\"text-sm font-medium text-gray-500 truncate\">\n                        Pagamentos Falhados\n                      </dt>\n                      <dd className=\"text-lg font-medium text-gray-900\">\n                        {stats.failedPayments}\n                      </dd>\n                    </dl>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Recent Activity */}\n            <div className=\"bg-white rounded-lg shadow\">\n              <div className=\"px-6 py-4 border-b border-gray-200\">\n                <h3 className=\"text-lg font-medium text-gray-900\">Atividade Recente</h3>\n              </div>\n              <div className=\"p-6\">\n                <div className=\"space-y-4\">\n                  {payments.slice(0, 5).map((payment) => (\n                    <div key={payment.id} className=\"flex items-center justify-between\">\n                      <div className=\"flex items-center space-x-3\">\n                        <PaymentStatus status={payment.status} size=\"sm\" />\n                        <div>\n                          <p className=\"text-sm font-medium text-gray-900\">\n                            Pagamento #{payment.id.slice(-8)}\n                          </p>\n                          <p className=\"text-sm text-gray-500\">\n                            {formatCurrency(payment.amount)}\n                          </p>\n                        </div>\n                      </div>\n                      <div className=\"text-sm text-gray-500\">\n                        {new Date(payment.createdAt).toLocaleDateString('pt-PT')}\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Payments Tab */}\n        {activeTab === 'payments' && (\n          <div className=\"bg-white rounded-lg shadow\">\n            <div className=\"px-6 py-4 border-b border-gray-200\">\n              <h3 className=\"text-lg font-medium text-gray-900\">Gestão de Pagamentos</h3>\n            </div>\n            <div className=\"overflow-x-auto\">\n              <table className=\"min-w-full divide-y divide-gray-200\">\n                <thead className=\"bg-gray-50\">\n                  <tr>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      ID\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Valor\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Status\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Método\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Data\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Ações\n                    </th>\n                  </tr>\n                </thead>\n                <tbody className=\"bg-white divide-y divide-gray-200\">\n                  {payments.map((payment) => (\n                    <tr key={payment.id}>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">\n                        #{payment.id.slice(-8)}\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                        {formatCurrency(payment.amount)}\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\n                        <PaymentStatus status={payment.status} size=\"sm\" />\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                        {payment.paymentMethod}\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                        {new Date(payment.createdAt).toLocaleDateString('pt-PT')}\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                        {MulticaixaService.canRefund(payment.status) && (\n                          <button\n                            onClick={() => handleRefundPayment(payment.id)}\n                            className=\"text-red-600 hover:text-red-900\"\n                          >\n                            Reembolsar\n                          </button>\n                        )}\n                      </td>\n                    </tr>\n                  ))}\n                </tbody>\n              </table>\n            </div>\n          </div>\n        )}\n\n        {/* Orders Tab */}\n        {activeTab === 'orders' && (\n          <div className=\"bg-white rounded-lg shadow\">\n            <div className=\"px-6 py-4 border-b border-gray-200\">\n              <h3 className=\"text-lg font-medium text-gray-900\">Gestão de Pedidos</h3>\n            </div>\n            <div className=\"p-6\">\n              <div className=\"space-y-4\">\n                {orders.map((order) => (\n                  <div key={order.id} className=\"border border-gray-200 rounded-lg p-4\">\n                    <div className=\"flex items-center justify-between mb-2\">\n                      <h4 className=\"text-lg font-medium text-gray-900\">\n                        Pedido #{order.id.slice(-8)}\n                      </h4>\n                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${\n                        order.status === 'delivered' ? 'bg-green-100 text-green-800' :\n                        order.status === 'in_progress' ? 'bg-blue-100 text-blue-800' :\n                        order.status === 'ready' ? 'bg-yellow-100 text-yellow-800' :\n                        'bg-gray-100 text-gray-800'\n                      }`}>\n                        {order.status}\n                      </span>\n                    </div>\n                    <div className=\"grid grid-cols-2 gap-4 text-sm\">\n                      <div>\n                        <span className=\"text-gray-500\">Total:</span>\n                        <span className=\"ml-2 font-medium\">{formatCurrency(order.totalAmount)}</span>\n                      </div>\n                      <div>\n                        <span className=\"text-gray-500\">Pagamento:</span>\n                        <span className={`ml-2 font-medium ${\n                          order.paymentStatus === 'paid' ? 'text-green-600' :\n                          order.paymentStatus === 'pending' ? 'text-yellow-600' :\n                          'text-red-600'\n                        }`}>\n                          {order.paymentStatus}\n                        </span>\n                      </div>\n                      <div>\n                        <span className=\"text-gray-500\">Data:</span>\n                        <span className=\"ml-2\">{new Date(order.createdAt).toLocaleDateString('pt-PT')}</span>\n                      </div>\n                      <div>\n                        <span className=\"text-gray-500\">Itens:</span>\n                        <span className=\"ml-2\">{order.printJobs.length}</span>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default AdminPanel;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,aAAa,QAAQ,eAAe;AAC7C,SAASC,iBAAiB,QAAQ,wBAAwB;AAC1D,SAASC,UAAU,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAW7C,MAAMC,UAAoB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjC,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGX,QAAQ,CAAqC,UAAU,CAAC;EAC1F,MAAM,CAACY,KAAK,EAAEC,QAAQ,CAAC,GAAGb,QAAQ,CAAiB;IACjDc,YAAY,EAAE,CAAC;IACfC,aAAa,EAAE,CAAC;IAChBC,eAAe,EAAE,CAAC;IAClBC,iBAAiB,EAAE,CAAC;IACpBC,cAAc,EAAE;EAClB,CAAC,CAAC;EACF,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGpB,QAAQ,CAAY,EAAE,CAAC;EACvD,MAAM,CAACqB,MAAM,EAAEC,SAAS,CAAC,GAAGtB,QAAQ,CAAU,EAAE,CAAC;EACjD,MAAM,CAACuB,OAAO,EAAEC,UAAU,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EAE5C,MAAMyB,iBAAiB,GAAGvB,WAAW,CAAC,YAAY;IAChDsB,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF;MACA,MAAME,gBAAgB,GAAG,MAAMrB,UAAU,CAACsB,GAAG,CAAY,WAAW,CAAC;MACrE,IAAID,gBAAgB,CAACE,OAAO,IAAIF,gBAAgB,CAACG,IAAI,EAAE;QACrDT,WAAW,CAACM,gBAAgB,CAACG,IAAI,CAAC;QAClCC,cAAc,CAACJ,gBAAgB,CAACG,IAAI,CAAC;MACvC;;MAEA;MACA,MAAME,cAAc,GAAG,MAAM1B,UAAU,CAACsB,GAAG,CAAU,SAAS,CAAC;MAC/D,IAAII,cAAc,CAACH,OAAO,IAAIG,cAAc,CAACF,IAAI,EAAE;QACjDP,SAAS,CAACS,cAAc,CAACF,IAAI,CAAC;MAChC;IACF,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACvD,CAAC,SAAS;MACRR,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,EAAE,CAAC;EAENvB,SAAS,CAAC,MAAM;IACdwB,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,CAACA,iBAAiB,CAAC,CAAC;EAEvB,MAAMK,cAAc,GAAII,YAAuB,IAAK;IAClD,MAAMtB,KAAK,GAAGsB,YAAY,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,OAAO,KAAK;MAClDD,GAAG,CAACrB,aAAa,EAAE;MAEnB,IAAIsB,OAAO,CAACC,MAAM,KAAK,WAAW,EAAE;QAClCF,GAAG,CAACnB,iBAAiB,EAAE;QACvBmB,GAAG,CAACtB,YAAY,IAAIuB,OAAO,CAACE,MAAM;MACpC,CAAC,MAAM,IAAIF,OAAO,CAACC,MAAM,KAAK,SAAS,IAAID,OAAO,CAACC,MAAM,KAAK,YAAY,EAAE;QAC1EF,GAAG,CAACpB,eAAe,EAAE;MACvB,CAAC,MAAM,IAAIqB,OAAO,CAACC,MAAM,KAAK,QAAQ,EAAE;QACtCF,GAAG,CAAClB,cAAc,EAAE;MACtB;MAEA,OAAOkB,GAAG;IACZ,CAAC,EAAE;MACDtB,YAAY,EAAE,CAAC;MACfC,aAAa,EAAE,CAAC;MAChBC,eAAe,EAAE,CAAC;MAClBC,iBAAiB,EAAE,CAAC;MACpBC,cAAc,EAAE;IAClB,CAAC,CAAC;IAEFL,QAAQ,CAACD,KAAK,CAAC;EACjB,CAAC;EAED,MAAM4B,mBAAmB,GAAG,MAAOC,SAAiB,IAAK;IACvD;IACA,IAAI,CAACC,OAAO,CAAC,kDAAkD,CAAC,EAAE;MAChE;IACF;IAEA,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMvC,iBAAiB,CAACwC,aAAa,CAACH,SAAS,CAAC;MACjE,IAAIE,QAAQ,CAACf,OAAO,EAAE;QACpBiB,KAAK,CAAC,mCAAmC,CAAC;QAC1CpB,iBAAiB,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,MAAM;QACLoB,KAAK,CAAC,gCAAgCF,QAAQ,CAACX,KAAK,EAAE,CAAC;MACzD;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACda,KAAK,CAAC,6BAA6B,CAAC;IACtC;EACF,CAAC;EAED,MAAMC,cAAc,GAAIP,MAAc,IAAK;IACzC,OAAOnC,iBAAiB,CAAC2C,eAAe,CAACR,MAAM,CAAC;EAClD,CAAC;EAED,IAAIhB,OAAO,EAAE;IACX,oBACEhB,OAAA;MAAKyC,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eACvE1C,OAAA;QAAKyC,SAAS,EAAC;MAAgE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnF,CAAC;EAEV;EAEA,oBACE9C,OAAA;IAAKyC,SAAS,EAAC,yBAAyB;IAAAC,QAAA,eACtC1C,OAAA;MAAKyC,SAAS,EAAC,6CAA6C;MAAAC,QAAA,gBAE1D1C,OAAA;QAAKyC,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnB1C,OAAA;UAAIyC,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAqB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3E9C,OAAA;UAAGyC,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAyC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvE,CAAC,eAGN9C,OAAA;QAAKyC,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnB1C,OAAA;UAAKyC,SAAS,EAAC,gBAAgB;UAAAC,QAAA,EAC5B,CACC;YAAEK,EAAE,EAAE,UAAU;YAAEC,KAAK,EAAE;UAAc,CAAC,EACxC;YAAED,EAAE,EAAE,UAAU;YAAEC,KAAK,EAAE;UAAa,CAAC,EACvC;YAAED,EAAE,EAAE,QAAQ;YAAEC,KAAK,EAAE;UAAU,CAAC,CACnC,CAACC,GAAG,CAAEC,GAAG,iBACRlD,OAAA;YAEEmD,OAAO,EAAEA,CAAA,KAAM/C,YAAY,CAAC8C,GAAG,CAACH,EAAS,CAAE;YAC3CN,SAAS,EAAE,4CACTtC,SAAS,KAAK+C,GAAG,CAACH,EAAE,GAChB,+BAA+B,GAC/B,4EAA4E,EAC/E;YAAAL,QAAA,EAEFQ,GAAG,CAACF;UAAK,GARLE,GAAG,CAACH,EAAE;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OASL,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGL3C,SAAS,KAAK,UAAU,iBACvBH,OAAA;QAAKyC,SAAS,EAAC,WAAW;QAAAC,QAAA,gBAExB1C,OAAA;UAAKyC,SAAS,EAAC,sDAAsD;UAAAC,QAAA,gBACnE1C,OAAA;YAAKyC,SAAS,EAAC,gCAAgC;YAAAC,QAAA,eAC7C1C,OAAA;cAAKyC,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChC1C,OAAA;gBAAKyC,SAAS,EAAC,eAAe;gBAAAC,QAAA,eAC5B1C,OAAA;kBAAKyC,SAAS,EAAC,kEAAkE;kBAAAC,QAAA,eAC/E1C,OAAA;oBAAKyC,SAAS,EAAC,oBAAoB;oBAACW,IAAI,EAAC,cAAc;oBAACC,OAAO,EAAC,WAAW;oBAAAX,QAAA,eACzE1C,OAAA;sBAAMsD,CAAC,EAAC;oBAAmJ;sBAAAX,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3J;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN9C,OAAA;gBAAKyC,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,eAC9B1C,OAAA;kBAAA0C,QAAA,gBACE1C,OAAA;oBAAIyC,SAAS,EAAC,4CAA4C;oBAAAC,QAAA,EAAC;kBAE3D;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACL9C,OAAA;oBAAIyC,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAC9CH,cAAc,CAAClC,KAAK,CAACE,YAAY;kBAAC;oBAAAoC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN9C,OAAA;YAAKyC,SAAS,EAAC,gCAAgC;YAAAC,QAAA,eAC7C1C,OAAA;cAAKyC,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChC1C,OAAA;gBAAKyC,SAAS,EAAC,eAAe;gBAAAC,QAAA,eAC5B1C,OAAA;kBAAKyC,SAAS,EAAC,iEAAiE;kBAAAC,QAAA,eAC9E1C,OAAA;oBAAKyC,SAAS,EAAC,oBAAoB;oBAACW,IAAI,EAAC,cAAc;oBAACC,OAAO,EAAC,WAAW;oBAAAX,QAAA,eACzE1C,OAAA;sBAAMuD,QAAQ,EAAC,SAAS;sBAACD,CAAC,EAAC,uIAAuI;sBAACE,QAAQ,EAAC;oBAAS;sBAAAb,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrL;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN9C,OAAA;gBAAKyC,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,eAC9B1C,OAAA;kBAAA0C,QAAA,gBACE1C,OAAA;oBAAIyC,SAAS,EAAC,4CAA4C;oBAAAC,QAAA,EAAC;kBAE3D;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACL9C,OAAA;oBAAIyC,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAC9CrC,KAAK,CAACK;kBAAiB;oBAAAiC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN9C,OAAA;YAAKyC,SAAS,EAAC,gCAAgC;YAAAC,QAAA,eAC7C1C,OAAA;cAAKyC,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChC1C,OAAA;gBAAKyC,SAAS,EAAC,eAAe;gBAAAC,QAAA,eAC5B1C,OAAA;kBAAKyC,SAAS,EAAC,mEAAmE;kBAAAC,QAAA,eAChF1C,OAAA;oBAAKyC,SAAS,EAAC,oBAAoB;oBAACW,IAAI,EAAC,cAAc;oBAACC,OAAO,EAAC,WAAW;oBAAAX,QAAA,eACzE1C,OAAA;sBAAMuD,QAAQ,EAAC,SAAS;sBAACD,CAAC,EAAC,oHAAoH;sBAACE,QAAQ,EAAC;oBAAS;sBAAAb,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClK;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN9C,OAAA;gBAAKyC,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,eAC9B1C,OAAA;kBAAA0C,QAAA,gBACE1C,OAAA;oBAAIyC,SAAS,EAAC,4CAA4C;oBAAAC,QAAA,EAAC;kBAE3D;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACL9C,OAAA;oBAAIyC,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAC9CrC,KAAK,CAACI;kBAAe;oBAAAkC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN9C,OAAA;YAAKyC,SAAS,EAAC,gCAAgC;YAAAC,QAAA,eAC7C1C,OAAA;cAAKyC,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChC1C,OAAA;gBAAKyC,SAAS,EAAC,eAAe;gBAAAC,QAAA,eAC5B1C,OAAA;kBAAKyC,SAAS,EAAC,gEAAgE;kBAAAC,QAAA,eAC7E1C,OAAA;oBAAKyC,SAAS,EAAC,oBAAoB;oBAACW,IAAI,EAAC,cAAc;oBAACC,OAAO,EAAC,WAAW;oBAAAX,QAAA,eACzE1C,OAAA;sBAAMuD,QAAQ,EAAC,SAAS;sBAACD,CAAC,EAAC,yNAAyN;sBAACE,QAAQ,EAAC;oBAAS;sBAAAb,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN9C,OAAA;gBAAKyC,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,eAC9B1C,OAAA;kBAAA0C,QAAA,gBACE1C,OAAA;oBAAIyC,SAAS,EAAC,4CAA4C;oBAAAC,QAAA,EAAC;kBAE3D;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACL9C,OAAA;oBAAIyC,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAC9CrC,KAAK,CAACM;kBAAc;oBAAAgC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN9C,OAAA;UAAKyC,SAAS,EAAC,4BAA4B;UAAAC,QAAA,gBACzC1C,OAAA;YAAKyC,SAAS,EAAC,oCAAoC;YAAAC,QAAA,eACjD1C,OAAA;cAAIyC,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrE,CAAC,eACN9C,OAAA;YAAKyC,SAAS,EAAC,KAAK;YAAAC,QAAA,eAClB1C,OAAA;cAAKyC,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvB9B,QAAQ,CAAC6C,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACR,GAAG,CAAEnB,OAAO,iBAChC9B,OAAA;gBAAsByC,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,gBACjE1C,OAAA;kBAAKyC,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1C1C,OAAA,CAACJ,aAAa;oBAACmC,MAAM,EAAED,OAAO,CAACC,MAAO;oBAAC2B,IAAI,EAAC;kBAAI;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACnD9C,OAAA;oBAAA0C,QAAA,gBACE1C,OAAA;sBAAGyC,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,GAAC,aACpC,EAACZ,OAAO,CAACiB,EAAE,CAACU,KAAK,CAAC,CAAC,CAAC,CAAC;oBAAA;sBAAAd,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/B,CAAC,eACJ9C,OAAA;sBAAGyC,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EACjCH,cAAc,CAACT,OAAO,CAACE,MAAM;oBAAC;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN9C,OAAA;kBAAKyC,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EACnC,IAAIiB,IAAI,CAAC7B,OAAO,CAAC8B,SAAS,CAAC,CAACC,kBAAkB,CAAC,OAAO;gBAAC;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD,CAAC;cAAA,GAdEhB,OAAO,CAACiB,EAAE;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAef,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA3C,SAAS,KAAK,UAAU,iBACvBH,OAAA;QAAKyC,SAAS,EAAC,4BAA4B;QAAAC,QAAA,gBACzC1C,OAAA;UAAKyC,SAAS,EAAC,oCAAoC;UAAAC,QAAA,eACjD1C,OAAA;YAAIyC,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxE,CAAC,eACN9C,OAAA;UAAKyC,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9B1C,OAAA;YAAOyC,SAAS,EAAC,qCAAqC;YAAAC,QAAA,gBACpD1C,OAAA;cAAOyC,SAAS,EAAC,YAAY;cAAAC,QAAA,eAC3B1C,OAAA;gBAAA0C,QAAA,gBACE1C,OAAA;kBAAIyC,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL9C,OAAA;kBAAIyC,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL9C,OAAA;kBAAIyC,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL9C,OAAA;kBAAIyC,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL9C,OAAA;kBAAIyC,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL9C,OAAA;kBAAIyC,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACR9C,OAAA;cAAOyC,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EACjD9B,QAAQ,CAACqC,GAAG,CAAEnB,OAAO,iBACpB9B,OAAA;gBAAA0C,QAAA,gBACE1C,OAAA;kBAAIyC,SAAS,EAAC,+DAA+D;kBAAAC,QAAA,GAAC,GAC3E,EAACZ,OAAO,CAACiB,EAAE,CAACU,KAAK,CAAC,CAAC,CAAC,CAAC;gBAAA;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB,CAAC,eACL9C,OAAA;kBAAIyC,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,EAC9DH,cAAc,CAACT,OAAO,CAACE,MAAM;gBAAC;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B,CAAC,eACL9C,OAAA;kBAAIyC,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,eACzC1C,OAAA,CAACJ,aAAa;oBAACmC,MAAM,EAAED,OAAO,CAACC,MAAO;oBAAC2B,IAAI,EAAC;kBAAI;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjD,CAAC,eACL9C,OAAA;kBAAIyC,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,EAC9DZ,OAAO,CAACgC;gBAAa;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB,CAAC,eACL9C,OAAA;kBAAIyC,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,EAC9D,IAAIiB,IAAI,CAAC7B,OAAO,CAAC8B,SAAS,CAAC,CAACC,kBAAkB,CAAC,OAAO;gBAAC;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD,CAAC,eACL9C,OAAA;kBAAIyC,SAAS,EAAC,iDAAiD;kBAAAC,QAAA,EAC5D7C,iBAAiB,CAACkE,SAAS,CAACjC,OAAO,CAACC,MAAM,CAAC,iBAC1C/B,OAAA;oBACEmD,OAAO,EAAEA,CAAA,KAAMlB,mBAAmB,CAACH,OAAO,CAACiB,EAAE,CAAE;oBAC/CN,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAC5C;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBACT;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA,GAzBEhB,OAAO,CAACiB,EAAE;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA0Bf,CACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA3C,SAAS,KAAK,QAAQ,iBACrBH,OAAA;QAAKyC,SAAS,EAAC,4BAA4B;QAAAC,QAAA,gBACzC1C,OAAA;UAAKyC,SAAS,EAAC,oCAAoC;UAAAC,QAAA,eACjD1C,OAAA;YAAIyC,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrE,CAAC,eACN9C,OAAA;UAAKyC,SAAS,EAAC,KAAK;UAAAC,QAAA,eAClB1C,OAAA;YAAKyC,SAAS,EAAC,WAAW;YAAAC,QAAA,EACvB5B,MAAM,CAACmC,GAAG,CAAEe,KAAK,iBAChBhE,OAAA;cAAoByC,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACnE1C,OAAA;gBAAKyC,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,gBACrD1C,OAAA;kBAAIyC,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,GAAC,UACxC,EAACsB,KAAK,CAACjB,EAAE,CAACU,KAAK,CAAC,CAAC,CAAC,CAAC;gBAAA;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC,eACL9C,OAAA;kBAAMyC,SAAS,EAAE,8CACfuB,KAAK,CAACjC,MAAM,KAAK,WAAW,GAAG,6BAA6B,GAC5DiC,KAAK,CAACjC,MAAM,KAAK,aAAa,GAAG,2BAA2B,GAC5DiC,KAAK,CAACjC,MAAM,KAAK,OAAO,GAAG,+BAA+B,GAC1D,2BAA2B,EAC1B;kBAAAW,QAAA,EACAsB,KAAK,CAACjC;gBAAM;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACN9C,OAAA;gBAAKyC,SAAS,EAAC,gCAAgC;gBAAAC,QAAA,gBAC7C1C,OAAA;kBAAA0C,QAAA,gBACE1C,OAAA;oBAAMyC,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC7C9C,OAAA;oBAAMyC,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,EAAEH,cAAc,CAACyB,KAAK,CAACC,WAAW;kBAAC;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1E,CAAC,eACN9C,OAAA;kBAAA0C,QAAA,gBACE1C,OAAA;oBAAMyC,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACjD9C,OAAA;oBAAMyC,SAAS,EAAE,oBACfuB,KAAK,CAACE,aAAa,KAAK,MAAM,GAAG,gBAAgB,GACjDF,KAAK,CAACE,aAAa,KAAK,SAAS,GAAG,iBAAiB,GACrD,cAAc,EACb;oBAAAxB,QAAA,EACAsB,KAAK,CAACE;kBAAa;oBAAAvB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACN9C,OAAA;kBAAA0C,QAAA,gBACE1C,OAAA;oBAAMyC,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC5C9C,OAAA;oBAAMyC,SAAS,EAAC,MAAM;oBAAAC,QAAA,EAAE,IAAIiB,IAAI,CAACK,KAAK,CAACJ,SAAS,CAAC,CAACC,kBAAkB,CAAC,OAAO;kBAAC;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClF,CAAC,eACN9C,OAAA;kBAAA0C,QAAA,gBACE1C,OAAA;oBAAMyC,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC7C9C,OAAA;oBAAMyC,SAAS,EAAC,MAAM;oBAAAC,QAAA,EAAEsB,KAAK,CAACG,SAAS,CAACC;kBAAM;oBAAAzB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA,GArCEkB,KAAK,CAACjB,EAAE;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAsCb,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC5C,EAAA,CAtXID,UAAoB;AAAAoE,EAAA,GAApBpE,UAAoB;AAwX1B,eAAeA,UAAU;AAAC,IAAAoE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}