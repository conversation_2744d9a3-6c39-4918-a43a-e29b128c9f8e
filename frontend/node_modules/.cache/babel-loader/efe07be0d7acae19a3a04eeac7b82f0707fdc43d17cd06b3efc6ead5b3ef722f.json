{"ast": null, "code": "import React from'react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const LandingPage=()=>/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col items-center justify-center min-h-screen bg-gray-50\",children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-3xl font-bold mb-4\",children:\"WePrint AI\"}),/*#__PURE__*/_jsx(\"p\",{className:\"mb-8\",children:\"A gr\\xE1fica que vai at\\xE9 ti\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-x-4\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"px-6 py-2 bg-blue-600 text-white rounded\",children:\"Come\\xE7ar Encomenda\"}),/*#__PURE__*/_jsx(\"button\",{className:\"px-6 py-2 bg-gray-300 text-gray-800 rounded\",children:\"Login/Cadastro\"})]})]});export default LandingPage;", "map": {"version": 3, "names": ["React", "jsx", "_jsx", "jsxs", "_jsxs", "LandingPage", "className", "children"], "sources": ["/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/LandingPage.tsx"], "sourcesContent": ["import React from 'react';\n\nconst LandingPage: React.FC = () => (\n  <div className=\"flex flex-col items-center justify-center min-h-screen bg-gray-50\">\n    <h1 className=\"text-3xl font-bold mb-4\">WePrint AI</h1>\n    <p className=\"mb-8\">A gráfica que vai até ti</p>\n    <div className=\"space-x-4\">\n      <button className=\"px-6 py-2 bg-blue-600 text-white rounded\">Começar Encomenda</button>\n      <button className=\"px-6 py-2 bg-gray-300 text-gray-800 rounded\">Login/Cadastro</button>\n    </div>\n  </div>\n);\n\nexport default LandingPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE1B,KAAM,CAAAC,WAAqB,CAAGA,CAAA,gBAC5BD,KAAA,QAAKE,SAAS,CAAC,mEAAmE,CAAAC,QAAA,eAChFL,IAAA,OAAII,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CAAC,YAAU,CAAI,CAAC,cACvDL,IAAA,MAAGI,SAAS,CAAC,MAAM,CAAAC,QAAA,CAAC,gCAAwB,CAAG,CAAC,cAChDH,KAAA,QAAKE,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBL,IAAA,WAAQI,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,sBAAiB,CAAQ,CAAC,cACvFL,IAAA,WAAQI,SAAS,CAAC,6CAA6C,CAAAC,QAAA,CAAC,gBAAc,CAAQ,CAAC,EACpF,CAAC,EACH,CACN,CAED,cAAe,CAAAF,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}