{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/CheckoutPage.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CheckoutPage = () => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: \"flex flex-col items-center justify-center min-h-screen\",\n  children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n    className: \"text-2xl font-semibold mb-4\",\n    children: \"Checkout\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n    className: \"space-y-4\",\n    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n      className: \"block w-64 p-2 border rounded\",\n      placeholder: \"Nome\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n      className: \"block w-64 p-2 border rounded\",\n      placeholder: \"Endere\\xE7o de Entrega\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n      className: \"block w-64 p-2 border rounded\",\n      placeholder: \"Telefone\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n      className: \"block w-64 p-2 border rounded\",\n      children: [/*#__PURE__*/_jsxDEV(\"option\", {\n        children: \"M\\xE9todo de Pagamento\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 11,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n        children: \"Multicaixa\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 12,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n        children: \"Mobile Money\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 13,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n        children: \"PayPal\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 14,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n        children: \"Cart\\xE3o\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 15,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"px-4 py-2 bg-green-600 text-white rounded\",\n      children: \"Confirmar\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 4,\n  columnNumber: 3\n}, this);\n_c = CheckoutPage;\nexport default CheckoutPage;\nvar _c;\n$RefreshReg$(_c, \"CheckoutPage\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "CheckoutPage", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "placeholder", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/CheckoutPage.tsx"], "sourcesContent": ["import React from 'react';\n\nconst CheckoutPage: React.FC = () => (\n  <div className=\"flex flex-col items-center justify-center min-h-screen\">\n    <h2 className=\"text-2xl font-semibold mb-4\">Checkout</h2>\n    <form className=\"space-y-4\">\n      <input className=\"block w-64 p-2 border rounded\" placeholder=\"Nome\" />\n      <input className=\"block w-64 p-2 border rounded\" placeholder=\"Endereço de Entrega\" />\n      <input className=\"block w-64 p-2 border rounded\" placeholder=\"Telefone\" />\n      <select className=\"block w-64 p-2 border rounded\">\n        <option>Método de Pagamento</option>\n        <option>Multicaixa</option>\n        <option>Mobile Money</option>\n        <option>PayPal</option>\n        <option>Cartão</option>\n      </select>\n      <button className=\"px-4 py-2 bg-green-600 text-white rounded\">Confirmar</button>\n    </form>\n  </div>\n);\n\nexport default CheckoutPage;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,YAAsB,GAAGA,CAAA,kBAC7BD,OAAA;EAAKE,SAAS,EAAC,wDAAwD;EAAAC,QAAA,gBACrEH,OAAA;IAAIE,SAAS,EAAC,6BAA6B;IAAAC,QAAA,EAAC;EAAQ;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAI,CAAC,eACzDP,OAAA;IAAME,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACzBH,OAAA;MAAOE,SAAS,EAAC,+BAA+B;MAACM,WAAW,EAAC;IAAM;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACtEP,OAAA;MAAOE,SAAS,EAAC,+BAA+B;MAACM,WAAW,EAAC;IAAqB;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACrFP,OAAA;MAAOE,SAAS,EAAC,+BAA+B;MAACM,WAAW,EAAC;IAAU;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC1EP,OAAA;MAAQE,SAAS,EAAC,+BAA+B;MAAAC,QAAA,gBAC/CH,OAAA;QAAAG,QAAA,EAAQ;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACpCP,OAAA;QAAAG,QAAA,EAAQ;MAAU;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC3BP,OAAA;QAAAG,QAAA,EAAQ;MAAY;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC7BP,OAAA;QAAAG,QAAA,EAAQ;MAAM;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACvBP,OAAA;QAAAG,QAAA,EAAQ;MAAM;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC,eACTP,OAAA;MAAQE,SAAS,EAAC,2CAA2C;MAAAC,QAAA,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC5E,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACJ,CACN;AAACE,EAAA,GAjBIR,YAAsB;AAmB5B,eAAeA,YAAY;AAAC,IAAAQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}