{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/OrderSummary.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst OrderSummary = () => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: \"flex flex-col items-center justify-center min-h-screen\",\n  children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n    className: \"text-2xl font-semibold mb-4\",\n    children: \"Resumo do Pedido\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"w-64 h-40 bg-gray-100 flex items-center justify-center mb-4\",\n    children: \"[Mockup do Pedido]\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n    className: \"mb-2\",\n    children: \"Formato: A4\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 7,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n    className: \"mb-2\",\n    children: \"Papel: Offset\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 8,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n    className: \"mb-2\",\n    children: \"Acabamento: Brilho\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 9,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n    className: \"mb-4 font-bold\",\n    children: \"Pre\\xE7o: 0,00 AKZ\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 10,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n    className: \"px-4 py-2 bg-blue-600 text-white rounded\",\n    children: \"Finalizar Pedido\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 11,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 4,\n  columnNumber: 3\n}, this);\n_c = OrderSummary;\nexport default OrderSummary;\nvar _c;\n$RefreshReg$(_c, \"OrderSummary\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "OrderSummary", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/OrderSummary.tsx"], "sourcesContent": ["import React from 'react';\n\nconst OrderSummary: React.FC = () => (\n  <div className=\"flex flex-col items-center justify-center min-h-screen\">\n    <h2 className=\"text-2xl font-semibold mb-4\">Resumo do Pedido</h2>\n    <div className=\"w-64 h-40 bg-gray-100 flex items-center justify-center mb-4\">[Mockup do Pedido]</div>\n    <p className=\"mb-2\">Formato: A4</p>\n    <p className=\"mb-2\">Papel: Offset</p>\n    <p className=\"mb-2\">Acabamento: Brilho</p>\n    <p className=\"mb-4 font-bold\">Preço: 0,00 AKZ</p>\n    <button className=\"px-4 py-2 bg-blue-600 text-white rounded\">Finalizar Pedido</button>\n  </div>\n);\n\nexport default OrderSummary;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,YAAsB,GAAGA,CAAA,kBAC7BD,OAAA;EAAKE,SAAS,EAAC,wDAAwD;EAAAC,QAAA,gBACrEH,OAAA;IAAIE,SAAS,EAAC,6BAA6B;IAAAC,QAAA,EAAC;EAAgB;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAI,CAAC,eACjEP,OAAA;IAAKE,SAAS,EAAC,6DAA6D;IAAAC,QAAA,EAAC;EAAkB;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC,eACrGP,OAAA;IAAGE,SAAS,EAAC,MAAM;IAAAC,QAAA,EAAC;EAAW;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAG,CAAC,eACnCP,OAAA;IAAGE,SAAS,EAAC,MAAM;IAAAC,QAAA,EAAC;EAAa;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAG,CAAC,eACrCP,OAAA;IAAGE,SAAS,EAAC,MAAM;IAAAC,QAAA,EAAC;EAAkB;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAG,CAAC,eAC1CP,OAAA;IAAGE,SAAS,EAAC,gBAAgB;IAAAC,QAAA,EAAC;EAAe;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAG,CAAC,eACjDP,OAAA;IAAQE,SAAS,EAAC,0CAA0C;IAAAC,QAAA,EAAC;EAAgB;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAQ,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACnF,CACN;AAACC,EAAA,GAVIP,YAAsB;AAY5B,eAAeA,YAAY;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}