{"ast": null, "code": "import React,{useState,useEffect}from'react';import{useMulticaixa,usePaymentStatusPolling}from'../hooks/useMulticaixa';import{MulticaixaService}from'../services/multicaixa';import PaymentStatus from'./PaymentStatus';import PaymentQRCode from'./PaymentQRCode';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const PaymentFlow=_ref=>{let{orderId,amount,currency='AOA',description,customerEmail,customerPhone,onPaymentComplete,onPaymentFailed,onCancel,className=''}=_ref;const[paymentId,setPaymentId]=useState(null);const[transactionId,setTransactionId]=useState(null);const[expiresAt,setExpiresAt]=useState(null);const{loading,error,paymentUrl,qrCode,status,createPayment,clearError,reset}=useMulticaixa();const{isPolling,status:pollingStatus,startPolling,stopPolling}=usePaymentStatusPolling(paymentId,5000,60);// Use polling status if available, otherwise use hook status\nconst currentStatus=pollingStatus||status;useEffect(()=>{// Handle payment completion\nif(currentStatus==='COMPLETED'&&paymentId){stopPolling();onPaymentComplete===null||onPaymentComplete===void 0?void 0:onPaymentComplete(paymentId);}// Handle payment failure\nif(['FAILED','EXPIRED','CANCELLED'].includes(currentStatus||'')){stopPolling();if(currentStatus==='FAILED'){onPaymentFailed===null||onPaymentFailed===void 0?void 0:onPaymentFailed('Payment failed');}else if(currentStatus==='EXPIRED'){onPaymentFailed===null||onPaymentFailed===void 0?void 0:onPaymentFailed('Payment expired');}}},[currentStatus,paymentId,onPaymentComplete,onPaymentFailed,stopPolling]);const handleCreatePayment=async()=>{clearError();// Validate amount\nif(!MulticaixaService.validatePaymentAmount(amount,currency)){const minAmount=currency==='AOA'?'100 AOA':'1 EUR (≈850 AOA)';onPaymentFailed===null||onPaymentFailed===void 0?void 0:onPaymentFailed(\"Valor m\\xEDnimo de pagamento: \".concat(minAmount));return;}const paymentData={orderId,amount,currency,description,customerEmail,customerPhone};const response=await createPayment(paymentData);if(response.success){setPaymentId(response.paymentId||null);setTransactionId(response.transactionId||null);setExpiresAt(response.expiresAt||null);// Start polling for status updates\nif(response.paymentId){startPolling();}}else{onPaymentFailed===null||onPaymentFailed===void 0?void 0:onPaymentFailed(response.error||'Failed to create payment');}};const handleCancel=()=>{stopPolling();reset();setPaymentId(null);setTransactionId(null);setExpiresAt(null);onCancel===null||onCancel===void 0?void 0:onCancel();};const handleRetry=()=>{reset();setPaymentId(null);setTransactionId(null);setExpiresAt(null);handleCreatePayment();};const formatAmount=(amount,currency)=>{if(currency==='AOA'){return MulticaixaService.formatAoaAmount(amount);}return\"\".concat(amount,\" \").concat(currency);};// Initial state - show payment details and create button\nif(!paymentId&&!loading){return/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-lg shadow-lg p-6 max-w-md mx-auto \".concat(className),children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-center mb-6\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold text-gray-900 mb-2\",children:\"Confirmar Pagamento\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-3xl font-bold text-blue-600 mb-2\",children:formatAmount(amount,currency)}),description&&/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-600\",children:description})]}),error&&/*#__PURE__*/_jsx(\"div\",{className:\"mb-4 p-3 bg-red-50 border border-red-200 rounded-md\",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-red-700\",children:error})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex gap-3\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:handleCreatePayment,disabled:loading,className:\"flex-1 bg-blue-600 text-white py-3 px-4 rounded-md font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",children:loading?'Criando...':'Pagar com Multicaixa'}),onCancel&&/*#__PURE__*/_jsx(\"button\",{onClick:handleCancel,className:\"px-4 py-3 border border-gray-300 text-gray-700 rounded-md font-medium hover:bg-gray-50 transition-colors\",children:\"Cancelar\"})]})]});}// Loading state\nif(loading){return/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-lg shadow-lg p-6 max-w-md mx-auto \".concat(className),children:/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600\",children:\"Criando pagamento...\"})]})});}// Payment created - show QR code and status\nif(paymentUrl&&qrCode){return/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-4 \".concat(className),children:[/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-lg shadow-lg p-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold text-gray-900\",children:\"Status do Pagamento\"}),transactionId&&/*#__PURE__*/_jsxs(\"p\",{className:\"text-sm text-gray-500\",children:[\"ID: \",transactionId]})]}),/*#__PURE__*/_jsx(PaymentStatus,{status:currentStatus||'PENDING'})]}),isPolling&&/*#__PURE__*/_jsxs(\"div\",{className:\"mt-3 flex items-center gap-2 text-sm text-blue-600\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600\"}),\"Verificando status...\"]})]}),currentStatus!=='COMPLETED'&&/*#__PURE__*/_jsx(PaymentQRCode,{qrCode:qrCode,paymentUrl:paymentUrl,amount:amount,currency:currency,expiresAt:expiresAt||undefined}),currentStatus==='COMPLETED'&&/*#__PURE__*/_jsxs(\"div\",{className:\"bg-green-50 border border-green-200 rounded-lg p-6 text-center\",children:[/*#__PURE__*/_jsx(\"svg\",{className:\"w-16 h-16 text-green-500 mx-auto mb-4\",fill:\"currentColor\",viewBox:\"0 0 20 20\",children:/*#__PURE__*/_jsx(\"path\",{fillRule:\"evenodd\",d:\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",clipRule:\"evenodd\"})}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold text-green-900 mb-2\",children:\"Pagamento Conclu\\xEDdo!\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-green-700\",children:\"O seu pagamento foi processado com sucesso.\"})]}),['FAILED','EXPIRED','CANCELLED'].includes(currentStatus||'')&&/*#__PURE__*/_jsxs(\"div\",{className:\"bg-red-50 border border-red-200 rounded-lg p-6 text-center\",children:[/*#__PURE__*/_jsx(\"svg\",{className:\"w-16 h-16 text-red-500 mx-auto mb-4\",fill:\"currentColor\",viewBox:\"0 0 20 20\",children:/*#__PURE__*/_jsx(\"path\",{fillRule:\"evenodd\",d:\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",clipRule:\"evenodd\"})}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold text-red-900 mb-2\",children:currentStatus==='EXPIRED'?'Pagamento Expirado':'Pagamento Falhado'}),/*#__PURE__*/_jsx(\"p\",{className:\"text-red-700 mb-4\",children:currentStatus==='EXPIRED'?'O tempo limite para pagamento foi excedido.':'Ocorreu um erro durante o processamento do pagamento.'}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex gap-3 justify-center\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:handleRetry,className:\"bg-blue-600 text-white py-2 px-4 rounded-md font-medium hover:bg-blue-700 transition-colors\",children:\"Tentar Novamente\"}),onCancel&&/*#__PURE__*/_jsx(\"button\",{onClick:handleCancel,className:\"border border-gray-300 text-gray-700 py-2 px-4 rounded-md font-medium hover:bg-gray-50 transition-colors\",children:\"Cancelar\"})]})]})]});}return null;};export default PaymentFlow;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useMulticaixa", "usePaymentStatusPolling", "MulticaixaService", "PaymentStatus", "PaymentQRCode", "jsx", "_jsx", "jsxs", "_jsxs", "PaymentFlow", "_ref", "orderId", "amount", "currency", "description", "customerEmail", "customerPhone", "onPaymentComplete", "onPaymentFailed", "onCancel", "className", "paymentId", "setPaymentId", "transactionId", "setTransactionId", "expiresAt", "setExpiresAt", "loading", "error", "paymentUrl", "qrCode", "status", "createPayment", "clearError", "reset", "isPolling", "pollingStatus", "startPolling", "stopPolling", "currentStatus", "includes", "handleCreatePayment", "validatePaymentAmount", "minAmount", "concat", "paymentData", "response", "success", "handleCancel", "handleRetry", "formatAmount", "formatAoaAmount", "children", "onClick", "disabled", "undefined", "fill", "viewBox", "fillRule", "d", "clipRule"], "sources": ["/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/PaymentFlow.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useMulticaixa, usePaymentStatusPolling } from '../hooks/useMulticaixa';\nimport { MulticaixaService } from '../services/multicaixa';\nimport PaymentStatus from './PaymentStatus';\nimport PaymentQRCode from './PaymentQRCode';\nimport { MulticaixaPaymentRequest } from '../types';\n\ninterface PaymentFlowProps {\n  orderId: string;\n  amount: number;\n  currency?: string;\n  description?: string;\n  customerEmail?: string;\n  customerPhone?: string;\n  onPaymentComplete?: (paymentId: string) => void;\n  onPaymentFailed?: (error: string) => void;\n  onCancel?: () => void;\n  className?: string;\n}\n\nconst PaymentFlow: React.FC<PaymentFlowProps> = ({\n  orderId,\n  amount,\n  currency = 'AOA',\n  description,\n  customerEmail,\n  customerPhone,\n  onPaymentComplete,\n  onPaymentFailed,\n  onCancel,\n  className = ''\n}) => {\n  const [paymentId, setPaymentId] = useState<string | null>(null);\n  const [transactionId, setTransactionId] = useState<string | null>(null);\n  const [expiresAt, setExpiresAt] = useState<string | null>(null);\n  \n  const {\n    loading,\n    error,\n    paymentUrl,\n    qrCode,\n    status,\n    createPayment,\n    clearError,\n    reset\n  } = useMulticaixa();\n\n  const {\n    isPolling,\n    status: pollingStatus,\n    startPolling,\n    stopPolling\n  } = usePaymentStatusPolling(paymentId, 5000, 60);\n\n  // Use polling status if available, otherwise use hook status\n  const currentStatus = pollingStatus || status;\n\n  useEffect(() => {\n    // Handle payment completion\n    if (currentStatus === 'COMPLETED' && paymentId) {\n      stopPolling();\n      onPaymentComplete?.(paymentId);\n    }\n    \n    // Handle payment failure\n    if (['FAILED', 'EXPIRED', 'CANCELLED'].includes(currentStatus || '')) {\n      stopPolling();\n      if (currentStatus === 'FAILED') {\n        onPaymentFailed?.('Payment failed');\n      } else if (currentStatus === 'EXPIRED') {\n        onPaymentFailed?.('Payment expired');\n      }\n    }\n  }, [currentStatus, paymentId, onPaymentComplete, onPaymentFailed, stopPolling]);\n\n  const handleCreatePayment = async () => {\n    clearError();\n    \n    // Validate amount\n    if (!MulticaixaService.validatePaymentAmount(amount, currency)) {\n      const minAmount = currency === 'AOA' ? '100 AOA' : '1 EUR (≈850 AOA)';\n      onPaymentFailed?.(`Valor mínimo de pagamento: ${minAmount}`);\n      return;\n    }\n\n    const paymentData: MulticaixaPaymentRequest = {\n      orderId,\n      amount,\n      currency,\n      description,\n      customerEmail,\n      customerPhone,\n    };\n\n    const response = await createPayment(paymentData);\n    \n    if (response.success) {\n      setPaymentId(response.paymentId || null);\n      setTransactionId(response.transactionId || null);\n      setExpiresAt(response.expiresAt || null);\n      \n      // Start polling for status updates\n      if (response.paymentId) {\n        startPolling();\n      }\n    } else {\n      onPaymentFailed?.(response.error || 'Failed to create payment');\n    }\n  };\n\n  const handleCancel = () => {\n    stopPolling();\n    reset();\n    setPaymentId(null);\n    setTransactionId(null);\n    setExpiresAt(null);\n    onCancel?.();\n  };\n\n  const handleRetry = () => {\n    reset();\n    setPaymentId(null);\n    setTransactionId(null);\n    setExpiresAt(null);\n    handleCreatePayment();\n  };\n\n  const formatAmount = (amount: number, currency: string) => {\n    if (currency === 'AOA') {\n      return MulticaixaService.formatAoaAmount(amount);\n    }\n    return `${amount} ${currency}`;\n  };\n\n  // Initial state - show payment details and create button\n  if (!paymentId && !loading) {\n    return (\n      <div className={`bg-white rounded-lg shadow-lg p-6 max-w-md mx-auto ${className}`}>\n        <div className=\"text-center mb-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">\n            Confirmar Pagamento\n          </h3>\n          <p className=\"text-3xl font-bold text-blue-600 mb-2\">\n            {formatAmount(amount, currency)}\n          </p>\n          {description && (\n            <p className=\"text-sm text-gray-600\">{description}</p>\n          )}\n        </div>\n\n        {error && (\n          <div className=\"mb-4 p-3 bg-red-50 border border-red-200 rounded-md\">\n            <p className=\"text-sm text-red-700\">{error}</p>\n          </div>\n        )}\n\n        <div className=\"flex gap-3\">\n          <button\n            onClick={handleCreatePayment}\n            disabled={loading}\n            className=\"flex-1 bg-blue-600 text-white py-3 px-4 rounded-md font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n          >\n            {loading ? 'Criando...' : 'Pagar com Multicaixa'}\n          </button>\n          {onCancel && (\n            <button\n              onClick={handleCancel}\n              className=\"px-4 py-3 border border-gray-300 text-gray-700 rounded-md font-medium hover:bg-gray-50 transition-colors\"\n            >\n              Cancelar\n            </button>\n          )}\n        </div>\n      </div>\n    );\n  }\n\n  // Loading state\n  if (loading) {\n    return (\n      <div className={`bg-white rounded-lg shadow-lg p-6 max-w-md mx-auto ${className}`}>\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600\">Criando pagamento...</p>\n        </div>\n      </div>\n    );\n  }\n\n  // Payment created - show QR code and status\n  if (paymentUrl && qrCode) {\n    return (\n      <div className={`space-y-4 ${className}`}>\n        {/* Status Header */}\n        <div className=\"bg-white rounded-lg shadow-lg p-4\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h3 className=\"text-lg font-semibold text-gray-900\">\n                Status do Pagamento\n              </h3>\n              {transactionId && (\n                <p className=\"text-sm text-gray-500\">ID: {transactionId}</p>\n              )}\n            </div>\n            <PaymentStatus status={currentStatus || 'PENDING'} />\n          </div>\n          \n          {isPolling && (\n            <div className=\"mt-3 flex items-center gap-2 text-sm text-blue-600\">\n              <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600\"></div>\n              Verificando status...\n            </div>\n          )}\n        </div>\n\n        {/* QR Code */}\n        {currentStatus !== 'COMPLETED' && (\n          <PaymentQRCode\n            qrCode={qrCode}\n            paymentUrl={paymentUrl}\n            amount={amount}\n            currency={currency}\n            expiresAt={expiresAt || undefined}\n          />\n        )}\n\n        {/* Success Message */}\n        {currentStatus === 'COMPLETED' && (\n          <div className=\"bg-green-50 border border-green-200 rounded-lg p-6 text-center\">\n            <svg className=\"w-16 h-16 text-green-500 mx-auto mb-4\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n            </svg>\n            <h3 className=\"text-lg font-semibold text-green-900 mb-2\">\n              Pagamento Concluído!\n            </h3>\n            <p className=\"text-green-700\">\n              O seu pagamento foi processado com sucesso.\n            </p>\n          </div>\n        )}\n\n        {/* Error State */}\n        {['FAILED', 'EXPIRED', 'CANCELLED'].includes(currentStatus || '') && (\n          <div className=\"bg-red-50 border border-red-200 rounded-lg p-6 text-center\">\n            <svg className=\"w-16 h-16 text-red-500 mx-auto mb-4\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\n            </svg>\n            <h3 className=\"text-lg font-semibold text-red-900 mb-2\">\n              {currentStatus === 'EXPIRED' ? 'Pagamento Expirado' : 'Pagamento Falhado'}\n            </h3>\n            <p className=\"text-red-700 mb-4\">\n              {currentStatus === 'EXPIRED' \n                ? 'O tempo limite para pagamento foi excedido.'\n                : 'Ocorreu um erro durante o processamento do pagamento.'\n              }\n            </p>\n            <div className=\"flex gap-3 justify-center\">\n              <button\n                onClick={handleRetry}\n                className=\"bg-blue-600 text-white py-2 px-4 rounded-md font-medium hover:bg-blue-700 transition-colors\"\n              >\n                Tentar Novamente\n              </button>\n              {onCancel && (\n                <button\n                  onClick={handleCancel}\n                  className=\"border border-gray-300 text-gray-700 py-2 px-4 rounded-md font-medium hover:bg-gray-50 transition-colors\"\n                >\n                  Cancelar\n                </button>\n              )}\n            </div>\n          </div>\n        )}\n      </div>\n    );\n  }\n\n  return null;\n};\n\nexport default PaymentFlow;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,aAAa,CAAEC,uBAAuB,KAAQ,wBAAwB,CAC/E,OAASC,iBAAiB,KAAQ,wBAAwB,CAC1D,MAAO,CAAAC,aAAa,KAAM,iBAAiB,CAC3C,MAAO,CAAAC,aAAa,KAAM,iBAAiB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAgB5C,KAAM,CAAAC,WAAuC,CAAGC,IAAA,EAW1C,IAX2C,CAC/CC,OAAO,CACPC,MAAM,CACNC,QAAQ,CAAG,KAAK,CAChBC,WAAW,CACXC,aAAa,CACbC,aAAa,CACbC,iBAAiB,CACjBC,eAAe,CACfC,QAAQ,CACRC,SAAS,CAAG,EACd,CAAC,CAAAV,IAAA,CACC,KAAM,CAACW,SAAS,CAAEC,YAAY,CAAC,CAAGxB,QAAQ,CAAgB,IAAI,CAAC,CAC/D,KAAM,CAACyB,aAAa,CAAEC,gBAAgB,CAAC,CAAG1B,QAAQ,CAAgB,IAAI,CAAC,CACvE,KAAM,CAAC2B,SAAS,CAAEC,YAAY,CAAC,CAAG5B,QAAQ,CAAgB,IAAI,CAAC,CAE/D,KAAM,CACJ6B,OAAO,CACPC,KAAK,CACLC,UAAU,CACVC,MAAM,CACNC,MAAM,CACNC,aAAa,CACbC,UAAU,CACVC,KACF,CAAC,CAAGlC,aAAa,CAAC,CAAC,CAEnB,KAAM,CACJmC,SAAS,CACTJ,MAAM,CAAEK,aAAa,CACrBC,YAAY,CACZC,WACF,CAAC,CAAGrC,uBAAuB,CAACoB,SAAS,CAAE,IAAI,CAAE,EAAE,CAAC,CAEhD;AACA,KAAM,CAAAkB,aAAa,CAAGH,aAAa,EAAIL,MAAM,CAE7ChC,SAAS,CAAC,IAAM,CACd;AACA,GAAIwC,aAAa,GAAK,WAAW,EAAIlB,SAAS,CAAE,CAC9CiB,WAAW,CAAC,CAAC,CACbrB,iBAAiB,SAAjBA,iBAAiB,iBAAjBA,iBAAiB,CAAGI,SAAS,CAAC,CAChC,CAEA;AACA,GAAI,CAAC,QAAQ,CAAE,SAAS,CAAE,WAAW,CAAC,CAACmB,QAAQ,CAACD,aAAa,EAAI,EAAE,CAAC,CAAE,CACpED,WAAW,CAAC,CAAC,CACb,GAAIC,aAAa,GAAK,QAAQ,CAAE,CAC9BrB,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAG,gBAAgB,CAAC,CACrC,CAAC,IAAM,IAAIqB,aAAa,GAAK,SAAS,CAAE,CACtCrB,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAG,iBAAiB,CAAC,CACtC,CACF,CACF,CAAC,CAAE,CAACqB,aAAa,CAAElB,SAAS,CAAEJ,iBAAiB,CAAEC,eAAe,CAAEoB,WAAW,CAAC,CAAC,CAE/E,KAAM,CAAAG,mBAAmB,CAAG,KAAAA,CAAA,GAAY,CACtCR,UAAU,CAAC,CAAC,CAEZ;AACA,GAAI,CAAC/B,iBAAiB,CAACwC,qBAAqB,CAAC9B,MAAM,CAAEC,QAAQ,CAAC,CAAE,CAC9D,KAAM,CAAA8B,SAAS,CAAG9B,QAAQ,GAAK,KAAK,CAAG,SAAS,CAAG,kBAAkB,CACrEK,eAAe,SAAfA,eAAe,iBAAfA,eAAe,kCAAA0B,MAAA,CAAiCD,SAAS,CAAE,CAAC,CAC5D,OACF,CAEA,KAAM,CAAAE,WAAqC,CAAG,CAC5ClC,OAAO,CACPC,MAAM,CACNC,QAAQ,CACRC,WAAW,CACXC,aAAa,CACbC,aACF,CAAC,CAED,KAAM,CAAA8B,QAAQ,CAAG,KAAM,CAAAd,aAAa,CAACa,WAAW,CAAC,CAEjD,GAAIC,QAAQ,CAACC,OAAO,CAAE,CACpBzB,YAAY,CAACwB,QAAQ,CAACzB,SAAS,EAAI,IAAI,CAAC,CACxCG,gBAAgB,CAACsB,QAAQ,CAACvB,aAAa,EAAI,IAAI,CAAC,CAChDG,YAAY,CAACoB,QAAQ,CAACrB,SAAS,EAAI,IAAI,CAAC,CAExC;AACA,GAAIqB,QAAQ,CAACzB,SAAS,CAAE,CACtBgB,YAAY,CAAC,CAAC,CAChB,CACF,CAAC,IAAM,CACLnB,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAG4B,QAAQ,CAAClB,KAAK,EAAI,0BAA0B,CAAC,CACjE,CACF,CAAC,CAED,KAAM,CAAAoB,YAAY,CAAGA,CAAA,GAAM,CACzBV,WAAW,CAAC,CAAC,CACbJ,KAAK,CAAC,CAAC,CACPZ,YAAY,CAAC,IAAI,CAAC,CAClBE,gBAAgB,CAAC,IAAI,CAAC,CACtBE,YAAY,CAAC,IAAI,CAAC,CAClBP,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAG,CAAC,CACd,CAAC,CAED,KAAM,CAAA8B,WAAW,CAAGA,CAAA,GAAM,CACxBf,KAAK,CAAC,CAAC,CACPZ,YAAY,CAAC,IAAI,CAAC,CAClBE,gBAAgB,CAAC,IAAI,CAAC,CACtBE,YAAY,CAAC,IAAI,CAAC,CAClBe,mBAAmB,CAAC,CAAC,CACvB,CAAC,CAED,KAAM,CAAAS,YAAY,CAAGA,CAACtC,MAAc,CAAEC,QAAgB,GAAK,CACzD,GAAIA,QAAQ,GAAK,KAAK,CAAE,CACtB,MAAO,CAAAX,iBAAiB,CAACiD,eAAe,CAACvC,MAAM,CAAC,CAClD,CACA,SAAAgC,MAAA,CAAUhC,MAAM,MAAAgC,MAAA,CAAI/B,QAAQ,EAC9B,CAAC,CAED;AACA,GAAI,CAACQ,SAAS,EAAI,CAACM,OAAO,CAAE,CAC1B,mBACEnB,KAAA,QAAKY,SAAS,uDAAAwB,MAAA,CAAwDxB,SAAS,CAAG,CAAAgC,QAAA,eAChF5C,KAAA,QAAKY,SAAS,CAAC,kBAAkB,CAAAgC,QAAA,eAC/B9C,IAAA,OAAIc,SAAS,CAAC,0CAA0C,CAAAgC,QAAA,CAAC,qBAEzD,CAAI,CAAC,cACL9C,IAAA,MAAGc,SAAS,CAAC,uCAAuC,CAAAgC,QAAA,CACjDF,YAAY,CAACtC,MAAM,CAAEC,QAAQ,CAAC,CAC9B,CAAC,CACHC,WAAW,eACVR,IAAA,MAAGc,SAAS,CAAC,uBAAuB,CAAAgC,QAAA,CAAEtC,WAAW,CAAI,CACtD,EACE,CAAC,CAELc,KAAK,eACJtB,IAAA,QAAKc,SAAS,CAAC,qDAAqD,CAAAgC,QAAA,cAClE9C,IAAA,MAAGc,SAAS,CAAC,sBAAsB,CAAAgC,QAAA,CAAExB,KAAK,CAAI,CAAC,CAC5C,CACN,cAEDpB,KAAA,QAAKY,SAAS,CAAC,YAAY,CAAAgC,QAAA,eACzB9C,IAAA,WACE+C,OAAO,CAAEZ,mBAAoB,CAC7Ba,QAAQ,CAAE3B,OAAQ,CAClBP,SAAS,CAAC,oJAAoJ,CAAAgC,QAAA,CAE7JzB,OAAO,CAAG,YAAY,CAAG,sBAAsB,CAC1C,CAAC,CACRR,QAAQ,eACPb,IAAA,WACE+C,OAAO,CAAEL,YAAa,CACtB5B,SAAS,CAAC,0GAA0G,CAAAgC,QAAA,CACrH,UAED,CAAQ,CACT,EACE,CAAC,EACH,CAAC,CAEV,CAEA;AACA,GAAIzB,OAAO,CAAE,CACX,mBACErB,IAAA,QAAKc,SAAS,uDAAAwB,MAAA,CAAwDxB,SAAS,CAAG,CAAAgC,QAAA,cAChF5C,KAAA,QAAKY,SAAS,CAAC,aAAa,CAAAgC,QAAA,eAC1B9C,IAAA,QAAKc,SAAS,CAAC,6EAA6E,CAAM,CAAC,cACnGd,IAAA,MAAGc,SAAS,CAAC,eAAe,CAAAgC,QAAA,CAAC,sBAAoB,CAAG,CAAC,EAClD,CAAC,CACH,CAAC,CAEV,CAEA;AACA,GAAIvB,UAAU,EAAIC,MAAM,CAAE,CACxB,mBACEtB,KAAA,QAAKY,SAAS,cAAAwB,MAAA,CAAexB,SAAS,CAAG,CAAAgC,QAAA,eAEvC5C,KAAA,QAAKY,SAAS,CAAC,mCAAmC,CAAAgC,QAAA,eAChD5C,KAAA,QAAKY,SAAS,CAAC,mCAAmC,CAAAgC,QAAA,eAChD5C,KAAA,QAAA4C,QAAA,eACE9C,IAAA,OAAIc,SAAS,CAAC,qCAAqC,CAAAgC,QAAA,CAAC,qBAEpD,CAAI,CAAC,CACJ7B,aAAa,eACZf,KAAA,MAAGY,SAAS,CAAC,uBAAuB,CAAAgC,QAAA,EAAC,MAAI,CAAC7B,aAAa,EAAI,CAC5D,EACE,CAAC,cACNjB,IAAA,CAACH,aAAa,EAAC4B,MAAM,CAAEQ,aAAa,EAAI,SAAU,CAAE,CAAC,EAClD,CAAC,CAELJ,SAAS,eACR3B,KAAA,QAAKY,SAAS,CAAC,oDAAoD,CAAAgC,QAAA,eACjE9C,IAAA,QAAKc,SAAS,CAAC,8DAA8D,CAAM,CAAC,wBAEtF,EAAK,CACN,EACE,CAAC,CAGLmB,aAAa,GAAK,WAAW,eAC5BjC,IAAA,CAACF,aAAa,EACZ0B,MAAM,CAAEA,MAAO,CACfD,UAAU,CAAEA,UAAW,CACvBjB,MAAM,CAAEA,MAAO,CACfC,QAAQ,CAAEA,QAAS,CACnBY,SAAS,CAAEA,SAAS,EAAI8B,SAAU,CACnC,CACF,CAGAhB,aAAa,GAAK,WAAW,eAC5B/B,KAAA,QAAKY,SAAS,CAAC,gEAAgE,CAAAgC,QAAA,eAC7E9C,IAAA,QAAKc,SAAS,CAAC,uCAAuC,CAACoC,IAAI,CAAC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAAL,QAAA,cAC5F9C,IAAA,SAAMoD,QAAQ,CAAC,SAAS,CAACC,CAAC,CAAC,uIAAuI,CAACC,QAAQ,CAAC,SAAS,CAAE,CAAC,CACrL,CAAC,cACNtD,IAAA,OAAIc,SAAS,CAAC,2CAA2C,CAAAgC,QAAA,CAAC,yBAE1D,CAAI,CAAC,cACL9C,IAAA,MAAGc,SAAS,CAAC,gBAAgB,CAAAgC,QAAA,CAAC,6CAE9B,CAAG,CAAC,EACD,CACN,CAGA,CAAC,QAAQ,CAAE,SAAS,CAAE,WAAW,CAAC,CAACZ,QAAQ,CAACD,aAAa,EAAI,EAAE,CAAC,eAC/D/B,KAAA,QAAKY,SAAS,CAAC,4DAA4D,CAAAgC,QAAA,eACzE9C,IAAA,QAAKc,SAAS,CAAC,qCAAqC,CAACoC,IAAI,CAAC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAAL,QAAA,cAC1F9C,IAAA,SAAMoD,QAAQ,CAAC,SAAS,CAACC,CAAC,CAAC,yNAAyN,CAACC,QAAQ,CAAC,SAAS,CAAE,CAAC,CACvQ,CAAC,cACNtD,IAAA,OAAIc,SAAS,CAAC,yCAAyC,CAAAgC,QAAA,CACpDb,aAAa,GAAK,SAAS,CAAG,oBAAoB,CAAG,mBAAmB,CACvE,CAAC,cACLjC,IAAA,MAAGc,SAAS,CAAC,mBAAmB,CAAAgC,QAAA,CAC7Bb,aAAa,GAAK,SAAS,CACxB,6CAA6C,CAC7C,uDAAuD,CAE1D,CAAC,cACJ/B,KAAA,QAAKY,SAAS,CAAC,2BAA2B,CAAAgC,QAAA,eACxC9C,IAAA,WACE+C,OAAO,CAAEJ,WAAY,CACrB7B,SAAS,CAAC,6FAA6F,CAAAgC,QAAA,CACxG,kBAED,CAAQ,CAAC,CACRjC,QAAQ,eACPb,IAAA,WACE+C,OAAO,CAAEL,YAAa,CACtB5B,SAAS,CAAC,0GAA0G,CAAAgC,QAAA,CACrH,UAED,CAAQ,CACT,EACE,CAAC,EACH,CACN,EACE,CAAC,CAEV,CAEA,MAAO,KAAI,CACb,CAAC,CAED,cAAe,CAAA3C,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}