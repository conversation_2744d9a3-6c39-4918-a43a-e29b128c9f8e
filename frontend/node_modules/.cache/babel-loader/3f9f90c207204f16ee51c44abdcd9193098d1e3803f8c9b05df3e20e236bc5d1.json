{"ast": null, "code": "import { apiService } from './api';\n/**\n * MulticaixaService - Frontend service for Multicaixa Express payment integration\n * Handles communication with backend Multicaixa APIs\n */\nexport class MulticaixaService {\n  /**\n   * Create a new Multicaixa payment\n   */\n  static async createPayment(paymentData) {\n    try {\n      const response = await apiService.post('/payments/create', paymentData);\n      if (response.success && response.data) {\n        return {\n          success: true,\n          paymentId: response.data.paymentId,\n          paymentUrl: response.data.paymentUrl,\n          qrCode: response.data.qrCode,\n          transactionId: response.data.transactionId,\n          expiresAt: response.data.expiresAt\n        };\n      } else {\n        return {\n          success: false,\n          error: response.error || 'Failed to create payment'\n        };\n      }\n    } catch (error) {\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : 'Network error'\n      };\n    }\n  }\n\n  /**\n   * Check payment status manually\n   */\n  static async checkPaymentStatus(paymentId) {\n    try {\n      const response = await apiService.post(`/payments/${paymentId}/check-status`);\n      if (response.success && response.data) {\n        return {\n          success: true,\n          status: response.data.status,\n          transactionId: response.data.transactionId\n        };\n      } else {\n        return {\n          success: false,\n          error: response.error || 'Failed to check payment status'\n        };\n      }\n    } catch (error) {\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : 'Network error'\n      };\n    }\n  }\n\n  /**\n   * Get payment details by ID\n   */\n  static async getPayment(paymentId) {\n    try {\n      return await apiService.get(`/payments/${paymentId}`);\n    } catch (error) {\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : 'Network error'\n      };\n    }\n  }\n\n  /**\n   * Get all payments for an order\n   */\n  static async getOrderPayments(orderId) {\n    try {\n      return await apiService.get(`/orders/${orderId}/payments`);\n    } catch (error) {\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : 'Network error'\n      };\n    }\n  }\n\n  /**\n   * Request a refund for a payment\n   */\n  static async requestRefund(paymentId, amount, reason) {\n    try {\n      return await apiService.post(`/payments/${paymentId}/refund`, {\n        amount,\n        reason\n      });\n    } catch (error) {\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : 'Network error'\n      };\n    }\n  }\n\n  /**\n   * Convert EUR to AOA using current exchange rate\n   */\n  static convertEurToAoa(eurAmount, exchangeRate = 850) {\n    return Math.round(eurAmount * exchangeRate);\n  }\n\n  /**\n   * Convert AOA to EUR using current exchange rate\n   */\n  static convertAoaToEur(aoaAmount, exchangeRate = 850) {\n    return Math.round(aoaAmount / exchangeRate * 100) / 100;\n  }\n\n  /**\n   * Format AOA amount for display\n   */\n  static formatAoaAmount(amount) {\n    return new Intl.NumberFormat('pt-AO', {\n      style: 'currency',\n      currency: 'AOA',\n      minimumFractionDigits: 0,\n      maximumFractionDigits: 0\n    }).format(amount);\n  }\n\n  /**\n   * Validate payment amount (minimum 100 AOA)\n   */\n  static validatePaymentAmount(amount, currency = 'AOA') {\n    if (currency === 'AOA') {\n      return amount >= 100;\n    }\n    // For EUR, convert to AOA first\n    const aoaAmount = this.convertEurToAoa(amount);\n    return aoaAmount >= 100;\n  }\n\n  /**\n   * Get payment status display text\n   */\n  static getStatusDisplayText(status) {\n    const statusMap = {\n      'PENDING': 'Pendente',\n      'PROCESSING': 'Processando',\n      'COMPLETED': 'Concluído',\n      'FAILED': 'Falhado',\n      'CANCELLED': 'Cancelado',\n      'REFUNDED': 'Reembolsado',\n      'EXPIRED': 'Expirado'\n    };\n    return statusMap[status] || status;\n  }\n\n  /**\n   * Get payment status color for UI\n   */\n  static getStatusColor(status) {\n    const colorMap = {\n      'PENDING': 'text-yellow-600',\n      'PROCESSING': 'text-blue-600',\n      'COMPLETED': 'text-green-600',\n      'FAILED': 'text-red-600',\n      'CANCELLED': 'text-gray-600',\n      'REFUNDED': 'text-purple-600',\n      'EXPIRED': 'text-orange-600'\n    };\n    return colorMap[status] || 'text-gray-600';\n  }\n\n  /**\n   * Check if payment is in a final state\n   */\n  static isPaymentFinal(status) {\n    return ['COMPLETED', 'FAILED', 'CANCELLED', 'REFUNDED', 'EXPIRED'].includes(status);\n  }\n\n  /**\n   * Check if payment can be refunded\n   */\n  static canRefund(status) {\n    return status === 'COMPLETED';\n  }\n}\nexport default MulticaixaService;", "map": {"version": 3, "names": ["apiService", "MulticaixaService", "createPayment", "paymentData", "response", "post", "success", "data", "paymentId", "paymentUrl", "qrCode", "transactionId", "expiresAt", "error", "Error", "message", "checkPaymentStatus", "status", "getPayment", "get", "getOrderPayments", "orderId", "requestRefund", "amount", "reason", "convertEurToAoa", "eurAmount", "exchangeRate", "Math", "round", "convertAoaToEur", "aoaAmount", "formatAoaAmount", "Intl", "NumberFormat", "style", "currency", "minimumFractionDigits", "maximumFractionDigits", "format", "validatePaymentAmount", "getStatusDisplayText", "statusMap", "getStatusColor", "colorMap", "isPaymentFinal", "includes", "canRefund"], "sources": ["/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/services/multicaixa.ts"], "sourcesContent": ["import { apiService } from './api';\nimport { \n  MulticaixaPaymentRequest, \n  MulticaixaPaymentResponse, \n  MulticaixaStatusResponse,\n  Payment,\n  ApiResponse \n} from '../types';\n\n/**\n * MulticaixaService - Frontend service for Multicaixa Express payment integration\n * Handles communication with backend Multicaixa APIs\n */\nexport class MulticaixaService {\n  \n  /**\n   * Create a new Multicaixa payment\n   */\n  static async createPayment(\n    paymentData: MulticaixaPaymentRequest\n  ): Promise<MulticaixaPaymentResponse> {\n    try {\n      const response = await apiService.post<{\n        paymentId: string;\n        paymentUrl: string;\n        qrCode: string;\n        transactionId: string;\n        expiresAt: string;\n      }>('/payments/create', paymentData);\n\n      if (response.success && response.data) {\n        return {\n          success: true,\n          paymentId: response.data.paymentId,\n          paymentUrl: response.data.paymentUrl,\n          qrCode: response.data.qrCode,\n          transactionId: response.data.transactionId,\n          expiresAt: response.data.expiresAt,\n        };\n      } else {\n        return {\n          success: false,\n          error: response.error || 'Failed to create payment',\n        };\n      }\n    } catch (error) {\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : 'Network error',\n      };\n    }\n  }\n\n  /**\n   * Check payment status manually\n   */\n  static async checkPaymentStatus(paymentId: string): Promise<MulticaixaStatusResponse> {\n    try {\n      const response = await apiService.post<{\n        status: string;\n        transactionId: string;\n      }>(`/payments/${paymentId}/check-status`);\n\n      if (response.success && response.data) {\n        return {\n          success: true,\n          status: response.data.status as any,\n          transactionId: response.data.transactionId,\n        };\n      } else {\n        return {\n          success: false,\n          error: response.error || 'Failed to check payment status',\n        };\n      }\n    } catch (error) {\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : 'Network error',\n      };\n    }\n  }\n\n  /**\n   * Get payment details by ID\n   */\n  static async getPayment(paymentId: string): Promise<ApiResponse<Payment>> {\n    try {\n      return await apiService.get<Payment>(`/payments/${paymentId}`);\n    } catch (error) {\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : 'Network error',\n      };\n    }\n  }\n\n  /**\n   * Get all payments for an order\n   */\n  static async getOrderPayments(orderId: string): Promise<ApiResponse<Payment[]>> {\n    try {\n      return await apiService.get<Payment[]>(`/orders/${orderId}/payments`);\n    } catch (error) {\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : 'Network error',\n      };\n    }\n  }\n\n  /**\n   * Request a refund for a payment\n   */\n  static async requestRefund(\n    paymentId: string, \n    amount?: number, \n    reason?: string\n  ): Promise<ApiResponse<{ refundId: string; status: string }>> {\n    try {\n      return await apiService.post<{ refundId: string; status: string }>(\n        `/payments/${paymentId}/refund`,\n        { amount, reason }\n      );\n    } catch (error) {\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : 'Network error',\n      };\n    }\n  }\n\n  /**\n   * Convert EUR to AOA using current exchange rate\n   */\n  static convertEurToAoa(eurAmount: number, exchangeRate: number = 850): number {\n    return Math.round(eurAmount * exchangeRate);\n  }\n\n  /**\n   * Convert AOA to EUR using current exchange rate\n   */\n  static convertAoaToEur(aoaAmount: number, exchangeRate: number = 850): number {\n    return Math.round((aoaAmount / exchangeRate) * 100) / 100;\n  }\n\n  /**\n   * Format AOA amount for display\n   */\n  static formatAoaAmount(amount: number): string {\n    return new Intl.NumberFormat('pt-AO', {\n      style: 'currency',\n      currency: 'AOA',\n      minimumFractionDigits: 0,\n      maximumFractionDigits: 0,\n    }).format(amount);\n  }\n\n  /**\n   * Validate payment amount (minimum 100 AOA)\n   */\n  static validatePaymentAmount(amount: number, currency: string = 'AOA'): boolean {\n    if (currency === 'AOA') {\n      return amount >= 100;\n    }\n    // For EUR, convert to AOA first\n    const aoaAmount = this.convertEurToAoa(amount);\n    return aoaAmount >= 100;\n  }\n\n  /**\n   * Get payment status display text\n   */\n  static getStatusDisplayText(status: string): string {\n    const statusMap: Record<string, string> = {\n      'PENDING': 'Pendente',\n      'PROCESSING': 'Processando',\n      'COMPLETED': 'Concluído',\n      'FAILED': 'Falhado',\n      'CANCELLED': 'Cancelado',\n      'REFUNDED': 'Reembolsado',\n      'EXPIRED': 'Expirado',\n    };\n    return statusMap[status] || status;\n  }\n\n  /**\n   * Get payment status color for UI\n   */\n  static getStatusColor(status: string): string {\n    const colorMap: Record<string, string> = {\n      'PENDING': 'text-yellow-600',\n      'PROCESSING': 'text-blue-600',\n      'COMPLETED': 'text-green-600',\n      'FAILED': 'text-red-600',\n      'CANCELLED': 'text-gray-600',\n      'REFUNDED': 'text-purple-600',\n      'EXPIRED': 'text-orange-600',\n    };\n    return colorMap[status] || 'text-gray-600';\n  }\n\n  /**\n   * Check if payment is in a final state\n   */\n  static isPaymentFinal(status: string): boolean {\n    return ['COMPLETED', 'FAILED', 'CANCELLED', 'REFUNDED', 'EXPIRED'].includes(status);\n  }\n\n  /**\n   * Check if payment can be refunded\n   */\n  static canRefund(status: string): boolean {\n    return status === 'COMPLETED';\n  }\n}\n\nexport default MulticaixaService;\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AASlC;AACA;AACA;AACA;AACA,OAAO,MAAMC,iBAAiB,CAAC;EAE7B;AACF;AACA;EACE,aAAaC,aAAaA,CACxBC,WAAqC,EACD;IACpC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMJ,UAAU,CAACK,IAAI,CAMnC,kBAAkB,EAAEF,WAAW,CAAC;MAEnC,IAAIC,QAAQ,CAACE,OAAO,IAAIF,QAAQ,CAACG,IAAI,EAAE;QACrC,OAAO;UACLD,OAAO,EAAE,IAAI;UACbE,SAAS,EAAEJ,QAAQ,CAACG,IAAI,CAACC,SAAS;UAClCC,UAAU,EAAEL,QAAQ,CAACG,IAAI,CAACE,UAAU;UACpCC,MAAM,EAAEN,QAAQ,CAACG,IAAI,CAACG,MAAM;UAC5BC,aAAa,EAAEP,QAAQ,CAACG,IAAI,CAACI,aAAa;UAC1CC,SAAS,EAAER,QAAQ,CAACG,IAAI,CAACK;QAC3B,CAAC;MACH,CAAC,MAAM;QACL,OAAO;UACLN,OAAO,EAAE,KAAK;UACdO,KAAK,EAAET,QAAQ,CAACS,KAAK,IAAI;QAC3B,CAAC;MACH;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd,OAAO;QACLP,OAAO,EAAE,KAAK;QACdO,KAAK,EAAEA,KAAK,YAAYC,KAAK,GAAGD,KAAK,CAACE,OAAO,GAAG;MAClD,CAAC;IACH;EACF;;EAEA;AACF;AACA;EACE,aAAaC,kBAAkBA,CAACR,SAAiB,EAAqC;IACpF,IAAI;MACF,MAAMJ,QAAQ,GAAG,MAAMJ,UAAU,CAACK,IAAI,CAGnC,aAAaG,SAAS,eAAe,CAAC;MAEzC,IAAIJ,QAAQ,CAACE,OAAO,IAAIF,QAAQ,CAACG,IAAI,EAAE;QACrC,OAAO;UACLD,OAAO,EAAE,IAAI;UACbW,MAAM,EAAEb,QAAQ,CAACG,IAAI,CAACU,MAAa;UACnCN,aAAa,EAAEP,QAAQ,CAACG,IAAI,CAACI;QAC/B,CAAC;MACH,CAAC,MAAM;QACL,OAAO;UACLL,OAAO,EAAE,KAAK;UACdO,KAAK,EAAET,QAAQ,CAACS,KAAK,IAAI;QAC3B,CAAC;MACH;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd,OAAO;QACLP,OAAO,EAAE,KAAK;QACdO,KAAK,EAAEA,KAAK,YAAYC,KAAK,GAAGD,KAAK,CAACE,OAAO,GAAG;MAClD,CAAC;IACH;EACF;;EAEA;AACF;AACA;EACE,aAAaG,UAAUA,CAACV,SAAiB,EAAiC;IACxE,IAAI;MACF,OAAO,MAAMR,UAAU,CAACmB,GAAG,CAAU,aAAaX,SAAS,EAAE,CAAC;IAChE,CAAC,CAAC,OAAOK,KAAK,EAAE;MACd,OAAO;QACLP,OAAO,EAAE,KAAK;QACdO,KAAK,EAAEA,KAAK,YAAYC,KAAK,GAAGD,KAAK,CAACE,OAAO,GAAG;MAClD,CAAC;IACH;EACF;;EAEA;AACF;AACA;EACE,aAAaK,gBAAgBA,CAACC,OAAe,EAAmC;IAC9E,IAAI;MACF,OAAO,MAAMrB,UAAU,CAACmB,GAAG,CAAY,WAAWE,OAAO,WAAW,CAAC;IACvE,CAAC,CAAC,OAAOR,KAAK,EAAE;MACd,OAAO;QACLP,OAAO,EAAE,KAAK;QACdO,KAAK,EAAEA,KAAK,YAAYC,KAAK,GAAGD,KAAK,CAACE,OAAO,GAAG;MAClD,CAAC;IACH;EACF;;EAEA;AACF;AACA;EACE,aAAaO,aAAaA,CACxBd,SAAiB,EACjBe,MAAe,EACfC,MAAe,EAC6C;IAC5D,IAAI;MACF,OAAO,MAAMxB,UAAU,CAACK,IAAI,CAC1B,aAAaG,SAAS,SAAS,EAC/B;QAAEe,MAAM;QAAEC;MAAO,CACnB,CAAC;IACH,CAAC,CAAC,OAAOX,KAAK,EAAE;MACd,OAAO;QACLP,OAAO,EAAE,KAAK;QACdO,KAAK,EAAEA,KAAK,YAAYC,KAAK,GAAGD,KAAK,CAACE,OAAO,GAAG;MAClD,CAAC;IACH;EACF;;EAEA;AACF;AACA;EACE,OAAOU,eAAeA,CAACC,SAAiB,EAAEC,YAAoB,GAAG,GAAG,EAAU;IAC5E,OAAOC,IAAI,CAACC,KAAK,CAACH,SAAS,GAAGC,YAAY,CAAC;EAC7C;;EAEA;AACF;AACA;EACE,OAAOG,eAAeA,CAACC,SAAiB,EAAEJ,YAAoB,GAAG,GAAG,EAAU;IAC5E,OAAOC,IAAI,CAACC,KAAK,CAAEE,SAAS,GAAGJ,YAAY,GAAI,GAAG,CAAC,GAAG,GAAG;EAC3D;;EAEA;AACF;AACA;EACE,OAAOK,eAAeA,CAACT,MAAc,EAAU;IAC7C,OAAO,IAAIU,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE,KAAK;MACfC,qBAAqB,EAAE,CAAC;MACxBC,qBAAqB,EAAE;IACzB,CAAC,CAAC,CAACC,MAAM,CAAChB,MAAM,CAAC;EACnB;;EAEA;AACF;AACA;EACE,OAAOiB,qBAAqBA,CAACjB,MAAc,EAAEa,QAAgB,GAAG,KAAK,EAAW;IAC9E,IAAIA,QAAQ,KAAK,KAAK,EAAE;MACtB,OAAOb,MAAM,IAAI,GAAG;IACtB;IACA;IACA,MAAMQ,SAAS,GAAG,IAAI,CAACN,eAAe,CAACF,MAAM,CAAC;IAC9C,OAAOQ,SAAS,IAAI,GAAG;EACzB;;EAEA;AACF;AACA;EACE,OAAOU,oBAAoBA,CAACxB,MAAc,EAAU;IAClD,MAAMyB,SAAiC,GAAG;MACxC,SAAS,EAAE,UAAU;MACrB,YAAY,EAAE,aAAa;MAC3B,WAAW,EAAE,WAAW;MACxB,QAAQ,EAAE,SAAS;MACnB,WAAW,EAAE,WAAW;MACxB,UAAU,EAAE,aAAa;MACzB,SAAS,EAAE;IACb,CAAC;IACD,OAAOA,SAAS,CAACzB,MAAM,CAAC,IAAIA,MAAM;EACpC;;EAEA;AACF;AACA;EACE,OAAO0B,cAAcA,CAAC1B,MAAc,EAAU;IAC5C,MAAM2B,QAAgC,GAAG;MACvC,SAAS,EAAE,iBAAiB;MAC5B,YAAY,EAAE,eAAe;MAC7B,WAAW,EAAE,gBAAgB;MAC7B,QAAQ,EAAE,cAAc;MACxB,WAAW,EAAE,eAAe;MAC5B,UAAU,EAAE,iBAAiB;MAC7B,SAAS,EAAE;IACb,CAAC;IACD,OAAOA,QAAQ,CAAC3B,MAAM,CAAC,IAAI,eAAe;EAC5C;;EAEA;AACF;AACA;EACE,OAAO4B,cAAcA,CAAC5B,MAAc,EAAW;IAC7C,OAAO,CAAC,WAAW,EAAE,QAAQ,EAAE,WAAW,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC6B,QAAQ,CAAC7B,MAAM,CAAC;EACrF;;EAEA;AACF;AACA;EACE,OAAO8B,SAASA,CAAC9B,MAAc,EAAW;IACxC,OAAOA,MAAM,KAAK,WAAW;EAC/B;AACF;AAEA,eAAehB,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}