{"ast": null, "code": "import React from'react';import{MulticaixaService}from'../services/multicaixa';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const PaymentStatus=_ref=>{let{status,className='',showIcon=true,size='md'}=_ref;const statusText=MulticaixaService.getStatusDisplayText(status);const statusColor=MulticaixaService.getStatusColor(status);const sizeClasses={sm:'text-xs px-2 py-1',md:'text-sm px-3 py-1',lg:'text-base px-4 py-2'};const iconClasses={sm:'w-3 h-3',md:'w-4 h-4',lg:'w-5 h-5'};const getStatusIcon=status=>{switch(status){case'PENDING':return/*#__PURE__*/_jsx(\"svg\",{className:\"\".concat(iconClasses[size],\" text-yellow-500\"),fill:\"currentColor\",viewBox:\"0 0 20 20\",children:/*#__PURE__*/_jsx(\"path\",{fillRule:\"evenodd\",d:\"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z\",clipRule:\"evenodd\"})});case'PROCESSING':return/*#__PURE__*/_jsxs(\"svg\",{className:\"\".concat(iconClasses[size],\" text-blue-500 animate-spin\"),fill:\"none\",viewBox:\"0 0 24 24\",children:[/*#__PURE__*/_jsx(\"circle\",{className:\"opacity-25\",cx:\"12\",cy:\"12\",r:\"10\",stroke:\"currentColor\",strokeWidth:\"4\"}),/*#__PURE__*/_jsx(\"path\",{className:\"opacity-75\",fill:\"currentColor\",d:\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"})]});case'COMPLETED':return/*#__PURE__*/_jsx(\"svg\",{className:\"\".concat(iconClasses[size],\" text-green-500\"),fill:\"currentColor\",viewBox:\"0 0 20 20\",children:/*#__PURE__*/_jsx(\"path\",{fillRule:\"evenodd\",d:\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",clipRule:\"evenodd\"})});case'FAILED':return/*#__PURE__*/_jsx(\"svg\",{className:\"\".concat(iconClasses[size],\" text-red-500\"),fill:\"currentColor\",viewBox:\"0 0 20 20\",children:/*#__PURE__*/_jsx(\"path\",{fillRule:\"evenodd\",d:\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",clipRule:\"evenodd\"})});case'CANCELLED':return/*#__PURE__*/_jsx(\"svg\",{className:\"\".concat(iconClasses[size],\" text-gray-500\"),fill:\"currentColor\",viewBox:\"0 0 20 20\",children:/*#__PURE__*/_jsx(\"path\",{fillRule:\"evenodd\",d:\"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\",clipRule:\"evenodd\"})});case'REFUNDED':return/*#__PURE__*/_jsx(\"svg\",{className:\"\".concat(iconClasses[size],\" text-purple-500\"),fill:\"currentColor\",viewBox:\"0 0 20 20\",children:/*#__PURE__*/_jsx(\"path\",{fillRule:\"evenodd\",d:\"M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z\",clipRule:\"evenodd\"})});case'EXPIRED':return/*#__PURE__*/_jsx(\"svg\",{className:\"\".concat(iconClasses[size],\" text-orange-500\"),fill:\"currentColor\",viewBox:\"0 0 20 20\",children:/*#__PURE__*/_jsx(\"path\",{fillRule:\"evenodd\",d:\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",clipRule:\"evenodd\"})});default:return null;}};return/*#__PURE__*/_jsxs(\"span\",{className:\"\\n      inline-flex items-center gap-2 \\n      \".concat(sizeClasses[size],\" \\n      \").concat(statusColor,\" \\n      bg-gray-100 rounded-full font-medium\\n      \").concat(className,\"\\n    \"),children:[showIcon&&getStatusIcon(status),statusText]});};export default PaymentStatus;", "map": {"version": 3, "names": ["React", "MulticaixaService", "jsx", "_jsx", "jsxs", "_jsxs", "PaymentStatus", "_ref", "status", "className", "showIcon", "size", "statusText", "getStatusDisplayText", "statusColor", "getStatusColor", "sizeClasses", "sm", "md", "lg", "iconClasses", "getStatusIcon", "concat", "fill", "viewBox", "children", "fillRule", "d", "clipRule", "cx", "cy", "r", "stroke", "strokeWidth"], "sources": ["/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/PaymentStatus.tsx"], "sourcesContent": ["import React from 'react';\nimport { MulticaixaService } from '../services/multicaixa';\nimport { PaymentStatus as PaymentStatusType } from '../types';\n\ninterface PaymentStatusProps {\n  status: PaymentStatusType;\n  className?: string;\n  showIcon?: boolean;\n  size?: 'sm' | 'md' | 'lg';\n}\n\nconst PaymentStatus: React.FC<PaymentStatusProps> = ({\n  status,\n  className = '',\n  showIcon = true,\n  size = 'md'\n}) => {\n  const statusText = MulticaixaService.getStatusDisplayText(status);\n  const statusColor = MulticaixaService.getStatusColor(status);\n  \n  const sizeClasses = {\n    sm: 'text-xs px-2 py-1',\n    md: 'text-sm px-3 py-1',\n    lg: 'text-base px-4 py-2'\n  };\n\n  const iconClasses = {\n    sm: 'w-3 h-3',\n    md: 'w-4 h-4',\n    lg: 'w-5 h-5'\n  };\n\n  const getStatusIcon = (status: PaymentStatusType) => {\n    switch (status) {\n      case 'PENDING':\n        return (\n          <svg className={`${iconClasses[size]} text-yellow-500`} fill=\"currentColor\" viewBox=\"0 0 20 20\">\n            <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z\" clipRule=\"evenodd\" />\n          </svg>\n        );\n      case 'PROCESSING':\n        return (\n          <svg className={`${iconClasses[size]} text-blue-500 animate-spin`} fill=\"none\" viewBox=\"0 0 24 24\">\n            <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n            <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n          </svg>\n        );\n      case 'COMPLETED':\n        return (\n          <svg className={`${iconClasses[size]} text-green-500`} fill=\"currentColor\" viewBox=\"0 0 20 20\">\n            <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n          </svg>\n        );\n      case 'FAILED':\n        return (\n          <svg className={`${iconClasses[size]} text-red-500`} fill=\"currentColor\" viewBox=\"0 0 20 20\">\n            <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\n          </svg>\n        );\n      case 'CANCELLED':\n        return (\n          <svg className={`${iconClasses[size]} text-gray-500`} fill=\"currentColor\" viewBox=\"0 0 20 20\">\n            <path fillRule=\"evenodd\" d=\"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\" clipRule=\"evenodd\" />\n          </svg>\n        );\n      case 'REFUNDED':\n        return (\n          <svg className={`${iconClasses[size]} text-purple-500`} fill=\"currentColor\" viewBox=\"0 0 20 20\">\n            <path fillRule=\"evenodd\" d=\"M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z\" clipRule=\"evenodd\" />\n          </svg>\n        );\n      case 'EXPIRED':\n        return (\n          <svg className={`${iconClasses[size]} text-orange-500`} fill=\"currentColor\" viewBox=\"0 0 20 20\">\n            <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\n          </svg>\n        );\n      default:\n        return null;\n    }\n  };\n\n  return (\n    <span className={`\n      inline-flex items-center gap-2 \n      ${sizeClasses[size]} \n      ${statusColor} \n      bg-gray-100 rounded-full font-medium\n      ${className}\n    `}>\n      {showIcon && getStatusIcon(status)}\n      {statusText}\n    </span>\n  );\n};\n\nexport default PaymentStatus;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,iBAAiB,KAAQ,wBAAwB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAU3D,KAAM,CAAAC,aAA2C,CAAGC,IAAA,EAK9C,IAL+C,CACnDC,MAAM,CACNC,SAAS,CAAG,EAAE,CACdC,QAAQ,CAAG,IAAI,CACfC,IAAI,CAAG,IACT,CAAC,CAAAJ,IAAA,CACC,KAAM,CAAAK,UAAU,CAAGX,iBAAiB,CAACY,oBAAoB,CAACL,MAAM,CAAC,CACjE,KAAM,CAAAM,WAAW,CAAGb,iBAAiB,CAACc,cAAc,CAACP,MAAM,CAAC,CAE5D,KAAM,CAAAQ,WAAW,CAAG,CAClBC,EAAE,CAAE,mBAAmB,CACvBC,EAAE,CAAE,mBAAmB,CACvBC,EAAE,CAAE,qBACN,CAAC,CAED,KAAM,CAAAC,WAAW,CAAG,CAClBH,EAAE,CAAE,SAAS,CACbC,EAAE,CAAE,SAAS,CACbC,EAAE,CAAE,SACN,CAAC,CAED,KAAM,CAAAE,aAAa,CAAIb,MAAyB,EAAK,CACnD,OAAQA,MAAM,EACZ,IAAK,SAAS,CACZ,mBACEL,IAAA,QAAKM,SAAS,IAAAa,MAAA,CAAKF,WAAW,CAACT,IAAI,CAAC,oBAAmB,CAACY,IAAI,CAAC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAAC,QAAA,cAC7FtB,IAAA,SAAMuB,QAAQ,CAAC,SAAS,CAACC,CAAC,CAAC,oHAAoH,CAACC,QAAQ,CAAC,SAAS,CAAE,CAAC,CAClK,CAAC,CAEV,IAAK,YAAY,CACf,mBACEvB,KAAA,QAAKI,SAAS,IAAAa,MAAA,CAAKF,WAAW,CAACT,IAAI,CAAC,+BAA8B,CAACY,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAAAC,QAAA,eAChGtB,IAAA,WAAQM,SAAS,CAAC,YAAY,CAACoB,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAACC,CAAC,CAAC,IAAI,CAACC,MAAM,CAAC,cAAc,CAACC,WAAW,CAAC,GAAG,CAAS,CAAC,cACrG9B,IAAA,SAAMM,SAAS,CAAC,YAAY,CAACc,IAAI,CAAC,cAAc,CAACI,CAAC,CAAC,iHAAiH,CAAO,CAAC,EACzK,CAAC,CAEV,IAAK,WAAW,CACd,mBACExB,IAAA,QAAKM,SAAS,IAAAa,MAAA,CAAKF,WAAW,CAACT,IAAI,CAAC,mBAAkB,CAACY,IAAI,CAAC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAAC,QAAA,cAC5FtB,IAAA,SAAMuB,QAAQ,CAAC,SAAS,CAACC,CAAC,CAAC,uIAAuI,CAACC,QAAQ,CAAC,SAAS,CAAE,CAAC,CACrL,CAAC,CAEV,IAAK,QAAQ,CACX,mBACEzB,IAAA,QAAKM,SAAS,IAAAa,MAAA,CAAKF,WAAW,CAACT,IAAI,CAAC,iBAAgB,CAACY,IAAI,CAAC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAAC,QAAA,cAC1FtB,IAAA,SAAMuB,QAAQ,CAAC,SAAS,CAACC,CAAC,CAAC,yNAAyN,CAACC,QAAQ,CAAC,SAAS,CAAE,CAAC,CACvQ,CAAC,CAEV,IAAK,WAAW,CACd,mBACEzB,IAAA,QAAKM,SAAS,IAAAa,MAAA,CAAKF,WAAW,CAACT,IAAI,CAAC,kBAAiB,CAACY,IAAI,CAAC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAAC,QAAA,cAC3FtB,IAAA,SAAMuB,QAAQ,CAAC,SAAS,CAACC,CAAC,CAAC,oMAAoM,CAACC,QAAQ,CAAC,SAAS,CAAE,CAAC,CAClP,CAAC,CAEV,IAAK,UAAU,CACb,mBACEzB,IAAA,QAAKM,SAAS,IAAAa,MAAA,CAAKF,WAAW,CAACT,IAAI,CAAC,oBAAmB,CAACY,IAAI,CAAC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAAC,QAAA,cAC7FtB,IAAA,SAAMuB,QAAQ,CAAC,SAAS,CAACC,CAAC,CAAC,sSAAsS,CAACC,QAAQ,CAAC,SAAS,CAAE,CAAC,CACpV,CAAC,CAEV,IAAK,SAAS,CACZ,mBACEzB,IAAA,QAAKM,SAAS,IAAAa,MAAA,CAAKF,WAAW,CAACT,IAAI,CAAC,oBAAmB,CAACY,IAAI,CAAC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAAC,QAAA,cAC7FtB,IAAA,SAAMuB,QAAQ,CAAC,SAAS,CAACC,CAAC,CAAC,yNAAyN,CAACC,QAAQ,CAAC,SAAS,CAAE,CAAC,CACvQ,CAAC,CAEV,QACE,MAAO,KAAI,CACf,CACF,CAAC,CAED,mBACEvB,KAAA,SAAMI,SAAS,mDAAAa,MAAA,CAEXN,WAAW,CAACL,IAAI,CAAC,cAAAW,MAAA,CACjBR,WAAW,0DAAAQ,MAAA,CAEXb,SAAS,UACX,CAAAgB,QAAA,EACCf,QAAQ,EAAIW,aAAa,CAACb,MAAM,CAAC,CACjCI,UAAU,EACP,CAAC,CAEX,CAAC,CAED,cAAe,CAAAN,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}