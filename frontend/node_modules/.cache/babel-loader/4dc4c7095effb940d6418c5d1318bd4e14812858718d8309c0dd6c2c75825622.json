{"ast": null, "code": "const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000/api';\nclass AdminApiService {\n  constructor(baseURL = API_BASE_URL) {\n    this.baseURL = void 0;\n    this.token = null;\n    this.baseURL = baseURL;\n    // Try to get token from localStorage\n    this.token = localStorage.getItem('adminToken');\n  }\n  async request(endpoint, options = {}) {\n    const url = `${this.baseURL}/admin${endpoint}`;\n    const defaultHeaders = {\n      'Content-Type': 'application/json'\n    };\n\n    // Add authorization header if token exists\n    if (this.token) {\n      defaultHeaders['Authorization'] = `Bearer ${this.token}`;\n    }\n    const config = {\n      headers: {\n        ...defaultHeaders,\n        ...options.headers\n      },\n      ...options\n    };\n    try {\n      const response = await fetch(url, config);\n      const data = await response.json();\n      if (!response.ok) {\n        return {\n          success: false,\n          error: data.message || 'An error occurred'\n        };\n      }\n      return {\n        success: true,\n        data\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : 'Network error'\n      };\n    }\n  }\n\n  // Authentication methods\n  async login(credentials) {\n    const response = await this.request('/auth/login', {\n      method: 'POST',\n      body: JSON.stringify(credentials)\n    });\n    if (response.success && response.data) {\n      this.token = response.data.token;\n      localStorage.setItem('adminToken', this.token);\n      localStorage.setItem('adminUser', JSON.stringify(response.data.admin));\n    }\n    return response;\n  }\n  async logout() {\n    if (this.token) {\n      await this.request('/auth/logout', {\n        method: 'POST'\n      });\n    }\n    this.token = null;\n    localStorage.removeItem('adminToken');\n    localStorage.removeItem('adminUser');\n  }\n  isAuthenticated() {\n    return !!this.token;\n  }\n  getStoredAdmin() {\n    const adminData = localStorage.getItem('adminUser');\n    return adminData ? JSON.parse(adminData) : null;\n  }\n\n  // Dashboard methods\n  async getDashboardStats() {\n    return this.request('/dashboard/stats');\n  }\n  async getOverview() {\n    return this.request('/dashboard/overview');\n  }\n\n  // Orders methods\n  async getOrders(params) {\n    const queryParams = new URLSearchParams();\n    if (params !== null && params !== void 0 && params.page) queryParams.append('page', params.page.toString());\n    if (params !== null && params !== void 0 && params.limit) queryParams.append('limit', params.limit.toString());\n    if (params !== null && params !== void 0 && params.status) queryParams.append('status', params.status);\n    const query = queryParams.toString();\n    return this.request(`/orders${query ? `?${query}` : ''}`);\n  }\n  async getOrder(id) {\n    return this.request(`/orders/${id}`);\n  }\n  async updateOrderStatus(id, status, notes) {\n    return this.request(`/orders/${id}/status`, {\n      method: 'PUT',\n      body: JSON.stringify({\n        status,\n        notes\n      })\n    });\n  }\n  async deleteOrder(id) {\n    return this.request(`/orders/${id}`, {\n      method: 'DELETE'\n    });\n  }\n\n  // Generic CRUD methods\n  async get(endpoint) {\n    return this.request(endpoint, {\n      method: 'GET'\n    });\n  }\n  async post(endpoint, data) {\n    return this.request(endpoint, {\n      method: 'POST',\n      body: data ? JSON.stringify(data) : undefined\n    });\n  }\n  async put(endpoint, data) {\n    return this.request(endpoint, {\n      method: 'PUT',\n      body: data ? JSON.stringify(data) : undefined\n    });\n  }\n  async delete(endpoint) {\n    return this.request(endpoint, {\n      method: 'DELETE'\n    });\n  }\n}\nexport const adminApiService = new AdminApiService();\nexport default AdminApiService;", "map": {"version": 3, "names": ["API_BASE_URL", "process", "env", "REACT_APP_API_URL", "AdminApiService", "constructor", "baseURL", "token", "localStorage", "getItem", "request", "endpoint", "options", "url", "defaultHeaders", "config", "headers", "response", "fetch", "data", "json", "ok", "success", "error", "message", "Error", "login", "credentials", "method", "body", "JSON", "stringify", "setItem", "admin", "logout", "removeItem", "isAuthenticated", "getStoredAdmin", "adminData", "parse", "getDashboardStats", "getOverview", "getOrders", "params", "queryParams", "URLSearchParams", "page", "append", "toString", "limit", "status", "query", "getOrder", "id", "updateOrderStatus", "notes", "deleteOrder", "get", "post", "undefined", "put", "delete", "adminApiService"], "sources": ["/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/services/adminApi.ts"], "sourcesContent": ["import { ApiResponse } from '../types';\n\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000/api';\n\ninterface AdminLoginData {\n  email: string;\n  password: string;\n}\n\ninterface AdminAuthResponse {\n  admin: {\n    id: number;\n    email: string;\n    name: string;\n    role: string;\n    status: string;\n  };\n  token: string;\n  expiresIn: string;\n}\n\nclass AdminApiService {\n  private baseURL: string;\n  private token: string | null = null;\n\n  constructor(baseURL: string = API_BASE_URL) {\n    this.baseURL = baseURL;\n    // Try to get token from localStorage\n    this.token = localStorage.getItem('adminToken');\n  }\n\n  private async request<T>(\n    endpoint: string,\n    options: RequestInit = {}\n  ): Promise<ApiResponse<T>> {\n    const url = `${this.baseURL}/admin${endpoint}`;\n    \n    const defaultHeaders: Record<string, string> = {\n      'Content-Type': 'application/json',\n    };\n\n    // Add authorization header if token exists\n    if (this.token) {\n      defaultHeaders['Authorization'] = `Bearer ${this.token}`;\n    }\n\n    const config: RequestInit = {\n      headers: { ...defaultHeaders, ...options.headers },\n      ...options,\n    };\n\n    try {\n      const response = await fetch(url, config);\n      const data = await response.json();\n\n      if (!response.ok) {\n        return {\n          success: false,\n          error: data.message || 'An error occurred',\n        };\n      }\n\n      return {\n        success: true,\n        data,\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : 'Network error',\n      };\n    }\n  }\n\n  // Authentication methods\n  async login(credentials: AdminLoginData): Promise<ApiResponse<AdminAuthResponse>> {\n    const response = await this.request<AdminAuthResponse>('/auth/login', {\n      method: 'POST',\n      body: JSON.stringify(credentials),\n    });\n\n    if (response.success && response.data) {\n      this.token = response.data.token;\n      localStorage.setItem('adminToken', this.token);\n      localStorage.setItem('adminUser', JSON.stringify(response.data.admin));\n    }\n\n    return response;\n  }\n\n  async logout(): Promise<void> {\n    if (this.token) {\n      await this.request('/auth/logout', { method: 'POST' });\n    }\n    this.token = null;\n    localStorage.removeItem('adminToken');\n    localStorage.removeItem('adminUser');\n  }\n\n  isAuthenticated(): boolean {\n    return !!this.token;\n  }\n\n  getStoredAdmin() {\n    const adminData = localStorage.getItem('adminUser');\n    return adminData ? JSON.parse(adminData) : null;\n  }\n\n  // Dashboard methods\n  async getDashboardStats(): Promise<ApiResponse<any>> {\n    return this.request('/dashboard/stats');\n  }\n\n  async getOverview(): Promise<ApiResponse<any>> {\n    return this.request('/dashboard/overview');\n  }\n\n  // Orders methods\n  async getOrders(params?: { page?: number; limit?: number; status?: string }): Promise<ApiResponse<any>> {\n    const queryParams = new URLSearchParams();\n    if (params?.page) queryParams.append('page', params.page.toString());\n    if (params?.limit) queryParams.append('limit', params.limit.toString());\n    if (params?.status) queryParams.append('status', params.status);\n    \n    const query = queryParams.toString();\n    return this.request(`/orders${query ? `?${query}` : ''}`);\n  }\n\n  async getOrder(id: string): Promise<ApiResponse<any>> {\n    return this.request(`/orders/${id}`);\n  }\n\n  async updateOrderStatus(id: string, status: string, notes?: string): Promise<ApiResponse<any>> {\n    return this.request(`/orders/${id}/status`, {\n      method: 'PUT',\n      body: JSON.stringify({ status, notes }),\n    });\n  }\n\n  async deleteOrder(id: string): Promise<ApiResponse<any>> {\n    return this.request(`/orders/${id}`, { method: 'DELETE' });\n  }\n\n  // Generic CRUD methods\n  async get<T>(endpoint: string): Promise<ApiResponse<T>> {\n    return this.request<T>(endpoint, { method: 'GET' });\n  }\n\n  async post<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {\n    return this.request<T>(endpoint, {\n      method: 'POST',\n      body: data ? JSON.stringify(data) : undefined,\n    });\n  }\n\n  async put<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {\n    return this.request<T>(endpoint, {\n      method: 'PUT',\n      body: data ? JSON.stringify(data) : undefined,\n    });\n  }\n\n  async delete<T>(endpoint: string): Promise<ApiResponse<T>> {\n    return this.request<T>(endpoint, { method: 'DELETE' });\n  }\n}\n\nexport const adminApiService = new AdminApiService();\nexport default AdminApiService;\n"], "mappings": "AAEA,MAAMA,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,2BAA2B;AAmBjF,MAAMC,eAAe,CAAC;EAIpBC,WAAWA,CAACC,OAAe,GAAGN,YAAY,EAAE;IAAA,KAHpCM,OAAO;IAAA,KACPC,KAAK,GAAkB,IAAI;IAGjC,IAAI,CAACD,OAAO,GAAGA,OAAO;IACtB;IACA,IAAI,CAACC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;EACjD;EAEA,MAAcC,OAAOA,CACnBC,QAAgB,EAChBC,OAAoB,GAAG,CAAC,CAAC,EACA;IACzB,MAAMC,GAAG,GAAG,GAAG,IAAI,CAACP,OAAO,SAASK,QAAQ,EAAE;IAE9C,MAAMG,cAAsC,GAAG;MAC7C,cAAc,EAAE;IAClB,CAAC;;IAED;IACA,IAAI,IAAI,CAACP,KAAK,EAAE;MACdO,cAAc,CAAC,eAAe,CAAC,GAAG,UAAU,IAAI,CAACP,KAAK,EAAE;IAC1D;IAEA,MAAMQ,MAAmB,GAAG;MAC1BC,OAAO,EAAE;QAAE,GAAGF,cAAc;QAAE,GAAGF,OAAO,CAACI;MAAQ,CAAC;MAClD,GAAGJ;IACL,CAAC;IAED,IAAI;MACF,MAAMK,QAAQ,GAAG,MAAMC,KAAK,CAACL,GAAG,EAAEE,MAAM,CAAC;MACzC,MAAMI,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAElC,IAAI,CAACH,QAAQ,CAACI,EAAE,EAAE;QAChB,OAAO;UACLC,OAAO,EAAE,KAAK;UACdC,KAAK,EAAEJ,IAAI,CAACK,OAAO,IAAI;QACzB,CAAC;MACH;MAEA,OAAO;QACLF,OAAO,EAAE,IAAI;QACbH;MACF,CAAC;IACH,CAAC,CAAC,OAAOI,KAAK,EAAE;MACd,OAAO;QACLD,OAAO,EAAE,KAAK;QACdC,KAAK,EAAEA,KAAK,YAAYE,KAAK,GAAGF,KAAK,CAACC,OAAO,GAAG;MAClD,CAAC;IACH;EACF;;EAEA;EACA,MAAME,KAAKA,CAACC,WAA2B,EAA2C;IAChF,MAAMV,QAAQ,GAAG,MAAM,IAAI,CAACP,OAAO,CAAoB,aAAa,EAAE;MACpEkB,MAAM,EAAE,MAAM;MACdC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACJ,WAAW;IAClC,CAAC,CAAC;IAEF,IAAIV,QAAQ,CAACK,OAAO,IAAIL,QAAQ,CAACE,IAAI,EAAE;MACrC,IAAI,CAACZ,KAAK,GAAGU,QAAQ,CAACE,IAAI,CAACZ,KAAK;MAChCC,YAAY,CAACwB,OAAO,CAAC,YAAY,EAAE,IAAI,CAACzB,KAAK,CAAC;MAC9CC,YAAY,CAACwB,OAAO,CAAC,WAAW,EAAEF,IAAI,CAACC,SAAS,CAACd,QAAQ,CAACE,IAAI,CAACc,KAAK,CAAC,CAAC;IACxE;IAEA,OAAOhB,QAAQ;EACjB;EAEA,MAAMiB,MAAMA,CAAA,EAAkB;IAC5B,IAAI,IAAI,CAAC3B,KAAK,EAAE;MACd,MAAM,IAAI,CAACG,OAAO,CAAC,cAAc,EAAE;QAAEkB,MAAM,EAAE;MAAO,CAAC,CAAC;IACxD;IACA,IAAI,CAACrB,KAAK,GAAG,IAAI;IACjBC,YAAY,CAAC2B,UAAU,CAAC,YAAY,CAAC;IACrC3B,YAAY,CAAC2B,UAAU,CAAC,WAAW,CAAC;EACtC;EAEAC,eAAeA,CAAA,EAAY;IACzB,OAAO,CAAC,CAAC,IAAI,CAAC7B,KAAK;EACrB;EAEA8B,cAAcA,CAAA,EAAG;IACf,MAAMC,SAAS,GAAG9B,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;IACnD,OAAO6B,SAAS,GAAGR,IAAI,CAACS,KAAK,CAACD,SAAS,CAAC,GAAG,IAAI;EACjD;;EAEA;EACA,MAAME,iBAAiBA,CAAA,EAA8B;IACnD,OAAO,IAAI,CAAC9B,OAAO,CAAC,kBAAkB,CAAC;EACzC;EAEA,MAAM+B,WAAWA,CAAA,EAA8B;IAC7C,OAAO,IAAI,CAAC/B,OAAO,CAAC,qBAAqB,CAAC;EAC5C;;EAEA;EACA,MAAMgC,SAASA,CAACC,MAA2D,EAA6B;IACtG,MAAMC,WAAW,GAAG,IAAIC,eAAe,CAAC,CAAC;IACzC,IAAIF,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEG,IAAI,EAAEF,WAAW,CAACG,MAAM,CAAC,MAAM,EAAEJ,MAAM,CAACG,IAAI,CAACE,QAAQ,CAAC,CAAC,CAAC;IACpE,IAAIL,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEM,KAAK,EAAEL,WAAW,CAACG,MAAM,CAAC,OAAO,EAAEJ,MAAM,CAACM,KAAK,CAACD,QAAQ,CAAC,CAAC,CAAC;IACvE,IAAIL,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEO,MAAM,EAAEN,WAAW,CAACG,MAAM,CAAC,QAAQ,EAAEJ,MAAM,CAACO,MAAM,CAAC;IAE/D,MAAMC,KAAK,GAAGP,WAAW,CAACI,QAAQ,CAAC,CAAC;IACpC,OAAO,IAAI,CAACtC,OAAO,CAAC,UAAUyC,KAAK,GAAG,IAAIA,KAAK,EAAE,GAAG,EAAE,EAAE,CAAC;EAC3D;EAEA,MAAMC,QAAQA,CAACC,EAAU,EAA6B;IACpD,OAAO,IAAI,CAAC3C,OAAO,CAAC,WAAW2C,EAAE,EAAE,CAAC;EACtC;EAEA,MAAMC,iBAAiBA,CAACD,EAAU,EAAEH,MAAc,EAAEK,KAAc,EAA6B;IAC7F,OAAO,IAAI,CAAC7C,OAAO,CAAC,WAAW2C,EAAE,SAAS,EAAE;MAC1CzB,MAAM,EAAE,KAAK;MACbC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;QAAEmB,MAAM;QAAEK;MAAM,CAAC;IACxC,CAAC,CAAC;EACJ;EAEA,MAAMC,WAAWA,CAACH,EAAU,EAA6B;IACvD,OAAO,IAAI,CAAC3C,OAAO,CAAC,WAAW2C,EAAE,EAAE,EAAE;MAAEzB,MAAM,EAAE;IAAS,CAAC,CAAC;EAC5D;;EAEA;EACA,MAAM6B,GAAGA,CAAI9C,QAAgB,EAA2B;IACtD,OAAO,IAAI,CAACD,OAAO,CAAIC,QAAQ,EAAE;MAAEiB,MAAM,EAAE;IAAM,CAAC,CAAC;EACrD;EAEA,MAAM8B,IAAIA,CAAI/C,QAAgB,EAAEQ,IAAU,EAA2B;IACnE,OAAO,IAAI,CAACT,OAAO,CAAIC,QAAQ,EAAE;MAC/BiB,MAAM,EAAE,MAAM;MACdC,IAAI,EAAEV,IAAI,GAAGW,IAAI,CAACC,SAAS,CAACZ,IAAI,CAAC,GAAGwC;IACtC,CAAC,CAAC;EACJ;EAEA,MAAMC,GAAGA,CAAIjD,QAAgB,EAAEQ,IAAU,EAA2B;IAClE,OAAO,IAAI,CAACT,OAAO,CAAIC,QAAQ,EAAE;MAC/BiB,MAAM,EAAE,KAAK;MACbC,IAAI,EAAEV,IAAI,GAAGW,IAAI,CAACC,SAAS,CAACZ,IAAI,CAAC,GAAGwC;IACtC,CAAC,CAAC;EACJ;EAEA,MAAME,MAAMA,CAAIlD,QAAgB,EAA2B;IACzD,OAAO,IAAI,CAACD,OAAO,CAAIC,QAAQ,EAAE;MAAEiB,MAAM,EAAE;IAAS,CAAC,CAAC;EACxD;AACF;AAEA,OAAO,MAAMkC,eAAe,GAAG,IAAI1D,eAAe,CAAC,CAAC;AACpD,eAAeA,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}