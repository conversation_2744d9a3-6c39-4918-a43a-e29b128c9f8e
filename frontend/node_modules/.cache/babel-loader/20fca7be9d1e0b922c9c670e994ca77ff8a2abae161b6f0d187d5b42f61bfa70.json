{"ast": null, "code": "'use strict';\n\nvar $propertyIsEnumerable = {}.propertyIsEnumerable;\n// eslint-disable-next-line es/no-object-getownpropertydescriptor -- safe\nvar getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\n\n// Nashorn ~ JDK8 bug\nvar NASHORN_BUG = getOwnPropertyDescriptor && !$propertyIsEnumerable.call({\n  1: 2\n}, 1);\n\n// `Object.prototype.propertyIsEnumerable` method implementation\n// https://tc39.es/ecma262/#sec-object.prototype.propertyisenumerable\nexports.f = NASHORN_BUG ? function propertyIsEnumerable(V) {\n  var descriptor = getOwnPropertyDescriptor(this, V);\n  return !!descriptor && descriptor.enumerable;\n} : $propertyIsEnumerable;", "map": {"version": 3, "names": ["$propertyIsEnumerable", "propertyIsEnumerable", "getOwnPropertyDescriptor", "Object", "NASHORN_BUG", "call", "exports", "f", "V", "descriptor", "enumerable"], "sources": ["/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/node_modules/core-js-pure/internals/object-property-is-enumerable.js"], "sourcesContent": ["'use strict';\nvar $propertyIsEnumerable = {}.propertyIsEnumerable;\n// eslint-disable-next-line es/no-object-getownpropertydescriptor -- safe\nvar getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\n\n// Nashorn ~ JDK8 bug\nvar NASHORN_BUG = getOwnPropertyDescriptor && !$propertyIsEnumerable.call({ 1: 2 }, 1);\n\n// `Object.prototype.propertyIsEnumerable` method implementation\n// https://tc39.es/ecma262/#sec-object.prototype.propertyisenumerable\nexports.f = NASHORN_BUG ? function propertyIsEnumerable(V) {\n  var descriptor = getOwnPropertyDescriptor(this, V);\n  return !!descriptor && descriptor.enumerable;\n} : $propertyIsEnumerable;\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,qBAAqB,GAAG,CAAC,CAAC,CAACC,oBAAoB;AACnD;AACA,IAAIC,wBAAwB,GAAGC,MAAM,CAACD,wBAAwB;;AAE9D;AACA,IAAIE,WAAW,GAAGF,wBAAwB,IAAI,CAACF,qBAAqB,CAACK,IAAI,CAAC;EAAE,CAAC,EAAE;AAAE,CAAC,EAAE,CAAC,CAAC;;AAEtF;AACA;AACAC,OAAO,CAACC,CAAC,GAAGH,WAAW,GAAG,SAASH,oBAAoBA,CAACO,CAAC,EAAE;EACzD,IAAIC,UAAU,GAAGP,wBAAwB,CAAC,IAAI,EAAEM,CAAC,CAAC;EAClD,OAAO,CAAC,CAACC,UAAU,IAAIA,UAAU,CAACC,UAAU;AAC9C,CAAC,GAAGV,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}