{"ast": null, "code": "import _objectSpread from\"/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";const API_BASE_URL=process.env.REACT_APP_API_URL||'http://localhost:8000/api';class AdminApiService{constructor(){let baseURL=arguments.length>0&&arguments[0]!==undefined?arguments[0]:API_BASE_URL;this.baseURL=void 0;this.token=null;this.baseURL=baseURL;// Try to get token from localStorage\nthis.token=localStorage.getItem('adminToken');}async request(endpoint){let options=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{};const url=\"\".concat(this.baseURL,\"/admin\").concat(endpoint);const defaultHeaders={'Content-Type':'application/json'};// Add authorization header if token exists\nif(this.token){defaultHeaders['Authorization']=\"Bearer \".concat(this.token);}const config=_objectSpread({headers:_objectSpread(_objectSpread({},defaultHeaders),options.headers)},options);try{const response=await fetch(url,config);const data=await response.json();if(!response.ok){return{success:false,error:data.message||'An error occurred'};}return{success:true,data};}catch(error){return{success:false,error:error instanceof Error?error.message:'Network error'};}}// Authentication methods\nasync login(credentials){const response=await this.request('/auth/login',{method:'POST',body:JSON.stringify(credentials)});if(response.success&&response.data){this.token=response.data.token;localStorage.setItem('adminToken',this.token);localStorage.setItem('adminUser',JSON.stringify(response.data.admin));}return response;}async logout(){if(this.token){await this.request('/auth/logout',{method:'POST'});}this.token=null;localStorage.removeItem('adminToken');localStorage.removeItem('adminUser');}isAuthenticated(){return!!this.token;}getStoredAdmin(){const adminData=localStorage.getItem('adminUser');return adminData?JSON.parse(adminData):null;}// Dashboard methods\nasync getDashboardStats(){return this.request('/dashboard/stats');}async getOverview(){return this.request('/dashboard/overview');}// Orders methods\nasync getOrders(params){const queryParams=new URLSearchParams();if(params!==null&&params!==void 0&&params.page)queryParams.append('page',params.page.toString());if(params!==null&&params!==void 0&&params.limit)queryParams.append('limit',params.limit.toString());if(params!==null&&params!==void 0&&params.status)queryParams.append('status',params.status);const query=queryParams.toString();return this.request(\"/orders\".concat(query?\"?\".concat(query):''));}async getOrder(id){return this.request(\"/orders/\".concat(id));}async updateOrderStatus(id,status,notes){return this.request(\"/orders/\".concat(id,\"/status\"),{method:'PUT',body:JSON.stringify({status,notes})});}async deleteOrder(id){return this.request(\"/orders/\".concat(id),{method:'DELETE'});}// Generic CRUD methods\nasync get(endpoint){return this.request(endpoint,{method:'GET'});}async post(endpoint,data){return this.request(endpoint,{method:'POST',body:data?JSON.stringify(data):undefined});}async put(endpoint,data){return this.request(endpoint,{method:'PUT',body:data?JSON.stringify(data):undefined});}async delete(endpoint){return this.request(endpoint,{method:'DELETE'});}}export const adminApiService=new AdminApiService();export default AdminApiService;", "map": {"version": 3, "names": ["API_BASE_URL", "process", "env", "REACT_APP_API_URL", "AdminApiService", "constructor", "baseURL", "arguments", "length", "undefined", "token", "localStorage", "getItem", "request", "endpoint", "options", "url", "concat", "defaultHeaders", "config", "_objectSpread", "headers", "response", "fetch", "data", "json", "ok", "success", "error", "message", "Error", "login", "credentials", "method", "body", "JSON", "stringify", "setItem", "admin", "logout", "removeItem", "isAuthenticated", "getStoredAdmin", "adminData", "parse", "getDashboardStats", "getOverview", "getOrders", "params", "queryParams", "URLSearchParams", "page", "append", "toString", "limit", "status", "query", "getOrder", "id", "updateOrderStatus", "notes", "deleteOrder", "get", "post", "put", "delete", "adminApiService"], "sources": ["/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/services/adminApi.ts"], "sourcesContent": ["import { ApiResponse } from '../types';\n\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000/api';\n\ninterface AdminLoginData {\n  email: string;\n  password: string;\n}\n\ninterface AdminAuthResponse {\n  admin: {\n    id: number;\n    email: string;\n    name: string;\n    role: string;\n    status: string;\n  };\n  token: string;\n  expiresIn: string;\n}\n\nclass AdminApiService {\n  private baseURL: string;\n  private token: string | null = null;\n\n  constructor(baseURL: string = API_BASE_URL) {\n    this.baseURL = baseURL;\n    // Try to get token from localStorage\n    this.token = localStorage.getItem('adminToken');\n  }\n\n  private async request<T>(\n    endpoint: string,\n    options: RequestInit = {}\n  ): Promise<ApiResponse<T>> {\n    const url = `${this.baseURL}/admin${endpoint}`;\n    \n    const defaultHeaders: Record<string, string> = {\n      'Content-Type': 'application/json',\n    };\n\n    // Add authorization header if token exists\n    if (this.token) {\n      defaultHeaders['Authorization'] = `Bearer ${this.token}`;\n    }\n\n    const config: RequestInit = {\n      headers: { ...defaultHeaders, ...options.headers },\n      ...options,\n    };\n\n    try {\n      const response = await fetch(url, config);\n      const data = await response.json();\n\n      if (!response.ok) {\n        return {\n          success: false,\n          error: data.message || 'An error occurred',\n        };\n      }\n\n      return {\n        success: true,\n        data,\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : 'Network error',\n      };\n    }\n  }\n\n  // Authentication methods\n  async login(credentials: AdminLoginData): Promise<ApiResponse<AdminAuthResponse>> {\n    const response = await this.request<AdminAuthResponse>('/auth/login', {\n      method: 'POST',\n      body: JSON.stringify(credentials),\n    });\n\n    if (response.success && response.data) {\n      this.token = response.data.token;\n      localStorage.setItem('adminToken', this.token);\n      localStorage.setItem('adminUser', JSON.stringify(response.data.admin));\n    }\n\n    return response;\n  }\n\n  async logout(): Promise<void> {\n    if (this.token) {\n      await this.request('/auth/logout', { method: 'POST' });\n    }\n    this.token = null;\n    localStorage.removeItem('adminToken');\n    localStorage.removeItem('adminUser');\n  }\n\n  isAuthenticated(): boolean {\n    return !!this.token;\n  }\n\n  getStoredAdmin() {\n    const adminData = localStorage.getItem('adminUser');\n    return adminData ? JSON.parse(adminData) : null;\n  }\n\n  // Dashboard methods\n  async getDashboardStats(): Promise<ApiResponse<any>> {\n    return this.request('/dashboard/stats');\n  }\n\n  async getOverview(): Promise<ApiResponse<any>> {\n    return this.request('/dashboard/overview');\n  }\n\n  // Orders methods\n  async getOrders(params?: { page?: number; limit?: number; status?: string }): Promise<ApiResponse<any>> {\n    const queryParams = new URLSearchParams();\n    if (params?.page) queryParams.append('page', params.page.toString());\n    if (params?.limit) queryParams.append('limit', params.limit.toString());\n    if (params?.status) queryParams.append('status', params.status);\n    \n    const query = queryParams.toString();\n    return this.request(`/orders${query ? `?${query}` : ''}`);\n  }\n\n  async getOrder(id: string): Promise<ApiResponse<any>> {\n    return this.request(`/orders/${id}`);\n  }\n\n  async updateOrderStatus(id: string, status: string, notes?: string): Promise<ApiResponse<any>> {\n    return this.request(`/orders/${id}/status`, {\n      method: 'PUT',\n      body: JSON.stringify({ status, notes }),\n    });\n  }\n\n  async deleteOrder(id: string): Promise<ApiResponse<any>> {\n    return this.request(`/orders/${id}`, { method: 'DELETE' });\n  }\n\n  // Generic CRUD methods\n  async get<T>(endpoint: string): Promise<ApiResponse<T>> {\n    return this.request<T>(endpoint, { method: 'GET' });\n  }\n\n  async post<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {\n    return this.request<T>(endpoint, {\n      method: 'POST',\n      body: data ? JSON.stringify(data) : undefined,\n    });\n  }\n\n  async put<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {\n    return this.request<T>(endpoint, {\n      method: 'PUT',\n      body: data ? JSON.stringify(data) : undefined,\n    });\n  }\n\n  async delete<T>(endpoint: string): Promise<ApiResponse<T>> {\n    return this.request<T>(endpoint, { method: 'DELETE' });\n  }\n}\n\nexport const adminApiService = new AdminApiService();\nexport default AdminApiService;\n"], "mappings": "sIAEA,KAAM,CAAAA,YAAY,CAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,EAAI,2BAA2B,CAmBjF,KAAM,CAAAC,eAAgB,CAIpBC,WAAWA,CAAA,CAAiC,IAAhC,CAAAC,OAAe,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAGP,YAAY,MAHlCM,OAAO,aACPI,KAAK,CAAkB,IAAI,CAGjC,IAAI,CAACJ,OAAO,CAAGA,OAAO,CACtB;AACA,IAAI,CAACI,KAAK,CAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,CACjD,CAEA,KAAc,CAAAC,OAAOA,CACnBC,QAAgB,CAES,IADzB,CAAAC,OAAoB,CAAAR,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAAC,CAEzB,KAAM,CAAAS,GAAG,IAAAC,MAAA,CAAM,IAAI,CAACX,OAAO,WAAAW,MAAA,CAASH,QAAQ,CAAE,CAE9C,KAAM,CAAAI,cAAsC,CAAG,CAC7C,cAAc,CAAE,kBAClB,CAAC,CAED;AACA,GAAI,IAAI,CAACR,KAAK,CAAE,CACdQ,cAAc,CAAC,eAAe,CAAC,WAAAD,MAAA,CAAa,IAAI,CAACP,KAAK,CAAE,CAC1D,CAEA,KAAM,CAAAS,MAAmB,CAAAC,aAAA,EACvBC,OAAO,CAAAD,aAAA,CAAAA,aAAA,IAAOF,cAAc,EAAKH,OAAO,CAACM,OAAO,CAAE,EAC/CN,OAAO,CACX,CAED,GAAI,CACF,KAAM,CAAAO,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAACP,GAAG,CAAEG,MAAM,CAAC,CACzC,KAAM,CAAAK,IAAI,CAAG,KAAM,CAAAF,QAAQ,CAACG,IAAI,CAAC,CAAC,CAElC,GAAI,CAACH,QAAQ,CAACI,EAAE,CAAE,CAChB,MAAO,CACLC,OAAO,CAAE,KAAK,CACdC,KAAK,CAAEJ,IAAI,CAACK,OAAO,EAAI,mBACzB,CAAC,CACH,CAEA,MAAO,CACLF,OAAO,CAAE,IAAI,CACbH,IACF,CAAC,CACH,CAAE,MAAOI,KAAK,CAAE,CACd,MAAO,CACLD,OAAO,CAAE,KAAK,CACdC,KAAK,CAAEA,KAAK,WAAY,CAAAE,KAAK,CAAGF,KAAK,CAACC,OAAO,CAAG,eAClD,CAAC,CACH,CACF,CAEA;AACA,KAAM,CAAAE,KAAKA,CAACC,WAA2B,CAA2C,CAChF,KAAM,CAAAV,QAAQ,CAAG,KAAM,KAAI,CAACT,OAAO,CAAoB,aAAa,CAAE,CACpEoB,MAAM,CAAE,MAAM,CACdC,IAAI,CAAEC,IAAI,CAACC,SAAS,CAACJ,WAAW,CAClC,CAAC,CAAC,CAEF,GAAIV,QAAQ,CAACK,OAAO,EAAIL,QAAQ,CAACE,IAAI,CAAE,CACrC,IAAI,CAACd,KAAK,CAAGY,QAAQ,CAACE,IAAI,CAACd,KAAK,CAChCC,YAAY,CAAC0B,OAAO,CAAC,YAAY,CAAE,IAAI,CAAC3B,KAAK,CAAC,CAC9CC,YAAY,CAAC0B,OAAO,CAAC,WAAW,CAAEF,IAAI,CAACC,SAAS,CAACd,QAAQ,CAACE,IAAI,CAACc,KAAK,CAAC,CAAC,CACxE,CAEA,MAAO,CAAAhB,QAAQ,CACjB,CAEA,KAAM,CAAAiB,MAAMA,CAAA,CAAkB,CAC5B,GAAI,IAAI,CAAC7B,KAAK,CAAE,CACd,KAAM,KAAI,CAACG,OAAO,CAAC,cAAc,CAAE,CAAEoB,MAAM,CAAE,MAAO,CAAC,CAAC,CACxD,CACA,IAAI,CAACvB,KAAK,CAAG,IAAI,CACjBC,YAAY,CAAC6B,UAAU,CAAC,YAAY,CAAC,CACrC7B,YAAY,CAAC6B,UAAU,CAAC,WAAW,CAAC,CACtC,CAEAC,eAAeA,CAAA,CAAY,CACzB,MAAO,CAAC,CAAC,IAAI,CAAC/B,KAAK,CACrB,CAEAgC,cAAcA,CAAA,CAAG,CACf,KAAM,CAAAC,SAAS,CAAGhC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC,CACnD,MAAO,CAAA+B,SAAS,CAAGR,IAAI,CAACS,KAAK,CAACD,SAAS,CAAC,CAAG,IAAI,CACjD,CAEA;AACA,KAAM,CAAAE,iBAAiBA,CAAA,CAA8B,CACnD,MAAO,KAAI,CAAChC,OAAO,CAAC,kBAAkB,CAAC,CACzC,CAEA,KAAM,CAAAiC,WAAWA,CAAA,CAA8B,CAC7C,MAAO,KAAI,CAACjC,OAAO,CAAC,qBAAqB,CAAC,CAC5C,CAEA;AACA,KAAM,CAAAkC,SAASA,CAACC,MAA2D,CAA6B,CACtG,KAAM,CAAAC,WAAW,CAAG,GAAI,CAAAC,eAAe,CAAC,CAAC,CACzC,GAAIF,MAAM,SAANA,MAAM,WAANA,MAAM,CAAEG,IAAI,CAAEF,WAAW,CAACG,MAAM,CAAC,MAAM,CAAEJ,MAAM,CAACG,IAAI,CAACE,QAAQ,CAAC,CAAC,CAAC,CACpE,GAAIL,MAAM,SAANA,MAAM,WAANA,MAAM,CAAEM,KAAK,CAAEL,WAAW,CAACG,MAAM,CAAC,OAAO,CAAEJ,MAAM,CAACM,KAAK,CAACD,QAAQ,CAAC,CAAC,CAAC,CACvE,GAAIL,MAAM,SAANA,MAAM,WAANA,MAAM,CAAEO,MAAM,CAAEN,WAAW,CAACG,MAAM,CAAC,QAAQ,CAAEJ,MAAM,CAACO,MAAM,CAAC,CAE/D,KAAM,CAAAC,KAAK,CAAGP,WAAW,CAACI,QAAQ,CAAC,CAAC,CACpC,MAAO,KAAI,CAACxC,OAAO,WAAAI,MAAA,CAAWuC,KAAK,KAAAvC,MAAA,CAAOuC,KAAK,EAAK,EAAE,CAAE,CAAC,CAC3D,CAEA,KAAM,CAAAC,QAAQA,CAACC,EAAU,CAA6B,CACpD,MAAO,KAAI,CAAC7C,OAAO,YAAAI,MAAA,CAAYyC,EAAE,CAAE,CAAC,CACtC,CAEA,KAAM,CAAAC,iBAAiBA,CAACD,EAAU,CAAEH,MAAc,CAAEK,KAAc,CAA6B,CAC7F,MAAO,KAAI,CAAC/C,OAAO,YAAAI,MAAA,CAAYyC,EAAE,YAAW,CAC1CzB,MAAM,CAAE,KAAK,CACbC,IAAI,CAAEC,IAAI,CAACC,SAAS,CAAC,CAAEmB,MAAM,CAAEK,KAAM,CAAC,CACxC,CAAC,CAAC,CACJ,CAEA,KAAM,CAAAC,WAAWA,CAACH,EAAU,CAA6B,CACvD,MAAO,KAAI,CAAC7C,OAAO,YAAAI,MAAA,CAAYyC,EAAE,EAAI,CAAEzB,MAAM,CAAE,QAAS,CAAC,CAAC,CAC5D,CAEA;AACA,KAAM,CAAA6B,GAAGA,CAAIhD,QAAgB,CAA2B,CACtD,MAAO,KAAI,CAACD,OAAO,CAAIC,QAAQ,CAAE,CAAEmB,MAAM,CAAE,KAAM,CAAC,CAAC,CACrD,CAEA,KAAM,CAAA8B,IAAIA,CAAIjD,QAAgB,CAAEU,IAAU,CAA2B,CACnE,MAAO,KAAI,CAACX,OAAO,CAAIC,QAAQ,CAAE,CAC/BmB,MAAM,CAAE,MAAM,CACdC,IAAI,CAAEV,IAAI,CAAGW,IAAI,CAACC,SAAS,CAACZ,IAAI,CAAC,CAAGf,SACtC,CAAC,CAAC,CACJ,CAEA,KAAM,CAAAuD,GAAGA,CAAIlD,QAAgB,CAAEU,IAAU,CAA2B,CAClE,MAAO,KAAI,CAACX,OAAO,CAAIC,QAAQ,CAAE,CAC/BmB,MAAM,CAAE,KAAK,CACbC,IAAI,CAAEV,IAAI,CAAGW,IAAI,CAACC,SAAS,CAACZ,IAAI,CAAC,CAAGf,SACtC,CAAC,CAAC,CACJ,CAEA,KAAM,CAAAwD,MAAMA,CAAInD,QAAgB,CAA2B,CACzD,MAAO,KAAI,CAACD,OAAO,CAAIC,QAAQ,CAAE,CAAEmB,MAAM,CAAE,QAAS,CAAC,CAAC,CACxD,CACF,CAEA,MAAO,MAAM,CAAAiC,eAAe,CAAG,GAAI,CAAA9D,eAAe,CAAC,CAAC,CACpD,cAAe,CAAAA,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}