{"ast": null, "code": "import _objectSpread from\"/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useEffect}from'react';import{useNavigate}from'react-router-dom';import{Layout,Button}from'../components';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const OptionsForm=()=>{const navigate=useNavigate();const[uploadedFile,setUploadedFile]=useState(null);const[options,setOptions]=useState({format:'A4',paperType:'standard',finish:'none',copies:1,hasColor:false,complexity:'low',notes:''});const[priceBreakdown,setPriceBreakdown]=useState({basePrice:0,paperCost:0,finishCost:0,complexityCost:0,colorCost:0,total:0});useEffect(()=>{// Get uploaded file info from localStorage\nconst fileInfo=localStorage.getItem('uploadedFile');if(fileInfo){setUploadedFile(JSON.parse(fileInfo));}else{// Redirect back to upload if no file\nnavigate('/upload');}},[navigate]);const calculatePrice=React.useCallback(()=>{let basePrice=2.5;// Base price per page in AOA\n// Format pricing\nconst formatPrices={'A4':1.0,'A3':2.0,'A5':0.8,'Letter':1.0};// Paper type pricing\nconst paperPrices={'standard':1.0,'premium':1.5,'photo':2.0,'cardstock':1.8};// Finish pricing\nconst finishPrices={'none':1.0,'glossy':1.3,'matte':1.2,'laminated':1.8};// Complexity pricing\nconst complexityPrices={'low':1.0,'medium':1.3,'high':1.6};const formatMultiplier=formatPrices[options.format]||1.0;const paperMultiplier=paperPrices[options.paperType]||1.0;const finishMultiplier=finishPrices[options.finish]||1.0;const complexityMultiplier=complexityPrices[options.complexity]||1.0;const colorMultiplier=options.hasColor?1.5:1.0;const paperCost=basePrice*formatMultiplier*(paperMultiplier-1);const finishCost=basePrice*formatMultiplier*(finishMultiplier-1);const complexityCost=basePrice*formatMultiplier*(complexityMultiplier-1);const colorCost=options.hasColor?basePrice*formatMultiplier*0.5:0;const unitPrice=basePrice*formatMultiplier*paperMultiplier*finishMultiplier*complexityMultiplier*colorMultiplier;const total=unitPrice*options.copies;setPriceBreakdown({basePrice:basePrice*formatMultiplier,paperCost,finishCost,complexityCost,colorCost,total});},[options]);useEffect(()=>{calculatePrice();},[calculatePrice]);const handleOptionChange=(field,value)=>{setOptions(prev=>_objectSpread(_objectSpread({},prev),{},{[field]:value}));};const handleContinue=()=>{// Store options in localStorage\nlocalStorage.setItem('printOptions',JSON.stringify(options));localStorage.setItem('priceBreakdown',JSON.stringify(priceBreakdown));navigate('/summary');};const formatPrice=price=>{return\"\".concat(price.toFixed(2),\" AOA\");};if(!uploadedFile){return/*#__PURE__*/_jsx(\"div\",{children:\"Carregando...\"});}return/*#__PURE__*/_jsx(Layout,{children:/*#__PURE__*/_jsxs(\"div\",{className:\"max-w-4xl mx-auto py-8\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-center mb-8\",children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-3xl font-bold text-gray-900 mb-4\",children:\"Op\\xE7\\xF5es de Impress\\xE3o\"}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-lg text-gray-600\",children:[\"Configure as op\\xE7\\xF5es para o seu documento: \",uploadedFile.name]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 lg:grid-cols-2 gap-8\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-lg shadow-lg p-6\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-xl font-semibold text-gray-900 mb-6\",children:\"Configura\\xE7\\xF5es\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"Formato do Papel\"}),/*#__PURE__*/_jsxs(\"select\",{value:options.format,onChange:e=>handleOptionChange('format',e.target.value),className:\"w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"A4\",children:\"A4 (210 \\xD7 297 mm)\"}),/*#__PURE__*/_jsx(\"option\",{value:\"A3\",children:\"A3 (297 \\xD7 420 mm)\"}),/*#__PURE__*/_jsx(\"option\",{value:\"A5\",children:\"A5 (148 \\xD7 210 mm)\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Letter\",children:\"Letter (216 \\xD7 279 mm)\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"Tipo de Papel\"}),/*#__PURE__*/_jsxs(\"select\",{value:options.paperType,onChange:e=>handleOptionChange('paperType',e.target.value),className:\"w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"standard\",children:\"Papel Standard (75g/m\\xB2)\"}),/*#__PURE__*/_jsx(\"option\",{value:\"premium\",children:\"Papel Premium (90g/m\\xB2)\"}),/*#__PURE__*/_jsx(\"option\",{value:\"photo\",children:\"Papel Fotogr\\xE1fico (200g/m\\xB2)\"}),/*#__PURE__*/_jsx(\"option\",{value:\"cardstock\",children:\"Cartolina (250g/m\\xB2)\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"Acabamento\"}),/*#__PURE__*/_jsxs(\"select\",{value:options.finish,onChange:e=>handleOptionChange('finish',e.target.value),className:\"w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"none\",children:\"Sem Acabamento\"}),/*#__PURE__*/_jsx(\"option\",{value:\"glossy\",children:\"Brilhante\"}),/*#__PURE__*/_jsx(\"option\",{value:\"matte\",children:\"Fosco\"}),/*#__PURE__*/_jsx(\"option\",{value:\"laminated\",children:\"Plastificado\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"N\\xFAmero de C\\xF3pias\"}),/*#__PURE__*/_jsx(\"input\",{type:\"number\",min:\"1\",max:\"1000\",value:options.copies,onChange:e=>handleOptionChange('copies',parseInt(e.target.value)||1),className:\"w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"})]}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(\"label\",{className:\"flex items-center space-x-3\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:options.hasColor,onChange:e=>handleOptionChange('hasColor',e.target.checked),className:\"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-medium text-gray-700\",children:\"Impress\\xE3o a Cores\"})]})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"Complexidade do Documento\"}),/*#__PURE__*/_jsxs(\"select\",{value:options.complexity,onChange:e=>handleOptionChange('complexity',e.target.value),className:\"w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"low\",children:\"Baixa (Texto simples)\"}),/*#__PURE__*/_jsx(\"option\",{value:\"medium\",children:\"M\\xE9dia (Texto + Imagens)\"}),/*#__PURE__*/_jsx(\"option\",{value:\"high\",children:\"Alta (Design complexo)\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"Observa\\xE7\\xF5es (Opcional)\"}),/*#__PURE__*/_jsx(\"textarea\",{value:options.notes,onChange:e=>handleOptionChange('notes',e.target.value),rows:3,className:\"w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",placeholder:\"Instru\\xE7\\xF5es especiais para a impress\\xE3o...\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-lg shadow-lg p-6\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-xl font-semibold text-gray-900 mb-6\",children:\"Resumo de Pre\\xE7os\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between text-sm\",children:[/*#__PURE__*/_jsxs(\"span\",{className:\"text-gray-600\",children:[\"Pre\\xE7o base (\",options.format,\"):\"]}),/*#__PURE__*/_jsx(\"span\",{children:formatPrice(priceBreakdown.basePrice)})]}),priceBreakdown.paperCost>0&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between text-sm\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-600\",children:\"Papel premium:\"}),/*#__PURE__*/_jsxs(\"span\",{children:[\"+\",formatPrice(priceBreakdown.paperCost)]})]}),priceBreakdown.finishCost>0&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between text-sm\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-600\",children:\"Acabamento:\"}),/*#__PURE__*/_jsxs(\"span\",{children:[\"+\",formatPrice(priceBreakdown.finishCost)]})]}),priceBreakdown.complexityCost>0&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between text-sm\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-600\",children:\"Complexidade:\"}),/*#__PURE__*/_jsxs(\"span\",{children:[\"+\",formatPrice(priceBreakdown.complexityCost)]})]}),priceBreakdown.colorCost>0&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between text-sm\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-600\",children:\"Impress\\xE3o a cores:\"}),/*#__PURE__*/_jsxs(\"span\",{children:[\"+\",formatPrice(priceBreakdown.colorCost)]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between text-sm\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-600\",children:\"Quantidade:\"}),/*#__PURE__*/_jsxs(\"span\",{children:[options.copies,\" c\\xF3pia(s)\"]})]}),/*#__PURE__*/_jsx(\"hr\",{className:\"my-4\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between text-lg font-semibold\",children:[/*#__PURE__*/_jsx(\"span\",{children:\"Total:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-blue-600\",children:formatPrice(priceBreakdown.total)})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mt-6 p-4 bg-blue-50 rounded-lg\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"font-medium text-blue-900 mb-2\",children:\"Informa\\xE7\\xF5es\"}),/*#__PURE__*/_jsxs(\"ul\",{className:\"text-sm text-blue-800 space-y-1\",children:[/*#__PURE__*/_jsx(\"li\",{children:\"\\u2022 Entrega gr\\xE1tis em Luanda\"}),/*#__PURE__*/_jsx(\"li\",{children:\"\\u2022 Prazo: 2-5 dias \\xFAteis\"}),/*#__PURE__*/_jsx(\"li\",{children:\"\\u2022 Garantia de qualidade\"}),/*#__PURE__*/_jsx(\"li\",{children:\"\\u2022 Suporte t\\xE9cnico inclu\\xEDdo\"})]})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between mt-8\",children:[/*#__PURE__*/_jsx(Button,{variant:\"outline\",onClick:()=>navigate('/upload'),children:\"Voltar\"}),/*#__PURE__*/_jsx(Button,{onClick:handleContinue,children:\"Ver Resumo\"})]})]})});};export default OptionsForm;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "Layout", "<PERSON><PERSON>", "jsx", "_jsx", "jsxs", "_jsxs", "OptionsForm", "navigate", "uploadedFile", "setUploadedFile", "options", "setOptions", "format", "paperType", "finish", "copies", "hasColor", "complexity", "notes", "priceBreakdown", "setPriceBreakdown", "basePrice", "paperCost", "finishCost", "complexityCost", "colorCost", "total", "fileInfo", "localStorage", "getItem", "JSON", "parse", "calculatePrice", "useCallback", "formatPrices", "paperPrices", "finishPrices", "complexityPrices", "formatMultiplier", "paperMultiplier", "finishMultiplier", "complexityMultiplier", "colorMultiplier", "unitPrice", "handleOptionChange", "field", "value", "prev", "_objectSpread", "handleContinue", "setItem", "stringify", "formatPrice", "price", "concat", "toFixed", "children", "className", "name", "onChange", "e", "target", "type", "min", "max", "parseInt", "checked", "rows", "placeholder", "variant", "onClick"], "sources": ["/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/OptionsForm.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { Layout, Button } from '../components';\n\ninterface PrintOptions {\n  format: string;\n  paperType: string;\n  finish: string;\n  copies: number;\n  hasColor: boolean;\n  complexity: 'low' | 'medium' | 'high';\n  notes: string;\n}\n\ninterface PriceBreakdown {\n  basePrice: number;\n  paperCost: number;\n  finishCost: number;\n  complexityCost: number;\n  colorCost: number;\n  total: number;\n}\n\nconst OptionsForm: React.FC = () => {\n  const navigate = useNavigate();\n  const [uploadedFile, setUploadedFile] = useState<any>(null);\n  const [options, setOptions] = useState<PrintOptions>({\n    format: 'A4',\n    paperType: 'standard',\n    finish: 'none',\n    copies: 1,\n    hasColor: false,\n    complexity: 'low',\n    notes: '',\n  });\n  const [priceBreakdown, setPriceBreakdown] = useState<PriceBreakdown>({\n    basePrice: 0,\n    paperCost: 0,\n    finishCost: 0,\n    complexityCost: 0,\n    colorCost: 0,\n    total: 0,\n  });\n\n  useEffect(() => {\n    // Get uploaded file info from localStorage\n    const fileInfo = localStorage.getItem('uploadedFile');\n    if (fileInfo) {\n      setUploadedFile(JSON.parse(fileInfo));\n    } else {\n      // Redirect back to upload if no file\n      navigate('/upload');\n    }\n  }, [navigate]);\n\n  const calculatePrice = React.useCallback(() => {\n    let basePrice = 2.5; // Base price per page in AOA\n\n    // Format pricing\n    const formatPrices: { [key: string]: number } = {\n      'A4': 1.0,\n      'A3': 2.0,\n      'A5': 0.8,\n      'Letter': 1.0,\n    };\n\n    // Paper type pricing\n    const paperPrices: { [key: string]: number } = {\n      'standard': 1.0,\n      'premium': 1.5,\n      'photo': 2.0,\n      'cardstock': 1.8,\n    };\n\n    // Finish pricing\n    const finishPrices: { [key: string]: number } = {\n      'none': 1.0,\n      'glossy': 1.3,\n      'matte': 1.2,\n      'laminated': 1.8,\n    };\n\n    // Complexity pricing\n    const complexityPrices: { [key: string]: number } = {\n      'low': 1.0,\n      'medium': 1.3,\n      'high': 1.6,\n    };\n\n    const formatMultiplier = formatPrices[options.format] || 1.0;\n    const paperMultiplier = paperPrices[options.paperType] || 1.0;\n    const finishMultiplier = finishPrices[options.finish] || 1.0;\n    const complexityMultiplier = complexityPrices[options.complexity] || 1.0;\n    const colorMultiplier = options.hasColor ? 1.5 : 1.0;\n\n    const paperCost = basePrice * formatMultiplier * (paperMultiplier - 1);\n    const finishCost = basePrice * formatMultiplier * (finishMultiplier - 1);\n    const complexityCost = basePrice * formatMultiplier * (complexityMultiplier - 1);\n    const colorCost = options.hasColor ? basePrice * formatMultiplier * 0.5 : 0;\n\n    const unitPrice = basePrice * formatMultiplier * paperMultiplier * finishMultiplier * complexityMultiplier * colorMultiplier;\n    const total = unitPrice * options.copies;\n\n    setPriceBreakdown({\n      basePrice: basePrice * formatMultiplier,\n      paperCost,\n      finishCost,\n      complexityCost,\n      colorCost,\n      total,\n    });\n  }, [options]);\n\n  useEffect(() => {\n    calculatePrice();\n  }, [calculatePrice]);\n\n  const handleOptionChange = (field: keyof PrintOptions, value: any) => {\n    setOptions(prev => ({ ...prev, [field]: value }));\n  };\n\n  const handleContinue = () => {\n    // Store options in localStorage\n    localStorage.setItem('printOptions', JSON.stringify(options));\n    localStorage.setItem('priceBreakdown', JSON.stringify(priceBreakdown));\n    navigate('/summary');\n  };\n\n  const formatPrice = (price: number) => {\n    return `${price.toFixed(2)} AOA`;\n  };\n\n  if (!uploadedFile) {\n    return <div>Carregando...</div>;\n  }\n\n  return (\n    <Layout>\n      <div className=\"max-w-4xl mx-auto py-8\">\n        <div className=\"text-center mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-4\">\n            Opções de Impressão\n          </h1>\n          <p className=\"text-lg text-gray-600\">\n            Configure as opções para o seu documento: {uploadedFile.name}\n          </p>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n          {/* Options Form */}\n          <div className=\"bg-white rounded-lg shadow-lg p-6\">\n            <h2 className=\"text-xl font-semibold text-gray-900 mb-6\">\n              Configurações\n            </h2>\n\n            <div className=\"space-y-6\">\n              {/* Format */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Formato do Papel\n                </label>\n                <select\n                  value={options.format}\n                  onChange={(e) => handleOptionChange('format', e.target.value)}\n                  className=\"w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                >\n                  <option value=\"A4\">A4 (210 × 297 mm)</option>\n                  <option value=\"A3\">A3 (297 × 420 mm)</option>\n                  <option value=\"A5\">A5 (148 × 210 mm)</option>\n                  <option value=\"Letter\">Letter (216 × 279 mm)</option>\n                </select>\n              </div>\n\n              {/* Paper Type */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Tipo de Papel\n                </label>\n                <select\n                  value={options.paperType}\n                  onChange={(e) => handleOptionChange('paperType', e.target.value)}\n                  className=\"w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                >\n                  <option value=\"standard\">Papel Standard (75g/m²)</option>\n                  <option value=\"premium\">Papel Premium (90g/m²)</option>\n                  <option value=\"photo\">Papel Fotográfico (200g/m²)</option>\n                  <option value=\"cardstock\">Cartolina (250g/m²)</option>\n                </select>\n              </div>\n\n              {/* Finish */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Acabamento\n                </label>\n                <select\n                  value={options.finish}\n                  onChange={(e) => handleOptionChange('finish', e.target.value)}\n                  className=\"w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                >\n                  <option value=\"none\">Sem Acabamento</option>\n                  <option value=\"glossy\">Brilhante</option>\n                  <option value=\"matte\">Fosco</option>\n                  <option value=\"laminated\">Plastificado</option>\n                </select>\n              </div>\n\n              {/* Copies */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Número de Cópias\n                </label>\n                <input\n                  type=\"number\"\n                  min=\"1\"\n                  max=\"1000\"\n                  value={options.copies}\n                  onChange={(e) => handleOptionChange('copies', parseInt(e.target.value) || 1)}\n                  className=\"w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                />\n              </div>\n\n              {/* Color */}\n              <div>\n                <label className=\"flex items-center space-x-3\">\n                  <input\n                    type=\"checkbox\"\n                    checked={options.hasColor}\n                    onChange={(e) => handleOptionChange('hasColor', e.target.checked)}\n                    className=\"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500\"\n                  />\n                  <span className=\"text-sm font-medium text-gray-700\">\n                    Impressão a Cores\n                  </span>\n                </label>\n              </div>\n\n              {/* Complexity */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Complexidade do Documento\n                </label>\n                <select\n                  value={options.complexity}\n                  onChange={(e) => handleOptionChange('complexity', e.target.value as 'low' | 'medium' | 'high')}\n                  className=\"w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                >\n                  <option value=\"low\">Baixa (Texto simples)</option>\n                  <option value=\"medium\">Média (Texto + Imagens)</option>\n                  <option value=\"high\">Alta (Design complexo)</option>\n                </select>\n              </div>\n\n              {/* Notes */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Observações (Opcional)\n                </label>\n                <textarea\n                  value={options.notes}\n                  onChange={(e) => handleOptionChange('notes', e.target.value)}\n                  rows={3}\n                  className=\"w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                  placeholder=\"Instruções especiais para a impressão...\"\n                />\n              </div>\n            </div>\n          </div>\n\n          {/* Price Breakdown */}\n          <div className=\"bg-white rounded-lg shadow-lg p-6\">\n            <h2 className=\"text-xl font-semibold text-gray-900 mb-6\">\n              Resumo de Preços\n            </h2>\n\n            <div className=\"space-y-4\">\n              <div className=\"flex justify-between text-sm\">\n                <span className=\"text-gray-600\">Preço base ({options.format}):</span>\n                <span>{formatPrice(priceBreakdown.basePrice)}</span>\n              </div>\n\n              {priceBreakdown.paperCost > 0 && (\n                <div className=\"flex justify-between text-sm\">\n                  <span className=\"text-gray-600\">Papel premium:</span>\n                  <span>+{formatPrice(priceBreakdown.paperCost)}</span>\n                </div>\n              )}\n\n              {priceBreakdown.finishCost > 0 && (\n                <div className=\"flex justify-between text-sm\">\n                  <span className=\"text-gray-600\">Acabamento:</span>\n                  <span>+{formatPrice(priceBreakdown.finishCost)}</span>\n                </div>\n              )}\n\n              {priceBreakdown.complexityCost > 0 && (\n                <div className=\"flex justify-between text-sm\">\n                  <span className=\"text-gray-600\">Complexidade:</span>\n                  <span>+{formatPrice(priceBreakdown.complexityCost)}</span>\n                </div>\n              )}\n\n              {priceBreakdown.colorCost > 0 && (\n                <div className=\"flex justify-between text-sm\">\n                  <span className=\"text-gray-600\">Impressão a cores:</span>\n                  <span>+{formatPrice(priceBreakdown.colorCost)}</span>\n                </div>\n              )}\n\n              <div className=\"flex justify-between text-sm\">\n                <span className=\"text-gray-600\">Quantidade:</span>\n                <span>{options.copies} cópia(s)</span>\n              </div>\n\n              <hr className=\"my-4\" />\n\n              <div className=\"flex justify-between text-lg font-semibold\">\n                <span>Total:</span>\n                <span className=\"text-blue-600\">{formatPrice(priceBreakdown.total)}</span>\n              </div>\n            </div>\n\n            <div className=\"mt-6 p-4 bg-blue-50 rounded-lg\">\n              <h3 className=\"font-medium text-blue-900 mb-2\">Informações</h3>\n              <ul className=\"text-sm text-blue-800 space-y-1\">\n                <li>• Entrega grátis em Luanda</li>\n                <li>• Prazo: 2-5 dias úteis</li>\n                <li>• Garantia de qualidade</li>\n                <li>• Suporte técnico incluído</li>\n              </ul>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"flex justify-between mt-8\">\n          <Button\n            variant=\"outline\"\n            onClick={() => navigate('/upload')}\n          >\n            Voltar\n          </Button>\n\n          <Button\n            onClick={handleContinue}\n          >\n            Ver Resumo\n          </Button>\n        </div>\n      </div>\n    </Layout>\n  );\n};\n\nexport default OptionsForm;\n"], "mappings": "sIAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,OAASC,MAAM,CAAEC,MAAM,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAqB/C,KAAM,CAAAC,WAAqB,CAAGA,CAAA,GAAM,CAClC,KAAM,CAAAC,QAAQ,CAAGR,WAAW,CAAC,CAAC,CAC9B,KAAM,CAACS,YAAY,CAAEC,eAAe,CAAC,CAAGZ,QAAQ,CAAM,IAAI,CAAC,CAC3D,KAAM,CAACa,OAAO,CAAEC,UAAU,CAAC,CAAGd,QAAQ,CAAe,CACnDe,MAAM,CAAE,IAAI,CACZC,SAAS,CAAE,UAAU,CACrBC,MAAM,CAAE,MAAM,CACdC,MAAM,CAAE,CAAC,CACTC,QAAQ,CAAE,KAAK,CACfC,UAAU,CAAE,KAAK,CACjBC,KAAK,CAAE,EACT,CAAC,CAAC,CACF,KAAM,CAACC,cAAc,CAAEC,iBAAiB,CAAC,CAAGvB,QAAQ,CAAiB,CACnEwB,SAAS,CAAE,CAAC,CACZC,SAAS,CAAE,CAAC,CACZC,UAAU,CAAE,CAAC,CACbC,cAAc,CAAE,CAAC,CACjBC,SAAS,CAAE,CAAC,CACZC,KAAK,CAAE,CACT,CAAC,CAAC,CAEF5B,SAAS,CAAC,IAAM,CACd;AACA,KAAM,CAAA6B,QAAQ,CAAGC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC,CACrD,GAAIF,QAAQ,CAAE,CACZlB,eAAe,CAACqB,IAAI,CAACC,KAAK,CAACJ,QAAQ,CAAC,CAAC,CACvC,CAAC,IAAM,CACL;AACApB,QAAQ,CAAC,SAAS,CAAC,CACrB,CACF,CAAC,CAAE,CAACA,QAAQ,CAAC,CAAC,CAEd,KAAM,CAAAyB,cAAc,CAAGpC,KAAK,CAACqC,WAAW,CAAC,IAAM,CAC7C,GAAI,CAAAZ,SAAS,CAAG,GAAG,CAAE;AAErB;AACA,KAAM,CAAAa,YAAuC,CAAG,CAC9C,IAAI,CAAE,GAAG,CACT,IAAI,CAAE,GAAG,CACT,IAAI,CAAE,GAAG,CACT,QAAQ,CAAE,GACZ,CAAC,CAED;AACA,KAAM,CAAAC,WAAsC,CAAG,CAC7C,UAAU,CAAE,GAAG,CACf,SAAS,CAAE,GAAG,CACd,OAAO,CAAE,GAAG,CACZ,WAAW,CAAE,GACf,CAAC,CAED;AACA,KAAM,CAAAC,YAAuC,CAAG,CAC9C,MAAM,CAAE,GAAG,CACX,QAAQ,CAAE,GAAG,CACb,OAAO,CAAE,GAAG,CACZ,WAAW,CAAE,GACf,CAAC,CAED;AACA,KAAM,CAAAC,gBAA2C,CAAG,CAClD,KAAK,CAAE,GAAG,CACV,QAAQ,CAAE,GAAG,CACb,MAAM,CAAE,GACV,CAAC,CAED,KAAM,CAAAC,gBAAgB,CAAGJ,YAAY,CAACxB,OAAO,CAACE,MAAM,CAAC,EAAI,GAAG,CAC5D,KAAM,CAAA2B,eAAe,CAAGJ,WAAW,CAACzB,OAAO,CAACG,SAAS,CAAC,EAAI,GAAG,CAC7D,KAAM,CAAA2B,gBAAgB,CAAGJ,YAAY,CAAC1B,OAAO,CAACI,MAAM,CAAC,EAAI,GAAG,CAC5D,KAAM,CAAA2B,oBAAoB,CAAGJ,gBAAgB,CAAC3B,OAAO,CAACO,UAAU,CAAC,EAAI,GAAG,CACxE,KAAM,CAAAyB,eAAe,CAAGhC,OAAO,CAACM,QAAQ,CAAG,GAAG,CAAG,GAAG,CAEpD,KAAM,CAAAM,SAAS,CAAGD,SAAS,CAAGiB,gBAAgB,EAAIC,eAAe,CAAG,CAAC,CAAC,CACtE,KAAM,CAAAhB,UAAU,CAAGF,SAAS,CAAGiB,gBAAgB,EAAIE,gBAAgB,CAAG,CAAC,CAAC,CACxE,KAAM,CAAAhB,cAAc,CAAGH,SAAS,CAAGiB,gBAAgB,EAAIG,oBAAoB,CAAG,CAAC,CAAC,CAChF,KAAM,CAAAhB,SAAS,CAAGf,OAAO,CAACM,QAAQ,CAAGK,SAAS,CAAGiB,gBAAgB,CAAG,GAAG,CAAG,CAAC,CAE3E,KAAM,CAAAK,SAAS,CAAGtB,SAAS,CAAGiB,gBAAgB,CAAGC,eAAe,CAAGC,gBAAgB,CAAGC,oBAAoB,CAAGC,eAAe,CAC5H,KAAM,CAAAhB,KAAK,CAAGiB,SAAS,CAAGjC,OAAO,CAACK,MAAM,CAExCK,iBAAiB,CAAC,CAChBC,SAAS,CAAEA,SAAS,CAAGiB,gBAAgB,CACvChB,SAAS,CACTC,UAAU,CACVC,cAAc,CACdC,SAAS,CACTC,KACF,CAAC,CAAC,CACJ,CAAC,CAAE,CAAChB,OAAO,CAAC,CAAC,CAEbZ,SAAS,CAAC,IAAM,CACdkC,cAAc,CAAC,CAAC,CAClB,CAAC,CAAE,CAACA,cAAc,CAAC,CAAC,CAEpB,KAAM,CAAAY,kBAAkB,CAAGA,CAACC,KAAyB,CAAEC,KAAU,GAAK,CACpEnC,UAAU,CAACoC,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAE,CAACF,KAAK,EAAGC,KAAK,EAAG,CAAC,CACnD,CAAC,CAED,KAAM,CAAAG,cAAc,CAAGA,CAAA,GAAM,CAC3B;AACArB,YAAY,CAACsB,OAAO,CAAC,cAAc,CAAEpB,IAAI,CAACqB,SAAS,CAACzC,OAAO,CAAC,CAAC,CAC7DkB,YAAY,CAACsB,OAAO,CAAC,gBAAgB,CAAEpB,IAAI,CAACqB,SAAS,CAAChC,cAAc,CAAC,CAAC,CACtEZ,QAAQ,CAAC,UAAU,CAAC,CACtB,CAAC,CAED,KAAM,CAAA6C,WAAW,CAAIC,KAAa,EAAK,CACrC,SAAAC,MAAA,CAAUD,KAAK,CAACE,OAAO,CAAC,CAAC,CAAC,SAC5B,CAAC,CAED,GAAI,CAAC/C,YAAY,CAAE,CACjB,mBAAOL,IAAA,QAAAqD,QAAA,CAAK,eAAa,CAAK,CAAC,CACjC,CAEA,mBACErD,IAAA,CAACH,MAAM,EAAAwD,QAAA,cACLnD,KAAA,QAAKoD,SAAS,CAAC,wBAAwB,CAAAD,QAAA,eACrCnD,KAAA,QAAKoD,SAAS,CAAC,kBAAkB,CAAAD,QAAA,eAC/BrD,IAAA,OAAIsD,SAAS,CAAC,uCAAuC,CAAAD,QAAA,CAAC,8BAEtD,CAAI,CAAC,cACLnD,KAAA,MAAGoD,SAAS,CAAC,uBAAuB,CAAAD,QAAA,EAAC,kDACO,CAAChD,YAAY,CAACkD,IAAI,EAC3D,CAAC,EACD,CAAC,cAENrD,KAAA,QAAKoD,SAAS,CAAC,uCAAuC,CAAAD,QAAA,eAEpDnD,KAAA,QAAKoD,SAAS,CAAC,mCAAmC,CAAAD,QAAA,eAChDrD,IAAA,OAAIsD,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,qBAEzD,CAAI,CAAC,cAELnD,KAAA,QAAKoD,SAAS,CAAC,WAAW,CAAAD,QAAA,eAExBnD,KAAA,QAAAmD,QAAA,eACErD,IAAA,UAAOsD,SAAS,CAAC,8CAA8C,CAAAD,QAAA,CAAC,kBAEhE,CAAO,CAAC,cACRnD,KAAA,WACEyC,KAAK,CAAEpC,OAAO,CAACE,MAAO,CACtB+C,QAAQ,CAAGC,CAAC,EAAKhB,kBAAkB,CAAC,QAAQ,CAAEgB,CAAC,CAACC,MAAM,CAACf,KAAK,CAAE,CAC9DW,SAAS,CAAC,qGAAqG,CAAAD,QAAA,eAE/GrD,IAAA,WAAQ2C,KAAK,CAAC,IAAI,CAAAU,QAAA,CAAC,sBAAiB,CAAQ,CAAC,cAC7CrD,IAAA,WAAQ2C,KAAK,CAAC,IAAI,CAAAU,QAAA,CAAC,sBAAiB,CAAQ,CAAC,cAC7CrD,IAAA,WAAQ2C,KAAK,CAAC,IAAI,CAAAU,QAAA,CAAC,sBAAiB,CAAQ,CAAC,cAC7CrD,IAAA,WAAQ2C,KAAK,CAAC,QAAQ,CAAAU,QAAA,CAAC,0BAAqB,CAAQ,CAAC,EAC/C,CAAC,EACN,CAAC,cAGNnD,KAAA,QAAAmD,QAAA,eACErD,IAAA,UAAOsD,SAAS,CAAC,8CAA8C,CAAAD,QAAA,CAAC,eAEhE,CAAO,CAAC,cACRnD,KAAA,WACEyC,KAAK,CAAEpC,OAAO,CAACG,SAAU,CACzB8C,QAAQ,CAAGC,CAAC,EAAKhB,kBAAkB,CAAC,WAAW,CAAEgB,CAAC,CAACC,MAAM,CAACf,KAAK,CAAE,CACjEW,SAAS,CAAC,qGAAqG,CAAAD,QAAA,eAE/GrD,IAAA,WAAQ2C,KAAK,CAAC,UAAU,CAAAU,QAAA,CAAC,4BAAuB,CAAQ,CAAC,cACzDrD,IAAA,WAAQ2C,KAAK,CAAC,SAAS,CAAAU,QAAA,CAAC,2BAAsB,CAAQ,CAAC,cACvDrD,IAAA,WAAQ2C,KAAK,CAAC,OAAO,CAAAU,QAAA,CAAC,mCAA2B,CAAQ,CAAC,cAC1DrD,IAAA,WAAQ2C,KAAK,CAAC,WAAW,CAAAU,QAAA,CAAC,wBAAmB,CAAQ,CAAC,EAChD,CAAC,EACN,CAAC,cAGNnD,KAAA,QAAAmD,QAAA,eACErD,IAAA,UAAOsD,SAAS,CAAC,8CAA8C,CAAAD,QAAA,CAAC,YAEhE,CAAO,CAAC,cACRnD,KAAA,WACEyC,KAAK,CAAEpC,OAAO,CAACI,MAAO,CACtB6C,QAAQ,CAAGC,CAAC,EAAKhB,kBAAkB,CAAC,QAAQ,CAAEgB,CAAC,CAACC,MAAM,CAACf,KAAK,CAAE,CAC9DW,SAAS,CAAC,qGAAqG,CAAAD,QAAA,eAE/GrD,IAAA,WAAQ2C,KAAK,CAAC,MAAM,CAAAU,QAAA,CAAC,gBAAc,CAAQ,CAAC,cAC5CrD,IAAA,WAAQ2C,KAAK,CAAC,QAAQ,CAAAU,QAAA,CAAC,WAAS,CAAQ,CAAC,cACzCrD,IAAA,WAAQ2C,KAAK,CAAC,OAAO,CAAAU,QAAA,CAAC,OAAK,CAAQ,CAAC,cACpCrD,IAAA,WAAQ2C,KAAK,CAAC,WAAW,CAAAU,QAAA,CAAC,cAAY,CAAQ,CAAC,EACzC,CAAC,EACN,CAAC,cAGNnD,KAAA,QAAAmD,QAAA,eACErD,IAAA,UAAOsD,SAAS,CAAC,8CAA8C,CAAAD,QAAA,CAAC,wBAEhE,CAAO,CAAC,cACRrD,IAAA,UACE2D,IAAI,CAAC,QAAQ,CACbC,GAAG,CAAC,GAAG,CACPC,GAAG,CAAC,MAAM,CACVlB,KAAK,CAAEpC,OAAO,CAACK,MAAO,CACtB4C,QAAQ,CAAGC,CAAC,EAAKhB,kBAAkB,CAAC,QAAQ,CAAEqB,QAAQ,CAACL,CAAC,CAACC,MAAM,CAACf,KAAK,CAAC,EAAI,CAAC,CAAE,CAC7EW,SAAS,CAAC,qGAAqG,CAChH,CAAC,EACC,CAAC,cAGNtD,IAAA,QAAAqD,QAAA,cACEnD,KAAA,UAAOoD,SAAS,CAAC,6BAA6B,CAAAD,QAAA,eAC5CrD,IAAA,UACE2D,IAAI,CAAC,UAAU,CACfI,OAAO,CAAExD,OAAO,CAACM,QAAS,CAC1B2C,QAAQ,CAAGC,CAAC,EAAKhB,kBAAkB,CAAC,UAAU,CAAEgB,CAAC,CAACC,MAAM,CAACK,OAAO,CAAE,CAClET,SAAS,CAAC,mEAAmE,CAC9E,CAAC,cACFtD,IAAA,SAAMsD,SAAS,CAAC,mCAAmC,CAAAD,QAAA,CAAC,sBAEpD,CAAM,CAAC,EACF,CAAC,CACL,CAAC,cAGNnD,KAAA,QAAAmD,QAAA,eACErD,IAAA,UAAOsD,SAAS,CAAC,8CAA8C,CAAAD,QAAA,CAAC,2BAEhE,CAAO,CAAC,cACRnD,KAAA,WACEyC,KAAK,CAAEpC,OAAO,CAACO,UAAW,CAC1B0C,QAAQ,CAAGC,CAAC,EAAKhB,kBAAkB,CAAC,YAAY,CAAEgB,CAAC,CAACC,MAAM,CAACf,KAAkC,CAAE,CAC/FW,SAAS,CAAC,qGAAqG,CAAAD,QAAA,eAE/GrD,IAAA,WAAQ2C,KAAK,CAAC,KAAK,CAAAU,QAAA,CAAC,uBAAqB,CAAQ,CAAC,cAClDrD,IAAA,WAAQ2C,KAAK,CAAC,QAAQ,CAAAU,QAAA,CAAC,4BAAuB,CAAQ,CAAC,cACvDrD,IAAA,WAAQ2C,KAAK,CAAC,MAAM,CAAAU,QAAA,CAAC,wBAAsB,CAAQ,CAAC,EAC9C,CAAC,EACN,CAAC,cAGNnD,KAAA,QAAAmD,QAAA,eACErD,IAAA,UAAOsD,SAAS,CAAC,8CAA8C,CAAAD,QAAA,CAAC,8BAEhE,CAAO,CAAC,cACRrD,IAAA,aACE2C,KAAK,CAAEpC,OAAO,CAACQ,KAAM,CACrByC,QAAQ,CAAGC,CAAC,EAAKhB,kBAAkB,CAAC,OAAO,CAAEgB,CAAC,CAACC,MAAM,CAACf,KAAK,CAAE,CAC7DqB,IAAI,CAAE,CAAE,CACRV,SAAS,CAAC,qGAAqG,CAC/GW,WAAW,CAAC,mDAA0C,CACvD,CAAC,EACC,CAAC,EACH,CAAC,EACH,CAAC,cAGN/D,KAAA,QAAKoD,SAAS,CAAC,mCAAmC,CAAAD,QAAA,eAChDrD,IAAA,OAAIsD,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,qBAEzD,CAAI,CAAC,cAELnD,KAAA,QAAKoD,SAAS,CAAC,WAAW,CAAAD,QAAA,eACxBnD,KAAA,QAAKoD,SAAS,CAAC,8BAA8B,CAAAD,QAAA,eAC3CnD,KAAA,SAAMoD,SAAS,CAAC,eAAe,CAAAD,QAAA,EAAC,iBAAY,CAAC9C,OAAO,CAACE,MAAM,CAAC,IAAE,EAAM,CAAC,cACrET,IAAA,SAAAqD,QAAA,CAAOJ,WAAW,CAACjC,cAAc,CAACE,SAAS,CAAC,CAAO,CAAC,EACjD,CAAC,CAELF,cAAc,CAACG,SAAS,CAAG,CAAC,eAC3BjB,KAAA,QAAKoD,SAAS,CAAC,8BAA8B,CAAAD,QAAA,eAC3CrD,IAAA,SAAMsD,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,gBAAc,CAAM,CAAC,cACrDnD,KAAA,SAAAmD,QAAA,EAAM,GAAC,CAACJ,WAAW,CAACjC,cAAc,CAACG,SAAS,CAAC,EAAO,CAAC,EAClD,CACN,CAEAH,cAAc,CAACI,UAAU,CAAG,CAAC,eAC5BlB,KAAA,QAAKoD,SAAS,CAAC,8BAA8B,CAAAD,QAAA,eAC3CrD,IAAA,SAAMsD,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,aAAW,CAAM,CAAC,cAClDnD,KAAA,SAAAmD,QAAA,EAAM,GAAC,CAACJ,WAAW,CAACjC,cAAc,CAACI,UAAU,CAAC,EAAO,CAAC,EACnD,CACN,CAEAJ,cAAc,CAACK,cAAc,CAAG,CAAC,eAChCnB,KAAA,QAAKoD,SAAS,CAAC,8BAA8B,CAAAD,QAAA,eAC3CrD,IAAA,SAAMsD,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,eAAa,CAAM,CAAC,cACpDnD,KAAA,SAAAmD,QAAA,EAAM,GAAC,CAACJ,WAAW,CAACjC,cAAc,CAACK,cAAc,CAAC,EAAO,CAAC,EACvD,CACN,CAEAL,cAAc,CAACM,SAAS,CAAG,CAAC,eAC3BpB,KAAA,QAAKoD,SAAS,CAAC,8BAA8B,CAAAD,QAAA,eAC3CrD,IAAA,SAAMsD,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,uBAAkB,CAAM,CAAC,cACzDnD,KAAA,SAAAmD,QAAA,EAAM,GAAC,CAACJ,WAAW,CAACjC,cAAc,CAACM,SAAS,CAAC,EAAO,CAAC,EAClD,CACN,cAEDpB,KAAA,QAAKoD,SAAS,CAAC,8BAA8B,CAAAD,QAAA,eAC3CrD,IAAA,SAAMsD,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,aAAW,CAAM,CAAC,cAClDnD,KAAA,SAAAmD,QAAA,EAAO9C,OAAO,CAACK,MAAM,CAAC,cAAS,EAAM,CAAC,EACnC,CAAC,cAENZ,IAAA,OAAIsD,SAAS,CAAC,MAAM,CAAE,CAAC,cAEvBpD,KAAA,QAAKoD,SAAS,CAAC,4CAA4C,CAAAD,QAAA,eACzDrD,IAAA,SAAAqD,QAAA,CAAM,QAAM,CAAM,CAAC,cACnBrD,IAAA,SAAMsD,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAEJ,WAAW,CAACjC,cAAc,CAACO,KAAK,CAAC,CAAO,CAAC,EACvE,CAAC,EACH,CAAC,cAENrB,KAAA,QAAKoD,SAAS,CAAC,gCAAgC,CAAAD,QAAA,eAC7CrD,IAAA,OAAIsD,SAAS,CAAC,gCAAgC,CAAAD,QAAA,CAAC,mBAAW,CAAI,CAAC,cAC/DnD,KAAA,OAAIoD,SAAS,CAAC,iCAAiC,CAAAD,QAAA,eAC7CrD,IAAA,OAAAqD,QAAA,CAAI,oCAA0B,CAAI,CAAC,cACnCrD,IAAA,OAAAqD,QAAA,CAAI,iCAAuB,CAAI,CAAC,cAChCrD,IAAA,OAAAqD,QAAA,CAAI,8BAAuB,CAAI,CAAC,cAChCrD,IAAA,OAAAqD,QAAA,CAAI,uCAA0B,CAAI,CAAC,EACjC,CAAC,EACF,CAAC,EACH,CAAC,EACH,CAAC,cAENnD,KAAA,QAAKoD,SAAS,CAAC,2BAA2B,CAAAD,QAAA,eACxCrD,IAAA,CAACF,MAAM,EACLoE,OAAO,CAAC,SAAS,CACjBC,OAAO,CAAEA,CAAA,GAAM/D,QAAQ,CAAC,SAAS,CAAE,CAAAiD,QAAA,CACpC,QAED,CAAQ,CAAC,cAETrD,IAAA,CAACF,MAAM,EACLqE,OAAO,CAAErB,cAAe,CAAAO,QAAA,CACzB,YAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CACA,CAAC,CAEb,CAAC,CAED,cAAe,CAAAlD,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}