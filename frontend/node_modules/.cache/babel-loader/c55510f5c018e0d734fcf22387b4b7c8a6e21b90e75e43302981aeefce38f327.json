{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/Button.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Button = ({\n  children,\n  onClick,\n  type = 'button',\n  variant = 'primary',\n  size = 'md',\n  disabled = false,\n  className = ''\n}) => {\n  const baseClasses = 'inline-flex items-center justify-center font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors';\n  const variantClasses = {\n    primary: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500',\n    secondary: 'bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500',\n    outline: 'border border-gray-300 text-gray-700 bg-white hover:bg-gray-50 focus:ring-blue-500'\n  };\n  const sizeClasses = {\n    sm: 'px-3 py-2 text-sm',\n    md: 'px-4 py-2 text-sm',\n    lg: 'px-6 py-3 text-base'\n  };\n  const disabledClasses = disabled ? 'opacity-50 cursor-not-allowed' : '';\n  const classes = `${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${disabledClasses} ${className}`;\n  return /*#__PURE__*/_jsxDEV(\"button\", {\n    type: type,\n    onClick: onClick,\n    disabled: disabled,\n    className: classes,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 41,\n    columnNumber: 5\n  }, this);\n};\n_c = Button;\nexport default Button;\nvar _c;\n$RefreshReg$(_c, \"Button\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "children", "onClick", "type", "variant", "size", "disabled", "className", "baseClasses", "variantClasses", "primary", "secondary", "outline", "sizeClasses", "sm", "md", "lg", "disabledClasses", "classes", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/Button.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface ButtonProps {\n  children: React.ReactNode;\n  onClick?: () => void;\n  type?: 'button' | 'submit' | 'reset';\n  variant?: 'primary' | 'secondary' | 'outline';\n  size?: 'sm' | 'md' | 'lg';\n  disabled?: boolean;\n  className?: string;\n}\n\nconst Button: React.FC<ButtonProps> = ({\n  children,\n  onClick,\n  type = 'button',\n  variant = 'primary',\n  size = 'md',\n  disabled = false,\n  className = '',\n}) => {\n  const baseClasses = 'inline-flex items-center justify-center font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors';\n  \n  const variantClasses = {\n    primary: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500',\n    secondary: 'bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500',\n    outline: 'border border-gray-300 text-gray-700 bg-white hover:bg-gray-50 focus:ring-blue-500',\n  };\n  \n  const sizeClasses = {\n    sm: 'px-3 py-2 text-sm',\n    md: 'px-4 py-2 text-sm',\n    lg: 'px-6 py-3 text-base',\n  };\n  \n  const disabledClasses = disabled ? 'opacity-50 cursor-not-allowed' : '';\n  \n  const classes = `${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${disabledClasses} ${className}`;\n  \n  return (\n    <button\n      type={type}\n      onClick={onClick}\n      disabled={disabled}\n      className={classes}\n    >\n      {children}\n    </button>\n  );\n};\n\nexport default Button;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAY1B,MAAMC,MAA6B,GAAGA,CAAC;EACrCC,QAAQ;EACRC,OAAO;EACPC,IAAI,GAAG,QAAQ;EACfC,OAAO,GAAG,SAAS;EACnBC,IAAI,GAAG,IAAI;EACXC,QAAQ,GAAG,KAAK;EAChBC,SAAS,GAAG;AACd,CAAC,KAAK;EACJ,MAAMC,WAAW,GAAG,sIAAsI;EAE1J,MAAMC,cAAc,GAAG;IACrBC,OAAO,EAAE,8DAA8D;IACvEC,SAAS,EAAE,8DAA8D;IACzEC,OAAO,EAAE;EACX,CAAC;EAED,MAAMC,WAAW,GAAG;IAClBC,EAAE,EAAE,mBAAmB;IACvBC,EAAE,EAAE,mBAAmB;IACvBC,EAAE,EAAE;EACN,CAAC;EAED,MAAMC,eAAe,GAAGX,QAAQ,GAAG,+BAA+B,GAAG,EAAE;EAEvE,MAAMY,OAAO,GAAG,GAAGV,WAAW,IAAIC,cAAc,CAACL,OAAO,CAAC,IAAIS,WAAW,CAACR,IAAI,CAAC,IAAIY,eAAe,IAAIV,SAAS,EAAE;EAEhH,oBACER,OAAA;IACEI,IAAI,EAAEA,IAAK;IACXD,OAAO,EAAEA,OAAQ;IACjBI,QAAQ,EAAEA,QAAS;IACnBC,SAAS,EAAEW,OAAQ;IAAAjB,QAAA,EAElBA;EAAQ;IAAAkB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEb,CAAC;AAACC,EAAA,GArCIvB,MAA6B;AAuCnC,eAAeA,MAAM;AAAC,IAAAuB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}