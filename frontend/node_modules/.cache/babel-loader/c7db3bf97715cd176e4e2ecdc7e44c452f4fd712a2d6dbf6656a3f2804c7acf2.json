{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/landing/HowItWorksSection.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Step = ({\n  number,\n  title,\n  description,\n  icon,\n  color\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"relative text-center group\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: `w-20 h-20 ${color} rounded-full flex items-center justify-center text-white font-black text-2xl mx-auto mb-6 shadow-lg group-hover:scale-110 transition-transform duration-300`,\n      children: number\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-6xl mb-4 group-hover:animate-bounce-gentle\",\n      children: icon\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n      className: \"text-xl font-bold text-weprint-black mb-3\",\n      children: title\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"text-gray-600 leading-relaxed\",\n      children: description\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 13,\n    columnNumber: 5\n  }, this);\n};\n_c = Step;\nconst HowItWorksSection = () => {\n  const steps = [{\n    number: \"1\",\n    title: \"Faça Upload\",\n    description: \"Carregue seus documentos, fotos ou arquivos diretamente no nosso site. Suportamos PDF, Word, Excel, PowerPoint e imagens.\",\n    icon: \"📤\",\n    color: \"bg-weprint-cyan\"\n  }, {\n    number: \"2\",\n    title: \"Configure\",\n    description: \"Escolha o tipo de papel, tamanho, cores, acabamento e quantidade. Veja o preço em tempo real enquanto configura.\",\n    icon: \"⚙️\",\n    color: \"bg-weprint-magenta\"\n  }, {\n    number: \"3\",\n    title: \"Pague\",\n    description: \"Pague online com Multicaixa Express ou escolha pagamento na entrega. Processo seguro e rápido.\",\n    icon: \"💳\",\n    color: \"bg-weprint-yellow\"\n  }, {\n    number: \"4\",\n    title: \"Receba\",\n    description: \"Entregamos na sua localização em Luanda ou retire na nossa loja. Acompanhe o status do seu pedido em tempo real.\",\n    icon: \"🚚\",\n    color: \"bg-weprint-black\"\n  }];\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"py-20 bg-white\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-6 lg:px-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-16\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-4xl md:text-5xl font-black text-weprint-black mb-6\",\n          children: [\"Como \", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-weprint-cyan\",\n            children: \"Funciona\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 18\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n          children: \"Processo simples e r\\xE1pido para transformar seus arquivos em impress\\xF5es profissionais. Em apenas 4 passos, voc\\xEA tem suas impress\\xF5es prontas.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-24 h-1 bg-weprint-gradient mx-auto mt-6 rounded-full\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12 relative\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hidden lg:block absolute top-10 left-1/4 right-1/4 h-0.5 bg-gradient-to-r from-weprint-cyan via-weprint-magenta via-weprint-yellow to-weprint-black opacity-30\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 11\n        }, this), steps.map((step, index) => /*#__PURE__*/_jsxDEV(Step, {\n          ...step\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 13\n        }, this))]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-16 text-center\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-weprint-gradient-subtle rounded-2xl p-8 max-w-2xl mx-auto\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-2xl font-bold text-weprint-black mb-4\",\n            children: \"\\u23F1\\uFE0F Tempo Total: 5-10 minutos\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 mb-6\",\n            children: \"Do upload \\xE0 confirma\\xE7\\xE3o do pedido, todo o processo \\xE9 r\\xE1pido e intuitivo. Suas impress\\xF5es ficam prontas em 24-48 horas.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-center gap-8 text-sm text-gray-600\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-3 h-3 bg-weprint-cyan rounded-full mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 100,\n                columnNumber: 17\n              }, this), \"Upload: 1-2 min\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 99,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-3 h-3 bg-weprint-magenta rounded-full mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 104,\n                columnNumber: 17\n              }, this), \"Configura\\xE7\\xE3o: 2-3 min\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 103,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-3 h-3 bg-weprint-yellow rounded-full mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 108,\n                columnNumber: 17\n              }, this), \"Pagamento: 1-2 min\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 107,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mt-12\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => window.location.href = '/upload',\n          className: \"bg-weprint-gradient text-white font-bold text-lg px-12 py-4 rounded-full shadow-2xl hover:shadow-3xl transform hover:scale-105 transition-all duration-300\",\n          children: \"Come\\xE7ar Agora \\u2192\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 64,\n    columnNumber: 5\n  }, this);\n};\n_c2 = HowItWorksSection;\nexport default HowItWorksSection;\nvar _c, _c2;\n$RefreshReg$(_c, \"Step\");\n$RefreshReg$(_c2, \"HowItWorksSection\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Step", "number", "title", "description", "icon", "color", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "HowItWorksSection", "steps", "map", "step", "index", "onClick", "window", "location", "href", "_c2", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/landing/HowItWorksSection.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface StepProps {\n  number: string;\n  title: string;\n  description: string;\n  icon: string;\n  color: string;\n}\n\nconst Step: React.FC<StepProps> = ({ number, title, description, icon, color }) => {\n  return (\n    <div className=\"relative text-center group\">\n      {/* Step Number */}\n      <div className={`w-20 h-20 ${color} rounded-full flex items-center justify-center text-white font-black text-2xl mx-auto mb-6 shadow-lg group-hover:scale-110 transition-transform duration-300`}>\n        {number}\n      </div>\n      \n      {/* Icon */}\n      <div className=\"text-6xl mb-4 group-hover:animate-bounce-gentle\">\n        {icon}\n      </div>\n      \n      {/* Content */}\n      <h3 className=\"text-xl font-bold text-weprint-black mb-3\">{title}</h3>\n      <p className=\"text-gray-600 leading-relaxed\">{description}</p>\n    </div>\n  );\n};\n\nconst HowItWorksSection: React.FC = () => {\n  const steps = [\n    {\n      number: \"1\",\n      title: \"Faça Upload\",\n      description: \"Carregue seus documentos, fotos ou arquivos diretamente no nosso site. Suportamos PDF, Word, Excel, PowerPoint e imagens.\",\n      icon: \"📤\",\n      color: \"bg-weprint-cyan\"\n    },\n    {\n      number: \"2\",\n      title: \"Configure\",\n      description: \"Escolha o tipo de papel, tamanho, cores, acabamento e quantidade. Veja o preço em tempo real enquanto configura.\",\n      icon: \"⚙️\",\n      color: \"bg-weprint-magenta\"\n    },\n    {\n      number: \"3\",\n      title: \"Pague\",\n      description: \"Pague online com Multicaixa Express ou escolha pagamento na entrega. Processo seguro e rápido.\",\n      icon: \"💳\",\n      color: \"bg-weprint-yellow\"\n    },\n    {\n      number: \"4\",\n      title: \"Receba\",\n      description: \"Entregamos na sua localização em Luanda ou retire na nossa loja. Acompanhe o status do seu pedido em tempo real.\",\n      icon: \"🚚\",\n      color: \"bg-weprint-black\"\n    }\n  ];\n\n  return (\n    <section className=\"py-20 bg-white\">\n      <div className=\"container mx-auto px-6 lg:px-8\">\n        {/* Section Header */}\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-4xl md:text-5xl font-black text-weprint-black mb-6\">\n            Como <span className=\"text-weprint-cyan\">Funciona</span>\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            Processo simples e rápido para transformar seus arquivos em impressões profissionais. \n            Em apenas 4 passos, você tem suas impressões prontas.\n          </p>\n          <div className=\"w-24 h-1 bg-weprint-gradient mx-auto mt-6 rounded-full\"></div>\n        </div>\n\n        {/* Steps Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12 relative\">\n          {/* Connection Lines (hidden on mobile) */}\n          <div className=\"hidden lg:block absolute top-10 left-1/4 right-1/4 h-0.5 bg-gradient-to-r from-weprint-cyan via-weprint-magenta via-weprint-yellow to-weprint-black opacity-30\"></div>\n          \n          {steps.map((step, index) => (\n            <Step key={index} {...step} />\n          ))}\n        </div>\n\n        {/* Time Estimate */}\n        <div className=\"mt-16 text-center\">\n          <div className=\"bg-weprint-gradient-subtle rounded-2xl p-8 max-w-2xl mx-auto\">\n            <h3 className=\"text-2xl font-bold text-weprint-black mb-4\">\n              ⏱️ Tempo Total: 5-10 minutos\n            </h3>\n            <p className=\"text-gray-600 mb-6\">\n              Do upload à confirmação do pedido, todo o processo é rápido e intuitivo. \n              Suas impressões ficam prontas em 24-48 horas.\n            </p>\n            <div className=\"flex justify-center gap-8 text-sm text-gray-600\">\n              <div className=\"flex items-center\">\n                <div className=\"w-3 h-3 bg-weprint-cyan rounded-full mr-2\"></div>\n                Upload: 1-2 min\n              </div>\n              <div className=\"flex items-center\">\n                <div className=\"w-3 h-3 bg-weprint-magenta rounded-full mr-2\"></div>\n                Configuração: 2-3 min\n              </div>\n              <div className=\"flex items-center\">\n                <div className=\"w-3 h-3 bg-weprint-yellow rounded-full mr-2\"></div>\n                Pagamento: 1-2 min\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* CTA */}\n        <div className=\"text-center mt-12\">\n          <button \n            onClick={() => window.location.href = '/upload'}\n            className=\"bg-weprint-gradient text-white font-bold text-lg px-12 py-4 rounded-full shadow-2xl hover:shadow-3xl transform hover:scale-105 transition-all duration-300\"\n          >\n            Começar Agora →\n          </button>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default HowItWorksSection;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAU1B,MAAMC,IAAyB,GAAGA,CAAC;EAAEC,MAAM;EAAEC,KAAK;EAAEC,WAAW;EAAEC,IAAI;EAAEC;AAAM,CAAC,KAAK;EACjF,oBACEN,OAAA;IAAKO,SAAS,EAAC,4BAA4B;IAAAC,QAAA,gBAEzCR,OAAA;MAAKO,SAAS,EAAE,aAAaD,KAAK,8JAA+J;MAAAE,QAAA,EAC9LN;IAAM;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGNZ,OAAA;MAAKO,SAAS,EAAC,iDAAiD;MAAAC,QAAA,EAC7DH;IAAI;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGNZ,OAAA;MAAIO,SAAS,EAAC,2CAA2C;MAAAC,QAAA,EAAEL;IAAK;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eACtEZ,OAAA;MAAGO,SAAS,EAAC,+BAA+B;MAAAC,QAAA,EAAEJ;IAAW;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC3D,CAAC;AAEV,CAAC;AAACC,EAAA,GAlBIZ,IAAyB;AAoB/B,MAAMa,iBAA2B,GAAGA,CAAA,KAAM;EACxC,MAAMC,KAAK,GAAG,CACZ;IACEb,MAAM,EAAE,GAAG;IACXC,KAAK,EAAE,aAAa;IACpBC,WAAW,EAAE,2HAA2H;IACxIC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,MAAM,EAAE,GAAG;IACXC,KAAK,EAAE,WAAW;IAClBC,WAAW,EAAE,kHAAkH;IAC/HC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,MAAM,EAAE,GAAG;IACXC,KAAK,EAAE,OAAO;IACdC,WAAW,EAAE,gGAAgG;IAC7GC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,MAAM,EAAE,GAAG;IACXC,KAAK,EAAE,QAAQ;IACfC,WAAW,EAAE,kHAAkH;IAC/HC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE;EACT,CAAC,CACF;EAED,oBACEN,OAAA;IAASO,SAAS,EAAC,gBAAgB;IAAAC,QAAA,eACjCR,OAAA;MAAKO,SAAS,EAAC,gCAAgC;MAAAC,QAAA,gBAE7CR,OAAA;QAAKO,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCR,OAAA;UAAIO,SAAS,EAAC,yDAAyD;UAAAC,QAAA,GAAC,OACjE,eAAAR,OAAA;YAAMO,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CAAC,eACLZ,OAAA;UAAGO,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EAAC;QAGvD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJZ,OAAA;UAAKO,SAAS,EAAC;QAAwD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3E,CAAC,eAGNZ,OAAA;QAAKO,SAAS,EAAC,gEAAgE;QAAAC,QAAA,gBAE7ER,OAAA;UAAKO,SAAS,EAAC;QAAgK;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EAErLG,KAAK,CAACC,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACrBlB,OAAA,CAACC,IAAI;UAAA,GAAiBgB;QAAI,GAAfC,KAAK;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAC9B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNZ,OAAA;QAAKO,SAAS,EAAC,mBAAmB;QAAAC,QAAA,eAChCR,OAAA;UAAKO,SAAS,EAAC,8DAA8D;UAAAC,QAAA,gBAC3ER,OAAA;YAAIO,SAAS,EAAC,4CAA4C;YAAAC,QAAA,EAAC;UAE3D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLZ,OAAA;YAAGO,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAGlC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJZ,OAAA;YAAKO,SAAS,EAAC,iDAAiD;YAAAC,QAAA,gBAC9DR,OAAA;cAAKO,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCR,OAAA;gBAAKO,SAAS,EAAC;cAA2C;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,mBAEnE;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNZ,OAAA;cAAKO,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCR,OAAA;gBAAKO,SAAS,EAAC;cAA8C;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,+BAEtE;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNZ,OAAA;cAAKO,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCR,OAAA;gBAAKO,SAAS,EAAC;cAA6C;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,sBAErE;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNZ,OAAA;QAAKO,SAAS,EAAC,mBAAmB;QAAAC,QAAA,eAChCR,OAAA;UACEmB,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,SAAU;UAChDf,SAAS,EAAC,4JAA4J;UAAAC,QAAA,EACvK;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACW,GAAA,GAhGIT,iBAA2B;AAkGjC,eAAeA,iBAAiB;AAAC,IAAAD,EAAA,EAAAU,GAAA;AAAAC,YAAA,CAAAX,EAAA;AAAAW,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}