{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/AdminPanel.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { PaymentStatus } from '../components';\nimport { MulticaixaService } from '../services/multicaixa';\nimport { apiService } from '../services/api';\nimport { adminApiService } from '../services/adminApi';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminPanel = () => {\n  _s();\n  const [activeTab, setActiveTab] = useState('login');\n  const [stats, setStats] = useState({\n    totalRevenue: 0,\n    totalPayments: 0,\n    pendingPayments: 0,\n    completedPayments: 0,\n    failedPayments: 0\n  });\n  const [payments, setPayments] = useState([]);\n  const [orders, setOrders] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  const [adminUser, setAdminUser] = useState(null);\n  const [loginForm, setLoginForm] = useState({\n    email: '',\n    password: ''\n  });\n  const loadDashboardData = useCallback(async () => {\n    setLoading(true);\n    try {\n      // Load dashboard stats from admin API\n      const statsResponse = await adminApiService.getDashboardStats();\n      if (statsResponse.success && statsResponse.data) {\n        var _dashboardData$overvi, _dashboardData$overvi2, _dashboardData$orderS, _dashboardData$orderS2, _dashboardData$orderS3, _dashboardData$orderS4, _dashboardData$orderS5, _dashboardData$orderS6;\n        const dashboardData = statsResponse.data;\n        setStats({\n          totalRevenue: ((_dashboardData$overvi = dashboardData.overview) === null || _dashboardData$overvi === void 0 ? void 0 : _dashboardData$overvi.totalRevenue) || 0,\n          totalPayments: ((_dashboardData$overvi2 = dashboardData.overview) === null || _dashboardData$overvi2 === void 0 ? void 0 : _dashboardData$overvi2.totalOrders) || 0,\n          pendingPayments: ((_dashboardData$orderS = dashboardData.orderStats) === null || _dashboardData$orderS === void 0 ? void 0 : (_dashboardData$orderS2 = _dashboardData$orderS.byStatus) === null || _dashboardData$orderS2 === void 0 ? void 0 : _dashboardData$orderS2.pending) || 0,\n          completedPayments: ((_dashboardData$orderS3 = dashboardData.orderStats) === null || _dashboardData$orderS3 === void 0 ? void 0 : (_dashboardData$orderS4 = _dashboardData$orderS3.byStatus) === null || _dashboardData$orderS4 === void 0 ? void 0 : _dashboardData$orderS4.completed) || 0,\n          failedPayments: ((_dashboardData$orderS5 = dashboardData.orderStats) === null || _dashboardData$orderS5 === void 0 ? void 0 : (_dashboardData$orderS6 = _dashboardData$orderS5.byStatus) === null || _dashboardData$orderS6 === void 0 ? void 0 : _dashboardData$orderS6.cancelled) || 0\n        });\n      }\n\n      // Load orders from admin API\n      const ordersResponse = await adminApiService.getOrders();\n      if (ordersResponse.success && ordersResponse.data) {\n        setOrders(ordersResponse.data.orders || []);\n      }\n\n      // Load payments (using regular API for now)\n      const paymentsResponse = await apiService.get('/payments');\n      if (paymentsResponse.success && paymentsResponse.data) {\n        setPayments(paymentsResponse.data);\n      }\n    } catch (error) {\n      console.error('Error loading dashboard data:', error);\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n  useEffect(() => {\n    // Check if admin is already authenticated\n    if (adminApiService.isAuthenticated()) {\n      const storedAdmin = adminApiService.getStoredAdmin();\n      if (storedAdmin) {\n        setAdminUser(storedAdmin);\n        setIsAuthenticated(true);\n        setActiveTab('overview');\n        loadDashboardData();\n      }\n    }\n  }, [loadDashboardData]);\n  const handleLogin = async e => {\n    e.preventDefault();\n    setLoading(true);\n    try {\n      const response = await adminApiService.login(loginForm);\n      if (response.success && response.data) {\n        setAdminUser(response.data.admin);\n        setIsAuthenticated(true);\n        setActiveTab('overview');\n        await loadDashboardData();\n      } else {\n        alert(response.error || 'Erro no login');\n      }\n    } catch (error) {\n      console.error('Login error:', error);\n      alert('Erro no login. Tente novamente.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleLogout = async () => {\n    await adminApiService.logout();\n    setAdminUser(null);\n    setIsAuthenticated(false);\n    setActiveTab('login');\n    setLoginForm({\n      email: '',\n      password: ''\n    });\n  };\n  const calculateStats = paymentsData => {\n    const stats = paymentsData.reduce((acc, payment) => {\n      acc.totalPayments++;\n      if (payment.status === 'COMPLETED') {\n        acc.completedPayments++;\n        acc.totalRevenue += payment.amount;\n      } else if (payment.status === 'PENDING' || payment.status === 'PROCESSING') {\n        acc.pendingPayments++;\n      } else if (payment.status === 'FAILED') {\n        acc.failedPayments++;\n      }\n      return acc;\n    }, {\n      totalRevenue: 0,\n      totalPayments: 0,\n      pendingPayments: 0,\n      completedPayments: 0,\n      failedPayments: 0\n    });\n    setStats(stats);\n  };\n  const handleRefundPayment = async paymentId => {\n    // eslint-disable-next-line no-restricted-globals\n    if (!confirm('Tem certeza que deseja processar este reembolso?')) {\n      return;\n    }\n    try {\n      const response = await MulticaixaService.requestRefund(paymentId);\n      if (response.success) {\n        alert('Reembolso processado com sucesso!');\n        loadDashboardData(); // Reload data\n      } else {\n        alert(`Erro ao processar reembolso: ${response.error}`);\n      }\n    } catch (error) {\n      alert('Erro ao processar reembolso');\n    }\n  };\n  const formatCurrency = amount => {\n    return MulticaixaService.formatAoaAmount(amount);\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 159,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Show login form if not authenticated\n  if (!isAuthenticated || activeTab === 'login') {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-md w-full space-y-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"mt-6 text-center text-3xl font-extrabold text-gray-900\",\n            children: \"Painel Administrativo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-2 text-center text-sm text-gray-600\",\n            children: \"Fa\\xE7a login para acessar o painel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          className: \"mt-8 space-y-6\",\n          onSubmit: handleLogin,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"rounded-md shadow-sm -space-y-px\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"email\",\n                required: true,\n                className: \"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm\",\n                placeholder: \"Email\",\n                value: loginForm.email,\n                onChange: e => setLoginForm({\n                  ...loginForm,\n                  email: e.target.value\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"password\",\n                required: true,\n                className: \"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm\",\n                placeholder: \"Senha\",\n                value: loginForm.password,\n                onChange: e => setLoginForm({\n                  ...loginForm,\n                  password: e.target.value\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n              children: \"Entrar\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center text-sm text-gray-600\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Credenciais padr\\xE3o:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Email: <EMAIL>\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Senha: admin123\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 168,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-8 flex justify-between items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-3xl font-bold text-gray-900\",\n            children: \"Painel Administrativo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: \"Gest\\xE3o de pagamentos e pedidos WePrint AI\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 13\n          }, this), adminUser && /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-500\",\n            children: [\"Logado como: \", adminUser.name, \" (\", adminUser.email, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleLogout,\n          className: \"bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium\",\n          children: \"Sair\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 224,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-8\",\n        children: /*#__PURE__*/_jsxDEV(\"nav\", {\n          className: \"flex space-x-8\",\n          children: [{\n            id: 'overview',\n            label: 'Visão Geral'\n          }, {\n            id: 'payments',\n            label: 'Pagamentos'\n          }, {\n            id: 'orders',\n            label: 'Pedidos'\n          }].map(tab => /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setActiveTab(tab.id),\n            className: `py-2 px-1 border-b-2 font-medium text-sm ${activeTab === tab.id ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`,\n            children: tab.label\n          }, tab.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 241,\n        columnNumber: 9\n      }, this), activeTab === 'overview' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow p-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-shrink-0\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-5 h-5 text-white\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 273,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 272,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 271,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"ml-5 w-0 flex-1\",\n                children: /*#__PURE__*/_jsxDEV(\"dl\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"dt\", {\n                    className: \"text-sm font-medium text-gray-500 truncate\",\n                    children: \"Receita Total\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 279,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"dd\", {\n                    className: \"text-lg font-medium text-gray-900\",\n                    children: formatCurrency(stats.totalRevenue)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 282,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 278,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 277,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow p-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-shrink-0\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-5 h-5 text-white\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      fillRule: \"evenodd\",\n                      d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                      clipRule: \"evenodd\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 295,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 294,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 293,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 292,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"ml-5 w-0 flex-1\",\n                children: /*#__PURE__*/_jsxDEV(\"dl\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"dt\", {\n                    className: \"text-sm font-medium text-gray-500 truncate\",\n                    children: \"Pagamentos Conclu\\xEDdos\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 301,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"dd\", {\n                    className: \"text-lg font-medium text-gray-900\",\n                    children: stats.completedPayments\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 304,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 300,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 299,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 291,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow p-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-shrink-0\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-5 h-5 text-white\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      fillRule: \"evenodd\",\n                      d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z\",\n                      clipRule: \"evenodd\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 317,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 316,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 315,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 314,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"ml-5 w-0 flex-1\",\n                children: /*#__PURE__*/_jsxDEV(\"dl\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"dt\", {\n                    className: \"text-sm font-medium text-gray-500 truncate\",\n                    children: \"Pagamentos Pendentes\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 323,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"dd\", {\n                    className: \"text-lg font-medium text-gray-900\",\n                    children: stats.pendingPayments\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 326,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 322,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 321,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 313,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 312,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow p-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-shrink-0\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-8 h-8 bg-red-500 rounded-md flex items-center justify-center\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-5 h-5 text-white\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      fillRule: \"evenodd\",\n                      d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n                      clipRule: \"evenodd\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 339,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 338,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 337,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 336,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"ml-5 w-0 flex-1\",\n                children: /*#__PURE__*/_jsxDEV(\"dl\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"dt\", {\n                    className: \"text-sm font-medium text-gray-500 truncate\",\n                    children: \"Pagamentos Falhados\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 345,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"dd\", {\n                    className: \"text-lg font-medium text-gray-900\",\n                    children: stats.failedPayments\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 348,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 344,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 343,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 335,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"px-6 py-4 border-b border-gray-200\",\n            children: /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-medium text-gray-900\",\n              children: \"Atividade Recente\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 360,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: payments.slice(0, 5).map(payment => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-3\",\n                  children: [/*#__PURE__*/_jsxDEV(PaymentStatus, {\n                    status: payment.status,\n                    size: \"sm\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 367,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm font-medium text-gray-900\",\n                      children: [\"Pagamento #\", payment.id.slice(-8)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 369,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-500\",\n                      children: formatCurrency(payment.amount)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 372,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 368,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 366,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-gray-500\",\n                  children: new Date(payment.createdAt).toLocaleDateString('pt-PT')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 377,\n                  columnNumber: 23\n                }, this)]\n              }, payment.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 365,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 363,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 362,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 358,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 265,\n        columnNumber: 11\n      }, this), activeTab === 'payments' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-6 py-4 border-b border-gray-200\",\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-medium text-gray-900\",\n            children: \"Gest\\xE3o de Pagamentos\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 392,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 391,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"overflow-x-auto\",\n          children: /*#__PURE__*/_jsxDEV(\"table\", {\n            className: \"min-w-full divide-y divide-gray-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              className: \"bg-gray-50\",\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"ID\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 398,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Valor\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 401,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 404,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"M\\xE9todo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 407,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Data\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 410,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"A\\xE7\\xF5es\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 413,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 397,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 396,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              className: \"bg-white divide-y divide-gray-200\",\n              children: payments.map(payment => /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                  children: [\"#\", payment.id.slice(-8)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 421,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                  children: formatCurrency(payment.amount)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 424,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap\",\n                  children: /*#__PURE__*/_jsxDEV(PaymentStatus, {\n                    status: payment.status,\n                    size: \"sm\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 428,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 427,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                  children: payment.paymentMethod\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 430,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                  children: new Date(payment.createdAt).toLocaleDateString('pt-PT')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 433,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                  children: MulticaixaService.canRefund(payment.status) && /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleRefundPayment(payment.id),\n                    className: \"text-red-600 hover:text-red-900\",\n                    children: \"Reembolsar\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 438,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 436,\n                  columnNumber: 23\n                }, this)]\n              }, payment.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 420,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 418,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 395,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 394,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 390,\n        columnNumber: 11\n      }, this), activeTab === 'orders' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-6 py-4 border-b border-gray-200\",\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-medium text-gray-900\",\n            children: \"Gest\\xE3o de Pedidos\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 458,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 457,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: orders.map(order => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"border border-gray-200 rounded-lg p-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"text-lg font-medium text-gray-900\",\n                  children: [\"Pedido #\", order.id.slice(-8)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 465,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `px-2 py-1 text-xs font-medium rounded-full ${order.status === 'delivered' ? 'bg-green-100 text-green-800' : order.status === 'in_progress' ? 'bg-blue-100 text-blue-800' : order.status === 'ready' ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800'}`,\n                  children: order.status\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 468,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 464,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-2 gap-4 text-sm\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-500\",\n                    children: \"Total:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 479,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"ml-2 font-medium\",\n                    children: formatCurrency(order.totalAmount)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 480,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 478,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-500\",\n                    children: \"Pagamento:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 483,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `ml-2 font-medium ${order.paymentStatus === 'paid' ? 'text-green-600' : order.paymentStatus === 'pending' ? 'text-yellow-600' : 'text-red-600'}`,\n                    children: order.paymentStatus\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 484,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 482,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-500\",\n                    children: \"Data:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 493,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"ml-2\",\n                    children: new Date(order.createdAt).toLocaleDateString('pt-PT')\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 494,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 492,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-500\",\n                    children: \"Itens:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 497,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"ml-2\",\n                    children: order.printJobs.length\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 498,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 496,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 477,\n                columnNumber: 21\n              }, this)]\n            }, order.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 463,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 461,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 460,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 456,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 222,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 221,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminPanel, \"TFducUsZMN9x0MalOpiDb/5IgeY=\");\n_c = AdminPanel;\nexport default AdminPanel;\nvar _c;\n$RefreshReg$(_c, \"AdminPanel\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "PaymentStatus", "MulticaixaService", "apiService", "adminApiService", "jsxDEV", "_jsxDEV", "AdminPanel", "_s", "activeTab", "setActiveTab", "stats", "setStats", "totalRevenue", "totalPayments", "pendingPayments", "completedPayments", "failedPayments", "payments", "setPayments", "orders", "setOrders", "loading", "setLoading", "isAuthenticated", "setIsAuthenticated", "adminUser", "setAdminUser", "loginForm", "setLoginForm", "email", "password", "loadDashboardData", "statsResponse", "getDashboardStats", "success", "data", "_dashboardData$overvi", "_dashboardData$overvi2", "_dashboardData$orderS", "_dashboardData$orderS2", "_dashboardData$orderS3", "_dashboardData$orderS4", "_dashboardData$orderS5", "_dashboardData$orderS6", "dashboardData", "overview", "totalOrders", "orderStats", "byStatus", "pending", "completed", "cancelled", "ordersResponse", "getOrders", "paymentsResponse", "get", "error", "console", "storedAdmin", "getStoredAdmin", "handleLogin", "e", "preventDefault", "response", "login", "admin", "alert", "handleLogout", "logout", "calculateStats", "paymentsData", "reduce", "acc", "payment", "status", "amount", "handleRefundPayment", "paymentId", "confirm", "requestRefund", "formatCurrency", "formatAoaAmount", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "type", "required", "placeholder", "value", "onChange", "target", "name", "onClick", "id", "label", "map", "tab", "fill", "viewBox", "d", "fillRule", "clipRule", "slice", "size", "Date", "createdAt", "toLocaleDateString", "paymentMethod", "canRefund", "order", "totalAmount", "paymentStatus", "printJobs", "length", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/AdminPanel.tsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport { PaymentStatus } from '../components';\nimport { MulticaixaService } from '../services/multicaixa';\nimport { apiService } from '../services/api';\nimport { adminApiService } from '../services/adminApi';\nimport { Payment, Order } from '../types';\n\ninterface DashboardStats {\n  totalRevenue: number;\n  totalPayments: number;\n  pendingPayments: number;\n  completedPayments: number;\n  failedPayments: number;\n}\n\nconst AdminPanel: React.FC = () => {\n  const [activeTab, setActiveTab] = useState<'overview' | 'payments' | 'orders' | 'login'>('login');\n  const [stats, setStats] = useState<DashboardStats>({\n    totalRevenue: 0,\n    totalPayments: 0,\n    pendingPayments: 0,\n    completedPayments: 0,\n    failedPayments: 0,\n  });\n  const [payments, setPayments] = useState<Payment[]>([]);\n  const [orders, setOrders] = useState<Order[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  const [adminUser, setAdminUser] = useState<any>(null);\n  const [loginForm, setLoginForm] = useState({ email: '', password: '' });\n\n  const loadDashboardData = useCallback(async () => {\n    setLoading(true);\n    try {\n      // Load dashboard stats from admin API\n      const statsResponse = await adminApiService.getDashboardStats();\n      if (statsResponse.success && statsResponse.data) {\n        const dashboardData = statsResponse.data;\n        setStats({\n          totalRevenue: dashboardData.overview?.totalRevenue || 0,\n          totalPayments: dashboardData.overview?.totalOrders || 0,\n          pendingPayments: dashboardData.orderStats?.byStatus?.pending || 0,\n          completedPayments: dashboardData.orderStats?.byStatus?.completed || 0,\n          failedPayments: dashboardData.orderStats?.byStatus?.cancelled || 0,\n        });\n      }\n\n      // Load orders from admin API\n      const ordersResponse = await adminApiService.getOrders();\n      if (ordersResponse.success && ordersResponse.data) {\n        setOrders(ordersResponse.data.orders || []);\n      }\n\n      // Load payments (using regular API for now)\n      const paymentsResponse = await apiService.get<Payment[]>('/payments');\n      if (paymentsResponse.success && paymentsResponse.data) {\n        setPayments(paymentsResponse.data);\n      }\n    } catch (error) {\n      console.error('Error loading dashboard data:', error);\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n\n  useEffect(() => {\n    // Check if admin is already authenticated\n    if (adminApiService.isAuthenticated()) {\n      const storedAdmin = adminApiService.getStoredAdmin();\n      if (storedAdmin) {\n        setAdminUser(storedAdmin);\n        setIsAuthenticated(true);\n        setActiveTab('overview');\n        loadDashboardData();\n      }\n    }\n  }, [loadDashboardData]);\n\n  const handleLogin = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setLoading(true);\n\n    try {\n      const response = await adminApiService.login(loginForm);\n      if (response.success && response.data) {\n        setAdminUser(response.data.admin);\n        setIsAuthenticated(true);\n        setActiveTab('overview');\n        await loadDashboardData();\n      } else {\n        alert(response.error || 'Erro no login');\n      }\n    } catch (error) {\n      console.error('Login error:', error);\n      alert('Erro no login. Tente novamente.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleLogout = async () => {\n    await adminApiService.logout();\n    setAdminUser(null);\n    setIsAuthenticated(false);\n    setActiveTab('login');\n    setLoginForm({ email: '', password: '' });\n  };\n\n  const calculateStats = (paymentsData: Payment[]) => {\n    const stats = paymentsData.reduce((acc, payment) => {\n      acc.totalPayments++;\n\n      if (payment.status === 'COMPLETED') {\n        acc.completedPayments++;\n        acc.totalRevenue += payment.amount;\n      } else if (payment.status === 'PENDING' || payment.status === 'PROCESSING') {\n        acc.pendingPayments++;\n      } else if (payment.status === 'FAILED') {\n        acc.failedPayments++;\n      }\n\n      return acc;\n    }, {\n      totalRevenue: 0,\n      totalPayments: 0,\n      pendingPayments: 0,\n      completedPayments: 0,\n      failedPayments: 0,\n    });\n\n    setStats(stats);\n  };\n\n  const handleRefundPayment = async (paymentId: string) => {\n    // eslint-disable-next-line no-restricted-globals\n    if (!confirm('Tem certeza que deseja processar este reembolso?')) {\n      return;\n    }\n\n    try {\n      const response = await MulticaixaService.requestRefund(paymentId);\n      if (response.success) {\n        alert('Reembolso processado com sucesso!');\n        loadDashboardData(); // Reload data\n      } else {\n        alert(`Erro ao processar reembolso: ${response.error}`);\n      }\n    } catch (error) {\n      alert('Erro ao processar reembolso');\n    }\n  };\n\n  const formatCurrency = (amount: number) => {\n    return MulticaixaService.formatAoaAmount(amount);\n  };\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  // Show login form if not authenticated\n  if (!isAuthenticated || activeTab === 'login') {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"max-w-md w-full space-y-8\">\n          <div>\n            <h2 className=\"mt-6 text-center text-3xl font-extrabold text-gray-900\">\n              Painel Administrativo\n            </h2>\n            <p className=\"mt-2 text-center text-sm text-gray-600\">\n              Faça login para acessar o painel\n            </p>\n          </div>\n          <form className=\"mt-8 space-y-6\" onSubmit={handleLogin}>\n            <div className=\"rounded-md shadow-sm -space-y-px\">\n              <div>\n                <input\n                  type=\"email\"\n                  required\n                  className=\"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm\"\n                  placeholder=\"Email\"\n                  value={loginForm.email}\n                  onChange={(e) => setLoginForm({ ...loginForm, email: e.target.value })}\n                />\n              </div>\n              <div>\n                <input\n                  type=\"password\"\n                  required\n                  className=\"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm\"\n                  placeholder=\"Senha\"\n                  value={loginForm.password}\n                  onChange={(e) => setLoginForm({ ...loginForm, password: e.target.value })}\n                />\n              </div>\n            </div>\n            <div>\n              <button\n                type=\"submit\"\n                className=\"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n              >\n                Entrar\n              </button>\n            </div>\n            <div className=\"text-center text-sm text-gray-600\">\n              <p>Credenciais padrão:</p>\n              <p>Email: <EMAIL></p>\n              <p>Senha: admin123</p>\n            </div>\n          </form>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Header */}\n        <div className=\"mb-8 flex justify-between items-center\">\n          <div>\n            <h1 className=\"text-3xl font-bold text-gray-900\">Painel Administrativo</h1>\n            <p className=\"text-gray-600\">Gestão de pagamentos e pedidos WePrint AI</p>\n            {adminUser && (\n              <p className=\"text-sm text-gray-500\">Logado como: {adminUser.name} ({adminUser.email})</p>\n            )}\n          </div>\n          <button\n            onClick={handleLogout}\n            className=\"bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium\"\n          >\n            Sair\n          </button>\n        </div>\n\n        {/* Navigation Tabs */}\n        <div className=\"mb-8\">\n          <nav className=\"flex space-x-8\">\n            {[\n              { id: 'overview', label: 'Visão Geral' },\n              { id: 'payments', label: 'Pagamentos' },\n              { id: 'orders', label: 'Pedidos' },\n            ].map((tab) => (\n              <button\n                key={tab.id}\n                onClick={() => setActiveTab(tab.id as any)}\n                className={`py-2 px-1 border-b-2 font-medium text-sm ${\n                  activeTab === tab.id\n                    ? 'border-blue-500 text-blue-600'\n                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                }`}\n              >\n                {tab.label}\n              </button>\n            ))}\n          </nav>\n        </div>\n\n        {/* Overview Tab */}\n        {activeTab === 'overview' && (\n          <div className=\"space-y-6\">\n            {/* Stats Cards */}\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n              <div className=\"bg-white rounded-lg shadow p-6\">\n                <div className=\"flex items-center\">\n                  <div className=\"flex-shrink-0\">\n                    <div className=\"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center\">\n                      <svg className=\"w-5 h-5 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path d=\"M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z\" />\n                      </svg>\n                    </div>\n                  </div>\n                  <div className=\"ml-5 w-0 flex-1\">\n                    <dl>\n                      <dt className=\"text-sm font-medium text-gray-500 truncate\">\n                        Receita Total\n                      </dt>\n                      <dd className=\"text-lg font-medium text-gray-900\">\n                        {formatCurrency(stats.totalRevenue)}\n                      </dd>\n                    </dl>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"bg-white rounded-lg shadow p-6\">\n                <div className=\"flex items-center\">\n                  <div className=\"flex-shrink-0\">\n                    <div className=\"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center\">\n                      <svg className=\"w-5 h-5 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n                      </svg>\n                    </div>\n                  </div>\n                  <div className=\"ml-5 w-0 flex-1\">\n                    <dl>\n                      <dt className=\"text-sm font-medium text-gray-500 truncate\">\n                        Pagamentos Concluídos\n                      </dt>\n                      <dd className=\"text-lg font-medium text-gray-900\">\n                        {stats.completedPayments}\n                      </dd>\n                    </dl>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"bg-white rounded-lg shadow p-6\">\n                <div className=\"flex items-center\">\n                  <div className=\"flex-shrink-0\">\n                    <div className=\"w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center\">\n                      <svg className=\"w-5 h-5 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z\" clipRule=\"evenodd\" />\n                      </svg>\n                    </div>\n                  </div>\n                  <div className=\"ml-5 w-0 flex-1\">\n                    <dl>\n                      <dt className=\"text-sm font-medium text-gray-500 truncate\">\n                        Pagamentos Pendentes\n                      </dt>\n                      <dd className=\"text-lg font-medium text-gray-900\">\n                        {stats.pendingPayments}\n                      </dd>\n                    </dl>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"bg-white rounded-lg shadow p-6\">\n                <div className=\"flex items-center\">\n                  <div className=\"flex-shrink-0\">\n                    <div className=\"w-8 h-8 bg-red-500 rounded-md flex items-center justify-center\">\n                      <svg className=\"w-5 h-5 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\n                      </svg>\n                    </div>\n                  </div>\n                  <div className=\"ml-5 w-0 flex-1\">\n                    <dl>\n                      <dt className=\"text-sm font-medium text-gray-500 truncate\">\n                        Pagamentos Falhados\n                      </dt>\n                      <dd className=\"text-lg font-medium text-gray-900\">\n                        {stats.failedPayments}\n                      </dd>\n                    </dl>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Recent Activity */}\n            <div className=\"bg-white rounded-lg shadow\">\n              <div className=\"px-6 py-4 border-b border-gray-200\">\n                <h3 className=\"text-lg font-medium text-gray-900\">Atividade Recente</h3>\n              </div>\n              <div className=\"p-6\">\n                <div className=\"space-y-4\">\n                  {payments.slice(0, 5).map((payment) => (\n                    <div key={payment.id} className=\"flex items-center justify-between\">\n                      <div className=\"flex items-center space-x-3\">\n                        <PaymentStatus status={payment.status} size=\"sm\" />\n                        <div>\n                          <p className=\"text-sm font-medium text-gray-900\">\n                            Pagamento #{payment.id.slice(-8)}\n                          </p>\n                          <p className=\"text-sm text-gray-500\">\n                            {formatCurrency(payment.amount)}\n                          </p>\n                        </div>\n                      </div>\n                      <div className=\"text-sm text-gray-500\">\n                        {new Date(payment.createdAt).toLocaleDateString('pt-PT')}\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Payments Tab */}\n        {activeTab === 'payments' && (\n          <div className=\"bg-white rounded-lg shadow\">\n            <div className=\"px-6 py-4 border-b border-gray-200\">\n              <h3 className=\"text-lg font-medium text-gray-900\">Gestão de Pagamentos</h3>\n            </div>\n            <div className=\"overflow-x-auto\">\n              <table className=\"min-w-full divide-y divide-gray-200\">\n                <thead className=\"bg-gray-50\">\n                  <tr>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      ID\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Valor\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Status\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Método\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Data\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Ações\n                    </th>\n                  </tr>\n                </thead>\n                <tbody className=\"bg-white divide-y divide-gray-200\">\n                  {payments.map((payment) => (\n                    <tr key={payment.id}>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">\n                        #{payment.id.slice(-8)}\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                        {formatCurrency(payment.amount)}\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\n                        <PaymentStatus status={payment.status} size=\"sm\" />\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                        {payment.paymentMethod}\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                        {new Date(payment.createdAt).toLocaleDateString('pt-PT')}\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                        {MulticaixaService.canRefund(payment.status) && (\n                          <button\n                            onClick={() => handleRefundPayment(payment.id)}\n                            className=\"text-red-600 hover:text-red-900\"\n                          >\n                            Reembolsar\n                          </button>\n                        )}\n                      </td>\n                    </tr>\n                  ))}\n                </tbody>\n              </table>\n            </div>\n          </div>\n        )}\n\n        {/* Orders Tab */}\n        {activeTab === 'orders' && (\n          <div className=\"bg-white rounded-lg shadow\">\n            <div className=\"px-6 py-4 border-b border-gray-200\">\n              <h3 className=\"text-lg font-medium text-gray-900\">Gestão de Pedidos</h3>\n            </div>\n            <div className=\"p-6\">\n              <div className=\"space-y-4\">\n                {orders.map((order) => (\n                  <div key={order.id} className=\"border border-gray-200 rounded-lg p-4\">\n                    <div className=\"flex items-center justify-between mb-2\">\n                      <h4 className=\"text-lg font-medium text-gray-900\">\n                        Pedido #{order.id.slice(-8)}\n                      </h4>\n                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${\n                        order.status === 'delivered' ? 'bg-green-100 text-green-800' :\n                        order.status === 'in_progress' ? 'bg-blue-100 text-blue-800' :\n                        order.status === 'ready' ? 'bg-yellow-100 text-yellow-800' :\n                        'bg-gray-100 text-gray-800'\n                      }`}>\n                        {order.status}\n                      </span>\n                    </div>\n                    <div className=\"grid grid-cols-2 gap-4 text-sm\">\n                      <div>\n                        <span className=\"text-gray-500\">Total:</span>\n                        <span className=\"ml-2 font-medium\">{formatCurrency(order.totalAmount)}</span>\n                      </div>\n                      <div>\n                        <span className=\"text-gray-500\">Pagamento:</span>\n                        <span className={`ml-2 font-medium ${\n                          order.paymentStatus === 'paid' ? 'text-green-600' :\n                          order.paymentStatus === 'pending' ? 'text-yellow-600' :\n                          'text-red-600'\n                        }`}>\n                          {order.paymentStatus}\n                        </span>\n                      </div>\n                      <div>\n                        <span className=\"text-gray-500\">Data:</span>\n                        <span className=\"ml-2\">{new Date(order.createdAt).toLocaleDateString('pt-PT')}</span>\n                      </div>\n                      <div>\n                        <span className=\"text-gray-500\">Itens:</span>\n                        <span className=\"ml-2\">{order.printJobs.length}</span>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default AdminPanel;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,aAAa,QAAQ,eAAe;AAC7C,SAASC,iBAAiB,QAAQ,wBAAwB;AAC1D,SAASC,UAAU,QAAQ,iBAAiB;AAC5C,SAASC,eAAe,QAAQ,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAWvD,MAAMC,UAAoB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjC,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGZ,QAAQ,CAA+C,OAAO,CAAC;EACjG,MAAM,CAACa,KAAK,EAAEC,QAAQ,CAAC,GAAGd,QAAQ,CAAiB;IACjDe,YAAY,EAAE,CAAC;IACfC,aAAa,EAAE,CAAC;IAChBC,eAAe,EAAE,CAAC;IAClBC,iBAAiB,EAAE,CAAC;IACpBC,cAAc,EAAE;EAClB,CAAC,CAAC;EACF,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGrB,QAAQ,CAAY,EAAE,CAAC;EACvD,MAAM,CAACsB,MAAM,EAAEC,SAAS,CAAC,GAAGvB,QAAQ,CAAU,EAAE,CAAC;EACjD,MAAM,CAACwB,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC0B,eAAe,EAAEC,kBAAkB,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC4B,SAAS,EAAEC,YAAY,CAAC,GAAG7B,QAAQ,CAAM,IAAI,CAAC;EACrD,MAAM,CAAC8B,SAAS,EAAEC,YAAY,CAAC,GAAG/B,QAAQ,CAAC;IAAEgC,KAAK,EAAE,EAAE;IAAEC,QAAQ,EAAE;EAAG,CAAC,CAAC;EAEvE,MAAMC,iBAAiB,GAAGhC,WAAW,CAAC,YAAY;IAChDuB,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF;MACA,MAAMU,aAAa,GAAG,MAAM7B,eAAe,CAAC8B,iBAAiB,CAAC,CAAC;MAC/D,IAAID,aAAa,CAACE,OAAO,IAAIF,aAAa,CAACG,IAAI,EAAE;QAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;QAC/C,MAAMC,aAAa,GAAGZ,aAAa,CAACG,IAAI;QACxCxB,QAAQ,CAAC;UACPC,YAAY,EAAE,EAAAwB,qBAAA,GAAAQ,aAAa,CAACC,QAAQ,cAAAT,qBAAA,uBAAtBA,qBAAA,CAAwBxB,YAAY,KAAI,CAAC;UACvDC,aAAa,EAAE,EAAAwB,sBAAA,GAAAO,aAAa,CAACC,QAAQ,cAAAR,sBAAA,uBAAtBA,sBAAA,CAAwBS,WAAW,KAAI,CAAC;UACvDhC,eAAe,EAAE,EAAAwB,qBAAA,GAAAM,aAAa,CAACG,UAAU,cAAAT,qBAAA,wBAAAC,sBAAA,GAAxBD,qBAAA,CAA0BU,QAAQ,cAAAT,sBAAA,uBAAlCA,sBAAA,CAAoCU,OAAO,KAAI,CAAC;UACjElC,iBAAiB,EAAE,EAAAyB,sBAAA,GAAAI,aAAa,CAACG,UAAU,cAAAP,sBAAA,wBAAAC,sBAAA,GAAxBD,sBAAA,CAA0BQ,QAAQ,cAAAP,sBAAA,uBAAlCA,sBAAA,CAAoCS,SAAS,KAAI,CAAC;UACrElC,cAAc,EAAE,EAAA0B,sBAAA,GAAAE,aAAa,CAACG,UAAU,cAAAL,sBAAA,wBAAAC,sBAAA,GAAxBD,sBAAA,CAA0BM,QAAQ,cAAAL,sBAAA,uBAAlCA,sBAAA,CAAoCQ,SAAS,KAAI;QACnE,CAAC,CAAC;MACJ;;MAEA;MACA,MAAMC,cAAc,GAAG,MAAMjD,eAAe,CAACkD,SAAS,CAAC,CAAC;MACxD,IAAID,cAAc,CAAClB,OAAO,IAAIkB,cAAc,CAACjB,IAAI,EAAE;QACjDf,SAAS,CAACgC,cAAc,CAACjB,IAAI,CAAChB,MAAM,IAAI,EAAE,CAAC;MAC7C;;MAEA;MACA,MAAMmC,gBAAgB,GAAG,MAAMpD,UAAU,CAACqD,GAAG,CAAY,WAAW,CAAC;MACrE,IAAID,gBAAgB,CAACpB,OAAO,IAAIoB,gBAAgB,CAACnB,IAAI,EAAE;QACrDjB,WAAW,CAACoC,gBAAgB,CAACnB,IAAI,CAAC;MACpC;IACF,CAAC,CAAC,OAAOqB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACvD,CAAC,SAAS;MACRlC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,EAAE,CAAC;EAENxB,SAAS,CAAC,MAAM;IACd;IACA,IAAIK,eAAe,CAACoB,eAAe,CAAC,CAAC,EAAE;MACrC,MAAMmC,WAAW,GAAGvD,eAAe,CAACwD,cAAc,CAAC,CAAC;MACpD,IAAID,WAAW,EAAE;QACfhC,YAAY,CAACgC,WAAW,CAAC;QACzBlC,kBAAkB,CAAC,IAAI,CAAC;QACxBf,YAAY,CAAC,UAAU,CAAC;QACxBsB,iBAAiB,CAAC,CAAC;MACrB;IACF;EACF,CAAC,EAAE,CAACA,iBAAiB,CAAC,CAAC;EAEvB,MAAM6B,WAAW,GAAG,MAAOC,CAAkB,IAAK;IAChDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBxC,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF,MAAMyC,QAAQ,GAAG,MAAM5D,eAAe,CAAC6D,KAAK,CAACrC,SAAS,CAAC;MACvD,IAAIoC,QAAQ,CAAC7B,OAAO,IAAI6B,QAAQ,CAAC5B,IAAI,EAAE;QACrCT,YAAY,CAACqC,QAAQ,CAAC5B,IAAI,CAAC8B,KAAK,CAAC;QACjCzC,kBAAkB,CAAC,IAAI,CAAC;QACxBf,YAAY,CAAC,UAAU,CAAC;QACxB,MAAMsB,iBAAiB,CAAC,CAAC;MAC3B,CAAC,MAAM;QACLmC,KAAK,CAACH,QAAQ,CAACP,KAAK,IAAI,eAAe,CAAC;MAC1C;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACpCU,KAAK,CAAC,iCAAiC,CAAC;IAC1C,CAAC,SAAS;MACR5C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM6C,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,MAAMhE,eAAe,CAACiE,MAAM,CAAC,CAAC;IAC9B1C,YAAY,CAAC,IAAI,CAAC;IAClBF,kBAAkB,CAAC,KAAK,CAAC;IACzBf,YAAY,CAAC,OAAO,CAAC;IACrBmB,YAAY,CAAC;MAAEC,KAAK,EAAE,EAAE;MAAEC,QAAQ,EAAE;IAAG,CAAC,CAAC;EAC3C,CAAC;EAED,MAAMuC,cAAc,GAAIC,YAAuB,IAAK;IAClD,MAAM5D,KAAK,GAAG4D,YAAY,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,OAAO,KAAK;MAClDD,GAAG,CAAC3D,aAAa,EAAE;MAEnB,IAAI4D,OAAO,CAACC,MAAM,KAAK,WAAW,EAAE;QAClCF,GAAG,CAACzD,iBAAiB,EAAE;QACvByD,GAAG,CAAC5D,YAAY,IAAI6D,OAAO,CAACE,MAAM;MACpC,CAAC,MAAM,IAAIF,OAAO,CAACC,MAAM,KAAK,SAAS,IAAID,OAAO,CAACC,MAAM,KAAK,YAAY,EAAE;QAC1EF,GAAG,CAAC1D,eAAe,EAAE;MACvB,CAAC,MAAM,IAAI2D,OAAO,CAACC,MAAM,KAAK,QAAQ,EAAE;QACtCF,GAAG,CAACxD,cAAc,EAAE;MACtB;MAEA,OAAOwD,GAAG;IACZ,CAAC,EAAE;MACD5D,YAAY,EAAE,CAAC;MACfC,aAAa,EAAE,CAAC;MAChBC,eAAe,EAAE,CAAC;MAClBC,iBAAiB,EAAE,CAAC;MACpBC,cAAc,EAAE;IAClB,CAAC,CAAC;IAEFL,QAAQ,CAACD,KAAK,CAAC;EACjB,CAAC;EAED,MAAMkE,mBAAmB,GAAG,MAAOC,SAAiB,IAAK;IACvD;IACA,IAAI,CAACC,OAAO,CAAC,kDAAkD,CAAC,EAAE;MAChE;IACF;IAEA,IAAI;MACF,MAAMf,QAAQ,GAAG,MAAM9D,iBAAiB,CAAC8E,aAAa,CAACF,SAAS,CAAC;MACjE,IAAId,QAAQ,CAAC7B,OAAO,EAAE;QACpBgC,KAAK,CAAC,mCAAmC,CAAC;QAC1CnC,iBAAiB,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,MAAM;QACLmC,KAAK,CAAC,gCAAgCH,QAAQ,CAACP,KAAK,EAAE,CAAC;MACzD;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdU,KAAK,CAAC,6BAA6B,CAAC;IACtC;EACF,CAAC;EAED,MAAMc,cAAc,GAAIL,MAAc,IAAK;IACzC,OAAO1E,iBAAiB,CAACgF,eAAe,CAACN,MAAM,CAAC;EAClD,CAAC;EAED,IAAItD,OAAO,EAAE;IACX,oBACEhB,OAAA;MAAK6E,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eACvE9E,OAAA;QAAK6E,SAAS,EAAC;MAAgE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnF,CAAC;EAEV;;EAEA;EACA,IAAI,CAAChE,eAAe,IAAIf,SAAS,KAAK,OAAO,EAAE;IAC7C,oBACEH,OAAA;MAAK6E,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eACvE9E,OAAA;QAAK6E,SAAS,EAAC,2BAA2B;QAAAC,QAAA,gBACxC9E,OAAA;UAAA8E,QAAA,gBACE9E,OAAA;YAAI6E,SAAS,EAAC,wDAAwD;YAAAC,QAAA,EAAC;UAEvE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLlF,OAAA;YAAG6E,SAAS,EAAC,wCAAwC;YAAAC,QAAA,EAAC;UAEtD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNlF,OAAA;UAAM6E,SAAS,EAAC,gBAAgB;UAACM,QAAQ,EAAE5B,WAAY;UAAAuB,QAAA,gBACrD9E,OAAA;YAAK6E,SAAS,EAAC,kCAAkC;YAAAC,QAAA,gBAC/C9E,OAAA;cAAA8E,QAAA,eACE9E,OAAA;gBACEoF,IAAI,EAAC,OAAO;gBACZC,QAAQ;gBACRR,SAAS,EAAC,wNAAwN;gBAClOS,WAAW,EAAC,OAAO;gBACnBC,KAAK,EAAEjE,SAAS,CAACE,KAAM;gBACvBgE,QAAQ,EAAGhC,CAAC,IAAKjC,YAAY,CAAC;kBAAE,GAAGD,SAAS;kBAAEE,KAAK,EAAEgC,CAAC,CAACiC,MAAM,CAACF;gBAAM,CAAC;cAAE;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNlF,OAAA;cAAA8E,QAAA,eACE9E,OAAA;gBACEoF,IAAI,EAAC,UAAU;gBACfC,QAAQ;gBACRR,SAAS,EAAC,wNAAwN;gBAClOS,WAAW,EAAC,OAAO;gBACnBC,KAAK,EAAEjE,SAAS,CAACG,QAAS;gBAC1B+D,QAAQ,EAAGhC,CAAC,IAAKjC,YAAY,CAAC;kBAAE,GAAGD,SAAS;kBAAEG,QAAQ,EAAE+B,CAAC,CAACiC,MAAM,CAACF;gBAAM,CAAC;cAAE;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3E;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNlF,OAAA;YAAA8E,QAAA,eACE9E,OAAA;cACEoF,IAAI,EAAC,QAAQ;cACbP,SAAS,EAAC,+NAA+N;cAAAC,QAAA,EAC1O;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACNlF,OAAA;YAAK6E,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChD9E,OAAA;cAAA8E,QAAA,EAAG;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC1BlF,OAAA;cAAA8E,QAAA,EAAG;YAAuB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC9BlF,OAAA;cAAA8E,QAAA,EAAG;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACElF,OAAA;IAAK6E,SAAS,EAAC,yBAAyB;IAAAC,QAAA,eACtC9E,OAAA;MAAK6E,SAAS,EAAC,6CAA6C;MAAAC,QAAA,gBAE1D9E,OAAA;QAAK6E,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrD9E,OAAA;UAAA8E,QAAA,gBACE9E,OAAA;YAAI6E,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAAqB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3ElF,OAAA;YAAG6E,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAyC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,EACzE9D,SAAS,iBACRpB,OAAA;YAAG6E,SAAS,EAAC,uBAAuB;YAAAC,QAAA,GAAC,eAAa,EAAC1D,SAAS,CAACsE,IAAI,EAAC,IAAE,EAACtE,SAAS,CAACI,KAAK,EAAC,GAAC;UAAA;YAAAuD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAC1F;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACNlF,OAAA;UACE2F,OAAO,EAAE7B,YAAa;UACtBe,SAAS,EAAC,iFAAiF;UAAAC,QAAA,EAC5F;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGNlF,OAAA;QAAK6E,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnB9E,OAAA;UAAK6E,SAAS,EAAC,gBAAgB;UAAAC,QAAA,EAC5B,CACC;YAAEc,EAAE,EAAE,UAAU;YAAEC,KAAK,EAAE;UAAc,CAAC,EACxC;YAAED,EAAE,EAAE,UAAU;YAAEC,KAAK,EAAE;UAAa,CAAC,EACvC;YAAED,EAAE,EAAE,QAAQ;YAAEC,KAAK,EAAE;UAAU,CAAC,CACnC,CAACC,GAAG,CAAEC,GAAG,iBACR/F,OAAA;YAEE2F,OAAO,EAAEA,CAAA,KAAMvF,YAAY,CAAC2F,GAAG,CAACH,EAAS,CAAE;YAC3Cf,SAAS,EAAE,4CACT1E,SAAS,KAAK4F,GAAG,CAACH,EAAE,GAChB,+BAA+B,GAC/B,4EAA4E,EAC/E;YAAAd,QAAA,EAEFiB,GAAG,CAACF;UAAK,GARLE,GAAG,CAACH,EAAE;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OASL,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGL/E,SAAS,KAAK,UAAU,iBACvBH,OAAA;QAAK6E,SAAS,EAAC,WAAW;QAAAC,QAAA,gBAExB9E,OAAA;UAAK6E,SAAS,EAAC,sDAAsD;UAAAC,QAAA,gBACnE9E,OAAA;YAAK6E,SAAS,EAAC,gCAAgC;YAAAC,QAAA,eAC7C9E,OAAA;cAAK6E,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChC9E,OAAA;gBAAK6E,SAAS,EAAC,eAAe;gBAAAC,QAAA,eAC5B9E,OAAA;kBAAK6E,SAAS,EAAC,kEAAkE;kBAAAC,QAAA,eAC/E9E,OAAA;oBAAK6E,SAAS,EAAC,oBAAoB;oBAACmB,IAAI,EAAC,cAAc;oBAACC,OAAO,EAAC,WAAW;oBAAAnB,QAAA,eACzE9E,OAAA;sBAAMkG,CAAC,EAAC;oBAAmJ;sBAAAnB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3J;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNlF,OAAA;gBAAK6E,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,eAC9B9E,OAAA;kBAAA8E,QAAA,gBACE9E,OAAA;oBAAI6E,SAAS,EAAC,4CAA4C;oBAAAC,QAAA,EAAC;kBAE3D;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACLlF,OAAA;oBAAI6E,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAC9CH,cAAc,CAACtE,KAAK,CAACE,YAAY;kBAAC;oBAAAwE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENlF,OAAA;YAAK6E,SAAS,EAAC,gCAAgC;YAAAC,QAAA,eAC7C9E,OAAA;cAAK6E,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChC9E,OAAA;gBAAK6E,SAAS,EAAC,eAAe;gBAAAC,QAAA,eAC5B9E,OAAA;kBAAK6E,SAAS,EAAC,iEAAiE;kBAAAC,QAAA,eAC9E9E,OAAA;oBAAK6E,SAAS,EAAC,oBAAoB;oBAACmB,IAAI,EAAC,cAAc;oBAACC,OAAO,EAAC,WAAW;oBAAAnB,QAAA,eACzE9E,OAAA;sBAAMmG,QAAQ,EAAC,SAAS;sBAACD,CAAC,EAAC,uIAAuI;sBAACE,QAAQ,EAAC;oBAAS;sBAAArB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrL;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNlF,OAAA;gBAAK6E,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,eAC9B9E,OAAA;kBAAA8E,QAAA,gBACE9E,OAAA;oBAAI6E,SAAS,EAAC,4CAA4C;oBAAAC,QAAA,EAAC;kBAE3D;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACLlF,OAAA;oBAAI6E,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAC9CzE,KAAK,CAACK;kBAAiB;oBAAAqE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENlF,OAAA;YAAK6E,SAAS,EAAC,gCAAgC;YAAAC,QAAA,eAC7C9E,OAAA;cAAK6E,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChC9E,OAAA;gBAAK6E,SAAS,EAAC,eAAe;gBAAAC,QAAA,eAC5B9E,OAAA;kBAAK6E,SAAS,EAAC,mEAAmE;kBAAAC,QAAA,eAChF9E,OAAA;oBAAK6E,SAAS,EAAC,oBAAoB;oBAACmB,IAAI,EAAC,cAAc;oBAACC,OAAO,EAAC,WAAW;oBAAAnB,QAAA,eACzE9E,OAAA;sBAAMmG,QAAQ,EAAC,SAAS;sBAACD,CAAC,EAAC,oHAAoH;sBAACE,QAAQ,EAAC;oBAAS;sBAAArB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClK;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNlF,OAAA;gBAAK6E,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,eAC9B9E,OAAA;kBAAA8E,QAAA,gBACE9E,OAAA;oBAAI6E,SAAS,EAAC,4CAA4C;oBAAAC,QAAA,EAAC;kBAE3D;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACLlF,OAAA;oBAAI6E,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAC9CzE,KAAK,CAACI;kBAAe;oBAAAsE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENlF,OAAA;YAAK6E,SAAS,EAAC,gCAAgC;YAAAC,QAAA,eAC7C9E,OAAA;cAAK6E,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChC9E,OAAA;gBAAK6E,SAAS,EAAC,eAAe;gBAAAC,QAAA,eAC5B9E,OAAA;kBAAK6E,SAAS,EAAC,gEAAgE;kBAAAC,QAAA,eAC7E9E,OAAA;oBAAK6E,SAAS,EAAC,oBAAoB;oBAACmB,IAAI,EAAC,cAAc;oBAACC,OAAO,EAAC,WAAW;oBAAAnB,QAAA,eACzE9E,OAAA;sBAAMmG,QAAQ,EAAC,SAAS;sBAACD,CAAC,EAAC,yNAAyN;sBAACE,QAAQ,EAAC;oBAAS;sBAAArB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNlF,OAAA;gBAAK6E,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,eAC9B9E,OAAA;kBAAA8E,QAAA,gBACE9E,OAAA;oBAAI6E,SAAS,EAAC,4CAA4C;oBAAAC,QAAA,EAAC;kBAE3D;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACLlF,OAAA;oBAAI6E,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAC9CzE,KAAK,CAACM;kBAAc;oBAAAoE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNlF,OAAA;UAAK6E,SAAS,EAAC,4BAA4B;UAAAC,QAAA,gBACzC9E,OAAA;YAAK6E,SAAS,EAAC,oCAAoC;YAAAC,QAAA,eACjD9E,OAAA;cAAI6E,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrE,CAAC,eACNlF,OAAA;YAAK6E,SAAS,EAAC,KAAK;YAAAC,QAAA,eAClB9E,OAAA;cAAK6E,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvBlE,QAAQ,CAACyF,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACP,GAAG,CAAE1B,OAAO,iBAChCpE,OAAA;gBAAsB6E,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,gBACjE9E,OAAA;kBAAK6E,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1C9E,OAAA,CAACL,aAAa;oBAAC0E,MAAM,EAAED,OAAO,CAACC,MAAO;oBAACiC,IAAI,EAAC;kBAAI;oBAAAvB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACnDlF,OAAA;oBAAA8E,QAAA,gBACE9E,OAAA;sBAAG6E,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,GAAC,aACpC,EAACV,OAAO,CAACwB,EAAE,CAACS,KAAK,CAAC,CAAC,CAAC,CAAC;oBAAA;sBAAAtB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/B,CAAC,eACJlF,OAAA;sBAAG6E,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EACjCH,cAAc,CAACP,OAAO,CAACE,MAAM;oBAAC;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNlF,OAAA;kBAAK6E,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EACnC,IAAIyB,IAAI,CAACnC,OAAO,CAACoC,SAAS,CAAC,CAACC,kBAAkB,CAAC,OAAO;gBAAC;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD,CAAC;cAAA,GAdEd,OAAO,CAACwB,EAAE;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAef,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA/E,SAAS,KAAK,UAAU,iBACvBH,OAAA;QAAK6E,SAAS,EAAC,4BAA4B;QAAAC,QAAA,gBACzC9E,OAAA;UAAK6E,SAAS,EAAC,oCAAoC;UAAAC,QAAA,eACjD9E,OAAA;YAAI6E,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxE,CAAC,eACNlF,OAAA;UAAK6E,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9B9E,OAAA;YAAO6E,SAAS,EAAC,qCAAqC;YAAAC,QAAA,gBACpD9E,OAAA;cAAO6E,SAAS,EAAC,YAAY;cAAAC,QAAA,eAC3B9E,OAAA;gBAAA8E,QAAA,gBACE9E,OAAA;kBAAI6E,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLlF,OAAA;kBAAI6E,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLlF,OAAA;kBAAI6E,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLlF,OAAA;kBAAI6E,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLlF,OAAA;kBAAI6E,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLlF,OAAA;kBAAI6E,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACRlF,OAAA;cAAO6E,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EACjDlE,QAAQ,CAACkF,GAAG,CAAE1B,OAAO,iBACpBpE,OAAA;gBAAA8E,QAAA,gBACE9E,OAAA;kBAAI6E,SAAS,EAAC,+DAA+D;kBAAAC,QAAA,GAAC,GAC3E,EAACV,OAAO,CAACwB,EAAE,CAACS,KAAK,CAAC,CAAC,CAAC,CAAC;gBAAA;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB,CAAC,eACLlF,OAAA;kBAAI6E,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,EAC9DH,cAAc,CAACP,OAAO,CAACE,MAAM;gBAAC;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B,CAAC,eACLlF,OAAA;kBAAI6E,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,eACzC9E,OAAA,CAACL,aAAa;oBAAC0E,MAAM,EAAED,OAAO,CAACC,MAAO;oBAACiC,IAAI,EAAC;kBAAI;oBAAAvB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjD,CAAC,eACLlF,OAAA;kBAAI6E,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,EAC9DV,OAAO,CAACsC;gBAAa;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB,CAAC,eACLlF,OAAA;kBAAI6E,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,EAC9D,IAAIyB,IAAI,CAACnC,OAAO,CAACoC,SAAS,CAAC,CAACC,kBAAkB,CAAC,OAAO;gBAAC;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD,CAAC,eACLlF,OAAA;kBAAI6E,SAAS,EAAC,iDAAiD;kBAAAC,QAAA,EAC5DlF,iBAAiB,CAAC+G,SAAS,CAACvC,OAAO,CAACC,MAAM,CAAC,iBAC1CrE,OAAA;oBACE2F,OAAO,EAAEA,CAAA,KAAMpB,mBAAmB,CAACH,OAAO,CAACwB,EAAE,CAAE;oBAC/Cf,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAC5C;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBACT;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA,GAzBEd,OAAO,CAACwB,EAAE;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA0Bf,CACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA/E,SAAS,KAAK,QAAQ,iBACrBH,OAAA;QAAK6E,SAAS,EAAC,4BAA4B;QAAAC,QAAA,gBACzC9E,OAAA;UAAK6E,SAAS,EAAC,oCAAoC;UAAAC,QAAA,eACjD9E,OAAA;YAAI6E,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrE,CAAC,eACNlF,OAAA;UAAK6E,SAAS,EAAC,KAAK;UAAAC,QAAA,eAClB9E,OAAA;YAAK6E,SAAS,EAAC,WAAW;YAAAC,QAAA,EACvBhE,MAAM,CAACgF,GAAG,CAAEc,KAAK,iBAChB5G,OAAA;cAAoB6E,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACnE9E,OAAA;gBAAK6E,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,gBACrD9E,OAAA;kBAAI6E,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,GAAC,UACxC,EAAC8B,KAAK,CAAChB,EAAE,CAACS,KAAK,CAAC,CAAC,CAAC,CAAC;gBAAA;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC,eACLlF,OAAA;kBAAM6E,SAAS,EAAE,8CACf+B,KAAK,CAACvC,MAAM,KAAK,WAAW,GAAG,6BAA6B,GAC5DuC,KAAK,CAACvC,MAAM,KAAK,aAAa,GAAG,2BAA2B,GAC5DuC,KAAK,CAACvC,MAAM,KAAK,OAAO,GAAG,+BAA+B,GAC1D,2BAA2B,EAC1B;kBAAAS,QAAA,EACA8B,KAAK,CAACvC;gBAAM;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNlF,OAAA;gBAAK6E,SAAS,EAAC,gCAAgC;gBAAAC,QAAA,gBAC7C9E,OAAA;kBAAA8E,QAAA,gBACE9E,OAAA;oBAAM6E,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC7ClF,OAAA;oBAAM6E,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,EAAEH,cAAc,CAACiC,KAAK,CAACC,WAAW;kBAAC;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1E,CAAC,eACNlF,OAAA;kBAAA8E,QAAA,gBACE9E,OAAA;oBAAM6E,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACjDlF,OAAA;oBAAM6E,SAAS,EAAE,oBACf+B,KAAK,CAACE,aAAa,KAAK,MAAM,GAAG,gBAAgB,GACjDF,KAAK,CAACE,aAAa,KAAK,SAAS,GAAG,iBAAiB,GACrD,cAAc,EACb;oBAAAhC,QAAA,EACA8B,KAAK,CAACE;kBAAa;oBAAA/B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACNlF,OAAA;kBAAA8E,QAAA,gBACE9E,OAAA;oBAAM6E,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC5ClF,OAAA;oBAAM6E,SAAS,EAAC,MAAM;oBAAAC,QAAA,EAAE,IAAIyB,IAAI,CAACK,KAAK,CAACJ,SAAS,CAAC,CAACC,kBAAkB,CAAC,OAAO;kBAAC;oBAAA1B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClF,CAAC,eACNlF,OAAA;kBAAA8E,QAAA,gBACE9E,OAAA;oBAAM6E,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC7ClF,OAAA;oBAAM6E,SAAS,EAAC,MAAM;oBAAAC,QAAA,EAAE8B,KAAK,CAACG,SAAS,CAACC;kBAAM;oBAAAjC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA,GArCE0B,KAAK,CAAChB,EAAE;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAsCb,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAChF,EAAA,CA9eID,UAAoB;AAAAgH,EAAA,GAApBhH,UAAoB;AAgf1B,eAAeA,UAAU;AAAC,IAAAgH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}