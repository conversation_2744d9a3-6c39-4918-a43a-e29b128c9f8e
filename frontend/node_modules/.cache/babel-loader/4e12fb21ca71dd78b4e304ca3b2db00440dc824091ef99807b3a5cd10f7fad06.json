{"ast": null, "code": "import React,{useEffect}from'react';import HeroSection from'../components/landing/HeroSection';import ServicesSection from'../components/landing/ServicesSection';import HowItWorksSection from'../components/landing/HowItWorksSection';import BenefitsSection from'../components/landing/BenefitsSection';import CTASection from'../components/landing/CTASection';import Footer from'../components/landing/Footer';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const LandingPage=()=>{useEffect(()=>{// Set page title\ndocument.title='WePrint - Impressão Digital de Qualidade em Angola';// Add meta description\nconst metaDescription=document.querySelector('meta[name=\"description\"]');if(metaDescription){metaDescription.setAttribute('content','Serviço de impressão digital profissional em Angola. Upload online, qualidade premium, entrega rápida. Documentos, fotos, apresentações e mais.');}else{const meta=document.createElement('meta');meta.name='description';meta.content='Serviço de impressão digital profissional em Angola. Upload online, qualidade premium, entrega rápida. Documentos, fotos, apresentações e mais.';document.head.appendChild(meta);}// Add structured data for SEO\nconst structuredData={\"@context\":\"https://schema.org\",\"@type\":\"LocalBusiness\",\"name\":\"WePrint\",\"description\":\"Serviço de impressão digital profissional em Angola\",\"url\":\"https://weprint.ai\",\"telephone\":\"+244900000000\",\"address\":{\"@type\":\"PostalAddress\",\"addressLocality\":\"Luanda\",\"addressCountry\":\"AO\"},\"openingHours\":\"Mo-Fr 08:00-18:00\",\"priceRange\":\"$$\",\"aggregateRating\":{\"@type\":\"AggregateRating\",\"ratingValue\":\"4.9\",\"reviewCount\":\"150\"}};const script=document.createElement('script');script.type='application/ld+json';script.text=JSON.stringify(structuredData);document.head.appendChild(script);return()=>{// Cleanup\ndocument.head.removeChild(script);};},[]);return/*#__PURE__*/_jsxs(\"div\",{className:\"min-h-screen bg-white\",children:[/*#__PURE__*/_jsx(\"nav\",{className:\"fixed top-0 left-0 right-0 z-50 bg-white bg-opacity-95 backdrop-blur-sm shadow-lg\",children:/*#__PURE__*/_jsx(\"div\",{className:\"container mx-auto px-6 lg:px-8\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between h-16\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"text-2xl font-black text-weprint-black\",children:[\"WE\",/*#__PURE__*/_jsx(\"span\",{className:\"text-weprint-magenta\",children:\"PRINT\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"hidden md:flex items-center space-x-8\",children:[/*#__PURE__*/_jsx(\"a\",{href:\"#services\",className:\"text-gray-700 hover:text-weprint-magenta transition-colors duration-300 font-medium\",onClick:e=>{var _document$getElementB;e.preventDefault();(_document$getElementB=document.getElementById('services'))===null||_document$getElementB===void 0?void 0:_document$getElementB.scrollIntoView({behavior:'smooth'});},children:\"Servi\\xE7os\"}),/*#__PURE__*/_jsx(\"a\",{href:\"#how-it-works\",className:\"text-gray-700 hover:text-weprint-cyan transition-colors duration-300 font-medium\",onClick:e=>{var _document$getElementB2;e.preventDefault();(_document$getElementB2=document.getElementById('how-it-works'))===null||_document$getElementB2===void 0?void 0:_document$getElementB2.scrollIntoView({behavior:'smooth'});},children:\"Como Funciona\"}),/*#__PURE__*/_jsx(\"a\",{href:\"#benefits\",className:\"text-gray-700 hover:text-weprint-yellow transition-colors duration-300 font-medium\",onClick:e=>{var _document$getElementB3;e.preventDefault();(_document$getElementB3=document.getElementById('benefits'))===null||_document$getElementB3===void 0?void 0:_document$getElementB3.scrollIntoView({behavior:'smooth'});},children:\"Vantagens\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>window.location.href='/upload',className:\"bg-weprint-gradient text-white font-bold px-6 py-2 rounded-full hover:shadow-lg transition-all duration-300\",children:\"Imprimir Agora\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"md:hidden\",children:/*#__PURE__*/_jsx(\"button\",{onClick:()=>window.location.href='/upload',className:\"bg-weprint-gradient text-white font-bold px-4 py-2 rounded-full text-sm\",children:\"Imprimir\"})})]})})}),/*#__PURE__*/_jsxs(\"main\",{children:[/*#__PURE__*/_jsx(HeroSection,{}),/*#__PURE__*/_jsx(\"div\",{id:\"services\",children:/*#__PURE__*/_jsx(ServicesSection,{})}),/*#__PURE__*/_jsx(\"div\",{id:\"how-it-works\",children:/*#__PURE__*/_jsx(HowItWorksSection,{})}),/*#__PURE__*/_jsx(\"div\",{id:\"benefits\",children:/*#__PURE__*/_jsx(BenefitsSection,{})}),/*#__PURE__*/_jsx(CTASection,{})]}),/*#__PURE__*/_jsx(Footer,{}),/*#__PURE__*/_jsx(\"div\",{className:\"fixed bottom-6 right-6 z-50\",children:/*#__PURE__*/_jsx(\"button\",{onClick:()=>window.open('https://wa.me/244900000000','_blank'),className:\"w-14 h-14 bg-green-500 text-white rounded-full shadow-2xl hover:shadow-3xl transform hover:scale-110 transition-all duration-300 flex items-center justify-center animate-bounce-gentle\",title:\"Falar no WhatsApp\",children:/*#__PURE__*/_jsx(\"span\",{className:\"text-2xl\",children:\"\\uD83D\\uDCAC\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"fixed bottom-6 left-6 z-50\",children:/*#__PURE__*/_jsx(\"button\",{onClick:()=>window.scrollTo({top:0,behavior:'smooth'}),className:\"w-12 h-12 bg-weprint-magenta text-white rounded-full shadow-lg hover:shadow-xl transform hover:scale-110 transition-all duration-300 flex items-center justify-center opacity-80 hover:opacity-100\",title:\"Voltar ao topo\",children:/*#__PURE__*/_jsx(\"span\",{className:\"text-lg\",children:\"\\u2191\"})})})]});};export default LandingPage;", "map": {"version": 3, "names": ["React", "useEffect", "HeroSection", "ServicesSection", "HowItWorksSection", "BenefitsSection", "CTASection", "Footer", "jsx", "_jsx", "jsxs", "_jsxs", "LandingPage", "document", "title", "metaDescription", "querySelector", "setAttribute", "meta", "createElement", "name", "content", "head", "append<PERSON><PERSON><PERSON>", "structuredData", "script", "type", "text", "JSON", "stringify", "<PERSON><PERSON><PERSON><PERSON>", "className", "children", "href", "onClick", "e", "_document$getElementB", "preventDefault", "getElementById", "scrollIntoView", "behavior", "_document$getElementB2", "_document$getElementB3", "window", "location", "id", "open", "scrollTo", "top"], "sources": ["/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/LandingPage.tsx"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport HeroSection from '../components/landing/HeroSection';\nimport ServicesSection from '../components/landing/ServicesSection';\nimport HowItWorksSection from '../components/landing/HowItWorksSection';\nimport BenefitsSection from '../components/landing/BenefitsSection';\nimport CTASection from '../components/landing/CTASection';\nimport Footer from '../components/landing/Footer';\n\nconst LandingPage: React.FC = () => {\n  useEffect(() => {\n    // Set page title\n    document.title = 'WePrint - Impressão Digital de Qualidade em Angola';\n\n    // Add meta description\n    const metaDescription = document.querySelector('meta[name=\"description\"]');\n    if (metaDescription) {\n      metaDescription.setAttribute('content', 'Serviço de impressão digital profissional em Angola. Upload online, qualidade premium, entrega rápida. Documentos, fotos, apresentações e mais.');\n    } else {\n      const meta = document.createElement('meta');\n      meta.name = 'description';\n      meta.content = 'Serviço de impressão digital profissional em Angola. Upload online, qualidade premium, entrega rápida. Documentos, fotos, apresentações e mais.';\n      document.head.appendChild(meta);\n    }\n\n    // Add structured data for SEO\n    const structuredData = {\n      \"@context\": \"https://schema.org\",\n      \"@type\": \"LocalBusiness\",\n      \"name\": \"WePrint\",\n      \"description\": \"Serviço de impressão digital profissional em Angola\",\n      \"url\": \"https://weprint.ai\",\n      \"telephone\": \"+244900000000\",\n      \"address\": {\n        \"@type\": \"PostalAddress\",\n        \"addressLocality\": \"Luanda\",\n        \"addressCountry\": \"AO\"\n      },\n      \"openingHours\": \"Mo-Fr 08:00-18:00\",\n      \"priceRange\": \"$$\",\n      \"aggregateRating\": {\n        \"@type\": \"AggregateRating\",\n        \"ratingValue\": \"4.9\",\n        \"reviewCount\": \"150\"\n      }\n    };\n\n    const script = document.createElement('script');\n    script.type = 'application/ld+json';\n    script.text = JSON.stringify(structuredData);\n    document.head.appendChild(script);\n\n    return () => {\n      // Cleanup\n      document.head.removeChild(script);\n    };\n  }, []);\n\n  return (\n    <div className=\"min-h-screen bg-white\">\n      {/* Navigation Bar */}\n      <nav className=\"fixed top-0 left-0 right-0 z-50 bg-white bg-opacity-95 backdrop-blur-sm shadow-lg\">\n        <div className=\"container mx-auto px-6 lg:px-8\">\n          <div className=\"flex items-center justify-between h-16\">\n            {/* Logo */}\n            <div className=\"flex items-center\">\n              <div className=\"text-2xl font-black text-weprint-black\">\n                WE<span className=\"text-weprint-magenta\">PRINT</span>\n              </div>\n            </div>\n\n            {/* Navigation Links */}\n            <div className=\"hidden md:flex items-center space-x-8\">\n              <a\n                href=\"#services\"\n                className=\"text-gray-700 hover:text-weprint-magenta transition-colors duration-300 font-medium\"\n                onClick={(e) => {\n                  e.preventDefault();\n                  document.getElementById('services')?.scrollIntoView({ behavior: 'smooth' });\n                }}\n              >\n                Serviços\n              </a>\n              <a\n                href=\"#how-it-works\"\n                className=\"text-gray-700 hover:text-weprint-cyan transition-colors duration-300 font-medium\"\n                onClick={(e) => {\n                  e.preventDefault();\n                  document.getElementById('how-it-works')?.scrollIntoView({ behavior: 'smooth' });\n                }}\n              >\n                Como Funciona\n              </a>\n              <a\n                href=\"#benefits\"\n                className=\"text-gray-700 hover:text-weprint-yellow transition-colors duration-300 font-medium\"\n                onClick={(e) => {\n                  e.preventDefault();\n                  document.getElementById('benefits')?.scrollIntoView({ behavior: 'smooth' });\n                }}\n              >\n                Vantagens\n              </a>\n              <button\n                onClick={() => window.location.href = '/upload'}\n                className=\"bg-weprint-gradient text-white font-bold px-6 py-2 rounded-full hover:shadow-lg transition-all duration-300\"\n              >\n                Imprimir Agora\n              </button>\n            </div>\n\n            {/* Mobile Menu Button */}\n            <div className=\"md:hidden\">\n              <button\n                onClick={() => window.location.href = '/upload'}\n                className=\"bg-weprint-gradient text-white font-bold px-4 py-2 rounded-full text-sm\"\n              >\n                Imprimir\n              </button>\n            </div>\n          </div>\n        </div>\n      </nav>\n\n      {/* Main Content */}\n      <main>\n        <HeroSection />\n\n        <div id=\"services\">\n          <ServicesSection />\n        </div>\n\n        <div id=\"how-it-works\">\n          <HowItWorksSection />\n        </div>\n\n        <div id=\"benefits\">\n          <BenefitsSection />\n        </div>\n\n        <CTASection />\n      </main>\n\n      <Footer />\n\n      {/* Floating WhatsApp Button */}\n      <div className=\"fixed bottom-6 right-6 z-50\">\n        <button\n          onClick={() => window.open('https://wa.me/244900000000', '_blank')}\n          className=\"w-14 h-14 bg-green-500 text-white rounded-full shadow-2xl hover:shadow-3xl transform hover:scale-110 transition-all duration-300 flex items-center justify-center animate-bounce-gentle\"\n          title=\"Falar no WhatsApp\"\n        >\n          <span className=\"text-2xl\">💬</span>\n        </button>\n      </div>\n\n      {/* Scroll to Top Button */}\n      <div className=\"fixed bottom-6 left-6 z-50\">\n        <button\n          onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}\n          className=\"w-12 h-12 bg-weprint-magenta text-white rounded-full shadow-lg hover:shadow-xl transform hover:scale-110 transition-all duration-300 flex items-center justify-center opacity-80 hover:opacity-100\"\n          title=\"Voltar ao topo\"\n        >\n          <span className=\"text-lg\">↑</span>\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default LandingPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,KAAQ,OAAO,CACxC,MAAO,CAAAC,WAAW,KAAM,mCAAmC,CAC3D,MAAO,CAAAC,eAAe,KAAM,uCAAuC,CACnE,MAAO,CAAAC,iBAAiB,KAAM,yCAAyC,CACvE,MAAO,CAAAC,eAAe,KAAM,uCAAuC,CACnE,MAAO,CAAAC,UAAU,KAAM,kCAAkC,CACzD,MAAO,CAAAC,MAAM,KAAM,8BAA8B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAElD,KAAM,CAAAC,WAAqB,CAAGA,CAAA,GAAM,CAClCX,SAAS,CAAC,IAAM,CACd;AACAY,QAAQ,CAACC,KAAK,CAAG,oDAAoD,CAErE;AACA,KAAM,CAAAC,eAAe,CAAGF,QAAQ,CAACG,aAAa,CAAC,0BAA0B,CAAC,CAC1E,GAAID,eAAe,CAAE,CACnBA,eAAe,CAACE,YAAY,CAAC,SAAS,CAAE,iJAAiJ,CAAC,CAC5L,CAAC,IAAM,CACL,KAAM,CAAAC,IAAI,CAAGL,QAAQ,CAACM,aAAa,CAAC,MAAM,CAAC,CAC3CD,IAAI,CAACE,IAAI,CAAG,aAAa,CACzBF,IAAI,CAACG,OAAO,CAAG,iJAAiJ,CAChKR,QAAQ,CAACS,IAAI,CAACC,WAAW,CAACL,IAAI,CAAC,CACjC,CAEA;AACA,KAAM,CAAAM,cAAc,CAAG,CACrB,UAAU,CAAE,oBAAoB,CAChC,OAAO,CAAE,eAAe,CACxB,MAAM,CAAE,SAAS,CACjB,aAAa,CAAE,qDAAqD,CACpE,KAAK,CAAE,oBAAoB,CAC3B,WAAW,CAAE,eAAe,CAC5B,SAAS,CAAE,CACT,OAAO,CAAE,eAAe,CACxB,iBAAiB,CAAE,QAAQ,CAC3B,gBAAgB,CAAE,IACpB,CAAC,CACD,cAAc,CAAE,mBAAmB,CACnC,YAAY,CAAE,IAAI,CAClB,iBAAiB,CAAE,CACjB,OAAO,CAAE,iBAAiB,CAC1B,aAAa,CAAE,KAAK,CACpB,aAAa,CAAE,KACjB,CACF,CAAC,CAED,KAAM,CAAAC,MAAM,CAAGZ,QAAQ,CAACM,aAAa,CAAC,QAAQ,CAAC,CAC/CM,MAAM,CAACC,IAAI,CAAG,qBAAqB,CACnCD,MAAM,CAACE,IAAI,CAAGC,IAAI,CAACC,SAAS,CAACL,cAAc,CAAC,CAC5CX,QAAQ,CAACS,IAAI,CAACC,WAAW,CAACE,MAAM,CAAC,CAEjC,MAAO,IAAM,CACX;AACAZ,QAAQ,CAACS,IAAI,CAACQ,WAAW,CAACL,MAAM,CAAC,CACnC,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAEN,mBACEd,KAAA,QAAKoB,SAAS,CAAC,uBAAuB,CAAAC,QAAA,eAEpCvB,IAAA,QAAKsB,SAAS,CAAC,mFAAmF,CAAAC,QAAA,cAChGvB,IAAA,QAAKsB,SAAS,CAAC,gCAAgC,CAAAC,QAAA,cAC7CrB,KAAA,QAAKoB,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eAErDvB,IAAA,QAAKsB,SAAS,CAAC,mBAAmB,CAAAC,QAAA,cAChCrB,KAAA,QAAKoB,SAAS,CAAC,wCAAwC,CAAAC,QAAA,EAAC,IACpD,cAAAvB,IAAA,SAAMsB,SAAS,CAAC,sBAAsB,CAAAC,QAAA,CAAC,OAAK,CAAM,CAAC,EAClD,CAAC,CACH,CAAC,cAGNrB,KAAA,QAAKoB,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eACpDvB,IAAA,MACEwB,IAAI,CAAC,WAAW,CAChBF,SAAS,CAAC,qFAAqF,CAC/FG,OAAO,CAAGC,CAAC,EAAK,KAAAC,qBAAA,CACdD,CAAC,CAACE,cAAc,CAAC,CAAC,CAClB,CAAAD,qBAAA,CAAAvB,QAAQ,CAACyB,cAAc,CAAC,UAAU,CAAC,UAAAF,qBAAA,iBAAnCA,qBAAA,CAAqCG,cAAc,CAAC,CAAEC,QAAQ,CAAE,QAAS,CAAC,CAAC,CAC7E,CAAE,CAAAR,QAAA,CACH,aAED,CAAG,CAAC,cACJvB,IAAA,MACEwB,IAAI,CAAC,eAAe,CACpBF,SAAS,CAAC,kFAAkF,CAC5FG,OAAO,CAAGC,CAAC,EAAK,KAAAM,sBAAA,CACdN,CAAC,CAACE,cAAc,CAAC,CAAC,CAClB,CAAAI,sBAAA,CAAA5B,QAAQ,CAACyB,cAAc,CAAC,cAAc,CAAC,UAAAG,sBAAA,iBAAvCA,sBAAA,CAAyCF,cAAc,CAAC,CAAEC,QAAQ,CAAE,QAAS,CAAC,CAAC,CACjF,CAAE,CAAAR,QAAA,CACH,eAED,CAAG,CAAC,cACJvB,IAAA,MACEwB,IAAI,CAAC,WAAW,CAChBF,SAAS,CAAC,oFAAoF,CAC9FG,OAAO,CAAGC,CAAC,EAAK,KAAAO,sBAAA,CACdP,CAAC,CAACE,cAAc,CAAC,CAAC,CAClB,CAAAK,sBAAA,CAAA7B,QAAQ,CAACyB,cAAc,CAAC,UAAU,CAAC,UAAAI,sBAAA,iBAAnCA,sBAAA,CAAqCH,cAAc,CAAC,CAAEC,QAAQ,CAAE,QAAS,CAAC,CAAC,CAC7E,CAAE,CAAAR,QAAA,CACH,WAED,CAAG,CAAC,cACJvB,IAAA,WACEyB,OAAO,CAAEA,CAAA,GAAMS,MAAM,CAACC,QAAQ,CAACX,IAAI,CAAG,SAAU,CAChDF,SAAS,CAAC,6GAA6G,CAAAC,QAAA,CACxH,gBAED,CAAQ,CAAC,EACN,CAAC,cAGNvB,IAAA,QAAKsB,SAAS,CAAC,WAAW,CAAAC,QAAA,cACxBvB,IAAA,WACEyB,OAAO,CAAEA,CAAA,GAAMS,MAAM,CAACC,QAAQ,CAACX,IAAI,CAAG,SAAU,CAChDF,SAAS,CAAC,yEAAyE,CAAAC,QAAA,CACpF,UAED,CAAQ,CAAC,CACN,CAAC,EACH,CAAC,CACH,CAAC,CACH,CAAC,cAGNrB,KAAA,SAAAqB,QAAA,eACEvB,IAAA,CAACP,WAAW,GAAE,CAAC,cAEfO,IAAA,QAAKoC,EAAE,CAAC,UAAU,CAAAb,QAAA,cAChBvB,IAAA,CAACN,eAAe,GAAE,CAAC,CAChB,CAAC,cAENM,IAAA,QAAKoC,EAAE,CAAC,cAAc,CAAAb,QAAA,cACpBvB,IAAA,CAACL,iBAAiB,GAAE,CAAC,CAClB,CAAC,cAENK,IAAA,QAAKoC,EAAE,CAAC,UAAU,CAAAb,QAAA,cAChBvB,IAAA,CAACJ,eAAe,GAAE,CAAC,CAChB,CAAC,cAENI,IAAA,CAACH,UAAU,GAAE,CAAC,EACV,CAAC,cAEPG,IAAA,CAACF,MAAM,GAAE,CAAC,cAGVE,IAAA,QAAKsB,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cAC1CvB,IAAA,WACEyB,OAAO,CAAEA,CAAA,GAAMS,MAAM,CAACG,IAAI,CAAC,4BAA4B,CAAE,QAAQ,CAAE,CACnEf,SAAS,CAAC,yLAAyL,CACnMjB,KAAK,CAAC,mBAAmB,CAAAkB,QAAA,cAEzBvB,IAAA,SAAMsB,SAAS,CAAC,UAAU,CAAAC,QAAA,CAAC,cAAE,CAAM,CAAC,CAC9B,CAAC,CACN,CAAC,cAGNvB,IAAA,QAAKsB,SAAS,CAAC,4BAA4B,CAAAC,QAAA,cACzCvB,IAAA,WACEyB,OAAO,CAAEA,CAAA,GAAMS,MAAM,CAACI,QAAQ,CAAC,CAAEC,GAAG,CAAE,CAAC,CAAER,QAAQ,CAAE,QAAS,CAAC,CAAE,CAC/DT,SAAS,CAAC,oMAAoM,CAC9MjB,KAAK,CAAC,gBAAgB,CAAAkB,QAAA,cAEtBvB,IAAA,SAAMsB,SAAS,CAAC,SAAS,CAAAC,QAAA,CAAC,QAAC,CAAM,CAAC,CAC5B,CAAC,CACN,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAApB,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}