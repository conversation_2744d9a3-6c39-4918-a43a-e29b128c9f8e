{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/OrderSummary.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { Layout, Button } from '../components';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst OrderSummary = () => {\n  _s();\n  const navigate = useNavigate();\n  const [fileInfo, setFileInfo] = useState(null);\n  const [printOptions, setPrintOptions] = useState(null);\n  const [priceBreakdown, setPriceBreakdown] = useState(null);\n  useEffect(() => {\n    // Get data from localStorage\n    const file = localStorage.getItem('uploadedFile');\n    const options = localStorage.getItem('printOptions');\n    const price = localStorage.getItem('priceBreakdown');\n    if (!file || !options || !price) {\n      // Redirect to upload if missing data\n      navigate('/upload');\n      return;\n    }\n    setFileInfo(JSON.parse(file));\n    setPrintOptions(JSON.parse(options));\n    setPriceBreakdown(JSON.parse(price));\n  }, [navigate]);\n  const formatPrice = price => {\n    return `${price.toFixed(2)} AOA`;\n  };\n  const getFormatDisplayName = format => {\n    const formats = {\n      'A4': 'A4 (210 × 297 mm)',\n      'A3': 'A3 (297 × 420 mm)',\n      'A5': 'A5 (148 × 210 mm)',\n      'Letter': 'Letter (216 × 279 mm)'\n    };\n    return formats[format] || format;\n  };\n  const getPaperDisplayName = paperType => {\n    const papers = {\n      'standard': 'Papel Standard (75g/m²)',\n      'premium': 'Papel Premium (90g/m²)',\n      'photo': 'Papel Fotográfico (200g/m²)',\n      'cardstock': 'Cartolina (250g/m²)'\n    };\n    return papers[paperType] || paperType;\n  };\n  const getFinishDisplayName = finish => {\n    const finishes = {\n      'none': 'Sem Acabamento',\n      'glossy': 'Brilhante',\n      'matte': 'Fosco',\n      'laminated': 'Plastificado'\n    };\n    return finishes[finish] || finish;\n  };\n  const getComplexityDisplayName = complexity => {\n    const complexities = {\n      'low': 'Baixa (Texto simples)',\n      'medium': 'Média (Texto + Imagens)',\n      'high': 'Alta (Design complexo)'\n    };\n    return complexities[complexity] || complexity;\n  };\n  const handleContinue = () => {\n    navigate('/checkout');\n  };\n  const handleEditOptions = () => {\n    navigate('/options');\n  };\n  if (!fileInfo || !printOptions || !priceBreakdown) {\n    return /*#__PURE__*/_jsxDEV(Layout, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-center min-h-screen\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: \"Carregando resumo...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Layout, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-4xl mx-auto py-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold text-gray-900 mb-4\",\n          children: \"Resumo do Pedido\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-gray-600\",\n          children: \"Revise os detalhes antes de finalizar\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-lg p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-semibold text-gray-900 mb-6\",\n            children: \"Arquivo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-4 mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center\",\n              children: fileInfo.type.startsWith('image/') ? /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-8 h-8 text-blue-600\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 20 20\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  fillRule: \"evenodd\",\n                  d: \"M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z\",\n                  clipRule: \"evenodd\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 140,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 139,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-8 h-8 text-blue-600\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 20 20\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  fillRule: \"evenodd\",\n                  d: \"M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z\",\n                  clipRule: \"evenodd\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 144,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 143,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"font-medium text-gray-900\",\n                children: fileInfo.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 150,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600\",\n                children: fileInfo.size\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 151,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-gray-600\",\n                children: \"Formato:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 157,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium\",\n                children: getFormatDisplayName(printOptions.format)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 158,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-gray-600\",\n                children: \"Papel:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium\",\n                children: getPaperDisplayName(printOptions.paperType)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 163,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-gray-600\",\n                children: \"Acabamento:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium\",\n                children: getFinishDisplayName(printOptions.finish)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-gray-600\",\n                children: \"C\\xF3pias:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium\",\n                children: printOptions.copies\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-gray-600\",\n                children: \"Cores:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 177,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium\",\n                children: printOptions.hasColor ? 'Colorido' : 'Preto e Branco'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 178,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-gray-600\",\n                children: \"Complexidade:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium\",\n                children: getComplexityDisplayName(printOptions.complexity)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 15\n            }, this), printOptions.notes && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"pt-3 border-t\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-gray-600 block mb-1\",\n                children: \"Observa\\xE7\\xF5es:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-800 bg-gray-50 p-3 rounded\",\n                children: printOptions.notes\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outline\",\n            onClick: handleEditOptions,\n            className: \"w-full mt-6\",\n            children: \"Editar Op\\xE7\\xF5es\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-lg p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-semibold text-gray-900 mb-6\",\n            children: \"Detalhes do Pre\\xE7o\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-gray-600\",\n                children: [\"Pre\\xE7o base (\", printOptions.format, \"):\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 215,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: formatPrice(priceBreakdown.basePrice)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 216,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 15\n            }, this), priceBreakdown.paperCost > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-gray-600\",\n                children: \"Papel premium:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-green-600\",\n                children: [\"+\", formatPrice(priceBreakdown.paperCost)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 222,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 17\n            }, this), priceBreakdown.finishCost > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-gray-600\",\n                children: \"Acabamento:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-green-600\",\n                children: [\"+\", formatPrice(priceBreakdown.finishCost)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 17\n            }, this), priceBreakdown.complexityCost > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-gray-600\",\n                children: \"Complexidade:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-green-600\",\n                children: [\"+\", formatPrice(priceBreakdown.complexityCost)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 17\n            }, this), priceBreakdown.colorCost > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-gray-600\",\n                children: \"Impress\\xE3o a cores:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 242,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-green-600\",\n                children: [\"+\", formatPrice(priceBreakdown.colorCost)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-gray-600\",\n                children: \"Quantidade:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [printOptions.copies, \" c\\xF3pia(s)\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 249,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n              className: \"my-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between text-xl font-bold\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Total:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 255,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-blue-600\",\n                children: formatPrice(priceBreakdown.total)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-6 p-4 bg-green-50 rounded-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"font-medium text-green-900 mb-2\",\n              children: \"\\u2705 Inclu\\xEDdo no Pre\\xE7o\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 261,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              className: \"text-sm text-green-800 space-y-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\u2022 Entrega gr\\xE1tis em Luanda\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\u2022 Embalagem protectora\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\u2022 Garantia de qualidade\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\u2022 Suporte t\\xE9cnico\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-4 p-4 bg-blue-50 rounded-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"font-medium text-blue-900 mb-2\",\n              children: \"\\uD83D\\uDCC5 Prazo de Entrega\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-blue-800\",\n              children: printOptions.complexity === 'high' ? '4-5 dias úteis' : printOptions.complexity === 'medium' ? '2-3 dias úteis' : '1-2 dias úteis'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 270,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between mt-8\",\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outline\",\n          onClick: () => navigate('/options'),\n          children: \"Voltar \\xE0s Op\\xE7\\xF5es\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 282,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleContinue,\n          size: \"lg\",\n          children: \"Finalizar Pedido\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 289,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 281,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 119,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 118,\n    columnNumber: 5\n  }, this);\n};\n_s(OrderSummary, \"1/0fTf9eclK/XCOYiT58QA5XOHA=\", false, function () {\n  return [useNavigate];\n});\n_c = OrderSummary;\nexport default OrderSummary;\nvar _c;\n$RefreshReg$(_c, \"OrderSummary\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "Layout", "<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "OrderSummary", "_s", "navigate", "fileInfo", "setFileInfo", "printOptions", "setPrintOptions", "priceBreakdown", "setPriceBreakdown", "file", "localStorage", "getItem", "options", "price", "JSON", "parse", "formatPrice", "toFixed", "getFormatDisplayName", "format", "formats", "getPaperDisplayName", "paperType", "papers", "getFinishDisplayName", "finish", "finishes", "getComplexityDisplayName", "complexity", "complexities", "handleContinue", "handleEditOptions", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "startsWith", "fill", "viewBox", "fillRule", "d", "clipRule", "name", "size", "copies", "hasColor", "notes", "variant", "onClick", "basePrice", "paperCost", "finishCost", "complexityCost", "colorCost", "total", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/OrderSummary.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { Layout, Button } from '../components';\n\ninterface FileInfo {\n  name: string;\n  size: string;\n  type: string;\n}\n\ninterface PrintOptions {\n  format: string;\n  paperType: string;\n  finish: string;\n  copies: number;\n  hasColor: boolean;\n  complexity: 'low' | 'medium' | 'high';\n  notes: string;\n}\n\ninterface PriceBreakdown {\n  basePrice: number;\n  paperCost: number;\n  finishCost: number;\n  complexityCost: number;\n  colorCost: number;\n  total: number;\n}\n\nconst OrderSummary: React.FC = () => {\n  const navigate = useNavigate();\n  const [fileInfo, setFileInfo] = useState<FileInfo | null>(null);\n  const [printOptions, setPrintOptions] = useState<PrintOptions | null>(null);\n  const [priceBreakdown, setPriceBreakdown] = useState<PriceBreakdown | null>(null);\n\n  useEffect(() => {\n    // Get data from localStorage\n    const file = localStorage.getItem('uploadedFile');\n    const options = localStorage.getItem('printOptions');\n    const price = localStorage.getItem('priceBreakdown');\n\n    if (!file || !options || !price) {\n      // Redirect to upload if missing data\n      navigate('/upload');\n      return;\n    }\n\n    setFileInfo(JSON.parse(file));\n    setPrintOptions(JSON.parse(options));\n    setPriceBreakdown(JSON.parse(price));\n  }, [navigate]);\n\n  const formatPrice = (price: number) => {\n    return `${price.toFixed(2)} AOA`;\n  };\n\n  const getFormatDisplayName = (format: string) => {\n    const formats: { [key: string]: string } = {\n      'A4': 'A4 (210 × 297 mm)',\n      'A3': 'A3 (297 × 420 mm)',\n      'A5': 'A5 (148 × 210 mm)',\n      'Letter': 'Letter (216 × 279 mm)',\n    };\n    return formats[format] || format;\n  };\n\n  const getPaperDisplayName = (paperType: string) => {\n    const papers: { [key: string]: string } = {\n      'standard': 'Papel Standard (75g/m²)',\n      'premium': 'Papel Premium (90g/m²)',\n      'photo': 'Papel Fotográfico (200g/m²)',\n      'cardstock': 'Cartolina (250g/m²)',\n    };\n    return papers[paperType] || paperType;\n  };\n\n  const getFinishDisplayName = (finish: string) => {\n    const finishes: { [key: string]: string } = {\n      'none': 'Sem Acabamento',\n      'glossy': 'Brilhante',\n      'matte': 'Fosco',\n      'laminated': 'Plastificado',\n    };\n    return finishes[finish] || finish;\n  };\n\n  const getComplexityDisplayName = (complexity: string) => {\n    const complexities: { [key: string]: string } = {\n      'low': 'Baixa (Texto simples)',\n      'medium': 'Média (Texto + Imagens)',\n      'high': 'Alta (Design complexo)',\n    };\n    return complexities[complexity] || complexity;\n  };\n\n  const handleContinue = () => {\n    navigate('/checkout');\n  };\n\n  const handleEditOptions = () => {\n    navigate('/options');\n  };\n\n  if (!fileInfo || !printOptions || !priceBreakdown) {\n    return (\n      <Layout>\n        <div className=\"flex items-center justify-center min-h-screen\">\n          <div className=\"text-center\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n            <p className=\"text-gray-600\">Carregando resumo...</p>\n          </div>\n        </div>\n      </Layout>\n    );\n  }\n\n  return (\n    <Layout>\n      <div className=\"max-w-4xl mx-auto py-8\">\n        <div className=\"text-center mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-4\">\n            Resumo do Pedido\n          </h1>\n          <p className=\"text-lg text-gray-600\">\n            Revise os detalhes antes de finalizar\n          </p>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n          {/* File Information */}\n          <div className=\"bg-white rounded-lg shadow-lg p-6\">\n            <h2 className=\"text-xl font-semibold text-gray-900 mb-6\">\n              Arquivo\n            </h2>\n\n            <div className=\"flex items-center space-x-4 mb-6\">\n              <div className=\"w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center\">\n                {fileInfo.type.startsWith('image/') ? (\n                  <svg className=\"w-8 h-8 text-blue-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z\" clipRule=\"evenodd\" />\n                  </svg>\n                ) : (\n                  <svg className=\"w-8 h-8 text-blue-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z\" clipRule=\"evenodd\" />\n                  </svg>\n                )}\n              </div>\n\n              <div className=\"flex-1\">\n                <p className=\"font-medium text-gray-900\">{fileInfo.name}</p>\n                <p className=\"text-sm text-gray-600\">{fileInfo.size}</p>\n              </div>\n            </div>\n\n            <div className=\"space-y-3\">\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600\">Formato:</span>\n                <span className=\"font-medium\">{getFormatDisplayName(printOptions.format)}</span>\n              </div>\n\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600\">Papel:</span>\n                <span className=\"font-medium\">{getPaperDisplayName(printOptions.paperType)}</span>\n              </div>\n\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600\">Acabamento:</span>\n                <span className=\"font-medium\">{getFinishDisplayName(printOptions.finish)}</span>\n              </div>\n\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600\">Cópias:</span>\n                <span className=\"font-medium\">{printOptions.copies}</span>\n              </div>\n\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600\">Cores:</span>\n                <span className=\"font-medium\">\n                  {printOptions.hasColor ? 'Colorido' : 'Preto e Branco'}\n                </span>\n              </div>\n\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600\">Complexidade:</span>\n                <span className=\"font-medium\">{getComplexityDisplayName(printOptions.complexity)}</span>\n              </div>\n\n              {printOptions.notes && (\n                <div className=\"pt-3 border-t\">\n                  <span className=\"text-gray-600 block mb-1\">Observações:</span>\n                  <p className=\"text-sm text-gray-800 bg-gray-50 p-3 rounded\">\n                    {printOptions.notes}\n                  </p>\n                </div>\n              )}\n            </div>\n\n            <Button\n              variant=\"outline\"\n              onClick={handleEditOptions}\n              className=\"w-full mt-6\"\n            >\n              Editar Opções\n            </Button>\n          </div>\n\n          {/* Price Breakdown */}\n          <div className=\"bg-white rounded-lg shadow-lg p-6\">\n            <h2 className=\"text-xl font-semibold text-gray-900 mb-6\">\n              Detalhes do Preço\n            </h2>\n\n            <div className=\"space-y-4\">\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600\">Preço base ({printOptions.format}):</span>\n                <span>{formatPrice(priceBreakdown.basePrice)}</span>\n              </div>\n\n              {priceBreakdown.paperCost > 0 && (\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-600\">Papel premium:</span>\n                  <span className=\"text-green-600\">+{formatPrice(priceBreakdown.paperCost)}</span>\n                </div>\n              )}\n\n              {priceBreakdown.finishCost > 0 && (\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-600\">Acabamento:</span>\n                  <span className=\"text-green-600\">+{formatPrice(priceBreakdown.finishCost)}</span>\n                </div>\n              )}\n\n              {priceBreakdown.complexityCost > 0 && (\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-600\">Complexidade:</span>\n                  <span className=\"text-green-600\">+{formatPrice(priceBreakdown.complexityCost)}</span>\n                </div>\n              )}\n\n              {priceBreakdown.colorCost > 0 && (\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-600\">Impressão a cores:</span>\n                  <span className=\"text-green-600\">+{formatPrice(priceBreakdown.colorCost)}</span>\n                </div>\n              )}\n\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600\">Quantidade:</span>\n                <span>{printOptions.copies} cópia(s)</span>\n              </div>\n\n              <hr className=\"my-4\" />\n\n              <div className=\"flex justify-between text-xl font-bold\">\n                <span>Total:</span>\n                <span className=\"text-blue-600\">{formatPrice(priceBreakdown.total)}</span>\n              </div>\n            </div>\n\n            <div className=\"mt-6 p-4 bg-green-50 rounded-lg\">\n              <h3 className=\"font-medium text-green-900 mb-2\">✅ Incluído no Preço</h3>\n              <ul className=\"text-sm text-green-800 space-y-1\">\n                <li>• Entrega grátis em Luanda</li>\n                <li>• Embalagem protectora</li>\n                <li>• Garantia de qualidade</li>\n                <li>• Suporte técnico</li>\n              </ul>\n            </div>\n\n            <div className=\"mt-4 p-4 bg-blue-50 rounded-lg\">\n              <h3 className=\"font-medium text-blue-900 mb-2\">📅 Prazo de Entrega</h3>\n              <p className=\"text-sm text-blue-800\">\n                {printOptions.complexity === 'high' ? '4-5 dias úteis' :\n                 printOptions.complexity === 'medium' ? '2-3 dias úteis' :\n                 '1-2 dias úteis'}\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"flex justify-between mt-8\">\n          <Button\n            variant=\"outline\"\n            onClick={() => navigate('/options')}\n          >\n            Voltar às Opções\n          </Button>\n\n          <Button\n            onClick={handleContinue}\n            size=\"lg\"\n          >\n            Finalizar Pedido\n          </Button>\n        </div>\n      </div>\n    </Layout>\n  );\n};\n\nexport default OrderSummary;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,MAAM,EAAEC,MAAM,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AA2B/C,MAAMC,YAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnC,MAAMC,QAAQ,GAAGP,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACQ,QAAQ,EAAEC,WAAW,CAAC,GAAGX,QAAQ,CAAkB,IAAI,CAAC;EAC/D,MAAM,CAACY,YAAY,EAAEC,eAAe,CAAC,GAAGb,QAAQ,CAAsB,IAAI,CAAC;EAC3E,MAAM,CAACc,cAAc,EAAEC,iBAAiB,CAAC,GAAGf,QAAQ,CAAwB,IAAI,CAAC;EAEjFC,SAAS,CAAC,MAAM;IACd;IACA,MAAMe,IAAI,GAAGC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;IACjD,MAAMC,OAAO,GAAGF,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;IACpD,MAAME,KAAK,GAAGH,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;IAEpD,IAAI,CAACF,IAAI,IAAI,CAACG,OAAO,IAAI,CAACC,KAAK,EAAE;MAC/B;MACAX,QAAQ,CAAC,SAAS,CAAC;MACnB;IACF;IAEAE,WAAW,CAACU,IAAI,CAACC,KAAK,CAACN,IAAI,CAAC,CAAC;IAC7BH,eAAe,CAACQ,IAAI,CAACC,KAAK,CAACH,OAAO,CAAC,CAAC;IACpCJ,iBAAiB,CAACM,IAAI,CAACC,KAAK,CAACF,KAAK,CAAC,CAAC;EACtC,CAAC,EAAE,CAACX,QAAQ,CAAC,CAAC;EAEd,MAAMc,WAAW,GAAIH,KAAa,IAAK;IACrC,OAAO,GAAGA,KAAK,CAACI,OAAO,CAAC,CAAC,CAAC,MAAM;EAClC,CAAC;EAED,MAAMC,oBAAoB,GAAIC,MAAc,IAAK;IAC/C,MAAMC,OAAkC,GAAG;MACzC,IAAI,EAAE,mBAAmB;MACzB,IAAI,EAAE,mBAAmB;MACzB,IAAI,EAAE,mBAAmB;MACzB,QAAQ,EAAE;IACZ,CAAC;IACD,OAAOA,OAAO,CAACD,MAAM,CAAC,IAAIA,MAAM;EAClC,CAAC;EAED,MAAME,mBAAmB,GAAIC,SAAiB,IAAK;IACjD,MAAMC,MAAiC,GAAG;MACxC,UAAU,EAAE,yBAAyB;MACrC,SAAS,EAAE,wBAAwB;MACnC,OAAO,EAAE,6BAA6B;MACtC,WAAW,EAAE;IACf,CAAC;IACD,OAAOA,MAAM,CAACD,SAAS,CAAC,IAAIA,SAAS;EACvC,CAAC;EAED,MAAME,oBAAoB,GAAIC,MAAc,IAAK;IAC/C,MAAMC,QAAmC,GAAG;MAC1C,MAAM,EAAE,gBAAgB;MACxB,QAAQ,EAAE,WAAW;MACrB,OAAO,EAAE,OAAO;MAChB,WAAW,EAAE;IACf,CAAC;IACD,OAAOA,QAAQ,CAACD,MAAM,CAAC,IAAIA,MAAM;EACnC,CAAC;EAED,MAAME,wBAAwB,GAAIC,UAAkB,IAAK;IACvD,MAAMC,YAAuC,GAAG;MAC9C,KAAK,EAAE,uBAAuB;MAC9B,QAAQ,EAAE,yBAAyB;MACnC,MAAM,EAAE;IACV,CAAC;IACD,OAAOA,YAAY,CAACD,UAAU,CAAC,IAAIA,UAAU;EAC/C,CAAC;EAED,MAAME,cAAc,GAAGA,CAAA,KAAM;IAC3B5B,QAAQ,CAAC,WAAW,CAAC;EACvB,CAAC;EAED,MAAM6B,iBAAiB,GAAGA,CAAA,KAAM;IAC9B7B,QAAQ,CAAC,UAAU,CAAC;EACtB,CAAC;EAED,IAAI,CAACC,QAAQ,IAAI,CAACE,YAAY,IAAI,CAACE,cAAc,EAAE;IACjD,oBACER,OAAA,CAACH,MAAM;MAAAoC,QAAA,eACLjC,OAAA;QAAKkC,SAAS,EAAC,+CAA+C;QAAAD,QAAA,eAC5DjC,OAAA;UAAKkC,SAAS,EAAC,aAAa;UAAAD,QAAA,gBAC1BjC,OAAA;YAAKkC,SAAS,EAAC;UAA6E;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACnGtC,OAAA;YAAGkC,SAAS,EAAC,eAAe;YAAAD,QAAA,EAAC;UAAoB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAEb;EAEA,oBACEtC,OAAA,CAACH,MAAM;IAAAoC,QAAA,eACLjC,OAAA;MAAKkC,SAAS,EAAC,wBAAwB;MAAAD,QAAA,gBACrCjC,OAAA;QAAKkC,SAAS,EAAC,kBAAkB;QAAAD,QAAA,gBAC/BjC,OAAA;UAAIkC,SAAS,EAAC,uCAAuC;UAAAD,QAAA,EAAC;QAEtD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLtC,OAAA;UAAGkC,SAAS,EAAC,uBAAuB;UAAAD,QAAA,EAAC;QAErC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENtC,OAAA;QAAKkC,SAAS,EAAC,uCAAuC;QAAAD,QAAA,gBAEpDjC,OAAA;UAAKkC,SAAS,EAAC,mCAAmC;UAAAD,QAAA,gBAChDjC,OAAA;YAAIkC,SAAS,EAAC,0CAA0C;YAAAD,QAAA,EAAC;UAEzD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAELtC,OAAA;YAAKkC,SAAS,EAAC,kCAAkC;YAAAD,QAAA,gBAC/CjC,OAAA;cAAKkC,SAAS,EAAC,mEAAmE;cAAAD,QAAA,EAC/E7B,QAAQ,CAACmC,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,gBACjCxC,OAAA;gBAAKkC,SAAS,EAAC,uBAAuB;gBAACO,IAAI,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAAT,QAAA,eAC5EjC,OAAA;kBAAM2C,QAAQ,EAAC,SAAS;kBAACC,CAAC,EAAC,4FAA4F;kBAACC,QAAQ,EAAC;gBAAS;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1I,CAAC,gBAENtC,OAAA;gBAAKkC,SAAS,EAAC,uBAAuB;gBAACO,IAAI,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAAT,QAAA,eAC5EjC,OAAA;kBAAM2C,QAAQ,EAAC,SAAS;kBAACC,CAAC,EAAC,oLAAoL;kBAACC,QAAQ,EAAC;gBAAS;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClO;YACN;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAENtC,OAAA;cAAKkC,SAAS,EAAC,QAAQ;cAAAD,QAAA,gBACrBjC,OAAA;gBAAGkC,SAAS,EAAC,2BAA2B;gBAAAD,QAAA,EAAE7B,QAAQ,CAAC0C;cAAI;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5DtC,OAAA;gBAAGkC,SAAS,EAAC,uBAAuB;gBAAAD,QAAA,EAAE7B,QAAQ,CAAC2C;cAAI;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENtC,OAAA;YAAKkC,SAAS,EAAC,WAAW;YAAAD,QAAA,gBACxBjC,OAAA;cAAKkC,SAAS,EAAC,sBAAsB;cAAAD,QAAA,gBACnCjC,OAAA;gBAAMkC,SAAS,EAAC,eAAe;gBAAAD,QAAA,EAAC;cAAQ;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC/CtC,OAAA;gBAAMkC,SAAS,EAAC,aAAa;gBAAAD,QAAA,EAAEd,oBAAoB,CAACb,YAAY,CAACc,MAAM;cAAC;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7E,CAAC,eAENtC,OAAA;cAAKkC,SAAS,EAAC,sBAAsB;cAAAD,QAAA,gBACnCjC,OAAA;gBAAMkC,SAAS,EAAC,eAAe;gBAAAD,QAAA,EAAC;cAAM;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC7CtC,OAAA;gBAAMkC,SAAS,EAAC,aAAa;gBAAAD,QAAA,EAAEX,mBAAmB,CAAChB,YAAY,CAACiB,SAAS;cAAC;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/E,CAAC,eAENtC,OAAA;cAAKkC,SAAS,EAAC,sBAAsB;cAAAD,QAAA,gBACnCjC,OAAA;gBAAMkC,SAAS,EAAC,eAAe;gBAAAD,QAAA,EAAC;cAAW;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAClDtC,OAAA;gBAAMkC,SAAS,EAAC,aAAa;gBAAAD,QAAA,EAAER,oBAAoB,CAACnB,YAAY,CAACoB,MAAM;cAAC;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7E,CAAC,eAENtC,OAAA;cAAKkC,SAAS,EAAC,sBAAsB;cAAAD,QAAA,gBACnCjC,OAAA;gBAAMkC,SAAS,EAAC,eAAe;gBAAAD,QAAA,EAAC;cAAO;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC9CtC,OAAA;gBAAMkC,SAAS,EAAC,aAAa;gBAAAD,QAAA,EAAE3B,YAAY,CAAC0C;cAAM;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC,eAENtC,OAAA;cAAKkC,SAAS,EAAC,sBAAsB;cAAAD,QAAA,gBACnCjC,OAAA;gBAAMkC,SAAS,EAAC,eAAe;gBAAAD,QAAA,EAAC;cAAM;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC7CtC,OAAA;gBAAMkC,SAAS,EAAC,aAAa;gBAAAD,QAAA,EAC1B3B,YAAY,CAAC2C,QAAQ,GAAG,UAAU,GAAG;cAAgB;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAENtC,OAAA;cAAKkC,SAAS,EAAC,sBAAsB;cAAAD,QAAA,gBACnCjC,OAAA;gBAAMkC,SAAS,EAAC,eAAe;gBAAAD,QAAA,EAAC;cAAa;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACpDtC,OAAA;gBAAMkC,SAAS,EAAC,aAAa;gBAAAD,QAAA,EAAEL,wBAAwB,CAACtB,YAAY,CAACuB,UAAU;cAAC;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrF,CAAC,EAELhC,YAAY,CAAC4C,KAAK,iBACjBlD,OAAA;cAAKkC,SAAS,EAAC,eAAe;cAAAD,QAAA,gBAC5BjC,OAAA;gBAAMkC,SAAS,EAAC,0BAA0B;gBAAAD,QAAA,EAAC;cAAY;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC9DtC,OAAA;gBAAGkC,SAAS,EAAC,8CAA8C;gBAAAD,QAAA,EACxD3B,YAAY,CAAC4C;cAAK;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAENtC,OAAA,CAACF,MAAM;YACLqD,OAAO,EAAC,SAAS;YACjBC,OAAO,EAAEpB,iBAAkB;YAC3BE,SAAS,EAAC,aAAa;YAAAD,QAAA,EACxB;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGNtC,OAAA;UAAKkC,SAAS,EAAC,mCAAmC;UAAAD,QAAA,gBAChDjC,OAAA;YAAIkC,SAAS,EAAC,0CAA0C;YAAAD,QAAA,EAAC;UAEzD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAELtC,OAAA;YAAKkC,SAAS,EAAC,WAAW;YAAAD,QAAA,gBACxBjC,OAAA;cAAKkC,SAAS,EAAC,sBAAsB;cAAAD,QAAA,gBACnCjC,OAAA;gBAAMkC,SAAS,EAAC,eAAe;gBAAAD,QAAA,GAAC,iBAAY,EAAC3B,YAAY,CAACc,MAAM,EAAC,IAAE;cAAA;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC1EtC,OAAA;gBAAAiC,QAAA,EAAOhB,WAAW,CAACT,cAAc,CAAC6C,SAAS;cAAC;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD,CAAC,EAEL9B,cAAc,CAAC8C,SAAS,GAAG,CAAC,iBAC3BtD,OAAA;cAAKkC,SAAS,EAAC,sBAAsB;cAAAD,QAAA,gBACnCjC,OAAA;gBAAMkC,SAAS,EAAC,eAAe;gBAAAD,QAAA,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACrDtC,OAAA;gBAAMkC,SAAS,EAAC,gBAAgB;gBAAAD,QAAA,GAAC,GAAC,EAAChB,WAAW,CAACT,cAAc,CAAC8C,SAAS,CAAC;cAAA;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7E,CACN,EAEA9B,cAAc,CAAC+C,UAAU,GAAG,CAAC,iBAC5BvD,OAAA;cAAKkC,SAAS,EAAC,sBAAsB;cAAAD,QAAA,gBACnCjC,OAAA;gBAAMkC,SAAS,EAAC,eAAe;gBAAAD,QAAA,EAAC;cAAW;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAClDtC,OAAA;gBAAMkC,SAAS,EAAC,gBAAgB;gBAAAD,QAAA,GAAC,GAAC,EAAChB,WAAW,CAACT,cAAc,CAAC+C,UAAU,CAAC;cAAA;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9E,CACN,EAEA9B,cAAc,CAACgD,cAAc,GAAG,CAAC,iBAChCxD,OAAA;cAAKkC,SAAS,EAAC,sBAAsB;cAAAD,QAAA,gBACnCjC,OAAA;gBAAMkC,SAAS,EAAC,eAAe;gBAAAD,QAAA,EAAC;cAAa;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACpDtC,OAAA;gBAAMkC,SAAS,EAAC,gBAAgB;gBAAAD,QAAA,GAAC,GAAC,EAAChB,WAAW,CAACT,cAAc,CAACgD,cAAc,CAAC;cAAA;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClF,CACN,EAEA9B,cAAc,CAACiD,SAAS,GAAG,CAAC,iBAC3BzD,OAAA;cAAKkC,SAAS,EAAC,sBAAsB;cAAAD,QAAA,gBACnCjC,OAAA;gBAAMkC,SAAS,EAAC,eAAe;gBAAAD,QAAA,EAAC;cAAkB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzDtC,OAAA;gBAAMkC,SAAS,EAAC,gBAAgB;gBAAAD,QAAA,GAAC,GAAC,EAAChB,WAAW,CAACT,cAAc,CAACiD,SAAS,CAAC;cAAA;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7E,CACN,eAEDtC,OAAA;cAAKkC,SAAS,EAAC,sBAAsB;cAAAD,QAAA,gBACnCjC,OAAA;gBAAMkC,SAAS,EAAC,eAAe;gBAAAD,QAAA,EAAC;cAAW;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAClDtC,OAAA;gBAAAiC,QAAA,GAAO3B,YAAY,CAAC0C,MAAM,EAAC,cAAS;cAAA;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC,eAENtC,OAAA;cAAIkC,SAAS,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAEvBtC,OAAA;cAAKkC,SAAS,EAAC,wCAAwC;cAAAD,QAAA,gBACrDjC,OAAA;gBAAAiC,QAAA,EAAM;cAAM;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACnBtC,OAAA;gBAAMkC,SAAS,EAAC,eAAe;gBAAAD,QAAA,EAAEhB,WAAW,CAACT,cAAc,CAACkD,KAAK;cAAC;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENtC,OAAA;YAAKkC,SAAS,EAAC,iCAAiC;YAAAD,QAAA,gBAC9CjC,OAAA;cAAIkC,SAAS,EAAC,iCAAiC;cAAAD,QAAA,EAAC;YAAmB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxEtC,OAAA;cAAIkC,SAAS,EAAC,kCAAkC;cAAAD,QAAA,gBAC9CjC,OAAA;gBAAAiC,QAAA,EAAI;cAA0B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnCtC,OAAA;gBAAAiC,QAAA,EAAI;cAAsB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC/BtC,OAAA;gBAAAiC,QAAA,EAAI;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChCtC,OAAA;gBAAAiC,QAAA,EAAI;cAAiB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAENtC,OAAA;YAAKkC,SAAS,EAAC,gCAAgC;YAAAD,QAAA,gBAC7CjC,OAAA;cAAIkC,SAAS,EAAC,gCAAgC;cAAAD,QAAA,EAAC;YAAmB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvEtC,OAAA;cAAGkC,SAAS,EAAC,uBAAuB;cAAAD,QAAA,EACjC3B,YAAY,CAACuB,UAAU,KAAK,MAAM,GAAG,gBAAgB,GACrDvB,YAAY,CAACuB,UAAU,KAAK,QAAQ,GAAG,gBAAgB,GACvD;YAAgB;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENtC,OAAA;QAAKkC,SAAS,EAAC,2BAA2B;QAAAD,QAAA,gBACxCjC,OAAA,CAACF,MAAM;UACLqD,OAAO,EAAC,SAAS;UACjBC,OAAO,EAAEA,CAAA,KAAMjD,QAAQ,CAAC,UAAU,CAAE;UAAA8B,QAAA,EACrC;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAETtC,OAAA,CAACF,MAAM;UACLsD,OAAO,EAAErB,cAAe;UACxBgB,IAAI,EAAC,IAAI;UAAAd,QAAA,EACV;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAACpC,EAAA,CA7QID,YAAsB;EAAA,QACTL,WAAW;AAAA;AAAA+D,EAAA,GADxB1D,YAAsB;AA+Q5B,eAAeA,YAAY;AAAC,IAAA0D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}