{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/landing/CTASection.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CTASection = () => {\n  _s();\n  const navigate = useNavigate();\n  const handleStartPrinting = () => {\n    navigate('/upload');\n  };\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"py-20 bg-weprint-gradient relative overflow-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 opacity-10\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-10 left-10 w-32 h-32 border-4 border-white rounded-full\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 15,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-32 right-20 w-24 h-24 border-4 border-white rounded-full\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute bottom-20 left-1/3 w-28 h-28 border-4 border-white rounded-full\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute bottom-10 right-10 w-20 h-20 border-4 border-white rounded-full\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-6 lg:px-8 relative z-10\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center text-white\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-4xl md:text-6xl font-black mb-6\",\n          children: \"Pronto para Imprimir?\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xl md:text-2xl mb-8 opacity-90 max-w-3xl mx-auto\",\n          children: [\"Junte-se a centenas de clientes satisfeitos e experimente a qualidade WePrint.\", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-bold\",\n            children: \" Seu primeiro pedido com desconto especial!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 30,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white bg-opacity-20 backdrop-blur-sm rounded-2xl p-6 max-w-2xl mx-auto mb-10\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-3xl font-black text-weprint-yellow mb-2\",\n            children: \"\\uD83C\\uDF89 OFERTA ESPECIAL\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-lg mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-bold\",\n              children: \"15% de desconto\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 39,\n              columnNumber: 15\n            }, this), \" no seu primeiro pedido\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm opacity-80\",\n            children: [\"Use o c\\xF3digo: \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-bold bg-white bg-opacity-30 px-2 py-1 rounded\",\n              children: \"PRIMEIRO15\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 42,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col sm:flex-row gap-4 justify-center items-center mb-12\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleStartPrinting,\n            className: \"bg-white text-weprint-magenta font-bold text-xl px-12 py-4 rounded-full shadow-2xl hover:shadow-3xl transform hover:scale-105 transition-all duration-300 animate-bounce-gentle\",\n            children: \"\\uD83D\\uDE80 Come\\xE7ar Agora\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => window.open('https://wa.me/244900000000', '_blank'),\n            className: \"border-2 border-white text-white font-bold text-xl px-12 py-4 rounded-full hover:bg-white hover:text-weprint-magenta transition-all duration-300\",\n            children: \"\\uD83D\\uDCAC Falar no WhatsApp\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-2xl mb-2\",\n              children: \"\\uD83D\\uDCCD\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 65,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"font-bold mb-1\",\n              children: \"Localiza\\xE7\\xE3o\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 66,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"opacity-80\",\n              children: \"Luanda, Angola\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 67,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-2xl mb-2\",\n              children: \"\\uD83D\\uDCDE\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 71,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"font-bold mb-1\",\n              children: \"Telefone\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 72,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"opacity-80\",\n              children: \"+244 900 000 000\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 73,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-2xl mb-2\",\n              children: \"\\u2709\\uFE0F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 77,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"font-bold mb-1\",\n              children: \"Email\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 78,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"opacity-80\",\n              children: \"<EMAIL>\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 79,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 12,\n    columnNumber: 5\n  }, this);\n};\n_s(CTASection, \"CzcTeTziyjMsSrAVmHuCCb6+Bfg=\", false, function () {\n  return [useNavigate];\n});\n_c = CTASection;\nexport default CTASection;\nvar _c;\n$RefreshReg$(_c, \"CTASection\");", "map": {"version": 3, "names": ["React", "useNavigate", "jsxDEV", "_jsxDEV", "CTASection", "_s", "navigate", "handleStartPrinting", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "window", "open", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/landing/CTASection.tsx"], "sourcesContent": ["import React from 'react';\nimport { useNavigate } from 'react-router-dom';\n\nconst CTASection: React.FC = () => {\n  const navigate = useNavigate();\n\n  const handleStartPrinting = () => {\n    navigate('/upload');\n  };\n\n  return (\n    <section className=\"py-20 bg-weprint-gradient relative overflow-hidden\">\n      {/* Background Pattern */}\n      <div className=\"absolute inset-0 opacity-10\">\n        <div className=\"absolute top-10 left-10 w-32 h-32 border-4 border-white rounded-full\"></div>\n        <div className=\"absolute top-32 right-20 w-24 h-24 border-4 border-white rounded-full\"></div>\n        <div className=\"absolute bottom-20 left-1/3 w-28 h-28 border-4 border-white rounded-full\"></div>\n        <div className=\"absolute bottom-10 right-10 w-20 h-20 border-4 border-white rounded-full\"></div>\n      </div>\n\n      <div className=\"container mx-auto px-6 lg:px-8 relative z-10\">\n        <div className=\"text-center text-white\">\n          {/* Main CTA */}\n          <h2 className=\"text-4xl md:text-6xl font-black mb-6\">\n            Pronto para Imprimir?\n          </h2>\n          \n          <p className=\"text-xl md:text-2xl mb-8 opacity-90 max-w-3xl mx-auto\">\n            Junte-se a centenas de clientes satisfeitos e experimente a qualidade WePrint. \n            <span className=\"font-bold\"> Seu primeiro pedido com desconto especial!</span>\n          </p>\n\n          {/* Special Offer */}\n          <div className=\"bg-white bg-opacity-20 backdrop-blur-sm rounded-2xl p-6 max-w-2xl mx-auto mb-10\">\n            <div className=\"text-3xl font-black text-weprint-yellow mb-2\">\n              🎉 OFERTA ESPECIAL\n            </div>\n            <div className=\"text-lg mb-4\">\n              <span className=\"font-bold\">15% de desconto</span> no seu primeiro pedido\n            </div>\n            <div className=\"text-sm opacity-80\">\n              Use o código: <span className=\"font-bold bg-white bg-opacity-30 px-2 py-1 rounded\">PRIMEIRO15</span>\n            </div>\n          </div>\n\n          {/* CTA Buttons */}\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center mb-12\">\n            <button\n              onClick={handleStartPrinting}\n              className=\"bg-white text-weprint-magenta font-bold text-xl px-12 py-4 rounded-full shadow-2xl hover:shadow-3xl transform hover:scale-105 transition-all duration-300 animate-bounce-gentle\"\n            >\n              🚀 Começar Agora\n            </button>\n            <button\n              onClick={() => window.open('https://wa.me/244900000000', '_blank')}\n              className=\"border-2 border-white text-white font-bold text-xl px-12 py-4 rounded-full hover:bg-white hover:text-weprint-magenta transition-all duration-300\"\n            >\n              💬 Falar no WhatsApp\n            </button>\n          </div>\n\n          {/* Contact Info */}\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto\">\n            <div className=\"text-center\">\n              <div className=\"text-2xl mb-2\">📍</div>\n              <div className=\"font-bold mb-1\">Localização</div>\n              <div className=\"opacity-80\">Luanda, Angola</div>\n            </div>\n            \n            <div className=\"text-center\">\n              <div className=\"text-2xl mb-2\">📞</div>\n              <div className=\"font-bold mb-1\">Telefone</div>\n              <div className=\"opacity-80\">+244 900 000 000</div>\n            </div>\n            \n            <div className=\"text-center\">\n              <div className=\"text-2xl mb-2\">✉️</div>\n              <div className=\"font-bold mb-1\">Email</div>\n              <div className=\"opacity-80\"><EMAIL></div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default CTASection;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,UAAoB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjC,MAAMC,QAAQ,GAAGL,WAAW,CAAC,CAAC;EAE9B,MAAMM,mBAAmB,GAAGA,CAAA,KAAM;IAChCD,QAAQ,CAAC,SAAS,CAAC;EACrB,CAAC;EAED,oBACEH,OAAA;IAASK,SAAS,EAAC,oDAAoD;IAAAC,QAAA,gBAErEN,OAAA;MAAKK,SAAS,EAAC,6BAA6B;MAAAC,QAAA,gBAC1CN,OAAA;QAAKK,SAAS,EAAC;MAAsE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC5FV,OAAA;QAAKK,SAAS,EAAC;MAAuE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC7FV,OAAA;QAAKK,SAAS,EAAC;MAA0E;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAChGV,OAAA;QAAKK,SAAS,EAAC;MAA0E;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7F,CAAC,eAENV,OAAA;MAAKK,SAAS,EAAC,8CAA8C;MAAAC,QAAA,eAC3DN,OAAA;QAAKK,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBAErCN,OAAA;UAAIK,SAAS,EAAC,sCAAsC;UAAAC,QAAA,EAAC;QAErD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAELV,OAAA;UAAGK,SAAS,EAAC,uDAAuD;UAAAC,QAAA,GAAC,gFAEnE,eAAAN,OAAA;YAAMK,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAC;UAA2C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7E,CAAC,eAGJV,OAAA;UAAKK,SAAS,EAAC,iFAAiF;UAAAC,QAAA,gBAC9FN,OAAA;YAAKK,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAAC;UAE9D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNV,OAAA;YAAKK,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BN,OAAA;cAAMK,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,2BACpD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNV,OAAA;YAAKK,SAAS,EAAC,oBAAoB;YAAAC,QAAA,GAAC,mBACpB,eAAAN,OAAA;cAAMK,SAAS,EAAC,oDAAoD;cAAAC,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNV,OAAA;UAAKK,SAAS,EAAC,mEAAmE;UAAAC,QAAA,gBAChFN,OAAA;YACEW,OAAO,EAAEP,mBAAoB;YAC7BC,SAAS,EAAC,iLAAiL;YAAAC,QAAA,EAC5L;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTV,OAAA;YACEW,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,IAAI,CAAC,4BAA4B,EAAE,QAAQ,CAAE;YACnER,SAAS,EAAC,kJAAkJ;YAAAC,QAAA,EAC7J;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGNV,OAAA;UAAKK,SAAS,EAAC,yDAAyD;UAAAC,QAAA,gBACtEN,OAAA;YAAKK,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BN,OAAA;cAAKK,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACvCV,OAAA;cAAKK,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACjDV,OAAA;cAAKK,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC,eAENV,OAAA;YAAKK,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BN,OAAA;cAAKK,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACvCV,OAAA;cAAKK,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC9CV,OAAA;cAAKK,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC,eAENV,OAAA;YAAKK,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BN,OAAA;cAAKK,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACvCV,OAAA;cAAKK,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC3CV,OAAA;cAAKK,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACR,EAAA,CAlFID,UAAoB;EAAA,QACPH,WAAW;AAAA;AAAAgB,EAAA,GADxBb,UAAoB;AAoF1B,eAAeA,UAAU;AAAC,IAAAa,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}