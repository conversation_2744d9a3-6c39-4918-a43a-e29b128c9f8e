{"ast": null, "code": "import _objectSpread from\"/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useEffect,useCallback}from'react';import{PaymentStatus}from'../components';import{MulticaixaService}from'../services/multicaixa';import{apiService}from'../services/api';import{adminApiService}from'../services/adminApi';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const AdminPanel=()=>{const[activeTab,setActiveTab]=useState('login');const[stats,setStats]=useState({totalRevenue:0,totalPayments:0,pendingPayments:0,completedPayments:0,failedPayments:0});const[payments,setPayments]=useState([]);const[orders,setOrders]=useState([]);const[loading,setLoading]=useState(true);const[isAuthenticated,setIsAuthenticated]=useState(false);const[adminUser,setAdminUser]=useState(null);const[loginForm,setLoginForm]=useState({email:'',password:''});const loadDashboardData=useCallback(async()=>{setLoading(true);try{// Load dashboard stats from admin API\nconst statsResponse=await adminApiService.getDashboardStats();if(statsResponse.success&&statsResponse.data){var _dashboardData$overvi,_dashboardData$overvi2,_dashboardData$orderS,_dashboardData$orderS2,_dashboardData$orderS3,_dashboardData$orderS4,_dashboardData$orderS5,_dashboardData$orderS6;const dashboardData=statsResponse.data;setStats({totalRevenue:((_dashboardData$overvi=dashboardData.overview)===null||_dashboardData$overvi===void 0?void 0:_dashboardData$overvi.totalRevenue)||0,totalPayments:((_dashboardData$overvi2=dashboardData.overview)===null||_dashboardData$overvi2===void 0?void 0:_dashboardData$overvi2.totalOrders)||0,pendingPayments:((_dashboardData$orderS=dashboardData.orderStats)===null||_dashboardData$orderS===void 0?void 0:(_dashboardData$orderS2=_dashboardData$orderS.byStatus)===null||_dashboardData$orderS2===void 0?void 0:_dashboardData$orderS2.pending)||0,completedPayments:((_dashboardData$orderS3=dashboardData.orderStats)===null||_dashboardData$orderS3===void 0?void 0:(_dashboardData$orderS4=_dashboardData$orderS3.byStatus)===null||_dashboardData$orderS4===void 0?void 0:_dashboardData$orderS4.completed)||0,failedPayments:((_dashboardData$orderS5=dashboardData.orderStats)===null||_dashboardData$orderS5===void 0?void 0:(_dashboardData$orderS6=_dashboardData$orderS5.byStatus)===null||_dashboardData$orderS6===void 0?void 0:_dashboardData$orderS6.cancelled)||0});}// Load orders from admin API\nconst ordersResponse=await adminApiService.getOrders();if(ordersResponse.success&&ordersResponse.data){setOrders(ordersResponse.data.orders||[]);}// Load payments (using regular API for now)\nconst paymentsResponse=await apiService.get('/payments');if(paymentsResponse.success&&paymentsResponse.data){setPayments(paymentsResponse.data);}}catch(error){console.error('Error loading dashboard data:',error);}finally{setLoading(false);}},[]);useEffect(()=>{// Check if admin is already authenticated\nif(adminApiService.isAuthenticated()){const storedAdmin=adminApiService.getStoredAdmin();if(storedAdmin){setAdminUser(storedAdmin);setIsAuthenticated(true);setActiveTab('overview');loadDashboardData();}else{setLoading(false);}}else{setLoading(false);}},[loadDashboardData]);const handleLogin=async e=>{e.preventDefault();setLoading(true);try{const response=await adminApiService.login(loginForm);if(response.success&&response.data){setAdminUser(response.data.admin);setIsAuthenticated(true);setActiveTab('overview');await loadDashboardData();}else{alert(response.error||'Erro no login');}}catch(error){console.error('Login error:',error);alert('Erro no login. Tente novamente.');}finally{setLoading(false);}};const handleLogout=async()=>{await adminApiService.logout();setAdminUser(null);setIsAuthenticated(false);setActiveTab('login');setLoginForm({email:'',password:''});};const calculateStats=paymentsData=>{const stats=paymentsData.reduce((acc,payment)=>{acc.totalPayments++;if(payment.status==='COMPLETED'){acc.completedPayments++;acc.totalRevenue+=payment.amount;}else if(payment.status==='PENDING'||payment.status==='PROCESSING'){acc.pendingPayments++;}else if(payment.status==='FAILED'){acc.failedPayments++;}return acc;},{totalRevenue:0,totalPayments:0,pendingPayments:0,completedPayments:0,failedPayments:0});setStats(stats);};const handleRefundPayment=async paymentId=>{// eslint-disable-next-line no-restricted-globals\nif(!confirm('Tem certeza que deseja processar este reembolso?')){return;}try{const response=await MulticaixaService.requestRefund(paymentId);if(response.success){alert('Reembolso processado com sucesso!');loadDashboardData();// Reload data\n}else{alert(\"Erro ao processar reembolso: \".concat(response.error));}}catch(error){alert('Erro ao processar reembolso');}};const formatCurrency=amount=>{return MulticaixaService.formatAoaAmount(amount);};if(loading){return/*#__PURE__*/_jsx(\"div\",{className:\"min-h-screen bg-gray-50 flex items-center justify-center\",children:/*#__PURE__*/_jsx(\"div\",{className:\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"})});}// Show login form if not authenticated\nif(!isAuthenticated||activeTab==='login'){return/*#__PURE__*/_jsx(\"div\",{className:\"min-h-screen bg-gray-50 flex items-center justify-center\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"max-w-md w-full space-y-8\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h2\",{className:\"mt-6 text-center text-3xl font-extrabold text-gray-900\",children:\"Painel Administrativo\"}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-2 text-center text-sm text-gray-600\",children:\"Fa\\xE7a login para acessar o painel\"})]}),/*#__PURE__*/_jsxs(\"form\",{className:\"mt-8 space-y-6\",onSubmit:handleLogin,children:[/*#__PURE__*/_jsxs(\"div\",{className:\"rounded-md shadow-sm -space-y-px\",children:[/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{type:\"email\",required:true,className:\"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm\",placeholder:\"Email\",value:loginForm.email,onChange:e=>setLoginForm(_objectSpread(_objectSpread({},loginForm),{},{email:e.target.value}))})}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{type:\"password\",required:true,className:\"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm\",placeholder:\"Senha\",value:loginForm.password,onChange:e=>setLoginForm(_objectSpread(_objectSpread({},loginForm),{},{password:e.target.value}))})})]}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"button\",{type:\"submit\",className:\"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",children:\"Entrar\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-center text-sm text-gray-600\",children:[/*#__PURE__*/_jsx(\"p\",{children:\"Credenciais padr\\xE3o:\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Email: <EMAIL>\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Senha: admin123\"})]})]})]})});}return/*#__PURE__*/_jsx(\"div\",{className:\"min-h-screen bg-gray-50\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"mb-8 flex justify-between items-center\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-3xl font-bold text-gray-900\",children:\"Painel Administrativo\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600\",children:\"Gest\\xE3o de pagamentos e pedidos WePrint AI\"}),adminUser&&/*#__PURE__*/_jsxs(\"p\",{className:\"text-sm text-gray-500\",children:[\"Logado como: \",adminUser.name,\" (\",adminUser.email,\")\"]})]}),/*#__PURE__*/_jsx(\"button\",{onClick:handleLogout,className:\"bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium\",children:\"Sair\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"mb-8\",children:/*#__PURE__*/_jsx(\"nav\",{className:\"flex space-x-8\",children:[{id:'overview',label:'Visão Geral'},{id:'payments',label:'Pagamentos'},{id:'orders',label:'Pedidos'}].map(tab=>/*#__PURE__*/_jsx(\"button\",{onClick:()=>setActiveTab(tab.id),className:\"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab===tab.id?'border-blue-500 text-blue-600':'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'),children:tab.label},tab.id))})}),activeTab==='overview'&&/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-lg shadow p-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex-shrink-0\",children:/*#__PURE__*/_jsx(\"div\",{className:\"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center\",children:/*#__PURE__*/_jsx(\"svg\",{className:\"w-5 h-5 text-white\",fill:\"currentColor\",viewBox:\"0 0 20 20\",children:/*#__PURE__*/_jsx(\"path\",{d:\"M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z\"})})})}),/*#__PURE__*/_jsx(\"div\",{className:\"ml-5 w-0 flex-1\",children:/*#__PURE__*/_jsxs(\"dl\",{children:[/*#__PURE__*/_jsx(\"dt\",{className:\"text-sm font-medium text-gray-500 truncate\",children:\"Receita Total\"}),/*#__PURE__*/_jsx(\"dd\",{className:\"text-lg font-medium text-gray-900\",children:formatCurrency(stats.totalRevenue)})]})})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-lg shadow p-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex-shrink-0\",children:/*#__PURE__*/_jsx(\"div\",{className:\"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center\",children:/*#__PURE__*/_jsx(\"svg\",{className:\"w-5 h-5 text-white\",fill:\"currentColor\",viewBox:\"0 0 20 20\",children:/*#__PURE__*/_jsx(\"path\",{fillRule:\"evenodd\",d:\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",clipRule:\"evenodd\"})})})}),/*#__PURE__*/_jsx(\"div\",{className:\"ml-5 w-0 flex-1\",children:/*#__PURE__*/_jsxs(\"dl\",{children:[/*#__PURE__*/_jsx(\"dt\",{className:\"text-sm font-medium text-gray-500 truncate\",children:\"Pagamentos Conclu\\xEDdos\"}),/*#__PURE__*/_jsx(\"dd\",{className:\"text-lg font-medium text-gray-900\",children:stats.completedPayments})]})})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-lg shadow p-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex-shrink-0\",children:/*#__PURE__*/_jsx(\"div\",{className:\"w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center\",children:/*#__PURE__*/_jsx(\"svg\",{className:\"w-5 h-5 text-white\",fill:\"currentColor\",viewBox:\"0 0 20 20\",children:/*#__PURE__*/_jsx(\"path\",{fillRule:\"evenodd\",d:\"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z\",clipRule:\"evenodd\"})})})}),/*#__PURE__*/_jsx(\"div\",{className:\"ml-5 w-0 flex-1\",children:/*#__PURE__*/_jsxs(\"dl\",{children:[/*#__PURE__*/_jsx(\"dt\",{className:\"text-sm font-medium text-gray-500 truncate\",children:\"Pagamentos Pendentes\"}),/*#__PURE__*/_jsx(\"dd\",{className:\"text-lg font-medium text-gray-900\",children:stats.pendingPayments})]})})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-lg shadow p-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex-shrink-0\",children:/*#__PURE__*/_jsx(\"div\",{className:\"w-8 h-8 bg-red-500 rounded-md flex items-center justify-center\",children:/*#__PURE__*/_jsx(\"svg\",{className:\"w-5 h-5 text-white\",fill:\"currentColor\",viewBox:\"0 0 20 20\",children:/*#__PURE__*/_jsx(\"path\",{fillRule:\"evenodd\",d:\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",clipRule:\"evenodd\"})})})}),/*#__PURE__*/_jsx(\"div\",{className:\"ml-5 w-0 flex-1\",children:/*#__PURE__*/_jsxs(\"dl\",{children:[/*#__PURE__*/_jsx(\"dt\",{className:\"text-sm font-medium text-gray-500 truncate\",children:\"Pagamentos Falhados\"}),/*#__PURE__*/_jsx(\"dd\",{className:\"text-lg font-medium text-gray-900\",children:stats.failedPayments})]})})]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-lg shadow\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"px-6 py-4 border-b border-gray-200\",children:/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium text-gray-900\",children:\"Atividade Recente\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"p-6\",children:/*#__PURE__*/_jsx(\"div\",{className:\"space-y-4\",children:payments.slice(0,5).map(payment=>/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-3\",children:[/*#__PURE__*/_jsx(PaymentStatus,{status:payment.status,size:\"sm\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"p\",{className:\"text-sm font-medium text-gray-900\",children:[\"Pagamento #\",payment.id.slice(-8)]}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-500\",children:formatCurrency(payment.amount)})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm text-gray-500\",children:new Date(payment.createdAt).toLocaleDateString('pt-PT')})]},payment.id))})})]})]}),activeTab==='payments'&&/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-lg shadow\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"px-6 py-4 border-b border-gray-200\",children:/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium text-gray-900\",children:\"Gest\\xE3o de Pagamentos\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"overflow-x-auto\",children:/*#__PURE__*/_jsxs(\"table\",{className:\"min-w-full divide-y divide-gray-200\",children:[/*#__PURE__*/_jsx(\"thead\",{className:\"bg-gray-50\",children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"ID\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"Valor\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"Status\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"M\\xE9todo\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"Data\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"A\\xE7\\xF5es\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{className:\"bg-white divide-y divide-gray-200\",children:payments.map(payment=>/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsxs(\"td\",{className:\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",children:[\"#\",payment.id.slice(-8)]}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",children:formatCurrency(payment.amount)}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap\",children:/*#__PURE__*/_jsx(PaymentStatus,{status:payment.status,size:\"sm\"})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",children:payment.paymentMethod}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",children:new Date(payment.createdAt).toLocaleDateString('pt-PT')}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap text-sm font-medium\",children:MulticaixaService.canRefund(payment.status)&&/*#__PURE__*/_jsx(\"button\",{onClick:()=>handleRefundPayment(payment.id),className:\"text-red-600 hover:text-red-900\",children:\"Reembolsar\"})})]},payment.id))})]})})]}),activeTab==='orders'&&/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-lg shadow\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"px-6 py-4 border-b border-gray-200\",children:/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium text-gray-900\",children:\"Gest\\xE3o de Pedidos\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"p-6\",children:/*#__PURE__*/_jsx(\"div\",{className:\"space-y-4\",children:orders.map(order=>/*#__PURE__*/_jsxs(\"div\",{className:\"border border-gray-200 rounded-lg p-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between mb-2\",children:[/*#__PURE__*/_jsxs(\"h4\",{className:\"text-lg font-medium text-gray-900\",children:[\"Pedido #\",order.id.slice(-8)]}),/*#__PURE__*/_jsx(\"span\",{className:\"px-2 py-1 text-xs font-medium rounded-full \".concat(order.status==='delivered'?'bg-green-100 text-green-800':order.status==='in_progress'?'bg-blue-100 text-blue-800':order.status==='ready'?'bg-yellow-100 text-yellow-800':'bg-gray-100 text-gray-800'),children:order.status})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-2 gap-4 text-sm\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-500\",children:\"Total:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"ml-2 font-medium\",children:formatCurrency(order.totalAmount)})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-500\",children:\"Pagamento:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"ml-2 font-medium \".concat(order.paymentStatus==='paid'?'text-green-600':order.paymentStatus==='pending'?'text-yellow-600':'text-red-600'),children:order.paymentStatus})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-500\",children:\"Data:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"ml-2\",children:new Date(order.createdAt).toLocaleDateString('pt-PT')})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-500\",children:\"Itens:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"ml-2\",children:order.printJobs.length})]})]})]},order.id))})})]})]})});};export default AdminPanel;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "PaymentStatus", "MulticaixaService", "apiService", "adminApiService", "jsx", "_jsx", "jsxs", "_jsxs", "AdminPanel", "activeTab", "setActiveTab", "stats", "setStats", "totalRevenue", "totalPayments", "pendingPayments", "completedPayments", "failedPayments", "payments", "setPayments", "orders", "setOrders", "loading", "setLoading", "isAuthenticated", "setIsAuthenticated", "adminUser", "setAdminUser", "loginForm", "setLoginForm", "email", "password", "loadDashboardData", "statsResponse", "getDashboardStats", "success", "data", "_dashboardData$overvi", "_dashboardData$overvi2", "_dashboardData$orderS", "_dashboardData$orderS2", "_dashboardData$orderS3", "_dashboardData$orderS4", "_dashboardData$orderS5", "_dashboardData$orderS6", "dashboardData", "overview", "totalOrders", "orderStats", "byStatus", "pending", "completed", "cancelled", "ordersResponse", "getOrders", "paymentsResponse", "get", "error", "console", "storedAdmin", "getStoredAdmin", "handleLogin", "e", "preventDefault", "response", "login", "admin", "alert", "handleLogout", "logout", "calculateStats", "paymentsData", "reduce", "acc", "payment", "status", "amount", "handleRefundPayment", "paymentId", "confirm", "requestRefund", "concat", "formatCurrency", "formatAoaAmount", "className", "children", "onSubmit", "type", "required", "placeholder", "value", "onChange", "_objectSpread", "target", "name", "onClick", "id", "label", "map", "tab", "fill", "viewBox", "d", "fillRule", "clipRule", "slice", "size", "Date", "createdAt", "toLocaleDateString", "paymentMethod", "canRefund", "order", "totalAmount", "paymentStatus", "printJobs", "length"], "sources": ["/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/AdminPanel.tsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport { PaymentStatus } from '../components';\nimport { MulticaixaService } from '../services/multicaixa';\nimport { apiService } from '../services/api';\nimport { adminApiService } from '../services/adminApi';\nimport { Payment, Order } from '../types';\n\ninterface DashboardStats {\n  totalRevenue: number;\n  totalPayments: number;\n  pendingPayments: number;\n  completedPayments: number;\n  failedPayments: number;\n}\n\nconst AdminPanel: React.FC = () => {\n  const [activeTab, setActiveTab] = useState<'overview' | 'payments' | 'orders' | 'login'>('login');\n  const [stats, setStats] = useState<DashboardStats>({\n    totalRevenue: 0,\n    totalPayments: 0,\n    pendingPayments: 0,\n    completedPayments: 0,\n    failedPayments: 0,\n  });\n  const [payments, setPayments] = useState<Payment[]>([]);\n  const [orders, setOrders] = useState<Order[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  const [adminUser, setAdminUser] = useState<any>(null);\n  const [loginForm, setLoginForm] = useState({ email: '', password: '' });\n\n  const loadDashboardData = useCallback(async () => {\n    setLoading(true);\n    try {\n      // Load dashboard stats from admin API\n      const statsResponse = await adminApiService.getDashboardStats();\n      if (statsResponse.success && statsResponse.data) {\n        const dashboardData = statsResponse.data;\n        setStats({\n          totalRevenue: dashboardData.overview?.totalRevenue || 0,\n          totalPayments: dashboardData.overview?.totalOrders || 0,\n          pendingPayments: dashboardData.orderStats?.byStatus?.pending || 0,\n          completedPayments: dashboardData.orderStats?.byStatus?.completed || 0,\n          failedPayments: dashboardData.orderStats?.byStatus?.cancelled || 0,\n        });\n      }\n\n      // Load orders from admin API\n      const ordersResponse = await adminApiService.getOrders();\n      if (ordersResponse.success && ordersResponse.data) {\n        setOrders(ordersResponse.data.orders || []);\n      }\n\n      // Load payments (using regular API for now)\n      const paymentsResponse = await apiService.get<Payment[]>('/payments');\n      if (paymentsResponse.success && paymentsResponse.data) {\n        setPayments(paymentsResponse.data);\n      }\n    } catch (error) {\n      console.error('Error loading dashboard data:', error);\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n\n  useEffect(() => {\n    // Check if admin is already authenticated\n    if (adminApiService.isAuthenticated()) {\n      const storedAdmin = adminApiService.getStoredAdmin();\n      if (storedAdmin) {\n        setAdminUser(storedAdmin);\n        setIsAuthenticated(true);\n        setActiveTab('overview');\n        loadDashboardData();\n      } else {\n        setLoading(false);\n      }\n    } else {\n      setLoading(false);\n    }\n  }, [loadDashboardData]);\n\n  const handleLogin = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setLoading(true);\n\n    try {\n      const response = await adminApiService.login(loginForm);\n      if (response.success && response.data) {\n        setAdminUser(response.data.admin);\n        setIsAuthenticated(true);\n        setActiveTab('overview');\n        await loadDashboardData();\n      } else {\n        alert(response.error || 'Erro no login');\n      }\n    } catch (error) {\n      console.error('Login error:', error);\n      alert('Erro no login. Tente novamente.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleLogout = async () => {\n    await adminApiService.logout();\n    setAdminUser(null);\n    setIsAuthenticated(false);\n    setActiveTab('login');\n    setLoginForm({ email: '', password: '' });\n  };\n\n  const calculateStats = (paymentsData: Payment[]) => {\n    const stats = paymentsData.reduce((acc, payment) => {\n      acc.totalPayments++;\n\n      if (payment.status === 'COMPLETED') {\n        acc.completedPayments++;\n        acc.totalRevenue += payment.amount;\n      } else if (payment.status === 'PENDING' || payment.status === 'PROCESSING') {\n        acc.pendingPayments++;\n      } else if (payment.status === 'FAILED') {\n        acc.failedPayments++;\n      }\n\n      return acc;\n    }, {\n      totalRevenue: 0,\n      totalPayments: 0,\n      pendingPayments: 0,\n      completedPayments: 0,\n      failedPayments: 0,\n    });\n\n    setStats(stats);\n  };\n\n  const handleRefundPayment = async (paymentId: string) => {\n    // eslint-disable-next-line no-restricted-globals\n    if (!confirm('Tem certeza que deseja processar este reembolso?')) {\n      return;\n    }\n\n    try {\n      const response = await MulticaixaService.requestRefund(paymentId);\n      if (response.success) {\n        alert('Reembolso processado com sucesso!');\n        loadDashboardData(); // Reload data\n      } else {\n        alert(`Erro ao processar reembolso: ${response.error}`);\n      }\n    } catch (error) {\n      alert('Erro ao processar reembolso');\n    }\n  };\n\n  const formatCurrency = (amount: number) => {\n    return MulticaixaService.formatAoaAmount(amount);\n  };\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  // Show login form if not authenticated\n  if (!isAuthenticated || activeTab === 'login') {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"max-w-md w-full space-y-8\">\n          <div>\n            <h2 className=\"mt-6 text-center text-3xl font-extrabold text-gray-900\">\n              Painel Administrativo\n            </h2>\n            <p className=\"mt-2 text-center text-sm text-gray-600\">\n              Faça login para acessar o painel\n            </p>\n          </div>\n          <form className=\"mt-8 space-y-6\" onSubmit={handleLogin}>\n            <div className=\"rounded-md shadow-sm -space-y-px\">\n              <div>\n                <input\n                  type=\"email\"\n                  required\n                  className=\"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm\"\n                  placeholder=\"Email\"\n                  value={loginForm.email}\n                  onChange={(e) => setLoginForm({ ...loginForm, email: e.target.value })}\n                />\n              </div>\n              <div>\n                <input\n                  type=\"password\"\n                  required\n                  className=\"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm\"\n                  placeholder=\"Senha\"\n                  value={loginForm.password}\n                  onChange={(e) => setLoginForm({ ...loginForm, password: e.target.value })}\n                />\n              </div>\n            </div>\n            <div>\n              <button\n                type=\"submit\"\n                className=\"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n              >\n                Entrar\n              </button>\n            </div>\n            <div className=\"text-center text-sm text-gray-600\">\n              <p>Credenciais padrão:</p>\n              <p>Email: <EMAIL></p>\n              <p>Senha: admin123</p>\n            </div>\n          </form>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Header */}\n        <div className=\"mb-8 flex justify-between items-center\">\n          <div>\n            <h1 className=\"text-3xl font-bold text-gray-900\">Painel Administrativo</h1>\n            <p className=\"text-gray-600\">Gestão de pagamentos e pedidos WePrint AI</p>\n            {adminUser && (\n              <p className=\"text-sm text-gray-500\">Logado como: {adminUser.name} ({adminUser.email})</p>\n            )}\n          </div>\n          <button\n            onClick={handleLogout}\n            className=\"bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium\"\n          >\n            Sair\n          </button>\n        </div>\n\n        {/* Navigation Tabs */}\n        <div className=\"mb-8\">\n          <nav className=\"flex space-x-8\">\n            {[\n              { id: 'overview', label: 'Visão Geral' },\n              { id: 'payments', label: 'Pagamentos' },\n              { id: 'orders', label: 'Pedidos' },\n            ].map((tab) => (\n              <button\n                key={tab.id}\n                onClick={() => setActiveTab(tab.id as any)}\n                className={`py-2 px-1 border-b-2 font-medium text-sm ${\n                  activeTab === tab.id\n                    ? 'border-blue-500 text-blue-600'\n                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                }`}\n              >\n                {tab.label}\n              </button>\n            ))}\n          </nav>\n        </div>\n\n        {/* Overview Tab */}\n        {activeTab === 'overview' && (\n          <div className=\"space-y-6\">\n            {/* Stats Cards */}\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n              <div className=\"bg-white rounded-lg shadow p-6\">\n                <div className=\"flex items-center\">\n                  <div className=\"flex-shrink-0\">\n                    <div className=\"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center\">\n                      <svg className=\"w-5 h-5 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path d=\"M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z\" />\n                      </svg>\n                    </div>\n                  </div>\n                  <div className=\"ml-5 w-0 flex-1\">\n                    <dl>\n                      <dt className=\"text-sm font-medium text-gray-500 truncate\">\n                        Receita Total\n                      </dt>\n                      <dd className=\"text-lg font-medium text-gray-900\">\n                        {formatCurrency(stats.totalRevenue)}\n                      </dd>\n                    </dl>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"bg-white rounded-lg shadow p-6\">\n                <div className=\"flex items-center\">\n                  <div className=\"flex-shrink-0\">\n                    <div className=\"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center\">\n                      <svg className=\"w-5 h-5 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n                      </svg>\n                    </div>\n                  </div>\n                  <div className=\"ml-5 w-0 flex-1\">\n                    <dl>\n                      <dt className=\"text-sm font-medium text-gray-500 truncate\">\n                        Pagamentos Concluídos\n                      </dt>\n                      <dd className=\"text-lg font-medium text-gray-900\">\n                        {stats.completedPayments}\n                      </dd>\n                    </dl>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"bg-white rounded-lg shadow p-6\">\n                <div className=\"flex items-center\">\n                  <div className=\"flex-shrink-0\">\n                    <div className=\"w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center\">\n                      <svg className=\"w-5 h-5 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z\" clipRule=\"evenodd\" />\n                      </svg>\n                    </div>\n                  </div>\n                  <div className=\"ml-5 w-0 flex-1\">\n                    <dl>\n                      <dt className=\"text-sm font-medium text-gray-500 truncate\">\n                        Pagamentos Pendentes\n                      </dt>\n                      <dd className=\"text-lg font-medium text-gray-900\">\n                        {stats.pendingPayments}\n                      </dd>\n                    </dl>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"bg-white rounded-lg shadow p-6\">\n                <div className=\"flex items-center\">\n                  <div className=\"flex-shrink-0\">\n                    <div className=\"w-8 h-8 bg-red-500 rounded-md flex items-center justify-center\">\n                      <svg className=\"w-5 h-5 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\n                      </svg>\n                    </div>\n                  </div>\n                  <div className=\"ml-5 w-0 flex-1\">\n                    <dl>\n                      <dt className=\"text-sm font-medium text-gray-500 truncate\">\n                        Pagamentos Falhados\n                      </dt>\n                      <dd className=\"text-lg font-medium text-gray-900\">\n                        {stats.failedPayments}\n                      </dd>\n                    </dl>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Recent Activity */}\n            <div className=\"bg-white rounded-lg shadow\">\n              <div className=\"px-6 py-4 border-b border-gray-200\">\n                <h3 className=\"text-lg font-medium text-gray-900\">Atividade Recente</h3>\n              </div>\n              <div className=\"p-6\">\n                <div className=\"space-y-4\">\n                  {payments.slice(0, 5).map((payment) => (\n                    <div key={payment.id} className=\"flex items-center justify-between\">\n                      <div className=\"flex items-center space-x-3\">\n                        <PaymentStatus status={payment.status} size=\"sm\" />\n                        <div>\n                          <p className=\"text-sm font-medium text-gray-900\">\n                            Pagamento #{payment.id.slice(-8)}\n                          </p>\n                          <p className=\"text-sm text-gray-500\">\n                            {formatCurrency(payment.amount)}\n                          </p>\n                        </div>\n                      </div>\n                      <div className=\"text-sm text-gray-500\">\n                        {new Date(payment.createdAt).toLocaleDateString('pt-PT')}\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Payments Tab */}\n        {activeTab === 'payments' && (\n          <div className=\"bg-white rounded-lg shadow\">\n            <div className=\"px-6 py-4 border-b border-gray-200\">\n              <h3 className=\"text-lg font-medium text-gray-900\">Gestão de Pagamentos</h3>\n            </div>\n            <div className=\"overflow-x-auto\">\n              <table className=\"min-w-full divide-y divide-gray-200\">\n                <thead className=\"bg-gray-50\">\n                  <tr>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      ID\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Valor\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Status\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Método\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Data\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Ações\n                    </th>\n                  </tr>\n                </thead>\n                <tbody className=\"bg-white divide-y divide-gray-200\">\n                  {payments.map((payment) => (\n                    <tr key={payment.id}>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">\n                        #{payment.id.slice(-8)}\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                        {formatCurrency(payment.amount)}\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\n                        <PaymentStatus status={payment.status} size=\"sm\" />\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                        {payment.paymentMethod}\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                        {new Date(payment.createdAt).toLocaleDateString('pt-PT')}\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                        {MulticaixaService.canRefund(payment.status) && (\n                          <button\n                            onClick={() => handleRefundPayment(payment.id)}\n                            className=\"text-red-600 hover:text-red-900\"\n                          >\n                            Reembolsar\n                          </button>\n                        )}\n                      </td>\n                    </tr>\n                  ))}\n                </tbody>\n              </table>\n            </div>\n          </div>\n        )}\n\n        {/* Orders Tab */}\n        {activeTab === 'orders' && (\n          <div className=\"bg-white rounded-lg shadow\">\n            <div className=\"px-6 py-4 border-b border-gray-200\">\n              <h3 className=\"text-lg font-medium text-gray-900\">Gestão de Pedidos</h3>\n            </div>\n            <div className=\"p-6\">\n              <div className=\"space-y-4\">\n                {orders.map((order) => (\n                  <div key={order.id} className=\"border border-gray-200 rounded-lg p-4\">\n                    <div className=\"flex items-center justify-between mb-2\">\n                      <h4 className=\"text-lg font-medium text-gray-900\">\n                        Pedido #{order.id.slice(-8)}\n                      </h4>\n                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${\n                        order.status === 'delivered' ? 'bg-green-100 text-green-800' :\n                        order.status === 'in_progress' ? 'bg-blue-100 text-blue-800' :\n                        order.status === 'ready' ? 'bg-yellow-100 text-yellow-800' :\n                        'bg-gray-100 text-gray-800'\n                      }`}>\n                        {order.status}\n                      </span>\n                    </div>\n                    <div className=\"grid grid-cols-2 gap-4 text-sm\">\n                      <div>\n                        <span className=\"text-gray-500\">Total:</span>\n                        <span className=\"ml-2 font-medium\">{formatCurrency(order.totalAmount)}</span>\n                      </div>\n                      <div>\n                        <span className=\"text-gray-500\">Pagamento:</span>\n                        <span className={`ml-2 font-medium ${\n                          order.paymentStatus === 'paid' ? 'text-green-600' :\n                          order.paymentStatus === 'pending' ? 'text-yellow-600' :\n                          'text-red-600'\n                        }`}>\n                          {order.paymentStatus}\n                        </span>\n                      </div>\n                      <div>\n                        <span className=\"text-gray-500\">Data:</span>\n                        <span className=\"ml-2\">{new Date(order.createdAt).toLocaleDateString('pt-PT')}</span>\n                      </div>\n                      <div>\n                        <span className=\"text-gray-500\">Itens:</span>\n                        <span className=\"ml-2\">{order.printJobs.length}</span>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default AdminPanel;\n"], "mappings": "sIAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,CAAEC,WAAW,KAAQ,OAAO,CAC/D,OAASC,aAAa,KAAQ,eAAe,CAC7C,OAASC,iBAAiB,KAAQ,wBAAwB,CAC1D,OAASC,UAAU,KAAQ,iBAAiB,CAC5C,OAASC,eAAe,KAAQ,sBAAsB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAWvD,KAAM,CAAAC,UAAoB,CAAGA,CAAA,GAAM,CACjC,KAAM,CAACC,SAAS,CAAEC,YAAY,CAAC,CAAGb,QAAQ,CAA+C,OAAO,CAAC,CACjG,KAAM,CAACc,KAAK,CAAEC,QAAQ,CAAC,CAAGf,QAAQ,CAAiB,CACjDgB,YAAY,CAAE,CAAC,CACfC,aAAa,CAAE,CAAC,CAChBC,eAAe,CAAE,CAAC,CAClBC,iBAAiB,CAAE,CAAC,CACpBC,cAAc,CAAE,CAClB,CAAC,CAAC,CACF,KAAM,CAACC,QAAQ,CAAEC,WAAW,CAAC,CAAGtB,QAAQ,CAAY,EAAE,CAAC,CACvD,KAAM,CAACuB,MAAM,CAAEC,SAAS,CAAC,CAAGxB,QAAQ,CAAU,EAAE,CAAC,CACjD,KAAM,CAACyB,OAAO,CAAEC,UAAU,CAAC,CAAG1B,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAAC2B,eAAe,CAAEC,kBAAkB,CAAC,CAAG5B,QAAQ,CAAC,KAAK,CAAC,CAC7D,KAAM,CAAC6B,SAAS,CAAEC,YAAY,CAAC,CAAG9B,QAAQ,CAAM,IAAI,CAAC,CACrD,KAAM,CAAC+B,SAAS,CAAEC,YAAY,CAAC,CAAGhC,QAAQ,CAAC,CAAEiC,KAAK,CAAE,EAAE,CAAEC,QAAQ,CAAE,EAAG,CAAC,CAAC,CAEvE,KAAM,CAAAC,iBAAiB,CAAGjC,WAAW,CAAC,SAAY,CAChDwB,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACF;AACA,KAAM,CAAAU,aAAa,CAAG,KAAM,CAAA9B,eAAe,CAAC+B,iBAAiB,CAAC,CAAC,CAC/D,GAAID,aAAa,CAACE,OAAO,EAAIF,aAAa,CAACG,IAAI,CAAE,KAAAC,qBAAA,CAAAC,sBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAC/C,KAAM,CAAAC,aAAa,CAAGZ,aAAa,CAACG,IAAI,CACxCxB,QAAQ,CAAC,CACPC,YAAY,CAAE,EAAAwB,qBAAA,CAAAQ,aAAa,CAACC,QAAQ,UAAAT,qBAAA,iBAAtBA,qBAAA,CAAwBxB,YAAY,GAAI,CAAC,CACvDC,aAAa,CAAE,EAAAwB,sBAAA,CAAAO,aAAa,CAACC,QAAQ,UAAAR,sBAAA,iBAAtBA,sBAAA,CAAwBS,WAAW,GAAI,CAAC,CACvDhC,eAAe,CAAE,EAAAwB,qBAAA,CAAAM,aAAa,CAACG,UAAU,UAAAT,qBAAA,kBAAAC,sBAAA,CAAxBD,qBAAA,CAA0BU,QAAQ,UAAAT,sBAAA,iBAAlCA,sBAAA,CAAoCU,OAAO,GAAI,CAAC,CACjElC,iBAAiB,CAAE,EAAAyB,sBAAA,CAAAI,aAAa,CAACG,UAAU,UAAAP,sBAAA,kBAAAC,sBAAA,CAAxBD,sBAAA,CAA0BQ,QAAQ,UAAAP,sBAAA,iBAAlCA,sBAAA,CAAoCS,SAAS,GAAI,CAAC,CACrElC,cAAc,CAAE,EAAA0B,sBAAA,CAAAE,aAAa,CAACG,UAAU,UAAAL,sBAAA,kBAAAC,sBAAA,CAAxBD,sBAAA,CAA0BM,QAAQ,UAAAL,sBAAA,iBAAlCA,sBAAA,CAAoCQ,SAAS,GAAI,CACnE,CAAC,CAAC,CACJ,CAEA;AACA,KAAM,CAAAC,cAAc,CAAG,KAAM,CAAAlD,eAAe,CAACmD,SAAS,CAAC,CAAC,CACxD,GAAID,cAAc,CAAClB,OAAO,EAAIkB,cAAc,CAACjB,IAAI,CAAE,CACjDf,SAAS,CAACgC,cAAc,CAACjB,IAAI,CAAChB,MAAM,EAAI,EAAE,CAAC,CAC7C,CAEA;AACA,KAAM,CAAAmC,gBAAgB,CAAG,KAAM,CAAArD,UAAU,CAACsD,GAAG,CAAY,WAAW,CAAC,CACrE,GAAID,gBAAgB,CAACpB,OAAO,EAAIoB,gBAAgB,CAACnB,IAAI,CAAE,CACrDjB,WAAW,CAACoC,gBAAgB,CAACnB,IAAI,CAAC,CACpC,CACF,CAAE,MAAOqB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,CAAEA,KAAK,CAAC,CACvD,CAAC,OAAS,CACRlC,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAAE,EAAE,CAAC,CAENzB,SAAS,CAAC,IAAM,CACd;AACA,GAAIK,eAAe,CAACqB,eAAe,CAAC,CAAC,CAAE,CACrC,KAAM,CAAAmC,WAAW,CAAGxD,eAAe,CAACyD,cAAc,CAAC,CAAC,CACpD,GAAID,WAAW,CAAE,CACfhC,YAAY,CAACgC,WAAW,CAAC,CACzBlC,kBAAkB,CAAC,IAAI,CAAC,CACxBf,YAAY,CAAC,UAAU,CAAC,CACxBsB,iBAAiB,CAAC,CAAC,CACrB,CAAC,IAAM,CACLT,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,IAAM,CACLA,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAAE,CAACS,iBAAiB,CAAC,CAAC,CAEvB,KAAM,CAAA6B,WAAW,CAAG,KAAO,CAAAC,CAAkB,EAAK,CAChDA,CAAC,CAACC,cAAc,CAAC,CAAC,CAClBxC,UAAU,CAAC,IAAI,CAAC,CAEhB,GAAI,CACF,KAAM,CAAAyC,QAAQ,CAAG,KAAM,CAAA7D,eAAe,CAAC8D,KAAK,CAACrC,SAAS,CAAC,CACvD,GAAIoC,QAAQ,CAAC7B,OAAO,EAAI6B,QAAQ,CAAC5B,IAAI,CAAE,CACrCT,YAAY,CAACqC,QAAQ,CAAC5B,IAAI,CAAC8B,KAAK,CAAC,CACjCzC,kBAAkB,CAAC,IAAI,CAAC,CACxBf,YAAY,CAAC,UAAU,CAAC,CACxB,KAAM,CAAAsB,iBAAiB,CAAC,CAAC,CAC3B,CAAC,IAAM,CACLmC,KAAK,CAACH,QAAQ,CAACP,KAAK,EAAI,eAAe,CAAC,CAC1C,CACF,CAAE,MAAOA,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,cAAc,CAAEA,KAAK,CAAC,CACpCU,KAAK,CAAC,iCAAiC,CAAC,CAC1C,CAAC,OAAS,CACR5C,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAA6C,YAAY,CAAG,KAAAA,CAAA,GAAY,CAC/B,KAAM,CAAAjE,eAAe,CAACkE,MAAM,CAAC,CAAC,CAC9B1C,YAAY,CAAC,IAAI,CAAC,CAClBF,kBAAkB,CAAC,KAAK,CAAC,CACzBf,YAAY,CAAC,OAAO,CAAC,CACrBmB,YAAY,CAAC,CAAEC,KAAK,CAAE,EAAE,CAAEC,QAAQ,CAAE,EAAG,CAAC,CAAC,CAC3C,CAAC,CAED,KAAM,CAAAuC,cAAc,CAAIC,YAAuB,EAAK,CAClD,KAAM,CAAA5D,KAAK,CAAG4D,YAAY,CAACC,MAAM,CAAC,CAACC,GAAG,CAAEC,OAAO,GAAK,CAClDD,GAAG,CAAC3D,aAAa,EAAE,CAEnB,GAAI4D,OAAO,CAACC,MAAM,GAAK,WAAW,CAAE,CAClCF,GAAG,CAACzD,iBAAiB,EAAE,CACvByD,GAAG,CAAC5D,YAAY,EAAI6D,OAAO,CAACE,MAAM,CACpC,CAAC,IAAM,IAAIF,OAAO,CAACC,MAAM,GAAK,SAAS,EAAID,OAAO,CAACC,MAAM,GAAK,YAAY,CAAE,CAC1EF,GAAG,CAAC1D,eAAe,EAAE,CACvB,CAAC,IAAM,IAAI2D,OAAO,CAACC,MAAM,GAAK,QAAQ,CAAE,CACtCF,GAAG,CAACxD,cAAc,EAAE,CACtB,CAEA,MAAO,CAAAwD,GAAG,CACZ,CAAC,CAAE,CACD5D,YAAY,CAAE,CAAC,CACfC,aAAa,CAAE,CAAC,CAChBC,eAAe,CAAE,CAAC,CAClBC,iBAAiB,CAAE,CAAC,CACpBC,cAAc,CAAE,CAClB,CAAC,CAAC,CAEFL,QAAQ,CAACD,KAAK,CAAC,CACjB,CAAC,CAED,KAAM,CAAAkE,mBAAmB,CAAG,KAAO,CAAAC,SAAiB,EAAK,CACvD;AACA,GAAI,CAACC,OAAO,CAAC,kDAAkD,CAAC,CAAE,CAChE,OACF,CAEA,GAAI,CACF,KAAM,CAAAf,QAAQ,CAAG,KAAM,CAAA/D,iBAAiB,CAAC+E,aAAa,CAACF,SAAS,CAAC,CACjE,GAAId,QAAQ,CAAC7B,OAAO,CAAE,CACpBgC,KAAK,CAAC,mCAAmC,CAAC,CAC1CnC,iBAAiB,CAAC,CAAC,CAAE;AACvB,CAAC,IAAM,CACLmC,KAAK,iCAAAc,MAAA,CAAiCjB,QAAQ,CAACP,KAAK,CAAE,CAAC,CACzD,CACF,CAAE,MAAOA,KAAK,CAAE,CACdU,KAAK,CAAC,6BAA6B,CAAC,CACtC,CACF,CAAC,CAED,KAAM,CAAAe,cAAc,CAAIN,MAAc,EAAK,CACzC,MAAO,CAAA3E,iBAAiB,CAACkF,eAAe,CAACP,MAAM,CAAC,CAClD,CAAC,CAED,GAAItD,OAAO,CAAE,CACX,mBACEjB,IAAA,QAAK+E,SAAS,CAAC,0DAA0D,CAAAC,QAAA,cACvEhF,IAAA,QAAK+E,SAAS,CAAC,gEAAgE,CAAM,CAAC,CACnF,CAAC,CAEV,CAEA;AACA,GAAI,CAAC5D,eAAe,EAAIf,SAAS,GAAK,OAAO,CAAE,CAC7C,mBACEJ,IAAA,QAAK+E,SAAS,CAAC,0DAA0D,CAAAC,QAAA,cACvE9E,KAAA,QAAK6E,SAAS,CAAC,2BAA2B,CAAAC,QAAA,eACxC9E,KAAA,QAAA8E,QAAA,eACEhF,IAAA,OAAI+E,SAAS,CAAC,wDAAwD,CAAAC,QAAA,CAAC,uBAEvE,CAAI,CAAC,cACLhF,IAAA,MAAG+E,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,qCAEtD,CAAG,CAAC,EACD,CAAC,cACN9E,KAAA,SAAM6E,SAAS,CAAC,gBAAgB,CAACE,QAAQ,CAAEzB,WAAY,CAAAwB,QAAA,eACrD9E,KAAA,QAAK6E,SAAS,CAAC,kCAAkC,CAAAC,QAAA,eAC/ChF,IAAA,QAAAgF,QAAA,cACEhF,IAAA,UACEkF,IAAI,CAAC,OAAO,CACZC,QAAQ,MACRJ,SAAS,CAAC,wNAAwN,CAClOK,WAAW,CAAC,OAAO,CACnBC,KAAK,CAAE9D,SAAS,CAACE,KAAM,CACvB6D,QAAQ,CAAG7B,CAAC,EAAKjC,YAAY,CAAA+D,aAAA,CAAAA,aAAA,IAAMhE,SAAS,MAAEE,KAAK,CAAEgC,CAAC,CAAC+B,MAAM,CAACH,KAAK,EAAE,CAAE,CACxE,CAAC,CACC,CAAC,cACNrF,IAAA,QAAAgF,QAAA,cACEhF,IAAA,UACEkF,IAAI,CAAC,UAAU,CACfC,QAAQ,MACRJ,SAAS,CAAC,wNAAwN,CAClOK,WAAW,CAAC,OAAO,CACnBC,KAAK,CAAE9D,SAAS,CAACG,QAAS,CAC1B4D,QAAQ,CAAG7B,CAAC,EAAKjC,YAAY,CAAA+D,aAAA,CAAAA,aAAA,IAAMhE,SAAS,MAAEG,QAAQ,CAAE+B,CAAC,CAAC+B,MAAM,CAACH,KAAK,EAAE,CAAE,CAC3E,CAAC,CACC,CAAC,EACH,CAAC,cACNrF,IAAA,QAAAgF,QAAA,cACEhF,IAAA,WACEkF,IAAI,CAAC,QAAQ,CACbH,SAAS,CAAC,+NAA+N,CAAAC,QAAA,CAC1O,QAED,CAAQ,CAAC,CACN,CAAC,cACN9E,KAAA,QAAK6E,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDhF,IAAA,MAAAgF,QAAA,CAAG,wBAAmB,CAAG,CAAC,cAC1BhF,IAAA,MAAAgF,QAAA,CAAG,yBAAuB,CAAG,CAAC,cAC9BhF,IAAA,MAAAgF,QAAA,CAAG,iBAAe,CAAG,CAAC,EACnB,CAAC,EACF,CAAC,EACJ,CAAC,CACH,CAAC,CAEV,CAEA,mBACEhF,IAAA,QAAK+E,SAAS,CAAC,yBAAyB,CAAAC,QAAA,cACtC9E,KAAA,QAAK6E,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAE1D9E,KAAA,QAAK6E,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eACrD9E,KAAA,QAAA8E,QAAA,eACEhF,IAAA,OAAI+E,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,uBAAqB,CAAI,CAAC,cAC3EhF,IAAA,MAAG+E,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,8CAAyC,CAAG,CAAC,CACzE3D,SAAS,eACRnB,KAAA,MAAG6E,SAAS,CAAC,uBAAuB,CAAAC,QAAA,EAAC,eAAa,CAAC3D,SAAS,CAACoE,IAAI,CAAC,IAAE,CAACpE,SAAS,CAACI,KAAK,CAAC,GAAC,EAAG,CAC1F,EACE,CAAC,cACNzB,IAAA,WACE0F,OAAO,CAAE3B,YAAa,CACtBgB,SAAS,CAAC,iFAAiF,CAAAC,QAAA,CAC5F,MAED,CAAQ,CAAC,EACN,CAAC,cAGNhF,IAAA,QAAK+E,SAAS,CAAC,MAAM,CAAAC,QAAA,cACnBhF,IAAA,QAAK+E,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAC5B,CACC,CAAEW,EAAE,CAAE,UAAU,CAAEC,KAAK,CAAE,aAAc,CAAC,CACxC,CAAED,EAAE,CAAE,UAAU,CAAEC,KAAK,CAAE,YAAa,CAAC,CACvC,CAAED,EAAE,CAAE,QAAQ,CAAEC,KAAK,CAAE,SAAU,CAAC,CACnC,CAACC,GAAG,CAAEC,GAAG,eACR9F,IAAA,WAEE0F,OAAO,CAAEA,CAAA,GAAMrF,YAAY,CAACyF,GAAG,CAACH,EAAS,CAAE,CAC3CZ,SAAS,6CAAAH,MAAA,CACPxE,SAAS,GAAK0F,GAAG,CAACH,EAAE,CAChB,+BAA+B,CAC/B,4EAA4E,CAC/E,CAAAX,QAAA,CAEFc,GAAG,CAACF,KAAK,EARLE,GAAG,CAACH,EASH,CACT,CAAC,CACC,CAAC,CACH,CAAC,CAGLvF,SAAS,GAAK,UAAU,eACvBF,KAAA,QAAK6E,SAAS,CAAC,WAAW,CAAAC,QAAA,eAExB9E,KAAA,QAAK6E,SAAS,CAAC,sDAAsD,CAAAC,QAAA,eACnEhF,IAAA,QAAK+E,SAAS,CAAC,gCAAgC,CAAAC,QAAA,cAC7C9E,KAAA,QAAK6E,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChChF,IAAA,QAAK+E,SAAS,CAAC,eAAe,CAAAC,QAAA,cAC5BhF,IAAA,QAAK+E,SAAS,CAAC,kEAAkE,CAAAC,QAAA,cAC/EhF,IAAA,QAAK+E,SAAS,CAAC,oBAAoB,CAACgB,IAAI,CAAC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAAhB,QAAA,cACzEhF,IAAA,SAAMiG,CAAC,CAAC,mJAAmJ,CAAE,CAAC,CAC3J,CAAC,CACH,CAAC,CACH,CAAC,cACNjG,IAAA,QAAK+E,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9B9E,KAAA,OAAA8E,QAAA,eACEhF,IAAA,OAAI+E,SAAS,CAAC,4CAA4C,CAAAC,QAAA,CAAC,eAE3D,CAAI,CAAC,cACLhF,IAAA,OAAI+E,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAC9CH,cAAc,CAACvE,KAAK,CAACE,YAAY,CAAC,CACjC,CAAC,EACH,CAAC,CACF,CAAC,EACH,CAAC,CACH,CAAC,cAENR,IAAA,QAAK+E,SAAS,CAAC,gCAAgC,CAAAC,QAAA,cAC7C9E,KAAA,QAAK6E,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChChF,IAAA,QAAK+E,SAAS,CAAC,eAAe,CAAAC,QAAA,cAC5BhF,IAAA,QAAK+E,SAAS,CAAC,iEAAiE,CAAAC,QAAA,cAC9EhF,IAAA,QAAK+E,SAAS,CAAC,oBAAoB,CAACgB,IAAI,CAAC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAAhB,QAAA,cACzEhF,IAAA,SAAMkG,QAAQ,CAAC,SAAS,CAACD,CAAC,CAAC,uIAAuI,CAACE,QAAQ,CAAC,SAAS,CAAE,CAAC,CACrL,CAAC,CACH,CAAC,CACH,CAAC,cACNnG,IAAA,QAAK+E,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9B9E,KAAA,OAAA8E,QAAA,eACEhF,IAAA,OAAI+E,SAAS,CAAC,4CAA4C,CAAAC,QAAA,CAAC,0BAE3D,CAAI,CAAC,cACLhF,IAAA,OAAI+E,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAC9C1E,KAAK,CAACK,iBAAiB,CACtB,CAAC,EACH,CAAC,CACF,CAAC,EACH,CAAC,CACH,CAAC,cAENX,IAAA,QAAK+E,SAAS,CAAC,gCAAgC,CAAAC,QAAA,cAC7C9E,KAAA,QAAK6E,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChChF,IAAA,QAAK+E,SAAS,CAAC,eAAe,CAAAC,QAAA,cAC5BhF,IAAA,QAAK+E,SAAS,CAAC,mEAAmE,CAAAC,QAAA,cAChFhF,IAAA,QAAK+E,SAAS,CAAC,oBAAoB,CAACgB,IAAI,CAAC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAAhB,QAAA,cACzEhF,IAAA,SAAMkG,QAAQ,CAAC,SAAS,CAACD,CAAC,CAAC,oHAAoH,CAACE,QAAQ,CAAC,SAAS,CAAE,CAAC,CAClK,CAAC,CACH,CAAC,CACH,CAAC,cACNnG,IAAA,QAAK+E,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9B9E,KAAA,OAAA8E,QAAA,eACEhF,IAAA,OAAI+E,SAAS,CAAC,4CAA4C,CAAAC,QAAA,CAAC,sBAE3D,CAAI,CAAC,cACLhF,IAAA,OAAI+E,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAC9C1E,KAAK,CAACI,eAAe,CACpB,CAAC,EACH,CAAC,CACF,CAAC,EACH,CAAC,CACH,CAAC,cAENV,IAAA,QAAK+E,SAAS,CAAC,gCAAgC,CAAAC,QAAA,cAC7C9E,KAAA,QAAK6E,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChChF,IAAA,QAAK+E,SAAS,CAAC,eAAe,CAAAC,QAAA,cAC5BhF,IAAA,QAAK+E,SAAS,CAAC,gEAAgE,CAAAC,QAAA,cAC7EhF,IAAA,QAAK+E,SAAS,CAAC,oBAAoB,CAACgB,IAAI,CAAC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAAhB,QAAA,cACzEhF,IAAA,SAAMkG,QAAQ,CAAC,SAAS,CAACD,CAAC,CAAC,yNAAyN,CAACE,QAAQ,CAAC,SAAS,CAAE,CAAC,CACvQ,CAAC,CACH,CAAC,CACH,CAAC,cACNnG,IAAA,QAAK+E,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9B9E,KAAA,OAAA8E,QAAA,eACEhF,IAAA,OAAI+E,SAAS,CAAC,4CAA4C,CAAAC,QAAA,CAAC,qBAE3D,CAAI,CAAC,cACLhF,IAAA,OAAI+E,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAC9C1E,KAAK,CAACM,cAAc,CACnB,CAAC,EACH,CAAC,CACF,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,cAGNV,KAAA,QAAK6E,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACzChF,IAAA,QAAK+E,SAAS,CAAC,oCAAoC,CAAAC,QAAA,cACjDhF,IAAA,OAAI+E,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,mBAAiB,CAAI,CAAC,CACrE,CAAC,cACNhF,IAAA,QAAK+E,SAAS,CAAC,KAAK,CAAAC,QAAA,cAClBhF,IAAA,QAAK+E,SAAS,CAAC,WAAW,CAAAC,QAAA,CACvBnE,QAAQ,CAACuF,KAAK,CAAC,CAAC,CAAE,CAAC,CAAC,CAACP,GAAG,CAAExB,OAAO,eAChCnE,KAAA,QAAsB6E,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eACjE9E,KAAA,QAAK6E,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1ChF,IAAA,CAACL,aAAa,EAAC2E,MAAM,CAAED,OAAO,CAACC,MAAO,CAAC+B,IAAI,CAAC,IAAI,CAAE,CAAC,cACnDnG,KAAA,QAAA8E,QAAA,eACE9E,KAAA,MAAG6E,SAAS,CAAC,mCAAmC,CAAAC,QAAA,EAAC,aACpC,CAACX,OAAO,CAACsB,EAAE,CAACS,KAAK,CAAC,CAAC,CAAC,CAAC,EAC/B,CAAC,cACJpG,IAAA,MAAG+E,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CACjCH,cAAc,CAACR,OAAO,CAACE,MAAM,CAAC,CAC9B,CAAC,EACD,CAAC,EACH,CAAC,cACNvE,IAAA,QAAK+E,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CACnC,GAAI,CAAAsB,IAAI,CAACjC,OAAO,CAACkC,SAAS,CAAC,CAACC,kBAAkB,CAAC,OAAO,CAAC,CACrD,CAAC,GAdEnC,OAAO,CAACsB,EAeb,CACN,CAAC,CACC,CAAC,CACH,CAAC,EACH,CAAC,EACH,CACN,CAGAvF,SAAS,GAAK,UAAU,eACvBF,KAAA,QAAK6E,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACzChF,IAAA,QAAK+E,SAAS,CAAC,oCAAoC,CAAAC,QAAA,cACjDhF,IAAA,OAAI+E,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,yBAAoB,CAAI,CAAC,CACxE,CAAC,cACNhF,IAAA,QAAK+E,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9B9E,KAAA,UAAO6E,SAAS,CAAC,qCAAqC,CAAAC,QAAA,eACpDhF,IAAA,UAAO+E,SAAS,CAAC,YAAY,CAAAC,QAAA,cAC3B9E,KAAA,OAAA8E,QAAA,eACEhF,IAAA,OAAI+E,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAAC,IAE/F,CAAI,CAAC,cACLhF,IAAA,OAAI+E,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAAC,OAE/F,CAAI,CAAC,cACLhF,IAAA,OAAI+E,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAAC,QAE/F,CAAI,CAAC,cACLhF,IAAA,OAAI+E,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAAC,WAE/F,CAAI,CAAC,cACLhF,IAAA,OAAI+E,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAAC,MAE/F,CAAI,CAAC,cACLhF,IAAA,OAAI+E,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAAC,aAE/F,CAAI,CAAC,EACH,CAAC,CACA,CAAC,cACRhF,IAAA,UAAO+E,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CACjDnE,QAAQ,CAACgF,GAAG,CAAExB,OAAO,eACpBnE,KAAA,OAAA8E,QAAA,eACE9E,KAAA,OAAI6E,SAAS,CAAC,+DAA+D,CAAAC,QAAA,EAAC,GAC3E,CAACX,OAAO,CAACsB,EAAE,CAACS,KAAK,CAAC,CAAC,CAAC,CAAC,EACpB,CAAC,cACLpG,IAAA,OAAI+E,SAAS,CAAC,mDAAmD,CAAAC,QAAA,CAC9DH,cAAc,CAACR,OAAO,CAACE,MAAM,CAAC,CAC7B,CAAC,cACLvE,IAAA,OAAI+E,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cACzChF,IAAA,CAACL,aAAa,EAAC2E,MAAM,CAAED,OAAO,CAACC,MAAO,CAAC+B,IAAI,CAAC,IAAI,CAAE,CAAC,CACjD,CAAC,cACLrG,IAAA,OAAI+E,SAAS,CAAC,mDAAmD,CAAAC,QAAA,CAC9DX,OAAO,CAACoC,aAAa,CACpB,CAAC,cACLzG,IAAA,OAAI+E,SAAS,CAAC,mDAAmD,CAAAC,QAAA,CAC9D,GAAI,CAAAsB,IAAI,CAACjC,OAAO,CAACkC,SAAS,CAAC,CAACC,kBAAkB,CAAC,OAAO,CAAC,CACtD,CAAC,cACLxG,IAAA,OAAI+E,SAAS,CAAC,iDAAiD,CAAAC,QAAA,CAC5DpF,iBAAiB,CAAC8G,SAAS,CAACrC,OAAO,CAACC,MAAM,CAAC,eAC1CtE,IAAA,WACE0F,OAAO,CAAEA,CAAA,GAAMlB,mBAAmB,CAACH,OAAO,CAACsB,EAAE,CAAE,CAC/CZ,SAAS,CAAC,iCAAiC,CAAAC,QAAA,CAC5C,YAED,CAAQ,CACT,CACC,CAAC,GAzBEX,OAAO,CAACsB,EA0Bb,CACL,CAAC,CACG,CAAC,EACH,CAAC,CACL,CAAC,EACH,CACN,CAGAvF,SAAS,GAAK,QAAQ,eACrBF,KAAA,QAAK6E,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACzChF,IAAA,QAAK+E,SAAS,CAAC,oCAAoC,CAAAC,QAAA,cACjDhF,IAAA,OAAI+E,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,sBAAiB,CAAI,CAAC,CACrE,CAAC,cACNhF,IAAA,QAAK+E,SAAS,CAAC,KAAK,CAAAC,QAAA,cAClBhF,IAAA,QAAK+E,SAAS,CAAC,WAAW,CAAAC,QAAA,CACvBjE,MAAM,CAAC8E,GAAG,CAAEc,KAAK,eAChBzG,KAAA,QAAoB6E,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eACnE9E,KAAA,QAAK6E,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eACrD9E,KAAA,OAAI6E,SAAS,CAAC,mCAAmC,CAAAC,QAAA,EAAC,UACxC,CAAC2B,KAAK,CAAChB,EAAE,CAACS,KAAK,CAAC,CAAC,CAAC,CAAC,EACzB,CAAC,cACLpG,IAAA,SAAM+E,SAAS,+CAAAH,MAAA,CACb+B,KAAK,CAACrC,MAAM,GAAK,WAAW,CAAG,6BAA6B,CAC5DqC,KAAK,CAACrC,MAAM,GAAK,aAAa,CAAG,2BAA2B,CAC5DqC,KAAK,CAACrC,MAAM,GAAK,OAAO,CAAG,+BAA+B,CAC1D,2BAA2B,CAC1B,CAAAU,QAAA,CACA2B,KAAK,CAACrC,MAAM,CACT,CAAC,EACJ,CAAC,cACNpE,KAAA,QAAK6E,SAAS,CAAC,gCAAgC,CAAAC,QAAA,eAC7C9E,KAAA,QAAA8E,QAAA,eACEhF,IAAA,SAAM+E,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,QAAM,CAAM,CAAC,cAC7ChF,IAAA,SAAM+E,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAAEH,cAAc,CAAC8B,KAAK,CAACC,WAAW,CAAC,CAAO,CAAC,EAC1E,CAAC,cACN1G,KAAA,QAAA8E,QAAA,eACEhF,IAAA,SAAM+E,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,YAAU,CAAM,CAAC,cACjDhF,IAAA,SAAM+E,SAAS,qBAAAH,MAAA,CACb+B,KAAK,CAACE,aAAa,GAAK,MAAM,CAAG,gBAAgB,CACjDF,KAAK,CAACE,aAAa,GAAK,SAAS,CAAG,iBAAiB,CACrD,cAAc,CACb,CAAA7B,QAAA,CACA2B,KAAK,CAACE,aAAa,CAChB,CAAC,EACJ,CAAC,cACN3G,KAAA,QAAA8E,QAAA,eACEhF,IAAA,SAAM+E,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,OAAK,CAAM,CAAC,cAC5ChF,IAAA,SAAM+E,SAAS,CAAC,MAAM,CAAAC,QAAA,CAAE,GAAI,CAAAsB,IAAI,CAACK,KAAK,CAACJ,SAAS,CAAC,CAACC,kBAAkB,CAAC,OAAO,CAAC,CAAO,CAAC,EAClF,CAAC,cACNtG,KAAA,QAAA8E,QAAA,eACEhF,IAAA,SAAM+E,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,QAAM,CAAM,CAAC,cAC7ChF,IAAA,SAAM+E,SAAS,CAAC,MAAM,CAAAC,QAAA,CAAE2B,KAAK,CAACG,SAAS,CAACC,MAAM,CAAO,CAAC,EACnD,CAAC,EACH,CAAC,GArCEJ,KAAK,CAAChB,EAsCX,CACN,CAAC,CACC,CAAC,CACH,CAAC,EACH,CACN,EACE,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAxF,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}