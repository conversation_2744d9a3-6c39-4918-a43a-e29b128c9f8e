{"ast": null, "code": "// This optional code is used to register a service worker.\n// register() is not called by default.\n// This lets the app load faster on subsequent visits in production, and gives\n// it offline capabilities. However, it also means that developers (and users)\n// will only see deployed updates on subsequent visits to a page, after all the\n// existing tabs open on the page have been closed, since previously cached\n// resources are updated in the background.\n// To learn more about the benefits of this model and instructions on how to\n// opt-in, read https://cra.link/PWA\nconst isLocalhost=Boolean(window.location.hostname==='localhost'||// [::1] is the IPv6 localhost address.\nwindow.location.hostname==='[::1]'||// *********/8 are considered localhost for IPv4.\nwindow.location.hostname.match(/^127(?:\\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}$/));export function register(config){if('serviceWorker'in navigator){// The URL constructor is available in all browsers that support SW.\nconst publicUrl=new URL(process.env.PUBLIC_URL,window.location.href);if(publicUrl.origin!==window.location.origin){// Our service worker won't work if PUBLIC_URL is on a different origin\n// from what our page is served on. This might happen if a CDN is used to\n// serve assets; see https://github.com/facebook/create-react-app/issues/2374\nreturn;}window.addEventListener('load',()=>{const swUrl=\"\".concat(process.env.PUBLIC_URL,\"/service-worker.js\");if(isLocalhost){// This is running on localhost. Let's check if a service worker still exists or not.\ncheckValidServiceWorker(swUrl,config);// Add some additional logging to localhost, pointing developers to the\n// service worker/PWA documentation.\nnavigator.serviceWorker.ready.then(()=>{console.log('This web app is being served cache-first by a service '+'worker. To learn more, visit https://cra.link/PWA');});}else{// Is not localhost. Just register service worker\nregisterValidSW(swUrl,config);}});}}function registerValidSW(swUrl,config){navigator.serviceWorker.register(swUrl).then(registration=>{registration.onupdatefound=()=>{const installingWorker=registration.installing;if(installingWorker==null){return;}installingWorker.onstatechange=()=>{if(installingWorker.state==='installed'){if(navigator.serviceWorker.controller){// At this point, the updated precached content has been fetched,\n// but the previous service worker will still serve the older\n// content until all client tabs are closed.\nconsole.log('New content is available and will be used when all '+'tabs for this page are closed. See https://cra.link/PWA.');// Execute callback\nif(config&&config.onUpdate){config.onUpdate(registration);}}else{// At this point, everything has been precached.\n// It's the perfect time to display a\n// \"Content is cached for offline use.\" message.\nconsole.log('Content is cached for offline use.');// Execute callback\nif(config&&config.onSuccess){config.onSuccess(registration);}}}};};}).catch(error=>{console.error('Error during service worker registration:',error);});}function checkValidServiceWorker(swUrl,config){// Check if the service worker can be found. If it can't reload the page.\nfetch(swUrl,{headers:{'Service-Worker':'script'}}).then(response=>{// Ensure service worker exists, and that we really are getting a JS file.\nconst contentType=response.headers.get('content-type');if(response.status===404||contentType!=null&&contentType.indexOf('javascript')===-1){// No service worker found. Probably a different app. Reload the page.\nnavigator.serviceWorker.ready.then(registration=>{registration.unregister().then(()=>{window.location.reload();});});}else{// Service worker found. Proceed as normal.\nregisterValidSW(swUrl,config);}}).catch(()=>{console.log('No internet connection found. App is running in offline mode.');});}export function unregister(){if('serviceWorker'in navigator){navigator.serviceWorker.ready.then(registration=>{registration.unregister();}).catch(error=>{console.error(error.message);});}}", "map": {"version": 3, "names": ["isLocalhost", "Boolean", "window", "location", "hostname", "match", "register", "config", "navigator", "publicUrl", "URL", "process", "env", "PUBLIC_URL", "href", "origin", "addEventListener", "swUrl", "concat", "checkValidServiceWorker", "serviceWorker", "ready", "then", "console", "log", "registerValidSW", "registration", "onupdatefound", "installingWorker", "installing", "onstatechange", "state", "controller", "onUpdate", "onSuccess", "catch", "error", "fetch", "headers", "response", "contentType", "get", "status", "indexOf", "unregister", "reload", "message"], "sources": ["/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/serviceWorkerRegistration.ts"], "sourcesContent": ["// This optional code is used to register a service worker.\n// register() is not called by default.\n\n// This lets the app load faster on subsequent visits in production, and gives\n// it offline capabilities. However, it also means that developers (and users)\n// will only see deployed updates on subsequent visits to a page, after all the\n// existing tabs open on the page have been closed, since previously cached\n// resources are updated in the background.\n\n// To learn more about the benefits of this model and instructions on how to\n// opt-in, read https://cra.link/PWA\n\nconst isLocalhost = Boolean(\n  window.location.hostname === 'localhost' ||\n    // [::1] is the IPv6 localhost address.\n    window.location.hostname === '[::1]' ||\n    // *********/8 are considered localhost for IPv4.\n    window.location.hostname.match(\n      /^127(?:\\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}$/\n    )\n);\n\ntype Config = {\n  onSuccess?: (registration: ServiceWorkerRegistration) => void;\n  onUpdate?: (registration: ServiceWorkerRegistration) => void;\n};\n\nexport function register(config?: Config) {\n  if ('serviceWorker' in navigator) {\n    // The URL constructor is available in all browsers that support SW.\n    const publicUrl = new URL(\n      process.env.PUBLIC_URL!,\n      window.location.href\n    );\n    if (publicUrl.origin !== window.location.origin) {\n      // Our service worker won't work if PUBLIC_URL is on a different origin\n      // from what our page is served on. This might happen if a CDN is used to\n      // serve assets; see https://github.com/facebook/create-react-app/issues/2374\n      return;\n    }\n\n    window.addEventListener('load', () => {\n      const swUrl = `${process.env.PUBLIC_URL}/service-worker.js`;\n\n      if (isLocalhost) {\n        // This is running on localhost. Let's check if a service worker still exists or not.\n        checkValidServiceWorker(swUrl, config);\n\n        // Add some additional logging to localhost, pointing developers to the\n        // service worker/PWA documentation.\n        navigator.serviceWorker.ready.then(() => {\n          console.log(\n            'This web app is being served cache-first by a service ' +\n              'worker. To learn more, visit https://cra.link/PWA'\n          );\n        });\n      } else {\n        // Is not localhost. Just register service worker\n        registerValidSW(swUrl, config);\n      }\n    });\n  }\n}\n\nfunction registerValidSW(swUrl: string, config?: Config) {\n  navigator.serviceWorker\n    .register(swUrl)\n    .then((registration) => {\n      registration.onupdatefound = () => {\n        const installingWorker = registration.installing;\n        if (installingWorker == null) {\n          return;\n        }\n        installingWorker.onstatechange = () => {\n          if (installingWorker.state === 'installed') {\n            if (navigator.serviceWorker.controller) {\n              // At this point, the updated precached content has been fetched,\n              // but the previous service worker will still serve the older\n              // content until all client tabs are closed.\n              console.log(\n                'New content is available and will be used when all ' +\n                  'tabs for this page are closed. See https://cra.link/PWA.'\n              );\n\n              // Execute callback\n              if (config && config.onUpdate) {\n                config.onUpdate(registration);\n              }\n            } else {\n              // At this point, everything has been precached.\n              // It's the perfect time to display a\n              // \"Content is cached for offline use.\" message.\n              console.log('Content is cached for offline use.');\n\n              // Execute callback\n              if (config && config.onSuccess) {\n                config.onSuccess(registration);\n              }\n            }\n          }\n        };\n      };\n    })\n    .catch((error) => {\n      console.error('Error during service worker registration:', error);\n    });\n}\n\nfunction checkValidServiceWorker(swUrl: string, config?: Config) {\n  // Check if the service worker can be found. If it can't reload the page.\n  fetch(swUrl, {\n    headers: { 'Service-Worker': 'script' },\n  })\n    .then((response) => {\n      // Ensure service worker exists, and that we really are getting a JS file.\n      const contentType = response.headers.get('content-type');\n      if (\n        response.status === 404 ||\n        (contentType != null && contentType.indexOf('javascript') === -1)\n      ) {\n        // No service worker found. Probably a different app. Reload the page.\n        navigator.serviceWorker.ready.then((registration) => {\n          registration.unregister().then(() => {\n            window.location.reload();\n          });\n        });\n      } else {\n        // Service worker found. Proceed as normal.\n        registerValidSW(swUrl, config);\n      }\n    })\n    .catch(() => {\n      console.log(\n        'No internet connection found. App is running in offline mode.'\n      );\n    });\n}\n\nexport function unregister() {\n  if ('serviceWorker' in navigator) {\n    navigator.serviceWorker.ready\n      .then((registration) => {\n        registration.unregister();\n      })\n      .catch((error) => {\n        console.error(error.message);\n      });\n  }\n}\n"], "mappings": "AAAA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA,KAAM,CAAAA,WAAW,CAAGC,OAAO,CACzBC,MAAM,CAACC,QAAQ,CAACC,QAAQ,GAAK,WAAW,EACtC;AACAF,MAAM,CAACC,QAAQ,CAACC,QAAQ,GAAK,OAAO,EACpC;AACAF,MAAM,CAACC,QAAQ,CAACC,QAAQ,CAACC,KAAK,CAC5B,wDACF,CACJ,CAAC,CAOD,MAAO,SAAS,CAAAC,QAAQA,CAACC,MAAe,CAAE,CACxC,GAAI,eAAe,EAAI,CAAAC,SAAS,CAAE,CAChC;AACA,KAAM,CAAAC,SAAS,CAAG,GAAI,CAAAC,GAAG,CACvBC,OAAO,CAACC,GAAG,CAACC,UAAU,CACtBX,MAAM,CAACC,QAAQ,CAACW,IAClB,CAAC,CACD,GAAIL,SAAS,CAACM,MAAM,GAAKb,MAAM,CAACC,QAAQ,CAACY,MAAM,CAAE,CAC/C;AACA;AACA;AACA,OACF,CAEAb,MAAM,CAACc,gBAAgB,CAAC,MAAM,CAAE,IAAM,CACpC,KAAM,CAAAC,KAAK,IAAAC,MAAA,CAAMP,OAAO,CAACC,GAAG,CAACC,UAAU,sBAAoB,CAE3D,GAAIb,WAAW,CAAE,CACf;AACAmB,uBAAuB,CAACF,KAAK,CAAEV,MAAM,CAAC,CAEtC;AACA;AACAC,SAAS,CAACY,aAAa,CAACC,KAAK,CAACC,IAAI,CAAC,IAAM,CACvCC,OAAO,CAACC,GAAG,CACT,wDAAwD,CACtD,mDACJ,CAAC,CACH,CAAC,CAAC,CACJ,CAAC,IAAM,CACL;AACAC,eAAe,CAACR,KAAK,CAAEV,MAAM,CAAC,CAChC,CACF,CAAC,CAAC,CACJ,CACF,CAEA,QAAS,CAAAkB,eAAeA,CAACR,KAAa,CAAEV,MAAe,CAAE,CACvDC,SAAS,CAACY,aAAa,CACpBd,QAAQ,CAACW,KAAK,CAAC,CACfK,IAAI,CAAEI,YAAY,EAAK,CACtBA,YAAY,CAACC,aAAa,CAAG,IAAM,CACjC,KAAM,CAAAC,gBAAgB,CAAGF,YAAY,CAACG,UAAU,CAChD,GAAID,gBAAgB,EAAI,IAAI,CAAE,CAC5B,OACF,CACAA,gBAAgB,CAACE,aAAa,CAAG,IAAM,CACrC,GAAIF,gBAAgB,CAACG,KAAK,GAAK,WAAW,CAAE,CAC1C,GAAIvB,SAAS,CAACY,aAAa,CAACY,UAAU,CAAE,CACtC;AACA;AACA;AACAT,OAAO,CAACC,GAAG,CACT,qDAAqD,CACnD,0DACJ,CAAC,CAED;AACA,GAAIjB,MAAM,EAAIA,MAAM,CAAC0B,QAAQ,CAAE,CAC7B1B,MAAM,CAAC0B,QAAQ,CAACP,YAAY,CAAC,CAC/B,CACF,CAAC,IAAM,CACL;AACA;AACA;AACAH,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC,CAEjD;AACA,GAAIjB,MAAM,EAAIA,MAAM,CAAC2B,SAAS,CAAE,CAC9B3B,MAAM,CAAC2B,SAAS,CAACR,YAAY,CAAC,CAChC,CACF,CACF,CACF,CAAC,CACH,CAAC,CACH,CAAC,CAAC,CACDS,KAAK,CAAEC,KAAK,EAAK,CAChBb,OAAO,CAACa,KAAK,CAAC,2CAA2C,CAAEA,KAAK,CAAC,CACnE,CAAC,CAAC,CACN,CAEA,QAAS,CAAAjB,uBAAuBA,CAACF,KAAa,CAAEV,MAAe,CAAE,CAC/D;AACA8B,KAAK,CAACpB,KAAK,CAAE,CACXqB,OAAO,CAAE,CAAE,gBAAgB,CAAE,QAAS,CACxC,CAAC,CAAC,CACChB,IAAI,CAAEiB,QAAQ,EAAK,CAClB;AACA,KAAM,CAAAC,WAAW,CAAGD,QAAQ,CAACD,OAAO,CAACG,GAAG,CAAC,cAAc,CAAC,CACxD,GACEF,QAAQ,CAACG,MAAM,GAAK,GAAG,EACtBF,WAAW,EAAI,IAAI,EAAIA,WAAW,CAACG,OAAO,CAAC,YAAY,CAAC,GAAK,CAAC,CAAE,CACjE,CACA;AACAnC,SAAS,CAACY,aAAa,CAACC,KAAK,CAACC,IAAI,CAAEI,YAAY,EAAK,CACnDA,YAAY,CAACkB,UAAU,CAAC,CAAC,CAACtB,IAAI,CAAC,IAAM,CACnCpB,MAAM,CAACC,QAAQ,CAAC0C,MAAM,CAAC,CAAC,CAC1B,CAAC,CAAC,CACJ,CAAC,CAAC,CACJ,CAAC,IAAM,CACL;AACApB,eAAe,CAACR,KAAK,CAAEV,MAAM,CAAC,CAChC,CACF,CAAC,CAAC,CACD4B,KAAK,CAAC,IAAM,CACXZ,OAAO,CAACC,GAAG,CACT,+DACF,CAAC,CACH,CAAC,CAAC,CACN,CAEA,MAAO,SAAS,CAAAoB,UAAUA,CAAA,CAAG,CAC3B,GAAI,eAAe,EAAI,CAAApC,SAAS,CAAE,CAChCA,SAAS,CAACY,aAAa,CAACC,KAAK,CAC1BC,IAAI,CAAEI,YAAY,EAAK,CACtBA,YAAY,CAACkB,UAAU,CAAC,CAAC,CAC3B,CAAC,CAAC,CACDT,KAAK,CAAEC,KAAK,EAAK,CAChBb,OAAO,CAACa,KAAK,CAACA,KAAK,CAACU,OAAO,CAAC,CAC9B,CAAC,CAAC,CACN,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}