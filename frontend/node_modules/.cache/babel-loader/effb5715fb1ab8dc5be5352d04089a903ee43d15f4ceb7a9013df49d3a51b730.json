{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/PaymentStatus.tsx\";\nimport React from 'react';\nimport { MulticaixaService } from '../services/multicaixa';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PaymentStatus = ({\n  status,\n  className = '',\n  showIcon = true,\n  size = 'md'\n}) => {\n  const statusText = MulticaixaService.getStatusDisplayText(status);\n  const statusColor = MulticaixaService.getStatusColor(status);\n  const sizeClasses = {\n    sm: 'text-xs px-2 py-1',\n    md: 'text-sm px-3 py-1',\n    lg: 'text-base px-4 py-2'\n  };\n  const iconClasses = {\n    sm: 'w-3 h-3',\n    md: 'w-4 h-4',\n    lg: 'w-5 h-5'\n  };\n  const getStatusIcon = status => {\n    switch (status) {\n      case 'PENDING':\n        return /*#__PURE__*/_jsxDEV(\"svg\", {\n          className: `${iconClasses[size]} text-yellow-500`,\n          fill: \"currentColor\",\n          viewBox: \"0 0 20 20\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            fillRule: \"evenodd\",\n            d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z\",\n            clipRule: \"evenodd\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 11\n        }, this);\n      case 'PROCESSING':\n        return /*#__PURE__*/_jsxDEV(\"svg\", {\n          className: `${iconClasses[size]} text-blue-500 animate-spin`,\n          fill: \"none\",\n          viewBox: \"0 0 24 24\",\n          children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n            className: \"opacity-25\",\n            cx: \"12\",\n            cy: \"12\",\n            r: \"10\",\n            stroke: \"currentColor\",\n            strokeWidth: \"4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n            className: \"opacity-75\",\n            fill: \"currentColor\",\n            d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 45,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 11\n        }, this);\n      case 'COMPLETED':\n        return /*#__PURE__*/_jsxDEV(\"svg\", {\n          className: `${iconClasses[size]} text-green-500`,\n          fill: \"currentColor\",\n          viewBox: \"0 0 20 20\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            fillRule: \"evenodd\",\n            d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n            clipRule: \"evenodd\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 11\n        }, this);\n      case 'FAILED':\n        return /*#__PURE__*/_jsxDEV(\"svg\", {\n          className: `${iconClasses[size]} text-red-500`,\n          fill: \"currentColor\",\n          viewBox: \"0 0 20 20\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            fillRule: \"evenodd\",\n            d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n            clipRule: \"evenodd\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 11\n        }, this);\n      case 'CANCELLED':\n        return /*#__PURE__*/_jsxDEV(\"svg\", {\n          className: `${iconClasses[size]} text-gray-500`,\n          fill: \"currentColor\",\n          viewBox: \"0 0 20 20\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            fillRule: \"evenodd\",\n            d: \"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\",\n            clipRule: \"evenodd\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 11\n        }, this);\n      case 'REFUNDED':\n        return /*#__PURE__*/_jsxDEV(\"svg\", {\n          className: `${iconClasses[size]} text-purple-500`,\n          fill: \"currentColor\",\n          viewBox: \"0 0 20 20\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            fillRule: \"evenodd\",\n            d: \"M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z\",\n            clipRule: \"evenodd\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 11\n        }, this);\n      case 'EXPIRED':\n        return /*#__PURE__*/_jsxDEV(\"svg\", {\n          className: `${iconClasses[size]} text-orange-500`,\n          fill: \"currentColor\",\n          viewBox: \"0 0 20 20\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            fillRule: \"evenodd\",\n            d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n            clipRule: \"evenodd\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 11\n        }, this);\n      default:\n        return null;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"span\", {\n    className: `\n      inline-flex items-center gap-2 \n      ${sizeClasses[size]} \n      ${statusColor} \n      bg-gray-100 rounded-full font-medium\n      ${className}\n    `,\n    children: [showIcon && getStatusIcon(status), statusText]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 84,\n    columnNumber: 5\n  }, this);\n};\n_c = PaymentStatus;\nexport default PaymentStatus;\nvar _c;\n$RefreshReg$(_c, \"PaymentStatus\");", "map": {"version": 3, "names": ["React", "MulticaixaService", "jsxDEV", "_jsxDEV", "PaymentStatus", "status", "className", "showIcon", "size", "statusText", "getStatusDisplayText", "statusColor", "getStatusColor", "sizeClasses", "sm", "md", "lg", "iconClasses", "getStatusIcon", "fill", "viewBox", "children", "fillRule", "d", "clipRule", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "cx", "cy", "r", "stroke", "strokeWidth", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/PaymentStatus.tsx"], "sourcesContent": ["import React from 'react';\nimport { MulticaixaService } from '../services/multicaixa';\nimport { PaymentStatus as PaymentStatusType } from '../types';\n\ninterface PaymentStatusProps {\n  status: PaymentStatusType;\n  className?: string;\n  showIcon?: boolean;\n  size?: 'sm' | 'md' | 'lg';\n}\n\nconst PaymentStatus: React.FC<PaymentStatusProps> = ({\n  status,\n  className = '',\n  showIcon = true,\n  size = 'md'\n}) => {\n  const statusText = MulticaixaService.getStatusDisplayText(status);\n  const statusColor = MulticaixaService.getStatusColor(status);\n  \n  const sizeClasses = {\n    sm: 'text-xs px-2 py-1',\n    md: 'text-sm px-3 py-1',\n    lg: 'text-base px-4 py-2'\n  };\n\n  const iconClasses = {\n    sm: 'w-3 h-3',\n    md: 'w-4 h-4',\n    lg: 'w-5 h-5'\n  };\n\n  const getStatusIcon = (status: PaymentStatusType) => {\n    switch (status) {\n      case 'PENDING':\n        return (\n          <svg className={`${iconClasses[size]} text-yellow-500`} fill=\"currentColor\" viewBox=\"0 0 20 20\">\n            <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z\" clipRule=\"evenodd\" />\n          </svg>\n        );\n      case 'PROCESSING':\n        return (\n          <svg className={`${iconClasses[size]} text-blue-500 animate-spin`} fill=\"none\" viewBox=\"0 0 24 24\">\n            <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n            <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n          </svg>\n        );\n      case 'COMPLETED':\n        return (\n          <svg className={`${iconClasses[size]} text-green-500`} fill=\"currentColor\" viewBox=\"0 0 20 20\">\n            <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n          </svg>\n        );\n      case 'FAILED':\n        return (\n          <svg className={`${iconClasses[size]} text-red-500`} fill=\"currentColor\" viewBox=\"0 0 20 20\">\n            <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\n          </svg>\n        );\n      case 'CANCELLED':\n        return (\n          <svg className={`${iconClasses[size]} text-gray-500`} fill=\"currentColor\" viewBox=\"0 0 20 20\">\n            <path fillRule=\"evenodd\" d=\"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\" clipRule=\"evenodd\" />\n          </svg>\n        );\n      case 'REFUNDED':\n        return (\n          <svg className={`${iconClasses[size]} text-purple-500`} fill=\"currentColor\" viewBox=\"0 0 20 20\">\n            <path fillRule=\"evenodd\" d=\"M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z\" clipRule=\"evenodd\" />\n          </svg>\n        );\n      case 'EXPIRED':\n        return (\n          <svg className={`${iconClasses[size]} text-orange-500`} fill=\"currentColor\" viewBox=\"0 0 20 20\">\n            <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\n          </svg>\n        );\n      default:\n        return null;\n    }\n  };\n\n  return (\n    <span className={`\n      inline-flex items-center gap-2 \n      ${sizeClasses[size]} \n      ${statusColor} \n      bg-gray-100 rounded-full font-medium\n      ${className}\n    `}>\n      {showIcon && getStatusIcon(status)}\n      {statusText}\n    </span>\n  );\n};\n\nexport default PaymentStatus;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,iBAAiB,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAU3D,MAAMC,aAA2C,GAAGA,CAAC;EACnDC,MAAM;EACNC,SAAS,GAAG,EAAE;EACdC,QAAQ,GAAG,IAAI;EACfC,IAAI,GAAG;AACT,CAAC,KAAK;EACJ,MAAMC,UAAU,GAAGR,iBAAiB,CAACS,oBAAoB,CAACL,MAAM,CAAC;EACjE,MAAMM,WAAW,GAAGV,iBAAiB,CAACW,cAAc,CAACP,MAAM,CAAC;EAE5D,MAAMQ,WAAW,GAAG;IAClBC,EAAE,EAAE,mBAAmB;IACvBC,EAAE,EAAE,mBAAmB;IACvBC,EAAE,EAAE;EACN,CAAC;EAED,MAAMC,WAAW,GAAG;IAClBH,EAAE,EAAE,SAAS;IACbC,EAAE,EAAE,SAAS;IACbC,EAAE,EAAE;EACN,CAAC;EAED,MAAME,aAAa,GAAIb,MAAyB,IAAK;IACnD,QAAQA,MAAM;MACZ,KAAK,SAAS;QACZ,oBACEF,OAAA;UAAKG,SAAS,EAAE,GAAGW,WAAW,CAACT,IAAI,CAAC,kBAAmB;UAACW,IAAI,EAAC,cAAc;UAACC,OAAO,EAAC,WAAW;UAAAC,QAAA,eAC7FlB,OAAA;YAAMmB,QAAQ,EAAC,SAAS;YAACC,CAAC,EAAC,oHAAoH;YAACC,QAAQ,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClK,CAAC;MAEV,KAAK,YAAY;QACf,oBACEzB,OAAA;UAAKG,SAAS,EAAE,GAAGW,WAAW,CAACT,IAAI,CAAC,6BAA8B;UAACW,IAAI,EAAC,MAAM;UAACC,OAAO,EAAC,WAAW;UAAAC,QAAA,gBAChGlB,OAAA;YAAQG,SAAS,EAAC,YAAY;YAACuB,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC,IAAI;YAACC,CAAC,EAAC,IAAI;YAACC,MAAM,EAAC,cAAc;YAACC,WAAW,EAAC;UAAG;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,eACrGzB,OAAA;YAAMG,SAAS,EAAC,YAAY;YAACa,IAAI,EAAC,cAAc;YAACI,CAAC,EAAC;UAAiH;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzK,CAAC;MAEV,KAAK,WAAW;QACd,oBACEzB,OAAA;UAAKG,SAAS,EAAE,GAAGW,WAAW,CAACT,IAAI,CAAC,iBAAkB;UAACW,IAAI,EAAC,cAAc;UAACC,OAAO,EAAC,WAAW;UAAAC,QAAA,eAC5FlB,OAAA;YAAMmB,QAAQ,EAAC,SAAS;YAACC,CAAC,EAAC,uIAAuI;YAACC,QAAQ,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrL,CAAC;MAEV,KAAK,QAAQ;QACX,oBACEzB,OAAA;UAAKG,SAAS,EAAE,GAAGW,WAAW,CAACT,IAAI,CAAC,eAAgB;UAACW,IAAI,EAAC,cAAc;UAACC,OAAO,EAAC,WAAW;UAAAC,QAAA,eAC1FlB,OAAA;YAAMmB,QAAQ,EAAC,SAAS;YAACC,CAAC,EAAC,yNAAyN;YAACC,QAAQ,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvQ,CAAC;MAEV,KAAK,WAAW;QACd,oBACEzB,OAAA;UAAKG,SAAS,EAAE,GAAGW,WAAW,CAACT,IAAI,CAAC,gBAAiB;UAACW,IAAI,EAAC,cAAc;UAACC,OAAO,EAAC,WAAW;UAAAC,QAAA,eAC3FlB,OAAA;YAAMmB,QAAQ,EAAC,SAAS;YAACC,CAAC,EAAC,oMAAoM;YAACC,QAAQ,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClP,CAAC;MAEV,KAAK,UAAU;QACb,oBACEzB,OAAA;UAAKG,SAAS,EAAE,GAAGW,WAAW,CAACT,IAAI,CAAC,kBAAmB;UAACW,IAAI,EAAC,cAAc;UAACC,OAAO,EAAC,WAAW;UAAAC,QAAA,eAC7FlB,OAAA;YAAMmB,QAAQ,EAAC,SAAS;YAACC,CAAC,EAAC,sSAAsS;YAACC,QAAQ,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpV,CAAC;MAEV,KAAK,SAAS;QACZ,oBACEzB,OAAA;UAAKG,SAAS,EAAE,GAAGW,WAAW,CAACT,IAAI,CAAC,kBAAmB;UAACW,IAAI,EAAC,cAAc;UAACC,OAAO,EAAC,WAAW;UAAAC,QAAA,eAC7FlB,OAAA;YAAMmB,QAAQ,EAAC,SAAS;YAACC,CAAC,EAAC,yNAAyN;YAACC,QAAQ,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvQ,CAAC;MAEV;QACE,OAAO,IAAI;IACf;EACF,CAAC;EAED,oBACEzB,OAAA;IAAMG,SAAS,EAAE;AACrB;AACA,QAAQO,WAAW,CAACL,IAAI,CAAC;AACzB,QAAQG,WAAW;AACnB;AACA,QAAQL,SAAS;AACjB,KAAM;IAAAe,QAAA,GACCd,QAAQ,IAAIW,aAAa,CAACb,MAAM,CAAC,EACjCI,UAAU;EAAA;IAAAgB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEX,CAAC;AAACM,EAAA,GAnFI9B,aAA2C;AAqFjD,eAAeA,aAAa;AAAC,IAAA8B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}