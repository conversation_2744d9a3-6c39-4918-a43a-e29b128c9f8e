{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/AdminPanel.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminPanel = () => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: \"flex flex-col items-center justify-center min-h-screen\",\n  children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n    className: \"text-2xl font-semibold mb-4\",\n    children: \"Painel Admin\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"w-96 bg-white shadow rounded p-4\",\n    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n      className: \"font-bold mb-2\",\n      children: \"Encomendas Recebidas\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n      className: \"mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"li\", {\n        children: [\"Pedido #1 - \", /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-green-600\",\n          children: \"Entregue\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 9,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        children: [\"Pedido #2 - \", /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-yellow-600\",\n          children: \"Em produ\\xE7\\xE3o\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 10,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 10,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"px-4 py-2 bg-green-600 text-white rounded\",\n      children: \"Atualizar Status\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 4,\n  columnNumber: 3\n}, this);\n_c = AdminPanel;\nexport default AdminPanel;\nvar _c;\n$RefreshReg$(_c, \"AdminPanel\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "AdminPanel", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/AdminPanel.tsx"], "sourcesContent": ["import React from 'react';\n\nconst AdminPanel: React.FC = () => (\n  <div className=\"flex flex-col items-center justify-center min-h-screen\">\n    <h2 className=\"text-2xl font-semibold mb-4\">Painel Admin</h2>\n    <div className=\"w-96 bg-white shadow rounded p-4\">\n      <h3 className=\"font-bold mb-2\">Encomendas Recebidas</h3>\n      <ul className=\"mb-4\">\n        <li>Pedido #1 - <span className=\"text-green-600\">Entregue</span></li>\n        <li>Pedido #2 - <span className=\"text-yellow-600\">Em produção</span></li>\n      </ul>\n      <button className=\"px-4 py-2 bg-green-600 text-white rounded\">Atualizar Status</button>\n    </div>\n  </div>\n);\n\nexport default AdminPanel;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,UAAoB,GAAGA,CAAA,kBAC3BD,OAAA;EAAKE,SAAS,EAAC,wDAAwD;EAAAC,QAAA,gBACrEH,OAAA;IAAIE,SAAS,EAAC,6BAA6B;IAAAC,QAAA,EAAC;EAAY;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAI,CAAC,eAC7DP,OAAA;IAAKE,SAAS,EAAC,kCAAkC;IAAAC,QAAA,gBAC/CH,OAAA;MAAIE,SAAS,EAAC,gBAAgB;MAAAC,QAAA,EAAC;IAAoB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACxDP,OAAA;MAAIE,SAAS,EAAC,MAAM;MAAAC,QAAA,gBAClBH,OAAA;QAAAG,QAAA,GAAI,cAAY,eAAAH,OAAA;UAAME,SAAS,EAAC,gBAAgB;UAAAC,QAAA,EAAC;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACrEP,OAAA;QAAAG,QAAA,GAAI,cAAY,eAAAH,OAAA;UAAME,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvE,CAAC,eACLP,OAAA;MAAQE,SAAS,EAAC,2CAA2C;MAAAC,QAAA,EAAC;IAAgB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACpF,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACH,CACN;AAACC,EAAA,GAZIP,UAAoB;AAc1B,eAAeA,UAAU;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}