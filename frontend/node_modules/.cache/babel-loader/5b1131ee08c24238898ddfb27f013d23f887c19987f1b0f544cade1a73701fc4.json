{"ast": null, "code": "import React,{useState,useEffect,useCallback}from'react';import{PaymentStatus}from'../components';import{MulticaixaService}from'../services/multicaixa';import{apiService}from'../services/api';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const AdminPanel=()=>{const[activeTab,setActiveTab]=useState('overview');const[stats,setStats]=useState({totalRevenue:0,totalPayments:0,pendingPayments:0,completedPayments:0,failedPayments:0});const[payments,setPayments]=useState([]);const[orders,setOrders]=useState([]);const[loading,setLoading]=useState(true);const loadDashboardData=useCallback(async()=>{setLoading(true);try{// Load payments\nconst paymentsResponse=await apiService.get('/payments');if(paymentsResponse.success&&paymentsResponse.data){setPayments(paymentsResponse.data);calculateStats(paymentsResponse.data);}// Load orders\nconst ordersResponse=await apiService.get('/orders');if(ordersResponse.success&&ordersResponse.data){setOrders(ordersResponse.data);}}catch(error){console.error('Error loading dashboard data:',error);}finally{setLoading(false);}},[]);useEffect(()=>{loadDashboardData();},[loadDashboardData]);const calculateStats=paymentsData=>{const stats=paymentsData.reduce((acc,payment)=>{acc.totalPayments++;if(payment.status==='COMPLETED'){acc.completedPayments++;acc.totalRevenue+=payment.amount;}else if(payment.status==='PENDING'||payment.status==='PROCESSING'){acc.pendingPayments++;}else if(payment.status==='FAILED'){acc.failedPayments++;}return acc;},{totalRevenue:0,totalPayments:0,pendingPayments:0,completedPayments:0,failedPayments:0});setStats(stats);};const handleRefundPayment=async paymentId=>{// eslint-disable-next-line no-restricted-globals\nif(!confirm('Tem certeza que deseja processar este reembolso?')){return;}try{const response=await MulticaixaService.requestRefund(paymentId);if(response.success){alert('Reembolso processado com sucesso!');loadDashboardData();// Reload data\n}else{alert(\"Erro ao processar reembolso: \".concat(response.error));}}catch(error){alert('Erro ao processar reembolso');}};const formatCurrency=amount=>{return MulticaixaService.formatAoaAmount(amount);};if(loading){return/*#__PURE__*/_jsx(\"div\",{className:\"min-h-screen bg-gray-50 flex items-center justify-center\",children:/*#__PURE__*/_jsx(\"div\",{className:\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"})});}return/*#__PURE__*/_jsx(\"div\",{className:\"min-h-screen bg-gray-50\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"mb-8\",children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-3xl font-bold text-gray-900\",children:\"Painel Administrativo\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600\",children:\"Gest\\xE3o de pagamentos e pedidos WePrint AI\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"mb-8\",children:/*#__PURE__*/_jsx(\"nav\",{className:\"flex space-x-8\",children:[{id:'overview',label:'Visão Geral'},{id:'payments',label:'Pagamentos'},{id:'orders',label:'Pedidos'}].map(tab=>/*#__PURE__*/_jsx(\"button\",{onClick:()=>setActiveTab(tab.id),className:\"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab===tab.id?'border-blue-500 text-blue-600':'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'),children:tab.label},tab.id))})}),activeTab==='overview'&&/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-lg shadow p-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex-shrink-0\",children:/*#__PURE__*/_jsx(\"div\",{className:\"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center\",children:/*#__PURE__*/_jsx(\"svg\",{className:\"w-5 h-5 text-white\",fill:\"currentColor\",viewBox:\"0 0 20 20\",children:/*#__PURE__*/_jsx(\"path\",{d:\"M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z\"})})})}),/*#__PURE__*/_jsx(\"div\",{className:\"ml-5 w-0 flex-1\",children:/*#__PURE__*/_jsxs(\"dl\",{children:[/*#__PURE__*/_jsx(\"dt\",{className:\"text-sm font-medium text-gray-500 truncate\",children:\"Receita Total\"}),/*#__PURE__*/_jsx(\"dd\",{className:\"text-lg font-medium text-gray-900\",children:formatCurrency(stats.totalRevenue)})]})})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-lg shadow p-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex-shrink-0\",children:/*#__PURE__*/_jsx(\"div\",{className:\"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center\",children:/*#__PURE__*/_jsx(\"svg\",{className:\"w-5 h-5 text-white\",fill:\"currentColor\",viewBox:\"0 0 20 20\",children:/*#__PURE__*/_jsx(\"path\",{fillRule:\"evenodd\",d:\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",clipRule:\"evenodd\"})})})}),/*#__PURE__*/_jsx(\"div\",{className:\"ml-5 w-0 flex-1\",children:/*#__PURE__*/_jsxs(\"dl\",{children:[/*#__PURE__*/_jsx(\"dt\",{className:\"text-sm font-medium text-gray-500 truncate\",children:\"Pagamentos Conclu\\xEDdos\"}),/*#__PURE__*/_jsx(\"dd\",{className:\"text-lg font-medium text-gray-900\",children:stats.completedPayments})]})})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-lg shadow p-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex-shrink-0\",children:/*#__PURE__*/_jsx(\"div\",{className:\"w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center\",children:/*#__PURE__*/_jsx(\"svg\",{className:\"w-5 h-5 text-white\",fill:\"currentColor\",viewBox:\"0 0 20 20\",children:/*#__PURE__*/_jsx(\"path\",{fillRule:\"evenodd\",d:\"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z\",clipRule:\"evenodd\"})})})}),/*#__PURE__*/_jsx(\"div\",{className:\"ml-5 w-0 flex-1\",children:/*#__PURE__*/_jsxs(\"dl\",{children:[/*#__PURE__*/_jsx(\"dt\",{className:\"text-sm font-medium text-gray-500 truncate\",children:\"Pagamentos Pendentes\"}),/*#__PURE__*/_jsx(\"dd\",{className:\"text-lg font-medium text-gray-900\",children:stats.pendingPayments})]})})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-lg shadow p-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex-shrink-0\",children:/*#__PURE__*/_jsx(\"div\",{className:\"w-8 h-8 bg-red-500 rounded-md flex items-center justify-center\",children:/*#__PURE__*/_jsx(\"svg\",{className:\"w-5 h-5 text-white\",fill:\"currentColor\",viewBox:\"0 0 20 20\",children:/*#__PURE__*/_jsx(\"path\",{fillRule:\"evenodd\",d:\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",clipRule:\"evenodd\"})})})}),/*#__PURE__*/_jsx(\"div\",{className:\"ml-5 w-0 flex-1\",children:/*#__PURE__*/_jsxs(\"dl\",{children:[/*#__PURE__*/_jsx(\"dt\",{className:\"text-sm font-medium text-gray-500 truncate\",children:\"Pagamentos Falhados\"}),/*#__PURE__*/_jsx(\"dd\",{className:\"text-lg font-medium text-gray-900\",children:stats.failedPayments})]})})]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-lg shadow\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"px-6 py-4 border-b border-gray-200\",children:/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium text-gray-900\",children:\"Atividade Recente\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"p-6\",children:/*#__PURE__*/_jsx(\"div\",{className:\"space-y-4\",children:payments.slice(0,5).map(payment=>/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-3\",children:[/*#__PURE__*/_jsx(PaymentStatus,{status:payment.status,size:\"sm\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"p\",{className:\"text-sm font-medium text-gray-900\",children:[\"Pagamento #\",payment.id.slice(-8)]}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-500\",children:formatCurrency(payment.amount)})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm text-gray-500\",children:new Date(payment.createdAt).toLocaleDateString('pt-PT')})]},payment.id))})})]})]}),activeTab==='payments'&&/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-lg shadow\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"px-6 py-4 border-b border-gray-200\",children:/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium text-gray-900\",children:\"Gest\\xE3o de Pagamentos\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"overflow-x-auto\",children:/*#__PURE__*/_jsxs(\"table\",{className:\"min-w-full divide-y divide-gray-200\",children:[/*#__PURE__*/_jsx(\"thead\",{className:\"bg-gray-50\",children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"ID\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"Valor\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"Status\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"M\\xE9todo\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"Data\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"A\\xE7\\xF5es\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{className:\"bg-white divide-y divide-gray-200\",children:payments.map(payment=>/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsxs(\"td\",{className:\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",children:[\"#\",payment.id.slice(-8)]}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",children:formatCurrency(payment.amount)}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap\",children:/*#__PURE__*/_jsx(PaymentStatus,{status:payment.status,size:\"sm\"})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",children:payment.paymentMethod}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",children:new Date(payment.createdAt).toLocaleDateString('pt-PT')}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap text-sm font-medium\",children:MulticaixaService.canRefund(payment.status)&&/*#__PURE__*/_jsx(\"button\",{onClick:()=>handleRefundPayment(payment.id),className:\"text-red-600 hover:text-red-900\",children:\"Reembolsar\"})})]},payment.id))})]})})]}),activeTab==='orders'&&/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-lg shadow\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"px-6 py-4 border-b border-gray-200\",children:/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium text-gray-900\",children:\"Gest\\xE3o de Pedidos\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"p-6\",children:/*#__PURE__*/_jsx(\"div\",{className:\"space-y-4\",children:orders.map(order=>/*#__PURE__*/_jsxs(\"div\",{className:\"border border-gray-200 rounded-lg p-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between mb-2\",children:[/*#__PURE__*/_jsxs(\"h4\",{className:\"text-lg font-medium text-gray-900\",children:[\"Pedido #\",order.id.slice(-8)]}),/*#__PURE__*/_jsx(\"span\",{className:\"px-2 py-1 text-xs font-medium rounded-full \".concat(order.status==='delivered'?'bg-green-100 text-green-800':order.status==='in_progress'?'bg-blue-100 text-blue-800':order.status==='ready'?'bg-yellow-100 text-yellow-800':'bg-gray-100 text-gray-800'),children:order.status})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-2 gap-4 text-sm\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-500\",children:\"Total:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"ml-2 font-medium\",children:formatCurrency(order.totalAmount)})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-500\",children:\"Pagamento:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"ml-2 font-medium \".concat(order.paymentStatus==='paid'?'text-green-600':order.paymentStatus==='pending'?'text-yellow-600':'text-red-600'),children:order.paymentStatus})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-500\",children:\"Data:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"ml-2\",children:new Date(order.createdAt).toLocaleDateString('pt-PT')})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-500\",children:\"Itens:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"ml-2\",children:order.printJobs.length})]})]})]},order.id))})})]})]})});};export default AdminPanel;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "PaymentStatus", "MulticaixaService", "apiService", "jsx", "_jsx", "jsxs", "_jsxs", "AdminPanel", "activeTab", "setActiveTab", "stats", "setStats", "totalRevenue", "totalPayments", "pendingPayments", "completedPayments", "failedPayments", "payments", "setPayments", "orders", "setOrders", "loading", "setLoading", "loadDashboardData", "paymentsResponse", "get", "success", "data", "calculateStats", "ordersResponse", "error", "console", "paymentsData", "reduce", "acc", "payment", "status", "amount", "handleRefundPayment", "paymentId", "confirm", "response", "requestRefund", "alert", "concat", "formatCurrency", "formatAoaAmount", "className", "children", "id", "label", "map", "tab", "onClick", "fill", "viewBox", "d", "fillRule", "clipRule", "slice", "size", "Date", "createdAt", "toLocaleDateString", "paymentMethod", "canRefund", "order", "totalAmount", "paymentStatus", "printJobs", "length"], "sources": ["/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/AdminPanel.tsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport { PaymentStatus } from '../components';\nimport { MulticaixaService } from '../services/multicaixa';\nimport { apiService } from '../services/api';\nimport { Payment, Order } from '../types';\n\ninterface DashboardStats {\n  totalRevenue: number;\n  totalPayments: number;\n  pendingPayments: number;\n  completedPayments: number;\n  failedPayments: number;\n}\n\nconst AdminPanel: React.FC = () => {\n  const [activeTab, setActiveTab] = useState<'overview' | 'payments' | 'orders'>('overview');\n  const [stats, setStats] = useState<DashboardStats>({\n    totalRevenue: 0,\n    totalPayments: 0,\n    pendingPayments: 0,\n    completedPayments: 0,\n    failedPayments: 0,\n  });\n  const [payments, setPayments] = useState<Payment[]>([]);\n  const [orders, setOrders] = useState<Order[]>([]);\n  const [loading, setLoading] = useState(true);\n\n  const loadDashboardData = useCallback(async () => {\n    setLoading(true);\n    try {\n      // Load payments\n      const paymentsResponse = await apiService.get<Payment[]>('/payments');\n      if (paymentsResponse.success && paymentsResponse.data) {\n        setPayments(paymentsResponse.data);\n        calculateStats(paymentsResponse.data);\n      }\n\n      // Load orders\n      const ordersResponse = await apiService.get<Order[]>('/orders');\n      if (ordersResponse.success && ordersResponse.data) {\n        setOrders(ordersResponse.data);\n      }\n    } catch (error) {\n      console.error('Error loading dashboard data:', error);\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n\n  useEffect(() => {\n    loadDashboardData();\n  }, [loadDashboardData]);\n\n  const calculateStats = (paymentsData: Payment[]) => {\n    const stats = paymentsData.reduce((acc, payment) => {\n      acc.totalPayments++;\n\n      if (payment.status === 'COMPLETED') {\n        acc.completedPayments++;\n        acc.totalRevenue += payment.amount;\n      } else if (payment.status === 'PENDING' || payment.status === 'PROCESSING') {\n        acc.pendingPayments++;\n      } else if (payment.status === 'FAILED') {\n        acc.failedPayments++;\n      }\n\n      return acc;\n    }, {\n      totalRevenue: 0,\n      totalPayments: 0,\n      pendingPayments: 0,\n      completedPayments: 0,\n      failedPayments: 0,\n    });\n\n    setStats(stats);\n  };\n\n  const handleRefundPayment = async (paymentId: string) => {\n    // eslint-disable-next-line no-restricted-globals\n    if (!confirm('Tem certeza que deseja processar este reembolso?')) {\n      return;\n    }\n\n    try {\n      const response = await MulticaixaService.requestRefund(paymentId);\n      if (response.success) {\n        alert('Reembolso processado com sucesso!');\n        loadDashboardData(); // Reload data\n      } else {\n        alert(`Erro ao processar reembolso: ${response.error}`);\n      }\n    } catch (error) {\n      alert('Erro ao processar reembolso');\n    }\n  };\n\n  const formatCurrency = (amount: number) => {\n    return MulticaixaService.formatAoaAmount(amount);\n  };\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Header */}\n        <div className=\"mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900\">Painel Administrativo</h1>\n          <p className=\"text-gray-600\">Gestão de pagamentos e pedidos WePrint AI</p>\n        </div>\n\n        {/* Navigation Tabs */}\n        <div className=\"mb-8\">\n          <nav className=\"flex space-x-8\">\n            {[\n              { id: 'overview', label: 'Visão Geral' },\n              { id: 'payments', label: 'Pagamentos' },\n              { id: 'orders', label: 'Pedidos' },\n            ].map((tab) => (\n              <button\n                key={tab.id}\n                onClick={() => setActiveTab(tab.id as any)}\n                className={`py-2 px-1 border-b-2 font-medium text-sm ${\n                  activeTab === tab.id\n                    ? 'border-blue-500 text-blue-600'\n                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                }`}\n              >\n                {tab.label}\n              </button>\n            ))}\n          </nav>\n        </div>\n\n        {/* Overview Tab */}\n        {activeTab === 'overview' && (\n          <div className=\"space-y-6\">\n            {/* Stats Cards */}\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n              <div className=\"bg-white rounded-lg shadow p-6\">\n                <div className=\"flex items-center\">\n                  <div className=\"flex-shrink-0\">\n                    <div className=\"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center\">\n                      <svg className=\"w-5 h-5 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path d=\"M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z\" />\n                      </svg>\n                    </div>\n                  </div>\n                  <div className=\"ml-5 w-0 flex-1\">\n                    <dl>\n                      <dt className=\"text-sm font-medium text-gray-500 truncate\">\n                        Receita Total\n                      </dt>\n                      <dd className=\"text-lg font-medium text-gray-900\">\n                        {formatCurrency(stats.totalRevenue)}\n                      </dd>\n                    </dl>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"bg-white rounded-lg shadow p-6\">\n                <div className=\"flex items-center\">\n                  <div className=\"flex-shrink-0\">\n                    <div className=\"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center\">\n                      <svg className=\"w-5 h-5 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n                      </svg>\n                    </div>\n                  </div>\n                  <div className=\"ml-5 w-0 flex-1\">\n                    <dl>\n                      <dt className=\"text-sm font-medium text-gray-500 truncate\">\n                        Pagamentos Concluídos\n                      </dt>\n                      <dd className=\"text-lg font-medium text-gray-900\">\n                        {stats.completedPayments}\n                      </dd>\n                    </dl>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"bg-white rounded-lg shadow p-6\">\n                <div className=\"flex items-center\">\n                  <div className=\"flex-shrink-0\">\n                    <div className=\"w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center\">\n                      <svg className=\"w-5 h-5 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z\" clipRule=\"evenodd\" />\n                      </svg>\n                    </div>\n                  </div>\n                  <div className=\"ml-5 w-0 flex-1\">\n                    <dl>\n                      <dt className=\"text-sm font-medium text-gray-500 truncate\">\n                        Pagamentos Pendentes\n                      </dt>\n                      <dd className=\"text-lg font-medium text-gray-900\">\n                        {stats.pendingPayments}\n                      </dd>\n                    </dl>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"bg-white rounded-lg shadow p-6\">\n                <div className=\"flex items-center\">\n                  <div className=\"flex-shrink-0\">\n                    <div className=\"w-8 h-8 bg-red-500 rounded-md flex items-center justify-center\">\n                      <svg className=\"w-5 h-5 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\n                      </svg>\n                    </div>\n                  </div>\n                  <div className=\"ml-5 w-0 flex-1\">\n                    <dl>\n                      <dt className=\"text-sm font-medium text-gray-500 truncate\">\n                        Pagamentos Falhados\n                      </dt>\n                      <dd className=\"text-lg font-medium text-gray-900\">\n                        {stats.failedPayments}\n                      </dd>\n                    </dl>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Recent Activity */}\n            <div className=\"bg-white rounded-lg shadow\">\n              <div className=\"px-6 py-4 border-b border-gray-200\">\n                <h3 className=\"text-lg font-medium text-gray-900\">Atividade Recente</h3>\n              </div>\n              <div className=\"p-6\">\n                <div className=\"space-y-4\">\n                  {payments.slice(0, 5).map((payment) => (\n                    <div key={payment.id} className=\"flex items-center justify-between\">\n                      <div className=\"flex items-center space-x-3\">\n                        <PaymentStatus status={payment.status} size=\"sm\" />\n                        <div>\n                          <p className=\"text-sm font-medium text-gray-900\">\n                            Pagamento #{payment.id.slice(-8)}\n                          </p>\n                          <p className=\"text-sm text-gray-500\">\n                            {formatCurrency(payment.amount)}\n                          </p>\n                        </div>\n                      </div>\n                      <div className=\"text-sm text-gray-500\">\n                        {new Date(payment.createdAt).toLocaleDateString('pt-PT')}\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Payments Tab */}\n        {activeTab === 'payments' && (\n          <div className=\"bg-white rounded-lg shadow\">\n            <div className=\"px-6 py-4 border-b border-gray-200\">\n              <h3 className=\"text-lg font-medium text-gray-900\">Gestão de Pagamentos</h3>\n            </div>\n            <div className=\"overflow-x-auto\">\n              <table className=\"min-w-full divide-y divide-gray-200\">\n                <thead className=\"bg-gray-50\">\n                  <tr>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      ID\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Valor\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Status\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Método\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Data\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Ações\n                    </th>\n                  </tr>\n                </thead>\n                <tbody className=\"bg-white divide-y divide-gray-200\">\n                  {payments.map((payment) => (\n                    <tr key={payment.id}>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">\n                        #{payment.id.slice(-8)}\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                        {formatCurrency(payment.amount)}\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\n                        <PaymentStatus status={payment.status} size=\"sm\" />\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                        {payment.paymentMethod}\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                        {new Date(payment.createdAt).toLocaleDateString('pt-PT')}\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                        {MulticaixaService.canRefund(payment.status) && (\n                          <button\n                            onClick={() => handleRefundPayment(payment.id)}\n                            className=\"text-red-600 hover:text-red-900\"\n                          >\n                            Reembolsar\n                          </button>\n                        )}\n                      </td>\n                    </tr>\n                  ))}\n                </tbody>\n              </table>\n            </div>\n          </div>\n        )}\n\n        {/* Orders Tab */}\n        {activeTab === 'orders' && (\n          <div className=\"bg-white rounded-lg shadow\">\n            <div className=\"px-6 py-4 border-b border-gray-200\">\n              <h3 className=\"text-lg font-medium text-gray-900\">Gestão de Pedidos</h3>\n            </div>\n            <div className=\"p-6\">\n              <div className=\"space-y-4\">\n                {orders.map((order) => (\n                  <div key={order.id} className=\"border border-gray-200 rounded-lg p-4\">\n                    <div className=\"flex items-center justify-between mb-2\">\n                      <h4 className=\"text-lg font-medium text-gray-900\">\n                        Pedido #{order.id.slice(-8)}\n                      </h4>\n                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${\n                        order.status === 'delivered' ? 'bg-green-100 text-green-800' :\n                        order.status === 'in_progress' ? 'bg-blue-100 text-blue-800' :\n                        order.status === 'ready' ? 'bg-yellow-100 text-yellow-800' :\n                        'bg-gray-100 text-gray-800'\n                      }`}>\n                        {order.status}\n                      </span>\n                    </div>\n                    <div className=\"grid grid-cols-2 gap-4 text-sm\">\n                      <div>\n                        <span className=\"text-gray-500\">Total:</span>\n                        <span className=\"ml-2 font-medium\">{formatCurrency(order.totalAmount)}</span>\n                      </div>\n                      <div>\n                        <span className=\"text-gray-500\">Pagamento:</span>\n                        <span className={`ml-2 font-medium ${\n                          order.paymentStatus === 'paid' ? 'text-green-600' :\n                          order.paymentStatus === 'pending' ? 'text-yellow-600' :\n                          'text-red-600'\n                        }`}>\n                          {order.paymentStatus}\n                        </span>\n                      </div>\n                      <div>\n                        <span className=\"text-gray-500\">Data:</span>\n                        <span className=\"ml-2\">{new Date(order.createdAt).toLocaleDateString('pt-PT')}</span>\n                      </div>\n                      <div>\n                        <span className=\"text-gray-500\">Itens:</span>\n                        <span className=\"ml-2\">{order.printJobs.length}</span>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default AdminPanel;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,CAAEC,WAAW,KAAQ,OAAO,CAC/D,OAASC,aAAa,KAAQ,eAAe,CAC7C,OAASC,iBAAiB,KAAQ,wBAAwB,CAC1D,OAASC,UAAU,KAAQ,iBAAiB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAW7C,KAAM,CAAAC,UAAoB,CAAGA,CAAA,GAAM,CACjC,KAAM,CAACC,SAAS,CAAEC,YAAY,CAAC,CAAGZ,QAAQ,CAAqC,UAAU,CAAC,CAC1F,KAAM,CAACa,KAAK,CAAEC,QAAQ,CAAC,CAAGd,QAAQ,CAAiB,CACjDe,YAAY,CAAE,CAAC,CACfC,aAAa,CAAE,CAAC,CAChBC,eAAe,CAAE,CAAC,CAClBC,iBAAiB,CAAE,CAAC,CACpBC,cAAc,CAAE,CAClB,CAAC,CAAC,CACF,KAAM,CAACC,QAAQ,CAAEC,WAAW,CAAC,CAAGrB,QAAQ,CAAY,EAAE,CAAC,CACvD,KAAM,CAACsB,MAAM,CAAEC,SAAS,CAAC,CAAGvB,QAAQ,CAAU,EAAE,CAAC,CACjD,KAAM,CAACwB,OAAO,CAAEC,UAAU,CAAC,CAAGzB,QAAQ,CAAC,IAAI,CAAC,CAE5C,KAAM,CAAA0B,iBAAiB,CAAGxB,WAAW,CAAC,SAAY,CAChDuB,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACF;AACA,KAAM,CAAAE,gBAAgB,CAAG,KAAM,CAAAtB,UAAU,CAACuB,GAAG,CAAY,WAAW,CAAC,CACrE,GAAID,gBAAgB,CAACE,OAAO,EAAIF,gBAAgB,CAACG,IAAI,CAAE,CACrDT,WAAW,CAACM,gBAAgB,CAACG,IAAI,CAAC,CAClCC,cAAc,CAACJ,gBAAgB,CAACG,IAAI,CAAC,CACvC,CAEA;AACA,KAAM,CAAAE,cAAc,CAAG,KAAM,CAAA3B,UAAU,CAACuB,GAAG,CAAU,SAAS,CAAC,CAC/D,GAAII,cAAc,CAACH,OAAO,EAAIG,cAAc,CAACF,IAAI,CAAE,CACjDP,SAAS,CAACS,cAAc,CAACF,IAAI,CAAC,CAChC,CACF,CAAE,MAAOG,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,CAAEA,KAAK,CAAC,CACvD,CAAC,OAAS,CACRR,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAAE,EAAE,CAAC,CAENxB,SAAS,CAAC,IAAM,CACdyB,iBAAiB,CAAC,CAAC,CACrB,CAAC,CAAE,CAACA,iBAAiB,CAAC,CAAC,CAEvB,KAAM,CAAAK,cAAc,CAAII,YAAuB,EAAK,CAClD,KAAM,CAAAtB,KAAK,CAAGsB,YAAY,CAACC,MAAM,CAAC,CAACC,GAAG,CAAEC,OAAO,GAAK,CAClDD,GAAG,CAACrB,aAAa,EAAE,CAEnB,GAAIsB,OAAO,CAACC,MAAM,GAAK,WAAW,CAAE,CAClCF,GAAG,CAACnB,iBAAiB,EAAE,CACvBmB,GAAG,CAACtB,YAAY,EAAIuB,OAAO,CAACE,MAAM,CACpC,CAAC,IAAM,IAAIF,OAAO,CAACC,MAAM,GAAK,SAAS,EAAID,OAAO,CAACC,MAAM,GAAK,YAAY,CAAE,CAC1EF,GAAG,CAACpB,eAAe,EAAE,CACvB,CAAC,IAAM,IAAIqB,OAAO,CAACC,MAAM,GAAK,QAAQ,CAAE,CACtCF,GAAG,CAAClB,cAAc,EAAE,CACtB,CAEA,MAAO,CAAAkB,GAAG,CACZ,CAAC,CAAE,CACDtB,YAAY,CAAE,CAAC,CACfC,aAAa,CAAE,CAAC,CAChBC,eAAe,CAAE,CAAC,CAClBC,iBAAiB,CAAE,CAAC,CACpBC,cAAc,CAAE,CAClB,CAAC,CAAC,CAEFL,QAAQ,CAACD,KAAK,CAAC,CACjB,CAAC,CAED,KAAM,CAAA4B,mBAAmB,CAAG,KAAO,CAAAC,SAAiB,EAAK,CACvD;AACA,GAAI,CAACC,OAAO,CAAC,kDAAkD,CAAC,CAAE,CAChE,OACF,CAEA,GAAI,CACF,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAxC,iBAAiB,CAACyC,aAAa,CAACH,SAAS,CAAC,CACjE,GAAIE,QAAQ,CAACf,OAAO,CAAE,CACpBiB,KAAK,CAAC,mCAAmC,CAAC,CAC1CpB,iBAAiB,CAAC,CAAC,CAAE;AACvB,CAAC,IAAM,CACLoB,KAAK,iCAAAC,MAAA,CAAiCH,QAAQ,CAACX,KAAK,CAAE,CAAC,CACzD,CACF,CAAE,MAAOA,KAAK,CAAE,CACda,KAAK,CAAC,6BAA6B,CAAC,CACtC,CACF,CAAC,CAED,KAAM,CAAAE,cAAc,CAAIR,MAAc,EAAK,CACzC,MAAO,CAAApC,iBAAiB,CAAC6C,eAAe,CAACT,MAAM,CAAC,CAClD,CAAC,CAED,GAAIhB,OAAO,CAAE,CACX,mBACEjB,IAAA,QAAK2C,SAAS,CAAC,0DAA0D,CAAAC,QAAA,cACvE5C,IAAA,QAAK2C,SAAS,CAAC,gEAAgE,CAAM,CAAC,CACnF,CAAC,CAEV,CAEA,mBACE3C,IAAA,QAAK2C,SAAS,CAAC,yBAAyB,CAAAC,QAAA,cACtC1C,KAAA,QAAKyC,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAE1D1C,KAAA,QAAKyC,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnB5C,IAAA,OAAI2C,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,uBAAqB,CAAI,CAAC,cAC3E5C,IAAA,MAAG2C,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,8CAAyC,CAAG,CAAC,EACvE,CAAC,cAGN5C,IAAA,QAAK2C,SAAS,CAAC,MAAM,CAAAC,QAAA,cACnB5C,IAAA,QAAK2C,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAC5B,CACC,CAAEC,EAAE,CAAE,UAAU,CAAEC,KAAK,CAAE,aAAc,CAAC,CACxC,CAAED,EAAE,CAAE,UAAU,CAAEC,KAAK,CAAE,YAAa,CAAC,CACvC,CAAED,EAAE,CAAE,QAAQ,CAAEC,KAAK,CAAE,SAAU,CAAC,CACnC,CAACC,GAAG,CAAEC,GAAG,eACRhD,IAAA,WAEEiD,OAAO,CAAEA,CAAA,GAAM5C,YAAY,CAAC2C,GAAG,CAACH,EAAS,CAAE,CAC3CF,SAAS,6CAAAH,MAAA,CACPpC,SAAS,GAAK4C,GAAG,CAACH,EAAE,CAChB,+BAA+B,CAC/B,4EAA4E,CAC/E,CAAAD,QAAA,CAEFI,GAAG,CAACF,KAAK,EARLE,GAAG,CAACH,EASH,CACT,CAAC,CACC,CAAC,CACH,CAAC,CAGLzC,SAAS,GAAK,UAAU,eACvBF,KAAA,QAAKyC,SAAS,CAAC,WAAW,CAAAC,QAAA,eAExB1C,KAAA,QAAKyC,SAAS,CAAC,sDAAsD,CAAAC,QAAA,eACnE5C,IAAA,QAAK2C,SAAS,CAAC,gCAAgC,CAAAC,QAAA,cAC7C1C,KAAA,QAAKyC,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC5C,IAAA,QAAK2C,SAAS,CAAC,eAAe,CAAAC,QAAA,cAC5B5C,IAAA,QAAK2C,SAAS,CAAC,kEAAkE,CAAAC,QAAA,cAC/E5C,IAAA,QAAK2C,SAAS,CAAC,oBAAoB,CAACO,IAAI,CAAC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAAP,QAAA,cACzE5C,IAAA,SAAMoD,CAAC,CAAC,mJAAmJ,CAAE,CAAC,CAC3J,CAAC,CACH,CAAC,CACH,CAAC,cACNpD,IAAA,QAAK2C,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9B1C,KAAA,OAAA0C,QAAA,eACE5C,IAAA,OAAI2C,SAAS,CAAC,4CAA4C,CAAAC,QAAA,CAAC,eAE3D,CAAI,CAAC,cACL5C,IAAA,OAAI2C,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAC9CH,cAAc,CAACnC,KAAK,CAACE,YAAY,CAAC,CACjC,CAAC,EACH,CAAC,CACF,CAAC,EACH,CAAC,CACH,CAAC,cAENR,IAAA,QAAK2C,SAAS,CAAC,gCAAgC,CAAAC,QAAA,cAC7C1C,KAAA,QAAKyC,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC5C,IAAA,QAAK2C,SAAS,CAAC,eAAe,CAAAC,QAAA,cAC5B5C,IAAA,QAAK2C,SAAS,CAAC,iEAAiE,CAAAC,QAAA,cAC9E5C,IAAA,QAAK2C,SAAS,CAAC,oBAAoB,CAACO,IAAI,CAAC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAAP,QAAA,cACzE5C,IAAA,SAAMqD,QAAQ,CAAC,SAAS,CAACD,CAAC,CAAC,uIAAuI,CAACE,QAAQ,CAAC,SAAS,CAAE,CAAC,CACrL,CAAC,CACH,CAAC,CACH,CAAC,cACNtD,IAAA,QAAK2C,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9B1C,KAAA,OAAA0C,QAAA,eACE5C,IAAA,OAAI2C,SAAS,CAAC,4CAA4C,CAAAC,QAAA,CAAC,0BAE3D,CAAI,CAAC,cACL5C,IAAA,OAAI2C,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAC9CtC,KAAK,CAACK,iBAAiB,CACtB,CAAC,EACH,CAAC,CACF,CAAC,EACH,CAAC,CACH,CAAC,cAENX,IAAA,QAAK2C,SAAS,CAAC,gCAAgC,CAAAC,QAAA,cAC7C1C,KAAA,QAAKyC,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC5C,IAAA,QAAK2C,SAAS,CAAC,eAAe,CAAAC,QAAA,cAC5B5C,IAAA,QAAK2C,SAAS,CAAC,mEAAmE,CAAAC,QAAA,cAChF5C,IAAA,QAAK2C,SAAS,CAAC,oBAAoB,CAACO,IAAI,CAAC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAAP,QAAA,cACzE5C,IAAA,SAAMqD,QAAQ,CAAC,SAAS,CAACD,CAAC,CAAC,oHAAoH,CAACE,QAAQ,CAAC,SAAS,CAAE,CAAC,CAClK,CAAC,CACH,CAAC,CACH,CAAC,cACNtD,IAAA,QAAK2C,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9B1C,KAAA,OAAA0C,QAAA,eACE5C,IAAA,OAAI2C,SAAS,CAAC,4CAA4C,CAAAC,QAAA,CAAC,sBAE3D,CAAI,CAAC,cACL5C,IAAA,OAAI2C,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAC9CtC,KAAK,CAACI,eAAe,CACpB,CAAC,EACH,CAAC,CACF,CAAC,EACH,CAAC,CACH,CAAC,cAENV,IAAA,QAAK2C,SAAS,CAAC,gCAAgC,CAAAC,QAAA,cAC7C1C,KAAA,QAAKyC,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC5C,IAAA,QAAK2C,SAAS,CAAC,eAAe,CAAAC,QAAA,cAC5B5C,IAAA,QAAK2C,SAAS,CAAC,gEAAgE,CAAAC,QAAA,cAC7E5C,IAAA,QAAK2C,SAAS,CAAC,oBAAoB,CAACO,IAAI,CAAC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAAP,QAAA,cACzE5C,IAAA,SAAMqD,QAAQ,CAAC,SAAS,CAACD,CAAC,CAAC,yNAAyN,CAACE,QAAQ,CAAC,SAAS,CAAE,CAAC,CACvQ,CAAC,CACH,CAAC,CACH,CAAC,cACNtD,IAAA,QAAK2C,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9B1C,KAAA,OAAA0C,QAAA,eACE5C,IAAA,OAAI2C,SAAS,CAAC,4CAA4C,CAAAC,QAAA,CAAC,qBAE3D,CAAI,CAAC,cACL5C,IAAA,OAAI2C,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAC9CtC,KAAK,CAACM,cAAc,CACnB,CAAC,EACH,CAAC,CACF,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,cAGNV,KAAA,QAAKyC,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACzC5C,IAAA,QAAK2C,SAAS,CAAC,oCAAoC,CAAAC,QAAA,cACjD5C,IAAA,OAAI2C,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,mBAAiB,CAAI,CAAC,CACrE,CAAC,cACN5C,IAAA,QAAK2C,SAAS,CAAC,KAAK,CAAAC,QAAA,cAClB5C,IAAA,QAAK2C,SAAS,CAAC,WAAW,CAAAC,QAAA,CACvB/B,QAAQ,CAAC0C,KAAK,CAAC,CAAC,CAAE,CAAC,CAAC,CAACR,GAAG,CAAEhB,OAAO,eAChC7B,KAAA,QAAsByC,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eACjE1C,KAAA,QAAKyC,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1C5C,IAAA,CAACJ,aAAa,EAACoC,MAAM,CAAED,OAAO,CAACC,MAAO,CAACwB,IAAI,CAAC,IAAI,CAAE,CAAC,cACnDtD,KAAA,QAAA0C,QAAA,eACE1C,KAAA,MAAGyC,SAAS,CAAC,mCAAmC,CAAAC,QAAA,EAAC,aACpC,CAACb,OAAO,CAACc,EAAE,CAACU,KAAK,CAAC,CAAC,CAAC,CAAC,EAC/B,CAAC,cACJvD,IAAA,MAAG2C,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CACjCH,cAAc,CAACV,OAAO,CAACE,MAAM,CAAC,CAC9B,CAAC,EACD,CAAC,EACH,CAAC,cACNjC,IAAA,QAAK2C,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CACnC,GAAI,CAAAa,IAAI,CAAC1B,OAAO,CAAC2B,SAAS,CAAC,CAACC,kBAAkB,CAAC,OAAO,CAAC,CACrD,CAAC,GAdE5B,OAAO,CAACc,EAeb,CACN,CAAC,CACC,CAAC,CACH,CAAC,EACH,CAAC,EACH,CACN,CAGAzC,SAAS,GAAK,UAAU,eACvBF,KAAA,QAAKyC,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACzC5C,IAAA,QAAK2C,SAAS,CAAC,oCAAoC,CAAAC,QAAA,cACjD5C,IAAA,OAAI2C,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,yBAAoB,CAAI,CAAC,CACxE,CAAC,cACN5C,IAAA,QAAK2C,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9B1C,KAAA,UAAOyC,SAAS,CAAC,qCAAqC,CAAAC,QAAA,eACpD5C,IAAA,UAAO2C,SAAS,CAAC,YAAY,CAAAC,QAAA,cAC3B1C,KAAA,OAAA0C,QAAA,eACE5C,IAAA,OAAI2C,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAAC,IAE/F,CAAI,CAAC,cACL5C,IAAA,OAAI2C,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAAC,OAE/F,CAAI,CAAC,cACL5C,IAAA,OAAI2C,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAAC,QAE/F,CAAI,CAAC,cACL5C,IAAA,OAAI2C,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAAC,WAE/F,CAAI,CAAC,cACL5C,IAAA,OAAI2C,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAAC,MAE/F,CAAI,CAAC,cACL5C,IAAA,OAAI2C,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAAC,aAE/F,CAAI,CAAC,EACH,CAAC,CACA,CAAC,cACR5C,IAAA,UAAO2C,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CACjD/B,QAAQ,CAACkC,GAAG,CAAEhB,OAAO,eACpB7B,KAAA,OAAA0C,QAAA,eACE1C,KAAA,OAAIyC,SAAS,CAAC,+DAA+D,CAAAC,QAAA,EAAC,GAC3E,CAACb,OAAO,CAACc,EAAE,CAACU,KAAK,CAAC,CAAC,CAAC,CAAC,EACpB,CAAC,cACLvD,IAAA,OAAI2C,SAAS,CAAC,mDAAmD,CAAAC,QAAA,CAC9DH,cAAc,CAACV,OAAO,CAACE,MAAM,CAAC,CAC7B,CAAC,cACLjC,IAAA,OAAI2C,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cACzC5C,IAAA,CAACJ,aAAa,EAACoC,MAAM,CAAED,OAAO,CAACC,MAAO,CAACwB,IAAI,CAAC,IAAI,CAAE,CAAC,CACjD,CAAC,cACLxD,IAAA,OAAI2C,SAAS,CAAC,mDAAmD,CAAAC,QAAA,CAC9Db,OAAO,CAAC6B,aAAa,CACpB,CAAC,cACL5D,IAAA,OAAI2C,SAAS,CAAC,mDAAmD,CAAAC,QAAA,CAC9D,GAAI,CAAAa,IAAI,CAAC1B,OAAO,CAAC2B,SAAS,CAAC,CAACC,kBAAkB,CAAC,OAAO,CAAC,CACtD,CAAC,cACL3D,IAAA,OAAI2C,SAAS,CAAC,iDAAiD,CAAAC,QAAA,CAC5D/C,iBAAiB,CAACgE,SAAS,CAAC9B,OAAO,CAACC,MAAM,CAAC,eAC1ChC,IAAA,WACEiD,OAAO,CAAEA,CAAA,GAAMf,mBAAmB,CAACH,OAAO,CAACc,EAAE,CAAE,CAC/CF,SAAS,CAAC,iCAAiC,CAAAC,QAAA,CAC5C,YAED,CAAQ,CACT,CACC,CAAC,GAzBEb,OAAO,CAACc,EA0Bb,CACL,CAAC,CACG,CAAC,EACH,CAAC,CACL,CAAC,EACH,CACN,CAGAzC,SAAS,GAAK,QAAQ,eACrBF,KAAA,QAAKyC,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACzC5C,IAAA,QAAK2C,SAAS,CAAC,oCAAoC,CAAAC,QAAA,cACjD5C,IAAA,OAAI2C,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,sBAAiB,CAAI,CAAC,CACrE,CAAC,cACN5C,IAAA,QAAK2C,SAAS,CAAC,KAAK,CAAAC,QAAA,cAClB5C,IAAA,QAAK2C,SAAS,CAAC,WAAW,CAAAC,QAAA,CACvB7B,MAAM,CAACgC,GAAG,CAAEe,KAAK,eAChB5D,KAAA,QAAoByC,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eACnE1C,KAAA,QAAKyC,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eACrD1C,KAAA,OAAIyC,SAAS,CAAC,mCAAmC,CAAAC,QAAA,EAAC,UACxC,CAACkB,KAAK,CAACjB,EAAE,CAACU,KAAK,CAAC,CAAC,CAAC,CAAC,EACzB,CAAC,cACLvD,IAAA,SAAM2C,SAAS,+CAAAH,MAAA,CACbsB,KAAK,CAAC9B,MAAM,GAAK,WAAW,CAAG,6BAA6B,CAC5D8B,KAAK,CAAC9B,MAAM,GAAK,aAAa,CAAG,2BAA2B,CAC5D8B,KAAK,CAAC9B,MAAM,GAAK,OAAO,CAAG,+BAA+B,CAC1D,2BAA2B,CAC1B,CAAAY,QAAA,CACAkB,KAAK,CAAC9B,MAAM,CACT,CAAC,EACJ,CAAC,cACN9B,KAAA,QAAKyC,SAAS,CAAC,gCAAgC,CAAAC,QAAA,eAC7C1C,KAAA,QAAA0C,QAAA,eACE5C,IAAA,SAAM2C,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,QAAM,CAAM,CAAC,cAC7C5C,IAAA,SAAM2C,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAAEH,cAAc,CAACqB,KAAK,CAACC,WAAW,CAAC,CAAO,CAAC,EAC1E,CAAC,cACN7D,KAAA,QAAA0C,QAAA,eACE5C,IAAA,SAAM2C,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,YAAU,CAAM,CAAC,cACjD5C,IAAA,SAAM2C,SAAS,qBAAAH,MAAA,CACbsB,KAAK,CAACE,aAAa,GAAK,MAAM,CAAG,gBAAgB,CACjDF,KAAK,CAACE,aAAa,GAAK,SAAS,CAAG,iBAAiB,CACrD,cAAc,CACb,CAAApB,QAAA,CACAkB,KAAK,CAACE,aAAa,CAChB,CAAC,EACJ,CAAC,cACN9D,KAAA,QAAA0C,QAAA,eACE5C,IAAA,SAAM2C,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,OAAK,CAAM,CAAC,cAC5C5C,IAAA,SAAM2C,SAAS,CAAC,MAAM,CAAAC,QAAA,CAAE,GAAI,CAAAa,IAAI,CAACK,KAAK,CAACJ,SAAS,CAAC,CAACC,kBAAkB,CAAC,OAAO,CAAC,CAAO,CAAC,EAClF,CAAC,cACNzD,KAAA,QAAA0C,QAAA,eACE5C,IAAA,SAAM2C,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,QAAM,CAAM,CAAC,cAC7C5C,IAAA,SAAM2C,SAAS,CAAC,MAAM,CAAAC,QAAA,CAAEkB,KAAK,CAACG,SAAS,CAACC,MAAM,CAAO,CAAC,EACnD,CAAC,EACH,CAAC,GArCEJ,KAAK,CAACjB,EAsCX,CACN,CAAC,CACC,CAAC,CACH,CAAC,EACH,CACN,EACE,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAA1C,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}