{"ast": null, "code": "import React from'react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ClientPanel=()=>/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col items-center justify-center min-h-screen\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-semibold mb-4\",children:\"Painel do Cliente\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"w-96 bg-white shadow rounded p-4\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"font-bold mb-2\",children:\"Hist\\xF3rico de Encomendas\"}),/*#__PURE__*/_jsxs(\"ul\",{className:\"mb-4\",children:[/*#__PURE__*/_jsx(\"li\",{children:\"Pedido #1 - Entregue\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Pedido #2 - Em produ\\xE7\\xE3o\"})]}),/*#__PURE__*/_jsx(\"button\",{className:\"px-4 py-2 bg-blue-600 text-white rounded\",children:\"Repetir Pedido\"})]})]});export default ClientPanel;", "map": {"version": 3, "names": ["React", "jsx", "_jsx", "jsxs", "_jsxs", "ClientPanel", "className", "children"], "sources": ["/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/ClientPanel.tsx"], "sourcesContent": ["import React from 'react';\n\nconst ClientPanel: React.FC = () => (\n  <div className=\"flex flex-col items-center justify-center min-h-screen\">\n    <h2 className=\"text-2xl font-semibold mb-4\">Painel do Cliente</h2>\n    <div className=\"w-96 bg-white shadow rounded p-4\">\n      <h3 className=\"font-bold mb-2\">Histórico de Encomendas</h3>\n      <ul className=\"mb-4\">\n        <li>Pedido #1 - Entregue</li>\n        <li>Pedido #2 - Em produção</li>\n      </ul>\n      <button className=\"px-4 py-2 bg-blue-600 text-white rounded\">Repetir Pedido</button>\n    </div>\n  </div>\n);\n\nexport default ClientPanel;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE1B,KAAM,CAAAC,WAAqB,CAAGA,CAAA,gBAC5BD,KAAA,QAAKE,SAAS,CAAC,wDAAwD,CAAAC,QAAA,eACrEL,IAAA,OAAII,SAAS,CAAC,6BAA6B,CAAAC,QAAA,CAAC,mBAAiB,CAAI,CAAC,cAClEH,KAAA,QAAKE,SAAS,CAAC,kCAAkC,CAAAC,QAAA,eAC/CL,IAAA,OAAII,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAAC,4BAAuB,CAAI,CAAC,cAC3DH,KAAA,OAAIE,SAAS,CAAC,MAAM,CAAAC,QAAA,eAClBL,IAAA,OAAAK,QAAA,CAAI,sBAAoB,CAAI,CAAC,cAC7BL,IAAA,OAAAK,QAAA,CAAI,+BAAuB,CAAI,CAAC,EAC9B,CAAC,cACLL,IAAA,WAAQI,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,gBAAc,CAAQ,CAAC,EACjF,CAAC,EACH,CACN,CAED,cAAe,CAAAF,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}