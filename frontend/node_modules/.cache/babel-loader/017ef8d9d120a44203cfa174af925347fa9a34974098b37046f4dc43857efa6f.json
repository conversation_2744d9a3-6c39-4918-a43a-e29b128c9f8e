{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/landing/Footer.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Footer = () => {\n  return /*#__PURE__*/_jsxDEV(\"footer\", {\n    className: \"bg-weprint-black text-white py-16\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-6 lg:px-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:col-span-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-3xl font-black mb-2\",\n              children: [\"WE\", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-weprint-magenta\",\n                children: \"PRINT\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 13,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 12,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-weprint-yellow font-bold tracking-wider\",\n              children: \"OPUL\\xCANCIA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 15,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-16 h-1 bg-weprint-gradient mt-2 rounded-full\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 16,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 11,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-300 mb-6 max-w-md\",\n            children: \"Transformando documentos em impress\\xF5es profissionais com qualidade premium, rapidez e atendimento excepcional em Angola.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 19,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#\",\n              className: \"w-10 h-10 bg-weprint-cyan rounded-full flex items-center justify-center hover:scale-110 transition-transform duration-300\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-white font-bold\",\n                children: \"f\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 27,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 26,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#\",\n              className: \"w-10 h-10 bg-weprint-magenta rounded-full flex items-center justify-center hover:scale-110 transition-transform duration-300\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-white font-bold\",\n                children: \"ig\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 30,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 29,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#\",\n              className: \"w-10 h-10 bg-weprint-yellow rounded-full flex items-center justify-center hover:scale-110 transition-transform duration-300\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-white font-bold\",\n                children: \"wa\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 33,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 32,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#\",\n              className: \"w-10 h-10 bg-white rounded-full flex items-center justify-center hover:scale-110 transition-transform duration-300\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-weprint-black font-bold\",\n                children: \"@\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 36,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 35,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 10,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-bold mb-4 text-weprint-cyan\",\n            children: \"Servi\\xE7os\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"space-y-2 text-gray-300\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#\",\n                className: \"hover:text-weprint-cyan transition-colors duration-300\",\n                children: \"Impress\\xE3o de Documentos\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 45,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 45,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#\",\n                className: \"hover:text-weprint-cyan transition-colors duration-300\",\n                children: \"Apresenta\\xE7\\xF5es\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 46,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 46,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#\",\n                className: \"hover:text-weprint-cyan transition-colors duration-300\",\n                children: \"Fotografias\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 47,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 47,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#\",\n                className: \"hover:text-weprint-cyan transition-colors duration-300\",\n                children: \"Livros & Revistas\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 48,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 48,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#\",\n                className: \"hover:text-weprint-cyan transition-colors duration-300\",\n                children: \"Projetos Especiais\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 49,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 49,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-bold mb-4 text-weprint-magenta\",\n            children: \"Suporte\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 55,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"space-y-2 text-gray-300\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#\",\n                className: \"hover:text-weprint-magenta transition-colors duration-300\",\n                children: \"Como Funciona\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 57,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 57,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#\",\n                className: \"hover:text-weprint-magenta transition-colors duration-300\",\n                children: \"Perguntas Frequentes\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 58,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 58,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#\",\n                className: \"hover:text-weprint-magenta transition-colors duration-300\",\n                children: \"Pol\\xEDtica de Privacidade\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 59,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 59,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#\",\n                className: \"hover:text-weprint-magenta transition-colors duration-300\",\n                children: \"Termos de Uso\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 60,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 60,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#\",\n                className: \"hover:text-weprint-magenta transition-colors duration-300\",\n                children: \"Contato\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 61,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 61,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"border-t border-gray-700 pt-8 mb-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-3 gap-6 text-center md:text-left\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-center md:justify-start\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-8 h-8 bg-weprint-cyan rounded-full flex items-center justify-center mr-3\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-white text-sm\",\n                children: \"\\uD83D\\uDCCD\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 71,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 70,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"font-bold text-weprint-cyan\",\n                children: \"Endere\\xE7o\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 74,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-gray-300 text-sm\",\n                children: \"Luanda, Angola\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 75,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 73,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-center md:justify-start\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-8 h-8 bg-weprint-magenta rounded-full flex items-center justify-center mr-3\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-white text-sm\",\n                children: \"\\uD83D\\uDCDE\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 81,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 80,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"font-bold text-weprint-magenta\",\n                children: \"Telefone\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 84,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-gray-300 text-sm\",\n                children: \"+244 900 000 000\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 85,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 83,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-center md:justify-start\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-8 h-8 bg-weprint-yellow rounded-full flex items-center justify-center mr-3\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-white text-sm\",\n                children: \"\\u2709\\uFE0F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 91,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 90,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"font-bold text-weprint-yellow\",\n                children: \"Email\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 94,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-gray-300 text-sm\",\n                children: \"<EMAIL>\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 95,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 93,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"border-t border-gray-700 pt-8 mb-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center max-w-2xl mx-auto\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-xl font-bold mb-4\",\n            children: [\"Receba \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-weprint-yellow\",\n              children: \"Ofertas Especiais\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 22\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-300 mb-6\",\n            children: \"Inscreva-se na nossa newsletter e seja o primeiro a saber sobre promo\\xE7\\xF5es e novidades.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col sm:flex-row gap-4 max-w-md mx-auto\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"email\",\n              placeholder: \"Seu email\",\n              className: \"flex-1 px-4 py-3 rounded-full bg-gray-800 text-white placeholder-gray-400 border border-gray-600 focus:border-weprint-cyan focus:outline-none\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 111,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"bg-weprint-gradient text-white font-bold px-8 py-3 rounded-full hover:shadow-lg transition-all duration-300\",\n              children: \"Inscrever\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"border-t border-gray-700 pt-8 text-center\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col md:flex-row justify-between items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-gray-400 text-sm mb-4 md:mb-0\",\n            children: \"\\xA9 2025 WePrint. Todos os direitos reservados.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex space-x-6 text-sm text-gray-400\",\n            children: [/*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#\",\n              className: \"hover:text-weprint-cyan transition-colors duration-300\",\n              children: \"Privacidade\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#\",\n              className: \"hover:text-weprint-cyan transition-colors duration-300\",\n              children: \"Termos\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#\",\n              className: \"hover:text-weprint-cyan transition-colors duration-300\",\n              children: \"Cookies\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 6,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this);\n};\n_c = Footer;\nexport default Footer;\nvar _c;\n$RefreshReg$(_c, \"Footer\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Footer", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "href", "type", "placeholder", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/landing/Footer.tsx"], "sourcesContent": ["import React from 'react';\n\nconst Footer: React.FC = () => {\n  return (\n    <footer className=\"bg-weprint-black text-white py-16\">\n      <div className=\"container mx-auto px-6 lg:px-8\">\n        {/* Main Footer Content */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12\">\n          {/* Company Info */}\n          <div className=\"lg:col-span-2\">\n            <div className=\"mb-6\">\n              <div className=\"text-3xl font-black mb-2\">\n                WE<span className=\"text-weprint-magenta\">PRINT</span>\n              </div>\n              <div className=\"text-weprint-yellow font-bold tracking-wider\">OPULÊNCIA</div>\n              <div className=\"w-16 h-1 bg-weprint-gradient mt-2 rounded-full\"></div>\n            </div>\n            \n            <p className=\"text-gray-300 mb-6 max-w-md\">\n              Transformando documentos em impressões profissionais com qualidade premium, \n              rapidez e atendimento excepcional em Angola.\n            </p>\n            \n            {/* Social Media */}\n            <div className=\"flex space-x-4\">\n              <a href=\"#\" className=\"w-10 h-10 bg-weprint-cyan rounded-full flex items-center justify-center hover:scale-110 transition-transform duration-300\">\n                <span className=\"text-white font-bold\">f</span>\n              </a>\n              <a href=\"#\" className=\"w-10 h-10 bg-weprint-magenta rounded-full flex items-center justify-center hover:scale-110 transition-transform duration-300\">\n                <span className=\"text-white font-bold\">ig</span>\n              </a>\n              <a href=\"#\" className=\"w-10 h-10 bg-weprint-yellow rounded-full flex items-center justify-center hover:scale-110 transition-transform duration-300\">\n                <span className=\"text-white font-bold\">wa</span>\n              </a>\n              <a href=\"#\" className=\"w-10 h-10 bg-white rounded-full flex items-center justify-center hover:scale-110 transition-transform duration-300\">\n                <span className=\"text-weprint-black font-bold\">@</span>\n              </a>\n            </div>\n          </div>\n\n          {/* Services */}\n          <div>\n            <h3 className=\"text-lg font-bold mb-4 text-weprint-cyan\">Serviços</h3>\n            <ul className=\"space-y-2 text-gray-300\">\n              <li><a href=\"#\" className=\"hover:text-weprint-cyan transition-colors duration-300\">Impressão de Documentos</a></li>\n              <li><a href=\"#\" className=\"hover:text-weprint-cyan transition-colors duration-300\">Apresentações</a></li>\n              <li><a href=\"#\" className=\"hover:text-weprint-cyan transition-colors duration-300\">Fotografias</a></li>\n              <li><a href=\"#\" className=\"hover:text-weprint-cyan transition-colors duration-300\">Livros & Revistas</a></li>\n              <li><a href=\"#\" className=\"hover:text-weprint-cyan transition-colors duration-300\">Projetos Especiais</a></li>\n            </ul>\n          </div>\n\n          {/* Support */}\n          <div>\n            <h3 className=\"text-lg font-bold mb-4 text-weprint-magenta\">Suporte</h3>\n            <ul className=\"space-y-2 text-gray-300\">\n              <li><a href=\"#\" className=\"hover:text-weprint-magenta transition-colors duration-300\">Como Funciona</a></li>\n              <li><a href=\"#\" className=\"hover:text-weprint-magenta transition-colors duration-300\">Perguntas Frequentes</a></li>\n              <li><a href=\"#\" className=\"hover:text-weprint-magenta transition-colors duration-300\">Política de Privacidade</a></li>\n              <li><a href=\"#\" className=\"hover:text-weprint-magenta transition-colors duration-300\">Termos de Uso</a></li>\n              <li><a href=\"#\" className=\"hover:text-weprint-magenta transition-colors duration-300\">Contato</a></li>\n            </ul>\n          </div>\n        </div>\n\n        {/* Contact Information */}\n        <div className=\"border-t border-gray-700 pt-8 mb-8\">\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 text-center md:text-left\">\n            <div className=\"flex items-center justify-center md:justify-start\">\n              <div className=\"w-8 h-8 bg-weprint-cyan rounded-full flex items-center justify-center mr-3\">\n                <span className=\"text-white text-sm\">📍</span>\n              </div>\n              <div>\n                <div className=\"font-bold text-weprint-cyan\">Endereço</div>\n                <div className=\"text-gray-300 text-sm\">Luanda, Angola</div>\n              </div>\n            </div>\n            \n            <div className=\"flex items-center justify-center md:justify-start\">\n              <div className=\"w-8 h-8 bg-weprint-magenta rounded-full flex items-center justify-center mr-3\">\n                <span className=\"text-white text-sm\">📞</span>\n              </div>\n              <div>\n                <div className=\"font-bold text-weprint-magenta\">Telefone</div>\n                <div className=\"text-gray-300 text-sm\">+244 900 000 000</div>\n              </div>\n            </div>\n            \n            <div className=\"flex items-center justify-center md:justify-start\">\n              <div className=\"w-8 h-8 bg-weprint-yellow rounded-full flex items-center justify-center mr-3\">\n                <span className=\"text-white text-sm\">✉️</span>\n              </div>\n              <div>\n                <div className=\"font-bold text-weprint-yellow\">Email</div>\n                <div className=\"text-gray-300 text-sm\"><EMAIL></div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Newsletter */}\n        <div className=\"border-t border-gray-700 pt-8 mb-8\">\n          <div className=\"text-center max-w-2xl mx-auto\">\n            <h3 className=\"text-xl font-bold mb-4\">\n              Receba <span className=\"text-weprint-yellow\">Ofertas Especiais</span>\n            </h3>\n            <p className=\"text-gray-300 mb-6\">\n              Inscreva-se na nossa newsletter e seja o primeiro a saber sobre promoções e novidades.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 max-w-md mx-auto\">\n              <input\n                type=\"email\"\n                placeholder=\"Seu email\"\n                className=\"flex-1 px-4 py-3 rounded-full bg-gray-800 text-white placeholder-gray-400 border border-gray-600 focus:border-weprint-cyan focus:outline-none\"\n              />\n              <button className=\"bg-weprint-gradient text-white font-bold px-8 py-3 rounded-full hover:shadow-lg transition-all duration-300\">\n                Inscrever\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Bottom Bar */}\n        <div className=\"border-t border-gray-700 pt-8 text-center\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center\">\n            <div className=\"text-gray-400 text-sm mb-4 md:mb-0\">\n              © 2025 WePrint. Todos os direitos reservados.\n            </div>\n            <div className=\"flex space-x-6 text-sm text-gray-400\">\n              <a href=\"#\" className=\"hover:text-weprint-cyan transition-colors duration-300\">Privacidade</a>\n              <a href=\"#\" className=\"hover:text-weprint-cyan transition-colors duration-300\">Termos</a>\n              <a href=\"#\" className=\"hover:text-weprint-cyan transition-colors duration-300\">Cookies</a>\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,MAAgB,GAAGA,CAAA,KAAM;EAC7B,oBACED,OAAA;IAAQE,SAAS,EAAC,mCAAmC;IAAAC,QAAA,eACnDH,OAAA;MAAKE,SAAS,EAAC,gCAAgC;MAAAC,QAAA,gBAE7CH,OAAA;QAAKE,SAAS,EAAC,4DAA4D;QAAAC,QAAA,gBAEzEH,OAAA;UAAKE,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BH,OAAA;YAAKE,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBH,OAAA;cAAKE,SAAS,EAAC,0BAA0B;cAAAC,QAAA,GAAC,IACtC,eAAAH,OAAA;gBAAME,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC,eACNP,OAAA;cAAKE,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC7EP,OAAA;cAAKE,SAAS,EAAC;YAAgD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnE,CAAC,eAENP,OAAA;YAAGE,SAAS,EAAC,6BAA6B;YAAAC,QAAA,EAAC;UAG3C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAGJP,OAAA;YAAKE,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BH,OAAA;cAAGQ,IAAI,EAAC,GAAG;cAACN,SAAS,EAAC,2HAA2H;cAAAC,QAAA,eAC/IH,OAAA;gBAAME,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC,eACJP,OAAA;cAAGQ,IAAI,EAAC,GAAG;cAACN,SAAS,EAAC,8HAA8H;cAAAC,QAAA,eAClJH,OAAA;gBAAME,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC,eACJP,OAAA;cAAGQ,IAAI,EAAC,GAAG;cAACN,SAAS,EAAC,6HAA6H;cAAAC,QAAA,eACjJH,OAAA;gBAAME,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC,eACJP,OAAA;cAAGQ,IAAI,EAAC,GAAG;cAACN,SAAS,EAAC,oHAAoH;cAAAC,QAAA,eACxIH,OAAA;gBAAME,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNP,OAAA;UAAAG,QAAA,gBACEH,OAAA;YAAIE,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtEP,OAAA;YAAIE,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACrCH,OAAA;cAAAG,QAAA,eAAIH,OAAA;gBAAGQ,IAAI,EAAC,GAAG;gBAACN,SAAS,EAAC,wDAAwD;gBAAAC,QAAA,EAAC;cAAuB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnHP,OAAA;cAAAG,QAAA,eAAIH,OAAA;gBAAGQ,IAAI,EAAC,GAAG;gBAACN,SAAS,EAAC,wDAAwD;gBAAAC,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzGP,OAAA;cAAAG,QAAA,eAAIH,OAAA;gBAAGQ,IAAI,EAAC,GAAG;gBAACN,SAAS,EAAC,wDAAwD;gBAAAC,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvGP,OAAA;cAAAG,QAAA,eAAIH,OAAA;gBAAGQ,IAAI,EAAC,GAAG;gBAACN,SAAS,EAAC,wDAAwD;gBAAAC,QAAA,EAAC;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7GP,OAAA;cAAAG,QAAA,eAAIH,OAAA;gBAAGQ,IAAI,EAAC,GAAG;gBAACN,SAAS,EAAC,wDAAwD;gBAAAC,QAAA,EAAC;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5G,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAGNP,OAAA;UAAAG,QAAA,gBACEH,OAAA;YAAIE,SAAS,EAAC,6CAA6C;YAAAC,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxEP,OAAA;YAAIE,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACrCH,OAAA;cAAAG,QAAA,eAAIH,OAAA;gBAAGQ,IAAI,EAAC,GAAG;gBAACN,SAAS,EAAC,2DAA2D;gBAAAC,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5GP,OAAA;cAAAG,QAAA,eAAIH,OAAA;gBAAGQ,IAAI,EAAC,GAAG;gBAACN,SAAS,EAAC,2DAA2D;gBAAAC,QAAA,EAAC;cAAoB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnHP,OAAA;cAAAG,QAAA,eAAIH,OAAA;gBAAGQ,IAAI,EAAC,GAAG;gBAACN,SAAS,EAAC,2DAA2D;gBAAAC,QAAA,EAAC;cAAuB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtHP,OAAA;cAAAG,QAAA,eAAIH,OAAA;gBAAGQ,IAAI,EAAC,GAAG;gBAACN,SAAS,EAAC,2DAA2D;gBAAAC,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5GP,OAAA;cAAAG,QAAA,eAAIH,OAAA;gBAAGQ,IAAI,EAAC,GAAG;gBAACN,SAAS,EAAC,2DAA2D;gBAAAC,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNP,OAAA;QAAKE,SAAS,EAAC,oCAAoC;QAAAC,QAAA,eACjDH,OAAA;UAAKE,SAAS,EAAC,gEAAgE;UAAAC,QAAA,gBAC7EH,OAAA;YAAKE,SAAS,EAAC,mDAAmD;YAAAC,QAAA,gBAChEH,OAAA;cAAKE,SAAS,EAAC,4EAA4E;cAAAC,QAAA,eACzFH,OAAA;gBAAME,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC,eACNP,OAAA;cAAAG,QAAA,gBACEH,OAAA;gBAAKE,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC3DP,OAAA;gBAAKE,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENP,OAAA;YAAKE,SAAS,EAAC,mDAAmD;YAAAC,QAAA,gBAChEH,OAAA;cAAKE,SAAS,EAAC,+EAA+E;cAAAC,QAAA,eAC5FH,OAAA;gBAAME,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC,eACNP,OAAA;cAAAG,QAAA,gBACEH,OAAA;gBAAKE,SAAS,EAAC,gCAAgC;gBAAAC,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC9DP,OAAA;gBAAKE,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENP,OAAA;YAAKE,SAAS,EAAC,mDAAmD;YAAAC,QAAA,gBAChEH,OAAA;cAAKE,SAAS,EAAC,8EAA8E;cAAAC,QAAA,eAC3FH,OAAA;gBAAME,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC,eACNP,OAAA;cAAAG,QAAA,gBACEH,OAAA;gBAAKE,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC1DP,OAAA;gBAAKE,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNP,OAAA;QAAKE,SAAS,EAAC,oCAAoC;QAAAC,QAAA,eACjDH,OAAA;UAAKE,SAAS,EAAC,+BAA+B;UAAAC,QAAA,gBAC5CH,OAAA;YAAIE,SAAS,EAAC,wBAAwB;YAAAC,QAAA,GAAC,SAC9B,eAAAH,OAAA;cAAME,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnE,CAAC,eACLP,OAAA;YAAGE,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAElC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJP,OAAA;YAAKE,SAAS,EAAC,kDAAkD;YAAAC,QAAA,gBAC/DH,OAAA;cACES,IAAI,EAAC,OAAO;cACZC,WAAW,EAAC,WAAW;cACvBR,SAAS,EAAC;YAA+I;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1J,CAAC,eACFP,OAAA;cAAQE,SAAS,EAAC,6GAA6G;cAAAC,QAAA,EAAC;YAEhI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNP,OAAA;QAAKE,SAAS,EAAC,2CAA2C;QAAAC,QAAA,eACxDH,OAAA;UAAKE,SAAS,EAAC,wDAAwD;UAAAC,QAAA,gBACrEH,OAAA;YAAKE,SAAS,EAAC,oCAAoC;YAAAC,QAAA,EAAC;UAEpD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNP,OAAA;YAAKE,SAAS,EAAC,sCAAsC;YAAAC,QAAA,gBACnDH,OAAA;cAAGQ,IAAI,EAAC,GAAG;cAACN,SAAS,EAAC,wDAAwD;cAAAC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC9FP,OAAA;cAAGQ,IAAI,EAAC,GAAG;cAACN,SAAS,EAAC,wDAAwD;cAAAC,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACzFP,OAAA;cAAGQ,IAAI,EAAC,GAAG;cAACN,SAAS,EAAC,wDAAwD;cAAAC,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAACI,EAAA,GAxIIV,MAAgB;AA0ItB,eAAeA,MAAM;AAAC,IAAAU,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}