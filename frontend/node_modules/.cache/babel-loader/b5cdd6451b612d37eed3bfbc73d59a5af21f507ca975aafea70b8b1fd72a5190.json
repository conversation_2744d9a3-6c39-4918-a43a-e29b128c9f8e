{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/LandingPage.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { Layout, Button } from '../components';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LandingPage = () => {\n  _s();\n  const navigate = useNavigate();\n  const handleStartOrder = () => {\n    navigate('/upload');\n  };\n  const handleLogin = () => {\n    navigate('/painel');\n  };\n  return /*#__PURE__*/_jsxDEV(Layout, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col items-center justify-center min-h-[60vh] text-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl mx-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-5xl font-bold text-gray-900 mb-6\",\n          children: \"WePrint AI\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xl text-gray-600 mb-8 max-w-2xl mx-auto\",\n          children: \"A gr\\xE1fica inteligente que vai at\\xE9 ti. Impress\\xE3o profissional com tecnologia AI para otimizar qualidade e custos.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col sm:flex-row gap-4 justify-center items-center\",\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleStartOrder,\n            size: \"lg\",\n            className: \"w-full sm:w-auto\",\n            children: \"Come\\xE7ar Encomenda\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 27,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleLogin,\n            variant: \"outline\",\n            size: \"lg\",\n            className: \"w-full sm:w-auto\",\n            children: \"Login/Cadastro\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 34,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-16 grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-2xl\",\n              children: \"\\uD83D\\uDE80\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 48,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 47,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold mb-2\",\n            children: \"R\\xE1pido e F\\xE1cil\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 50,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: \"Upload, configure e receba em casa\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-2xl\",\n              children: \"\\uD83E\\uDD16\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 56,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 55,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold mb-2\",\n            children: \"Tecnologia AI\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: \"Otimiza\\xE7\\xE3o autom\\xE1tica de qualidade\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-purple-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-2xl\",\n              children: \"\\uD83D\\uDCE6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 64,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold mb-2\",\n            children: \"Entrega Gr\\xE1tis\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: \"Levamos at\\xE9 \\xE0 sua porta\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 67,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 17,\n    columnNumber: 5\n  }, this);\n};\n_s(LandingPage, \"CzcTeTziyjMsSrAVmHuCCb6+Bfg=\", false, function () {\n  return [useNavigate];\n});\n_c = LandingPage;\nexport default LandingPage;\nvar _c;\n$RefreshReg$(_c, \"LandingPage\");", "map": {"version": 3, "names": ["React", "useNavigate", "Layout", "<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "LandingPage", "_s", "navigate", "handleStartOrder", "handleLogin", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "size", "variant", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/LandingPage.tsx"], "sourcesContent": ["import React from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { Layout, Button } from '../components';\n\nconst LandingPage: React.FC = () => {\n  const navigate = useNavigate();\n\n  const handleStartOrder = () => {\n    navigate('/upload');\n  };\n\n  const handleLogin = () => {\n    navigate('/painel');\n  };\n\n  return (\n    <Layout>\n      <div className=\"flex flex-col items-center justify-center min-h-[60vh] text-center\">\n        <div className=\"max-w-4xl mx-auto\">\n          <h1 className=\"text-5xl font-bold text-gray-900 mb-6\">\n            WePrint AI\n          </h1>\n          <p className=\"text-xl text-gray-600 mb-8 max-w-2xl mx-auto\">\n            A gráfica inteligente que vai até ti. Impressão profissional com tecnologia AI para otimizar qualidade e custos.\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center\">\n            <Button\n              onClick={handleStartOrder}\n              size=\"lg\"\n              className=\"w-full sm:w-auto\"\n            >\n              Começar Encomenda\n            </Button>\n            <Button\n              onClick={handleLogin}\n              variant=\"outline\"\n              size=\"lg\"\n              className=\"w-full sm:w-auto\"\n            >\n              Login/Cadastro\n            </Button>\n          </div>\n        </div>\n\n        <div className=\"mt-16 grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto\">\n          <div className=\"text-center\">\n            <div className=\"bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4\">\n              <span className=\"text-2xl\">🚀</span>\n            </div>\n            <h3 className=\"text-lg font-semibold mb-2\">Rápido e Fácil</h3>\n            <p className=\"text-gray-600\">Upload, configure e receba em casa</p>\n          </div>\n\n          <div className=\"text-center\">\n            <div className=\"bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4\">\n              <span className=\"text-2xl\">🤖</span>\n            </div>\n            <h3 className=\"text-lg font-semibold mb-2\">Tecnologia AI</h3>\n            <p className=\"text-gray-600\">Otimização automática de qualidade</p>\n          </div>\n\n          <div className=\"text-center\">\n            <div className=\"bg-purple-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4\">\n              <span className=\"text-2xl\">📦</span>\n            </div>\n            <h3 className=\"text-lg font-semibold mb-2\">Entrega Grátis</h3>\n            <p className=\"text-gray-600\">Levamos até à sua porta</p>\n          </div>\n        </div>\n      </div>\n    </Layout>\n  );\n};\n\nexport default LandingPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,MAAM,EAAEC,MAAM,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,WAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClC,MAAMC,QAAQ,GAAGP,WAAW,CAAC,CAAC;EAE9B,MAAMQ,gBAAgB,GAAGA,CAAA,KAAM;IAC7BD,QAAQ,CAAC,SAAS,CAAC;EACrB,CAAC;EAED,MAAME,WAAW,GAAGA,CAAA,KAAM;IACxBF,QAAQ,CAAC,SAAS,CAAC;EACrB,CAAC;EAED,oBACEH,OAAA,CAACH,MAAM;IAAAS,QAAA,eACLN,OAAA;MAAKO,SAAS,EAAC,oEAAoE;MAAAD,QAAA,gBACjFN,OAAA;QAAKO,SAAS,EAAC,mBAAmB;QAAAD,QAAA,gBAChCN,OAAA;UAAIO,SAAS,EAAC,uCAAuC;UAAAD,QAAA,EAAC;QAEtD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLX,OAAA;UAAGO,SAAS,EAAC,8CAA8C;UAAAD,QAAA,EAAC;QAE5D;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJX,OAAA;UAAKO,SAAS,EAAC,6DAA6D;UAAAD,QAAA,gBAC1EN,OAAA,CAACF,MAAM;YACLc,OAAO,EAAER,gBAAiB;YAC1BS,IAAI,EAAC,IAAI;YACTN,SAAS,EAAC,kBAAkB;YAAAD,QAAA,EAC7B;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTX,OAAA,CAACF,MAAM;YACLc,OAAO,EAAEP,WAAY;YACrBS,OAAO,EAAC,SAAS;YACjBD,IAAI,EAAC,IAAI;YACTN,SAAS,EAAC,kBAAkB;YAAAD,QAAA,EAC7B;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENX,OAAA;QAAKO,SAAS,EAAC,+DAA+D;QAAAD,QAAA,gBAC5EN,OAAA;UAAKO,SAAS,EAAC,aAAa;UAAAD,QAAA,gBAC1BN,OAAA;YAAKO,SAAS,EAAC,kFAAkF;YAAAD,QAAA,eAC/FN,OAAA;cAAMO,SAAS,EAAC,UAAU;cAAAD,QAAA,EAAC;YAAE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,eACNX,OAAA;YAAIO,SAAS,EAAC,4BAA4B;YAAAD,QAAA,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9DX,OAAA;YAAGO,SAAS,EAAC,eAAe;YAAAD,QAAA,EAAC;UAAkC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChE,CAAC,eAENX,OAAA;UAAKO,SAAS,EAAC,aAAa;UAAAD,QAAA,gBAC1BN,OAAA;YAAKO,SAAS,EAAC,mFAAmF;YAAAD,QAAA,eAChGN,OAAA;cAAMO,SAAS,EAAC,UAAU;cAAAD,QAAA,EAAC;YAAE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,eACNX,OAAA;YAAIO,SAAS,EAAC,4BAA4B;YAAAD,QAAA,EAAC;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7DX,OAAA;YAAGO,SAAS,EAAC,eAAe;YAAAD,QAAA,EAAC;UAAkC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChE,CAAC,eAENX,OAAA;UAAKO,SAAS,EAAC,aAAa;UAAAD,QAAA,gBAC1BN,OAAA;YAAKO,SAAS,EAAC,oFAAoF;YAAAD,QAAA,eACjGN,OAAA;cAAMO,SAAS,EAAC,UAAU;cAAAD,QAAA,EAAC;YAAE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,eACNX,OAAA;YAAIO,SAAS,EAAC,4BAA4B;YAAAD,QAAA,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9DX,OAAA;YAAGO,SAAS,EAAC,eAAe;YAAAD,QAAA,EAAC;UAAuB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAACT,EAAA,CApEID,WAAqB;EAAA,QACRL,WAAW;AAAA;AAAAmB,EAAA,GADxBd,WAAqB;AAsE3B,eAAeA,WAAW;AAAC,IAAAc,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}