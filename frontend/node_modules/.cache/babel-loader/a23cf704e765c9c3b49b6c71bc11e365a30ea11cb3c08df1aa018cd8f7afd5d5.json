{"ast": null, "code": "import _objectSpread from\"/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React from'react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Step=_ref=>{let{number,title,description,icon,color}=_ref;return/*#__PURE__*/_jsxs(\"div\",{className:\"relative text-center group\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-20 h-20 \".concat(color,\" rounded-full flex items-center justify-center text-white font-black text-2xl mx-auto mb-6 shadow-lg group-hover:scale-110 transition-transform duration-300\"),children:number}),/*#__PURE__*/_jsx(\"div\",{className:\"text-6xl mb-4 group-hover:animate-bounce-gentle\",children:icon}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-xl font-bold text-weprint-black mb-3\",children:title}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600 leading-relaxed\",children:description})]});};const HowItWorksSection=()=>{const steps=[{number:\"1\",title:\"Faça Upload\",description:\"Carregue seus documentos, fotos ou arquivos diretamente no nosso site. Suportamos PDF, Word, Excel, PowerPoint e imagens.\",icon:\"📤\",color:\"bg-weprint-cyan\"},{number:\"2\",title:\"Configure\",description:\"Escolha o tipo de papel, tamanho, cores, acabamento e quantidade. Veja o preço em tempo real enquanto configura.\",icon:\"⚙️\",color:\"bg-weprint-magenta\"},{number:\"3\",title:\"Pague\",description:\"Pague online com Multicaixa Express ou escolha pagamento na entrega. Processo seguro e rápido.\",icon:\"💳\",color:\"bg-weprint-yellow\"},{number:\"4\",title:\"Receba\",description:\"Entregamos na sua localização em Luanda ou retire na nossa loja. Acompanhe o status do seu pedido em tempo real.\",icon:\"🚚\",color:\"bg-weprint-black\"}];return/*#__PURE__*/_jsx(\"section\",{className:\"py-20 bg-white\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"container mx-auto px-6 lg:px-8\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-center mb-16\",children:[/*#__PURE__*/_jsxs(\"h2\",{className:\"text-4xl md:text-5xl font-black text-weprint-black mb-6\",children:[\"Como \",/*#__PURE__*/_jsx(\"span\",{className:\"text-weprint-cyan\",children:\"Funciona\"})]}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xl text-gray-600 max-w-3xl mx-auto\",children:\"Processo simples e r\\xE1pido para transformar seus arquivos em impress\\xF5es profissionais. Em apenas 4 passos, voc\\xEA tem suas impress\\xF5es prontas.\"}),/*#__PURE__*/_jsx(\"div\",{className:\"w-24 h-1 bg-weprint-gradient mx-auto mt-6 rounded-full\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12 relative\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"hidden lg:block absolute top-10 left-1/4 right-1/4 h-0.5 bg-gradient-to-r from-weprint-cyan via-weprint-magenta via-weprint-yellow to-weprint-black opacity-30\"}),steps.map((step,index)=>/*#__PURE__*/_jsx(Step,_objectSpread({},step),index))]}),/*#__PURE__*/_jsx(\"div\",{className:\"mt-16 text-center\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-weprint-gradient-subtle rounded-2xl p-8 max-w-2xl mx-auto\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-2xl font-bold text-weprint-black mb-4\",children:\"\\u23F1\\uFE0F Tempo Total: 5-10 minutos\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600 mb-6\",children:\"Do upload \\xE0 confirma\\xE7\\xE3o do pedido, todo o processo \\xE9 r\\xE1pido e intuitivo. Suas impress\\xF5es ficam prontas em 24-48 horas.\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-center gap-8 text-sm text-gray-600\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-3 h-3 bg-weprint-cyan rounded-full mr-2\"}),\"Upload: 1-2 min\"]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-3 h-3 bg-weprint-magenta rounded-full mr-2\"}),\"Configura\\xE7\\xE3o: 2-3 min\"]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-3 h-3 bg-weprint-yellow rounded-full mr-2\"}),\"Pagamento: 1-2 min\"]})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"text-center mt-12\",children:/*#__PURE__*/_jsx(\"button\",{onClick:()=>window.location.href='/upload',className:\"bg-weprint-gradient text-white font-bold text-lg px-12 py-4 rounded-full shadow-2xl hover:shadow-3xl transform hover:scale-105 transition-all duration-300\",children:\"Come\\xE7ar Agora \\u2192\"})})]})});};export default HowItWorksSection;", "map": {"version": 3, "names": ["React", "jsx", "_jsx", "jsxs", "_jsxs", "Step", "_ref", "number", "title", "description", "icon", "color", "className", "children", "concat", "HowItWorksSection", "steps", "map", "step", "index", "_objectSpread", "onClick", "window", "location", "href"], "sources": ["/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/landing/HowItWorksSection.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface StepProps {\n  number: string;\n  title: string;\n  description: string;\n  icon: string;\n  color: string;\n}\n\nconst Step: React.FC<StepProps> = ({ number, title, description, icon, color }) => {\n  return (\n    <div className=\"relative text-center group\">\n      {/* Step Number */}\n      <div className={`w-20 h-20 ${color} rounded-full flex items-center justify-center text-white font-black text-2xl mx-auto mb-6 shadow-lg group-hover:scale-110 transition-transform duration-300`}>\n        {number}\n      </div>\n      \n      {/* Icon */}\n      <div className=\"text-6xl mb-4 group-hover:animate-bounce-gentle\">\n        {icon}\n      </div>\n      \n      {/* Content */}\n      <h3 className=\"text-xl font-bold text-weprint-black mb-3\">{title}</h3>\n      <p className=\"text-gray-600 leading-relaxed\">{description}</p>\n    </div>\n  );\n};\n\nconst HowItWorksSection: React.FC = () => {\n  const steps = [\n    {\n      number: \"1\",\n      title: \"Faça Upload\",\n      description: \"Carregue seus documentos, fotos ou arquivos diretamente no nosso site. Suportamos PDF, Word, Excel, PowerPoint e imagens.\",\n      icon: \"📤\",\n      color: \"bg-weprint-cyan\"\n    },\n    {\n      number: \"2\",\n      title: \"Configure\",\n      description: \"Escolha o tipo de papel, tamanho, cores, acabamento e quantidade. Veja o preço em tempo real enquanto configura.\",\n      icon: \"⚙️\",\n      color: \"bg-weprint-magenta\"\n    },\n    {\n      number: \"3\",\n      title: \"Pague\",\n      description: \"Pague online com Multicaixa Express ou escolha pagamento na entrega. Processo seguro e rápido.\",\n      icon: \"💳\",\n      color: \"bg-weprint-yellow\"\n    },\n    {\n      number: \"4\",\n      title: \"Receba\",\n      description: \"Entregamos na sua localização em Luanda ou retire na nossa loja. Acompanhe o status do seu pedido em tempo real.\",\n      icon: \"🚚\",\n      color: \"bg-weprint-black\"\n    }\n  ];\n\n  return (\n    <section className=\"py-20 bg-white\">\n      <div className=\"container mx-auto px-6 lg:px-8\">\n        {/* Section Header */}\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-4xl md:text-5xl font-black text-weprint-black mb-6\">\n            Como <span className=\"text-weprint-cyan\">Funciona</span>\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            Processo simples e rápido para transformar seus arquivos em impressões profissionais. \n            Em apenas 4 passos, você tem suas impressões prontas.\n          </p>\n          <div className=\"w-24 h-1 bg-weprint-gradient mx-auto mt-6 rounded-full\"></div>\n        </div>\n\n        {/* Steps Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12 relative\">\n          {/* Connection Lines (hidden on mobile) */}\n          <div className=\"hidden lg:block absolute top-10 left-1/4 right-1/4 h-0.5 bg-gradient-to-r from-weprint-cyan via-weprint-magenta via-weprint-yellow to-weprint-black opacity-30\"></div>\n          \n          {steps.map((step, index) => (\n            <Step key={index} {...step} />\n          ))}\n        </div>\n\n        {/* Time Estimate */}\n        <div className=\"mt-16 text-center\">\n          <div className=\"bg-weprint-gradient-subtle rounded-2xl p-8 max-w-2xl mx-auto\">\n            <h3 className=\"text-2xl font-bold text-weprint-black mb-4\">\n              ⏱️ Tempo Total: 5-10 minutos\n            </h3>\n            <p className=\"text-gray-600 mb-6\">\n              Do upload à confirmação do pedido, todo o processo é rápido e intuitivo. \n              Suas impressões ficam prontas em 24-48 horas.\n            </p>\n            <div className=\"flex justify-center gap-8 text-sm text-gray-600\">\n              <div className=\"flex items-center\">\n                <div className=\"w-3 h-3 bg-weprint-cyan rounded-full mr-2\"></div>\n                Upload: 1-2 min\n              </div>\n              <div className=\"flex items-center\">\n                <div className=\"w-3 h-3 bg-weprint-magenta rounded-full mr-2\"></div>\n                Configuração: 2-3 min\n              </div>\n              <div className=\"flex items-center\">\n                <div className=\"w-3 h-3 bg-weprint-yellow rounded-full mr-2\"></div>\n                Pagamento: 1-2 min\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* CTA */}\n        <div className=\"text-center mt-12\">\n          <button \n            onClick={() => window.location.href = '/upload'}\n            className=\"bg-weprint-gradient text-white font-bold text-lg px-12 py-4 rounded-full shadow-2xl hover:shadow-3xl transform hover:scale-105 transition-all duration-300\"\n          >\n            Começar Agora →\n          </button>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default HowItWorksSection;\n"], "mappings": "sIAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAU1B,KAAM,CAAAC,IAAyB,CAAGC,IAAA,EAAiD,IAAhD,CAAEC,MAAM,CAAEC,KAAK,CAAEC,WAAW,CAAEC,IAAI,CAAEC,KAAM,CAAC,CAAAL,IAAA,CAC5E,mBACEF,KAAA,QAAKQ,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eAEzCX,IAAA,QAAKU,SAAS,cAAAE,MAAA,CAAeH,KAAK,gKAA+J,CAAAE,QAAA,CAC9LN,MAAM,CACJ,CAAC,cAGNL,IAAA,QAAKU,SAAS,CAAC,iDAAiD,CAAAC,QAAA,CAC7DH,IAAI,CACF,CAAC,cAGNR,IAAA,OAAIU,SAAS,CAAC,2CAA2C,CAAAC,QAAA,CAAEL,KAAK,CAAK,CAAC,cACtEN,IAAA,MAAGU,SAAS,CAAC,+BAA+B,CAAAC,QAAA,CAAEJ,WAAW,CAAI,CAAC,EAC3D,CAAC,CAEV,CAAC,CAED,KAAM,CAAAM,iBAA2B,CAAGA,CAAA,GAAM,CACxC,KAAM,CAAAC,KAAK,CAAG,CACZ,CACET,MAAM,CAAE,GAAG,CACXC,KAAK,CAAE,aAAa,CACpBC,WAAW,CAAE,2HAA2H,CACxIC,IAAI,CAAE,IAAI,CACVC,KAAK,CAAE,iBACT,CAAC,CACD,CACEJ,MAAM,CAAE,GAAG,CACXC,KAAK,CAAE,WAAW,CAClBC,WAAW,CAAE,kHAAkH,CAC/HC,IAAI,CAAE,IAAI,CACVC,KAAK,CAAE,oBACT,CAAC,CACD,CACEJ,MAAM,CAAE,GAAG,CACXC,KAAK,CAAE,OAAO,CACdC,WAAW,CAAE,gGAAgG,CAC7GC,IAAI,CAAE,IAAI,CACVC,KAAK,CAAE,mBACT,CAAC,CACD,CACEJ,MAAM,CAAE,GAAG,CACXC,KAAK,CAAE,QAAQ,CACfC,WAAW,CAAE,kHAAkH,CAC/HC,IAAI,CAAE,IAAI,CACVC,KAAK,CAAE,kBACT,CAAC,CACF,CAED,mBACET,IAAA,YAASU,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cACjCT,KAAA,QAAKQ,SAAS,CAAC,gCAAgC,CAAAC,QAAA,eAE7CT,KAAA,QAAKQ,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCT,KAAA,OAAIQ,SAAS,CAAC,yDAAyD,CAAAC,QAAA,EAAC,OACjE,cAAAX,IAAA,SAAMU,SAAS,CAAC,mBAAmB,CAAAC,QAAA,CAAC,UAAQ,CAAM,CAAC,EACtD,CAAC,cACLX,IAAA,MAAGU,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,yJAGvD,CAAG,CAAC,cACJX,IAAA,QAAKU,SAAS,CAAC,wDAAwD,CAAM,CAAC,EAC3E,CAAC,cAGNR,KAAA,QAAKQ,SAAS,CAAC,gEAAgE,CAAAC,QAAA,eAE7EX,IAAA,QAAKU,SAAS,CAAC,gKAAgK,CAAM,CAAC,CAErLI,KAAK,CAACC,GAAG,CAAC,CAACC,IAAI,CAAEC,KAAK,gBACrBjB,IAAA,CAACG,IAAI,CAAAe,aAAA,IAAiBF,IAAI,EAAfC,KAAkB,CAC9B,CAAC,EACC,CAAC,cAGNjB,IAAA,QAAKU,SAAS,CAAC,mBAAmB,CAAAC,QAAA,cAChCT,KAAA,QAAKQ,SAAS,CAAC,8DAA8D,CAAAC,QAAA,eAC3EX,IAAA,OAAIU,SAAS,CAAC,4CAA4C,CAAAC,QAAA,CAAC,wCAE3D,CAAI,CAAC,cACLX,IAAA,MAAGU,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,0IAGlC,CAAG,CAAC,cACJT,KAAA,QAAKQ,SAAS,CAAC,iDAAiD,CAAAC,QAAA,eAC9DT,KAAA,QAAKQ,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCX,IAAA,QAAKU,SAAS,CAAC,2CAA2C,CAAM,CAAC,kBAEnE,EAAK,CAAC,cACNR,KAAA,QAAKQ,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCX,IAAA,QAAKU,SAAS,CAAC,8CAA8C,CAAM,CAAC,8BAEtE,EAAK,CAAC,cACNR,KAAA,QAAKQ,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCX,IAAA,QAAKU,SAAS,CAAC,6CAA6C,CAAM,CAAC,qBAErE,EAAK,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,cAGNV,IAAA,QAAKU,SAAS,CAAC,mBAAmB,CAAAC,QAAA,cAChCX,IAAA,WACEmB,OAAO,CAAEA,CAAA,GAAMC,MAAM,CAACC,QAAQ,CAACC,IAAI,CAAG,SAAU,CAChDZ,SAAS,CAAC,4JAA4J,CAAAC,QAAA,CACvK,yBAED,CAAQ,CAAC,CACN,CAAC,EACH,CAAC,CACC,CAAC,CAEd,CAAC,CAED,cAAe,CAAAE,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}