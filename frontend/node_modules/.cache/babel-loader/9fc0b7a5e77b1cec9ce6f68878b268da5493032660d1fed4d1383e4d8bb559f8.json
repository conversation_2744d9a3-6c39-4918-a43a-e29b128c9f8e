{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/UploadPage.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UploadPage = () => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: \"flex flex-col items-center justify-center min-h-screen\",\n  children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n    className: \"text-2xl font-semibold mb-4\",\n    children: \"Upload de Ficheiro\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n    type: \"file\",\n    className: \"mb-4\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n    className: \"px-4 py-2 bg-blue-600 text-white rounded\",\n    children: \"Avan\\xE7ar\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 7,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 4,\n  columnNumber: 3\n}, this);\n_c = UploadPage;\nexport default UploadPage;\nvar _c;\n$RefreshReg$(_c, \"UploadPage\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "UploadPage", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/UploadPage.tsx"], "sourcesContent": ["import React from 'react';\n\nconst UploadPage: React.FC = () => (\n  <div className=\"flex flex-col items-center justify-center min-h-screen\">\n    <h2 className=\"text-2xl font-semibold mb-4\">Upload de Ficheiro</h2>\n    <input type=\"file\" className=\"mb-4\" />\n    <button className=\"px-4 py-2 bg-blue-600 text-white rounded\">Avançar</button>\n  </div>\n);\n\nexport default UploadPage;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,UAAoB,GAAGA,CAAA,kBAC3BD,OAAA;EAAKE,SAAS,EAAC,wDAAwD;EAAAC,QAAA,gBACrEH,OAAA;IAAIE,SAAS,EAAC,6BAA6B;IAAAC,QAAA,EAAC;EAAkB;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAI,CAAC,eACnEP,OAAA;IAAOQ,IAAI,EAAC,MAAM;IAACN,SAAS,EAAC;EAAM;IAAAE,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eACtCP,OAAA;IAAQE,SAAS,EAAC,0CAA0C;IAAAC,QAAA,EAAC;EAAO;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAQ,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAC1E,CACN;AAACE,EAAA,GANIR,UAAoB;AAQ1B,eAAeA,UAAU;AAAC,IAAAQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}