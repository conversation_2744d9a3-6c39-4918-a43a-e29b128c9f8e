{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/App.tsx\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport LandingPage from './pages/LandingPage';\nimport UploadPage from './pages/UploadPage';\nimport PreviewPage from './pages/PreviewPage';\nimport OptionsForm from './pages/OptionsForm';\nimport OrderSummary from './pages/OrderSummary';\nimport CheckoutPage from './pages/CheckoutPage';\nimport ClientPanel from './pages/ClientPanel';\nimport AdminPanel from './pages/AdminPanel';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst App = () => /*#__PURE__*/_jsxDEV(Router, {\n  children: /*#__PURE__*/_jsxDEV(Routes, {\n    children: [/*#__PURE__*/_jsxDEV(Route, {\n      path: \"/\",\n      element: /*#__PURE__*/_jsxDEV(LandingPage, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 15,\n        columnNumber: 32\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/upload\",\n      element: /*#__PURE__*/_jsxDEV(UploadPage, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 38\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/preview\",\n      element: /*#__PURE__*/_jsxDEV(PreviewPage, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 39\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/options\",\n      element: /*#__PURE__*/_jsxDEV(OptionsForm, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 39\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/summary\",\n      element: /*#__PURE__*/_jsxDEV(OrderSummary, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 39\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/checkout\",\n      element: /*#__PURE__*/_jsxDEV(CheckoutPage, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 40\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/painel\",\n      element: /*#__PURE__*/_jsxDEV(ClientPanel, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 38\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/admin\",\n      element: /*#__PURE__*/_jsxDEV(AdminPanel, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 37\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 14,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 13,\n  columnNumber: 3\n}, this);\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "LandingPage", "UploadPage", "PreviewPage", "OptionsForm", "OrderSummary", "CheckoutPage", "ClientPanel", "AdminPanel", "jsxDEV", "_jsxDEV", "App", "children", "path", "element", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport LandingPage from './pages/LandingPage';\nimport UploadPage from './pages/UploadPage';\nimport PreviewPage from './pages/PreviewPage';\nimport OptionsForm from './pages/OptionsForm';\nimport OrderSummary from './pages/OrderSummary';\nimport CheckoutPage from './pages/CheckoutPage';\nimport ClientPanel from './pages/ClientPanel';\nimport AdminPanel from './pages/AdminPanel';\n\nconst App: React.FC = () => (\n  <Router>\n    <Routes>\n      <Route path=\"/\" element={<LandingPage />} />\n      <Route path=\"/upload\" element={<UploadPage />} />\n      <Route path=\"/preview\" element={<PreviewPage />} />\n      <Route path=\"/options\" element={<OptionsForm />} />\n      <Route path=\"/summary\" element={<OrderSummary />} />\n      <Route path=\"/checkout\" element={<CheckoutPage />} />\n      <Route path=\"/painel\" element={<ClientPanel />} />\n      <Route path=\"/admin\" element={<AdminPanel />} />\n    </Routes>\n  </Router>\n);\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AACzE,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,UAAU,MAAM,oBAAoB;AAC3C,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,UAAU,MAAM,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5C,MAAMC,GAAa,GAAGA,CAAA,kBACpBD,OAAA,CAACZ,MAAM;EAAAc,QAAA,eACLF,OAAA,CAACX,MAAM;IAAAa,QAAA,gBACLF,OAAA,CAACV,KAAK;MAACa,IAAI,EAAC,GAAG;MAACC,OAAO,eAAEJ,OAAA,CAACT,WAAW;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC5CR,OAAA,CAACV,KAAK;MAACa,IAAI,EAAC,SAAS;MAACC,OAAO,eAAEJ,OAAA,CAACR,UAAU;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACjDR,OAAA,CAACV,KAAK;MAACa,IAAI,EAAC,UAAU;MAACC,OAAO,eAAEJ,OAAA,CAACP,WAAW;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACnDR,OAAA,CAACV,KAAK;MAACa,IAAI,EAAC,UAAU;MAACC,OAAO,eAAEJ,OAAA,CAACN,WAAW;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACnDR,OAAA,CAACV,KAAK;MAACa,IAAI,EAAC,UAAU;MAACC,OAAO,eAAEJ,OAAA,CAACL,YAAY;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACpDR,OAAA,CAACV,KAAK;MAACa,IAAI,EAAC,WAAW;MAACC,OAAO,eAAEJ,OAAA,CAACJ,YAAY;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACrDR,OAAA,CAACV,KAAK;MAACa,IAAI,EAAC,SAAS;MAACC,OAAO,eAAEJ,OAAA,CAACH,WAAW;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAClDR,OAAA,CAACV,KAAK;MAACa,IAAI,EAAC,QAAQ;MAACC,OAAO,eAAEJ,OAAA,CAACF,UAAU;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC1C;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACH,CACT;AAACC,EAAA,GAbIR,GAAa;AAenB,eAAeA,GAAG;AAAC,IAAAQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}