{"ast": null, "code": "import React from'react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const PreviewPage=()=>/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col items-center justify-center min-h-screen\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-semibold mb-4\",children:\"Preview do Ficheiro\"}),/*#__PURE__*/_jsx(\"div\",{className:\"w-64 h-80 bg-gray-200 flex items-center justify-center mb-4\",children:\"[Preview]\"}),/*#__PURE__*/_jsx(\"button\",{className:\"px-4 py-2 bg-blue-600 text-white rounded\",children:\"Escolher Op\\xE7\\xF5es\"})]});export default PreviewPage;", "map": {"version": 3, "names": ["React", "jsx", "_jsx", "jsxs", "_jsxs", "PreviewPage", "className", "children"], "sources": ["/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/PreviewPage.tsx"], "sourcesContent": ["import React from 'react';\n\nconst PreviewPage: React.FC = () => (\n  <div className=\"flex flex-col items-center justify-center min-h-screen\">\n    <h2 className=\"text-2xl font-semibold mb-4\">Preview do Ficheiro</h2>\n    <div className=\"w-64 h-80 bg-gray-200 flex items-center justify-center mb-4\">[Preview]</div>\n    <button className=\"px-4 py-2 bg-blue-600 text-white rounded\">Escolher Opções</button>\n  </div>\n);\n\nexport default PreviewPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE1B,KAAM,CAAAC,WAAqB,CAAGA,CAAA,gBAC5BD,KAAA,QAAKE,SAAS,CAAC,wDAAwD,CAAAC,QAAA,eACrEL,IAAA,OAAII,SAAS,CAAC,6BAA6B,CAAAC,QAAA,CAAC,qBAAmB,CAAI,CAAC,cACpEL,IAAA,QAAKI,SAAS,CAAC,6DAA6D,CAAAC,QAAA,CAAC,WAAS,CAAK,CAAC,cAC5FL,IAAA,WAAQI,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,uBAAe,CAAQ,CAAC,EAClF,CACN,CAED,cAAe,CAAAF,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}