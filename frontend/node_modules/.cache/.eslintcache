[{"/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/index.tsx": "1", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/reportWebVitals.ts": "2", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/serviceWorkerRegistration.ts": "3", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/App.tsx": "4", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/LandingPage.tsx": "5", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/CheckoutPage.tsx": "6", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/ClientPanel.tsx": "7", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/OrderSummary.tsx": "8", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/AdminPanel.tsx": "9", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/OptionsForm.tsx": "10", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/PreviewPage.tsx": "11", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/UploadPage.tsx": "12", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/index.ts": "13", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/Layout.tsx": "14", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/Button.tsx": "15", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/services/multicaixa.ts": "16", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/services/api.ts": "17", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/PaymentQRCode.tsx": "18", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/PaymentFlow.tsx": "19", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/PaymentStatus.tsx": "20", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/hooks/useMulticaixa.ts": "21", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/services/adminApi.ts": "22"}, {"size": 870, "mtime": 1751551408295, "results": "23", "hashOfConfig": "24"}, {"size": 425, "mtime": 1751551611759, "results": "25", "hashOfConfig": "24"}, {"size": 5262, "mtime": 1751551601942, "results": "26", "hashOfConfig": "24"}, {"size": 1037, "mtime": 1751550099353, "results": "27", "hashOfConfig": "24"}, {"size": 2692, "mtime": 1751552439560, "results": "28", "hashOfConfig": "24"}, {"size": 11137, "mtime": 1751573473031, "results": "29", "hashOfConfig": "24"}, {"size": 15846, "mtime": 1751572777003, "results": "30", "hashOfConfig": "24"}, {"size": 10707, "mtime": 1751568018056, "results": "31", "hashOfConfig": "24"}, {"size": 21457, "mtime": 1751573026503, "results": "32", "hashOfConfig": "24"}, {"size": 12540, "mtime": 1751567964968, "results": "33", "hashOfConfig": "24"}, {"size": 444, "mtime": 1751550099353, "results": "34", "hashOfConfig": "24"}, {"size": 10551, "mtime": 1751569206926, "results": "35", "hashOfConfig": "24"}, {"size": 327, "mtime": 1751566338263, "results": "36", "hashOfConfig": "24"}, {"size": 874, "mtime": 1751552316407, "results": "37", "hashOfConfig": "24"}, {"size": 1392, "mtime": 1751552327906, "results": "38", "hashOfConfig": "24"}, {"size": 5718, "mtime": 1751566192165, "results": "39", "hashOfConfig": "24"}, {"size": 2194, "mtime": 1751552366730, "results": "40", "hashOfConfig": "24"}, {"size": 5662, "mtime": 1751566280630, "results": "41", "hashOfConfig": "24"}, {"size": 9208, "mtime": 1751566321732, "results": "42", "hashOfConfig": "24"}, {"size": 4250, "mtime": 1751566250346, "results": "43", "hashOfConfig": "24"}, {"size": 5573, "mtime": 1751566216600, "results": "44", "hashOfConfig": "24"}, {"size": 4544, "mtime": 1751572805443, "results": "45", "hashOfConfig": "24"}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1wxc2lr", {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/index.tsx", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/reportWebVitals.ts", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/serviceWorkerRegistration.ts", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/App.tsx", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/LandingPage.tsx", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/CheckoutPage.tsx", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/ClientPanel.tsx", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/OrderSummary.tsx", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/AdminPanel.tsx", ["112"], ["113"], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/OptionsForm.tsx", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/PreviewPage.tsx", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/UploadPage.tsx", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/index.ts", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/Layout.tsx", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/Button.tsx", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/services/multicaixa.ts", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/services/api.ts", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/PaymentQRCode.tsx", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/PaymentFlow.tsx", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/PaymentStatus.tsx", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/hooks/useMulticaixa.ts", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/services/adminApi.ts", [], [], {"ruleId": "114", "severity": 1, "message": "115", "line": 109, "column": 9, "nodeType": "116", "messageId": "117", "endLine": 109, "endColumn": 23}, {"ruleId": "118", "severity": 2, "message": "119", "line": 136, "column": 10, "nodeType": "116", "messageId": "120", "endLine": 136, "endColumn": 17, "suppressions": "121"}, "@typescript-eslint/no-unused-vars", "'calculateStats' is assigned a value but never used.", "Identifier", "unusedVar", "no-restricted-globals", "Unexpected use of 'confirm'.", "defaultMessage", ["122"], {"kind": "123", "justification": "124"}, "directive", ""]