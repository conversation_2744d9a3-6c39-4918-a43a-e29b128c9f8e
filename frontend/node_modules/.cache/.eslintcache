[{"/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/index.tsx": "1", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/reportWebVitals.ts": "2", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/serviceWorkerRegistration.ts": "3", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/App.tsx": "4", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/LandingPage.tsx": "5", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/CheckoutPage.tsx": "6", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/ClientPanel.tsx": "7", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/OrderSummary.tsx": "8", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/AdminPanel.tsx": "9", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/OptionsForm.tsx": "10", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/PreviewPage.tsx": "11", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/UploadPage.tsx": "12", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/index.ts": "13", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/Layout.tsx": "14", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/Button.tsx": "15", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/services/multicaixa.ts": "16", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/services/api.ts": "17", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/PaymentQRCode.tsx": "18", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/PaymentFlow.tsx": "19", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/PaymentStatus.tsx": "20", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/hooks/useMulticaixa.ts": "21"}, {"size": 870, "mtime": 1751551408295, "results": "22", "hashOfConfig": "23"}, {"size": 425, "mtime": 1751551611759, "results": "24", "hashOfConfig": "23"}, {"size": 5262, "mtime": 1751551601942, "results": "25", "hashOfConfig": "23"}, {"size": 1037, "mtime": 1751550099353, "results": "26", "hashOfConfig": "23"}, {"size": 2692, "mtime": 1751552439560, "results": "27", "hashOfConfig": "23"}, {"size": 10662, "mtime": 1751572736688, "results": "28", "hashOfConfig": "23"}, {"size": 15846, "mtime": 1751572777003, "results": "29", "hashOfConfig": "23"}, {"size": 10707, "mtime": 1751568018056, "results": "30", "hashOfConfig": "23"}, {"size": 16805, "mtime": 1751572830009, "results": "31", "hashOfConfig": "23"}, {"size": 12540, "mtime": 1751567964968, "results": "32", "hashOfConfig": "23"}, {"size": 444, "mtime": 1751550099353, "results": "33", "hashOfConfig": "23"}, {"size": 10551, "mtime": 1751569206926, "results": "34", "hashOfConfig": "23"}, {"size": 327, "mtime": 1751566338263, "results": "35", "hashOfConfig": "23"}, {"size": 874, "mtime": 1751552316407, "results": "36", "hashOfConfig": "23"}, {"size": 1392, "mtime": 1751552327906, "results": "37", "hashOfConfig": "23"}, {"size": 5718, "mtime": 1751566192165, "results": "38", "hashOfConfig": "23"}, {"size": 2194, "mtime": 1751552366730, "results": "39", "hashOfConfig": "23"}, {"size": 5662, "mtime": 1751566280630, "results": "40", "hashOfConfig": "23"}, {"size": 9208, "mtime": 1751566321732, "results": "41", "hashOfConfig": "23"}, {"size": 4250, "mtime": 1751566250346, "results": "42", "hashOfConfig": "23"}, {"size": 5573, "mtime": 1751566216600, "results": "43", "hashOfConfig": "23"}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1wxc2lr", {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/index.tsx", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/reportWebVitals.ts", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/serviceWorkerRegistration.ts", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/App.tsx", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/LandingPage.tsx", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/CheckoutPage.tsx", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/ClientPanel.tsx", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/OrderSummary.tsx", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/AdminPanel.tsx", ["107", "108", "109", "110", "111", "112", "113"], ["114"], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/OptionsForm.tsx", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/PreviewPage.tsx", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/UploadPage.tsx", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/index.ts", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/Layout.tsx", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/Button.tsx", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/services/multicaixa.ts", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/services/api.ts", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/PaymentQRCode.tsx", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/PaymentFlow.tsx", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/PaymentStatus.tsx", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/hooks/useMulticaixa.ts", [], [], {"ruleId": "115", "severity": 1, "message": "116", "line": 5, "column": 10, "nodeType": "117", "messageId": "118", "endLine": 5, "endColumn": 25}, {"ruleId": "115", "severity": 1, "message": "119", "line": 28, "column": 10, "nodeType": "117", "messageId": "118", "endLine": 28, "endColumn": 25}, {"ruleId": "115", "severity": 1, "message": "120", "line": 28, "column": 27, "nodeType": "117", "messageId": "118", "endLine": 28, "endColumn": 45}, {"ruleId": "115", "severity": 1, "message": "121", "line": 29, "column": 10, "nodeType": "117", "messageId": "118", "endLine": 29, "endColumn": 19}, {"ruleId": "115", "severity": 1, "message": "122", "line": 29, "column": 21, "nodeType": "117", "messageId": "118", "endLine": 29, "endColumn": 33}, {"ruleId": "115", "severity": 1, "message": "123", "line": 30, "column": 10, "nodeType": "117", "messageId": "118", "endLine": 30, "endColumn": 19}, {"ruleId": "115", "severity": 1, "message": "124", "line": 30, "column": 21, "nodeType": "117", "messageId": "118", "endLine": 30, "endColumn": 33}, {"ruleId": "125", "severity": 2, "message": "126", "line": 85, "column": 10, "nodeType": "117", "messageId": "127", "endLine": 85, "endColumn": 17, "suppressions": "128"}, "@typescript-eslint/no-unused-vars", "'adminApiService' is defined but never used.", "Identifier", "unusedVar", "'isAuthenticated' is assigned a value but never used.", "'setIsAuthenticated' is assigned a value but never used.", "'adminUser' is assigned a value but never used.", "'setAdminUser' is assigned a value but never used.", "'loginForm' is assigned a value but never used.", "'setLoginForm' is assigned a value but never used.", "no-restricted-globals", "Unexpected use of 'confirm'.", "defaultMessage", ["129"], {"kind": "130", "justification": "131"}, "directive", ""]