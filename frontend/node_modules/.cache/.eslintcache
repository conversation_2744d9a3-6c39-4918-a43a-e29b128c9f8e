[{"/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/index.tsx": "1", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/reportWebVitals.ts": "2", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/serviceWorkerRegistration.ts": "3", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/App.tsx": "4", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/LandingPage.tsx": "5", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/CheckoutPage.tsx": "6", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/ClientPanel.tsx": "7", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/OrderSummary.tsx": "8", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/AdminPanel.tsx": "9", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/OptionsForm.tsx": "10", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/PreviewPage.tsx": "11", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/UploadPage.tsx": "12", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/index.ts": "13", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/Layout.tsx": "14", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/Button.tsx": "15", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/services/multicaixa.ts": "16", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/services/api.ts": "17", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/PaymentQRCode.tsx": "18", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/PaymentFlow.tsx": "19", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/PaymentStatus.tsx": "20", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/hooks/useMulticaixa.ts": "21", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/services/adminApi.ts": "22", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/landing/ServicesSection.tsx": "23", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/landing/HowItWorksSection.tsx": "24", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/landing/BenefitsSection.tsx": "25", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/landing/HeroSection.tsx": "26", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/landing/CTASection.tsx": "27", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/landing/Footer.tsx": "28"}, {"size": 870, "mtime": 1751551408295, "results": "29", "hashOfConfig": "30"}, {"size": 425, "mtime": 1751551611759, "results": "31", "hashOfConfig": "30"}, {"size": 5262, "mtime": 1751551601942, "results": "32", "hashOfConfig": "30"}, {"size": 1037, "mtime": 1751550099353, "results": "33", "hashOfConfig": "30"}, {"size": 6206, "mtime": 1751763967073, "results": "34", "hashOfConfig": "30"}, {"size": 12070, "mtime": 1751759308205, "results": "35", "hashOfConfig": "30"}, {"size": 15984, "mtime": 1751759284848, "results": "36", "hashOfConfig": "30"}, {"size": 10707, "mtime": 1751568018056, "results": "37", "hashOfConfig": "30"}, {"size": 21537, "mtime": 1751576130981, "results": "38", "hashOfConfig": "30"}, {"size": 12540, "mtime": 1751567964968, "results": "39", "hashOfConfig": "30"}, {"size": 444, "mtime": 1751550099353, "results": "40", "hashOfConfig": "30"}, {"size": 10551, "mtime": 1751569206926, "results": "41", "hashOfConfig": "30"}, {"size": 327, "mtime": 1751566338263, "results": "42", "hashOfConfig": "30"}, {"size": 874, "mtime": 1751552316407, "results": "43", "hashOfConfig": "30"}, {"size": 1392, "mtime": 1751552327906, "results": "44", "hashOfConfig": "30"}, {"size": 5718, "mtime": 1751566192165, "results": "45", "hashOfConfig": "30"}, {"size": 2194, "mtime": 1751552366730, "results": "46", "hashOfConfig": "30"}, {"size": 5662, "mtime": 1751566280630, "results": "47", "hashOfConfig": "30"}, {"size": 9208, "mtime": 1751566321732, "results": "48", "hashOfConfig": "30"}, {"size": 4250, "mtime": 1751566250346, "results": "49", "hashOfConfig": "30"}, {"size": 5573, "mtime": 1751566216600, "results": "50", "hashOfConfig": "30"}, {"size": 4544, "mtime": 1751572805443, "results": "51", "hashOfConfig": "30"}, {"size": 4768, "mtime": 1751763785262, "results": "52", "hashOfConfig": "30"}, {"size": 4792, "mtime": 1751763812329, "results": "53", "hashOfConfig": "30"}, {"size": 6528, "mtime": 1751763843118, "results": "54", "hashOfConfig": "30"}, {"size": 5005, "mtime": 1751763688031, "results": "55", "hashOfConfig": "30"}, {"size": 3781, "mtime": 1751763864696, "results": "56", "hashOfConfig": "30"}, {"size": 7407, "mtime": 1751763896359, "results": "57", "hashOfConfig": "30"}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1wxc2lr", {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/index.tsx", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/reportWebVitals.ts", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/serviceWorkerRegistration.ts", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/App.tsx", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/LandingPage.tsx", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/CheckoutPage.tsx", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/ClientPanel.tsx", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/OrderSummary.tsx", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/AdminPanel.tsx", ["142"], ["143"], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/OptionsForm.tsx", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/PreviewPage.tsx", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/UploadPage.tsx", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/index.ts", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/Layout.tsx", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/Button.tsx", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/services/multicaixa.ts", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/services/api.ts", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/PaymentQRCode.tsx", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/PaymentFlow.tsx", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/PaymentStatus.tsx", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/hooks/useMulticaixa.ts", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/services/adminApi.ts", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/landing/ServicesSection.tsx", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/landing/HowItWorksSection.tsx", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/landing/BenefitsSection.tsx", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/landing/HeroSection.tsx", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/landing/CTASection.tsx", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/landing/Footer.tsx", ["144", "145", "146", "147", "148", "149", "150", "151", "152", "153", "154", "155", "156", "157", "158", "159", "160"], [], {"ruleId": "161", "severity": 1, "message": "162", "line": 113, "column": 9, "nodeType": "163", "messageId": "164", "endLine": 113, "endColumn": 23}, {"ruleId": "165", "severity": 2, "message": "166", "line": 140, "column": 10, "nodeType": "163", "messageId": "167", "endLine": 140, "endColumn": 17, "suppressions": "168"}, {"ruleId": "169", "severity": 1, "message": "170", "line": 26, "column": 15, "nodeType": "171", "endLine": 26, "endColumn": 161}, {"ruleId": "169", "severity": 1, "message": "170", "line": 29, "column": 15, "nodeType": "171", "endLine": 29, "endColumn": 164}, {"ruleId": "169", "severity": 1, "message": "170", "line": 32, "column": 15, "nodeType": "171", "endLine": 32, "endColumn": 163}, {"ruleId": "169", "severity": 1, "message": "170", "line": 35, "column": 15, "nodeType": "171", "endLine": 35, "endColumn": 154}, {"ruleId": "169", "severity": 1, "message": "170", "line": 45, "column": 19, "nodeType": "171", "endLine": 45, "endColumn": 98}, {"ruleId": "169", "severity": 1, "message": "170", "line": 46, "column": 19, "nodeType": "171", "endLine": 46, "endColumn": 98}, {"ruleId": "169", "severity": 1, "message": "170", "line": 47, "column": 19, "nodeType": "171", "endLine": 47, "endColumn": 98}, {"ruleId": "169", "severity": 1, "message": "170", "line": 48, "column": 19, "nodeType": "171", "endLine": 48, "endColumn": 98}, {"ruleId": "169", "severity": 1, "message": "170", "line": 49, "column": 19, "nodeType": "171", "endLine": 49, "endColumn": 98}, {"ruleId": "169", "severity": 1, "message": "170", "line": 57, "column": 19, "nodeType": "171", "endLine": 57, "endColumn": 101}, {"ruleId": "169", "severity": 1, "message": "170", "line": 58, "column": 19, "nodeType": "171", "endLine": 58, "endColumn": 101}, {"ruleId": "169", "severity": 1, "message": "170", "line": 59, "column": 19, "nodeType": "171", "endLine": 59, "endColumn": 101}, {"ruleId": "169", "severity": 1, "message": "170", "line": 60, "column": 19, "nodeType": "171", "endLine": 60, "endColumn": 101}, {"ruleId": "169", "severity": 1, "message": "170", "line": 61, "column": 19, "nodeType": "171", "endLine": 61, "endColumn": 101}, {"ruleId": "169", "severity": 1, "message": "170", "line": 130, "column": 15, "nodeType": "171", "endLine": 130, "endColumn": 94}, {"ruleId": "169", "severity": 1, "message": "170", "line": 131, "column": 15, "nodeType": "171", "endLine": 131, "endColumn": 94}, {"ruleId": "169", "severity": 1, "message": "170", "line": 132, "column": 15, "nodeType": "171", "endLine": 132, "endColumn": 94}, "@typescript-eslint/no-unused-vars", "'calculateStats' is assigned a value but never used.", "Identifier", "unusedVar", "no-restricted-globals", "Unexpected use of 'confirm'.", "defaultMessage", ["172"], "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", {"kind": "173", "justification": "174"}, "directive", ""]