[{"/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/index.tsx": "1", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/reportWebVitals.ts": "2", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/serviceWorkerRegistration.ts": "3", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/App.tsx": "4", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/LandingPage.tsx": "5", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/CheckoutPage.tsx": "6", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/ClientPanel.tsx": "7", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/OrderSummary.tsx": "8", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/AdminPanel.tsx": "9", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/OptionsForm.tsx": "10", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/PreviewPage.tsx": "11", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/UploadPage.tsx": "12", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/index.ts": "13", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/Layout.tsx": "14", "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/Button.tsx": "15"}, {"size": 870, "mtime": 1751551408295, "results": "16", "hashOfConfig": "17"}, {"size": 425, "mtime": 1751551611759, "results": "18", "hashOfConfig": "17"}, {"size": 5262, "mtime": 1751551601942, "results": "19", "hashOfConfig": "17"}, {"size": 1037, "mtime": 1751550099353, "results": "20", "hashOfConfig": "17"}, {"size": 2692, "mtime": 1751552439560, "results": "21", "hashOfConfig": "17"}, {"size": 887, "mtime": 1751550099353, "results": "22", "hashOfConfig": "17"}, {"size": 598, "mtime": 1751550099362, "results": "23", "hashOfConfig": "17"}, {"size": 635, "mtime": 1751550099353, "results": "24", "hashOfConfig": "17"}, {"size": 671, "mtime": 1751550099390, "results": "25", "hashOfConfig": "17"}, {"size": 926, "mtime": 1751550099353, "results": "26", "hashOfConfig": "17"}, {"size": 444, "mtime": 1751550099353, "results": "27", "hashOfConfig": "17"}, {"size": 378, "mtime": 1751550099353, "results": "28", "hashOfConfig": "17"}, {"size": 151, "mtime": 1751552333466, "results": "29", "hashOfConfig": "17"}, {"size": 874, "mtime": 1751552316407, "results": "30", "hashOfConfig": "17"}, {"size": 1392, "mtime": 1751552327906, "results": "31", "hashOfConfig": "17"}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1wxc2lr", {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/index.tsx", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/reportWebVitals.ts", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/serviceWorkerRegistration.ts", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/App.tsx", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/LandingPage.tsx", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/CheckoutPage.tsx", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/ClientPanel.tsx", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/OrderSummary.tsx", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/AdminPanel.tsx", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/OptionsForm.tsx", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/PreviewPage.tsx", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/pages/UploadPage.tsx", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/index.ts", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/Layout.tsx", [], [], "/Users/<USER>/Documents/GitHub/WePrint-AI/frontend/src/components/Button.tsx", [], []]