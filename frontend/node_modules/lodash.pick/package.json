{"name": "lodash.pick", "version": "3.1.0", "description": "The modern build of lodash’s `_.pick` as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": "lodash, lodash-modularized, stdlib, util", "author": "<PERSON><PERSON><PERSON> <<EMAIL>> (http://allyoucanleet.com/)", "contributors": ["<PERSON><PERSON><PERSON> <<EMAIL>> (http://allyoucanleet.com/)", "<PERSON> <<EMAIL>> (https://d10.github.io/)", "<PERSON> <<EMAIL>> (http://www.iceddev.com/)", "<PERSON> <<EMAIL>> (http://kitcambridge.be/)", "<PERSON> <<EMAIL>> (https://mathiasbynens.be/)"], "repository": "lodash/lodash", "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "dependencies": {"lodash._baseflatten": "^3.0.0", "lodash._bindcallback": "^3.0.0", "lodash._pickbyarray": "^3.0.0", "lodash._pickbycallback": "^3.0.0", "lodash.restparam": "^3.0.0"}}