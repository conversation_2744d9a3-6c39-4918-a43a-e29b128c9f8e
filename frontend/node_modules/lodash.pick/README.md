# lodash.pick v3.1.0

The [modern build](https://github.com/lodash/lodash/wiki/Build-Differences) of [lodash’s](https://lodash.com/) `_.pick` exported as a [Node.js](http://nodejs.org/)/[io.js](https://iojs.org/) module.

## Installation

Using npm:

```bash
$ {sudo -H} npm i -g npm
$ npm i --save lodash.pick
```

In Node.js/io.js:

```js
var pick = require('lodash.pick');
```

See the [documentation](https://lodash.com/docs#pick) or [package source](https://github.com/lodash/lodash/blob/3.1.0-npm-packages/lodash.pick) for more details.
