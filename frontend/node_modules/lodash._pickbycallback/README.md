# lodash._pickbycallback v3.0.0

The [modern build](https://github.com/lodash/lodash/wiki/Build-Differences) of [lodash’s](https://lodash.com/) internal `pickByCallback` exported as a [Node.js](http://nodejs.org/)/[io.js](https://iojs.org/) module.

## Installation

Using npm:

```bash
$ {sudo -H} npm i -g npm
$ npm i --save lodash._pickbycallback
```

In Node.js/io.js:

```js
var pickByCallback = require('lodash._pickbycallback');
```

See the [package source](https://github.com/lodash/lodash/blob/3.0.0-npm-packages/lodash._pickbycallback) for more details.
