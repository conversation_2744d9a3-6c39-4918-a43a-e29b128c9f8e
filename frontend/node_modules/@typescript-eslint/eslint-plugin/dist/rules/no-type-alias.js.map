{"version": 3, "file": "no-type-alias.js", "sourceRoot": "", "sources": ["../../src/rules/no-type-alias.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA2E;AAE3E,8CAAgC;AAQhC,MAAM,UAAU,GAAa;IAC3B,QAAQ;IACR,OAAO;IACP,WAAW;IACX,kBAAkB;IAClB,6BAA6B;CAC9B,CAAC;AAwBF,kBAAe,IAAI,CAAC,UAAU,CAAsB;IAClD,IAAI,EAAE,eAAe;IACrB,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EAAE,uBAAuB;YACpC,oCAAoC;YACpC,WAAW,EAAE,KAAK;SACnB;QACD,QAAQ,EAAE;YACR,WAAW,EAAE,iCAAiC;YAC9C,kBAAkB,EAChB,4DAA4D;SAC/D;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,YAAY,EAAE;wBACZ,WAAW,EAAE,kDAAkD;wBAC/D,IAAI,EAAE,UAAU;qBACjB;oBACD,cAAc,EAAE;wBACd,WAAW,EAAE,8CAA8C;wBAC3D,IAAI,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC;qBAC1B;oBACD,qBAAqB,EAAE;wBACrB,WAAW,EAAE,sDAAsD;wBACnE,IAAI,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC;qBAC1B;oBACD,iBAAiB,EAAE;wBACjB,WAAW,EAAE,kDAAkD;wBAC/D,IAAI,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC;qBAC1B;oBACD,aAAa,EAAE;wBACb,WAAW,EACT,0DAA0D;wBAC5D,IAAI,EAAE,UAAU;qBACjB;oBACD,gBAAgB,EAAE;wBAChB,WAAW,EAAE,kDAAkD;wBAC/D,IAAI,EAAE,UAAU;qBACjB;oBACD,eAAe,EAAE;wBACf,WAAW,EAAE,iDAAiD;wBAC9D,IAAI,EAAE,UAAU;qBACjB;oBACD,aAAa,EAAE;wBACb,WAAW,EAAE,mDAAmD;wBAChE,IAAI,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC;qBAC1B;iBACF;gBACD,oBAAoB,EAAE,KAAK;aAC5B;SACF;KACF;IACD,cAAc,EAAE;QACd;YACE,YAAY,EAAE,OAAO;YACrB,cAAc,EAAE,OAAO;YACvB,qBAAqB,EAAE,OAAO;YAC9B,iBAAiB,EAAE,OAAO;YAC1B,aAAa,EAAE,OAAO;YACtB,gBAAgB,EAAE,OAAO;YACzB,eAAe,EAAE,OAAO;YACxB,aAAa,EAAE,OAAO;SACvB;KACF;IACD,MAAM,CACJ,OAAO,EACP,CACE,EACE,YAAY,EACZ,cAAc,EACd,qBAAqB,EACrB,iBAAiB,EACjB,aAAa,EACb,gBAAgB,EAChB,eAAe,EACf,aAAa,GACd,EACF;QAED,MAAM,MAAM,GAAG,CAAC,QAAQ,EAAE,WAAW,EAAE,6BAA6B,CAAC,CAAC;QACtE,MAAM,aAAa,GAAG;YACpB,QAAQ;YACR,kBAAkB;YAClB,6BAA6B;SAC9B,CAAC;QACF,MAAM,YAAY,GAAG;YACnB,WAAW;YACX,kBAAkB;YAClB,6BAA6B;SAC9B,CAAC;QACF,MAAM,UAAU,GAAG,IAAI,GAAG,CAAC;YACzB,sBAAc,CAAC,WAAW;YAC1B,sBAAc,CAAC,YAAY;YAC3B,sBAAc,CAAC,eAAe;YAC9B,sBAAc,CAAC,aAAa;YAC5B,sBAAc,CAAC,WAAW;YAC1B,sBAAc,CAAC,mBAAmB;YAClC,sBAAc,CAAC,qBAAqB;SACrC,CAAC,CAAC;QAEH;;;;;WAKG;QACH,SAAS,sBAAsB,CAC7B,UAAmB,EACnB,eAAuC,EACvC,OAAe;YAEf,OAAO,CACL,CAAC,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC;gBAC/B,CAAC,CAAC,UAAU;oBACV,CAAC,CAAC,eAAe,KAAK,sBAAc,CAAC,WAAW;wBAC9C,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;wBACzB,CAAC,eAAe,KAAK,sBAAc,CAAC,kBAAkB;4BACpD,aAAa,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CACzC,CAAC;QACJ,CAAC;QAED;;;;;;;WAOG;QACH,SAAS,WAAW,CAClB,IAAmB,EACnB,eAAuC,EACvC,MAAe,EACf,IAAY;YAEZ,IAAI,MAAM,EAAE;gBACV,OAAO,OAAO,CAAC,MAAM,CAAC;oBACpB,IAAI;oBACJ,SAAS,EAAE,aAAa;oBACxB,IAAI,EAAE;wBACJ,KAAK,EAAE,IAAI,CAAC,WAAW,EAAE;qBAC1B;iBACF,CAAC,CAAC;aACJ;YAED,OAAO,OAAO,CAAC,MAAM,CAAC;gBACpB,IAAI;gBACJ,SAAS,EAAE,oBAAoB;gBAC/B,IAAI,EAAE;oBACJ,eAAe,EACb,eAAe,KAAK,sBAAc,CAAC,WAAW;wBAC5C,CAAC,CAAC,OAAO;wBACT,CAAC,CAAC,cAAc;oBACpB,QAAQ,EAAE,IAAI;iBACf;aACF,CAAC,CAAC;QACL,CAAC;QAED,MAAM,gBAAgB,GAAG,CAAC,IAAmB,EAAW,EAAE;YACxD,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,WAAW,EAAE;gBACjD,OAAO,IAAI,CAAC;aACb;YACD,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc,EAAE;gBACpD,IACE,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;oBAClD,IAAI,CAAC,IAAI,CAAC,cAAc;oBACxB,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,KAAK,sBAAc,CAAC,WAAW,EAC5D;oBACA,OAAO,IAAI,CAAC;iBACb;aACF;YACD,OAAO,KAAK,CAAC;QACf,CAAC,CAAC;QAEF,MAAM,cAAc,GAAG,CAAC,IAAmB,EAAW,EAAE;YACtD,OAAO,CACL,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe;gBACjD,IAAI,CAAC,IAAI,CAAC,cAAc,KAAK,SAAS,CACvC,CAAC;QACJ,CAAC,CAAC;QAEF,MAAM,cAAc,GAAG,CACrB,WAAmB,EACnB,UAAmB,EACnB,IAAmB,EACnB,KAAa,EACP,EAAE;YACR,IACE,WAAW,KAAK,OAAO;gBACvB,CAAC,sBAAsB,CAAC,UAAU,EAAE,IAAI,CAAC,eAAe,EAAE,WAAW,CAAC,EACtE;gBACA,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,eAAe,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC;aACjE;QACH,CAAC,CAAC;QAEF;;;;;WAKG;QACH,SAAS,mBAAmB,CAC1B,IAAmB,EACnB,UAAU,GAAG,KAAK;YAElB,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc,EAAE;gBACpD,WAAW;gBACX,IAAI,cAAc,KAAK,OAAO,EAAE;oBAC9B,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,eAAe,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC;iBACvE;aACF;iBAAM,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB,EAAE;gBAC9D,mBAAmB;gBACnB,IAAI,qBAAqB,KAAK,OAAO,EAAE;oBACrC,WAAW,CACT,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,eAAe,EACpB,UAAU,EACV,mBAAmB,CACpB,CAAC;iBACH;aACF;iBAAM,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB,EAAE;gBAC9D,IAAI,iBAAiB,KAAK,OAAO,EAAE;oBACjC,WAAW,CACT,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,eAAe,EACpB,UAAU,EACV,cAAc,CACf,CAAC;iBACH;aACF;iBAAM,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,aAAa,EAAE;gBAC1D,sBAAsB;gBACtB,cAAc,CAAC,aAAc,EAAE,UAAU,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC;aAC9D;iBAAM,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,YAAY,EAAE;gBACzD,cAAc;gBACd,cAAc,CAAC,gBAAiB,EAAE,UAAU,EAAE,IAAI,EAAE,cAAc,CAAC,CAAC;aACrE;iBAAM,IAAI,gBAAgB,CAAC,IAAI,CAAC,EAAE;gBACjC,cAAc;gBACd,cAAc,CAAC,eAAgB,EAAE,UAAU,EAAE,IAAI,EAAE,aAAa,CAAC,CAAC;aACnE;iBAAM,IAAI,cAAc,CAAC,IAAI,CAAC,EAAE;gBAC/B,IAAI,aAAa,KAAK,OAAO,EAAE;oBAC7B,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,eAAe,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;iBACtE;aACF;iBAAM,IACL,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,uBAAe,CAAC,OAAO,CAAC;gBAChD,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;gBAC9B,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc;oBAC/C,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK,OAAO;wBAC7B,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK,UAAU;4BAChC,IAAI,CAAC,IAAI,CAAC,cAAc;4BACxB,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EACtD;gBACA,kBAAkB;gBAClB,cAAc,CAAC,YAAa,EAAE,UAAU,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;aAC5D;iBAAM;gBACL,oCAAoC;gBACpC,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,eAAe,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC;aACvE;QACH,CAAC;QAED;;WAEG;QACH,SAAS,QAAQ,CACf,IAAmB,EACnB,kBAA0C,IAAI;YAE9C,IACE,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,WAAW;gBACxC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB,EAC/C;gBACA,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAkB,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE;oBACtD,GAAG,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;oBACvC,OAAO,GAAG,CAAC;gBACb,CAAC,EAAE,EAAE,CAAC,CAAC;aACR;YACD,OAAO,CAAC,EAAE,IAAI,EAAE,eAAe,EAAE,CAAC,CAAC;QACrC,CAAC;QAED,OAAO;YACL,sBAAsB,CAAC,IAAI;gBACzB,MAAM,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;gBAC5C,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;oBACtB,iCAAiC;oBACjC,mBAAmB,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;iBACrC;qBAAM;oBACL,wBAAwB;oBACxB,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;wBACnB,mBAAmB,CAAC,IAAI,CAAC,CAAC;oBAC5B,CAAC,CAAC,CAAC;iBACJ;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}