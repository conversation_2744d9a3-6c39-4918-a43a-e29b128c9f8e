{"version": 3, "file": "misc.js", "sourceRoot": "", "sources": ["../../src/util/misc.ts"], "names": [], "mappings": ";AAAA;;GAEG;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,8DAAgE;AAEhE,oDAA0D;AAC1D,+CAAiC;AAEjC,MAAM,qBAAqB,GAAG;IAC5B,EAAE,CAAC,SAAS,CAAC,GAAG;IAChB,EAAE,CAAC,SAAS,CAAC,IAAI;IACjB,EAAE,CAAC,SAAS,CAAC,IAAI;CACT,CAAC;AACX;;GAEG;AACH,SAAS,gBAAgB,CAAC,QAAgB;IACxC,MAAM,aAAa,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC;IAC7C,KAAK,MAAM,aAAa,IAAI,qBAAqB,EAAE;QACjD,IAAI,aAAa,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE;YACzC,OAAO,IAAI,CAAC;SACb;KACF;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AA2MC,4CAAgB;AAzMlB;;GAEG;AACH,SAAS,cAAc,CAAC,GAAW;IACjC,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAC7C,CAAC;AAwMC,wCAAc;AAtMhB,SAAS,iBAAiB,CACxB,KAAU,EACV,MAAwB;IAExB,MAAM,MAAM,GAAG,IAAI,GAAG,EAAY,CAAC;IAEnC,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;QACxB,MAAM,GAAG,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;QACzB,MAAM,QAAQ,GAAG,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAEjC,IAAI,QAAQ,EAAE;YACZ,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACrB;aAAM;YACL,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;SACzB;KACF;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAuKC,8CAAiB;AAlKnB,SAAS,cAAc,CACrB,CAAkB,EAClB,CAAkB,EAClB,EAA2B;IAE3B,OAAO,CACL,CAAC,KAAK,CAAC;QACP,CAAC,CAAC,KAAK,SAAS;YACd,CAAC,KAAK,SAAS;YACf,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,MAAM;YACrB,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CACtC,CAAC;AACJ,CAAC;AAuJC,wCAAc;AArJhB,gDAAgD;AAChD,SAAS,eAAe,CACtB,MAAW,EACX,SAAkC;IAElC,KAAK,MAAM,OAAO,IAAI,MAAM,EAAE;QAC5B,MAAM,MAAM,GAAG,SAAS,CAAC,OAAO,CAAC,CAAC;QAClC,IAAI,MAAM,KAAK,SAAS,EAAE;YACxB,OAAO,MAAM,CAAC;SACf;KACF;IACD,OAAO,SAAS,CAAC;AACnB,CAAC;AA4IC,0CAAe;AA1IjB;;GAEG;AACH,SAAS,yBAAyB,CAAC,IAA+B;IAChE,MAAM,QAAQ,GAAsC,IAAI,CAAC,UAAU,CAAC,IAAI,CACtE,CAAC,SAA6B,EAAoC,EAAE,CAClE,SAAS,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU,CAC/C,CAAC;IACF,OAAO,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,mBAAmB,CAAC;AACxD,CAAC;AAoIC,8DAAyB;AAlI3B,IAAK,cAKJ;AALD,WAAK,cAAc;IACjB,yDAAW,CAAA;IACX,uDAAU,CAAA;IACV,uDAAU,CAAA;IACV,+DAAc,CAAA;AAChB,CAAC,EALI,cAAc,8BAAd,cAAc,QAKlB;AAED;;;GAGG;AACH,SAAS,iBAAiB,CACxB,MAOgC,EAChC,UAA+B;IAE/B,IAAI,MAAM,CAAC,GAAG,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU,EAAE;QACjD,OAAO;YACL,IAAI,EAAE,cAAc,CAAC,MAAM;YAC3B,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI;SACtB,CAAC;KACH;IACD,IAAI,MAAM,CAAC,GAAG,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB,EAAE;QACxD,OAAO;YACL,IAAI,EAAE,cAAc,CAAC,OAAO;YAC5B,IAAI,EAAE,IAAI,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE;SAC5B,CAAC;KACH;IACD,IAAI,MAAM,CAAC,GAAG,CAAC,IAAI,KAAK,sBAAc,CAAC,OAAO,EAAE;QAC9C,MAAM,IAAI,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC;QACnC,IAAI,IAAA,4BAAe,EAAC,IAAI,CAAC,EAAE;YACzB,OAAO;gBACL,IAAI,EAAE,cAAc,CAAC,MAAM;gBAC3B,IAAI,EAAE,IAAI,IAAI,GAAG;aAClB,CAAC;SACH;aAAM;YACL,OAAO;gBACL,IAAI,EAAE,cAAc,CAAC,MAAM;gBAC3B,IAAI;aACL,CAAC;SACH;KACF;IAED,OAAO;QACL,IAAI,EAAE,cAAc,CAAC,UAAU;QAC/B,IAAI,EAAE,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC;KACjD,CAAC;AACJ,CAAC;AA8EC,8CAAiB;AAnEnB,SAAS,YAAY,CAAmB,MAA0B;IAChE,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAQ,CAAC;AACpE,CAAC;AA+DC,oCAAY;AA7Dd;;;;;GAKG;AACH,SAAS,cAAc,CAAC,KAAe;IACrC,IAAI,CAAC,CAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,MAAM,CAAA,EAAE;QAClB,OAAO,EAAE,CAAC;KACX;IAED,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;QACtB,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC;KACjB;IAED,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAC3E,CAAC;AA4CC,wCAAc;AA1ChB;;;;;GAKG;AACH,SAAS,aAAa,CACpB,OAAY,EACZ,SAAoD;IAEpD,IAAI,GAAG,GAAG,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;IAE7B,OAAO,GAAG,IAAI,CAAC,EAAE;QACf,MAAM,KAAK,GAAG,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC;QACtC,IAAI,KAAK,EAAE;YACT,OAAO,GAAG,CAAC;SACZ;QACD,GAAG,EAAE,CAAC;KACP;IAED,OAAO,CAAC,CAAC,CAAC;AACZ,CAAC;AA8BC,sCAAa;AA5Bf,SAAS,2BAA2B,CAClC,IAAuB,EACvB,IAAY;IAEZ,OAAO,CACL,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc;QAC3C,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB;QAC9C,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB;QAC9C,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,WAAW,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;QAClE,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAC1E,CAAC;AACJ,CAAC;AAeC,kEAA2B"}