# lodash.isarray v3.0.4

The [modern build](https://github.com/lodash/lodash/wiki/Build-Differences) of [lodash’s](https://lodash.com/) `_.isArray` exported as a [Node.js](http://nodejs.org/)/[io.js](https://iojs.org/) module.

## Installation

Using npm:

```bash
$ {sudo -H} npm i -g npm
$ npm i --save lodash.isarray
```

In Node.js/io.js:

```js
var isArray = require('lodash.isarray');
```

See the [documentation](https://lodash.com/docs#isArray) or [package source](https://github.com/lodash/lodash/blob/3.0.4-npm-packages/lodash.isarray) for more details.
