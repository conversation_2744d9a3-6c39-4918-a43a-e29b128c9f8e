{"name": "color", "version": "4.2.3", "description": "Color conversion and manipulation with CSS string support", "sideEffects": false, "keywords": ["color", "colour", "css"], "authors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON>"], "license": "MIT", "repository": "Qix-/color", "xo": {"rules": {"no-cond-assign": 0, "new-cap": 0, "unicorn/prefer-module": 0, "no-mixed-operators": 0, "complexity": 0, "unicorn/numeric-separators-style": 0}}, "files": ["LICENSE", "index.js"], "scripts": {"pretest": "xo", "test": "mocha"}, "engines": {"node": ">=12.5.0"}, "dependencies": {"color-convert": "^2.0.1", "color-string": "^1.9.0"}, "devDependencies": {"mocha": "9.0.2", "xo": "0.42.0"}}