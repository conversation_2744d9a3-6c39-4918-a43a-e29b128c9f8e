{"author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "license": "MIT", "name": "bubble-stream-error", "description": "Bubble errors from an array of streams to a master/top stream", "version": "1.0.0", "repository": {"type": "git", "url": "git://github.com/alessioalex/bubble-stream-error.git"}, "main": "index.js", "engines": {"node": ">= 0.4.0"}, "keywords": ["bubble", "error", "stream", "stream-error", "bubble-stream-error"], "dependencies": {"once": "^1.3.3", "sliced": "^1.0.1"}, "devDependencies": {"alessioalex-standard": "*", "chopped-stream": "^0.1.0", "colorize-stream": "^0.1.0", "husky": "^0.10.2", "pump": "^1.0.1", "random-stream": "^0.0.4", "tape": "^4.2.2", "through2": "^2.0.0"}, "scripts": {"test": "node test.js", "lint": "alessioalex-standard", "precommit": "npm run lint && npm test"}}