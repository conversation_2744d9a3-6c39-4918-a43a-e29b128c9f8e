/*!
 * The MIT License (MIT)
 *
 * Copyright (c) 2019 <PERSON>
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy of
 * this software and associated documentation files (the "Software"), to deal in
 * the Software without restriction, including without limitation the rights to
 * use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of
 * the Software, and to permit persons to whom the Software is furnished to do so,
 * subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS
 * FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR
 * COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER
 * IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
 * CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 */

// Strict mode.
'use strict'

// Package modules.
const sharp = require('sharp')

// Exports.
module.exports = {
  BLEND: Object.keys(sharp.blend),
  BOOL: Object.keys(sharp.bool),
  CHANNEL: ['red', 'green', 'blue', 'alpha'],
  COLOURSPACE: Object.keys(sharp.colourspace),
  CONTAINER: ['fs', 'zip'],
  DEPTH: ['onepixel', 'onetile', 'one'],
  EXTEND_WITH: ['background', 'copy', 'repeat', 'mirror'],
  FAIL_ON: ['none', 'truncated', 'error', 'warning'],
  FIT: Object.keys(sharp.fit),
  FORMAT: ['avif', 'gif', 'heif', 'jpeg', 'jpg', 'png', 'raw', 'tiff', 'webp'],
  GRAVITY: Object.keys(sharp.gravity),
  HEIF_COMPRESSION: ['hevc', 'av1'],
  INTERPOLATORS: Object.keys(sharp.interpolators),
  KERNEL: Object.keys(sharp.kernel),
  LAYOUT: ['dz', 'google', 'iiif', 'zoomify'],
  POSITION: Object.keys(sharp.position),
  PRESETS: ['default', 'photo', 'picture', 'drawing', 'icon', 'text'],
  RESOLUTION_UNIT: ['cm', 'inch'],
  STRATEGY: Object.keys(sharp.strategy),
  TIFF_COMPRESSION: [
    'ccittfax4', 'deflate', 'jpeg', 'jp2k', 'lzw', 'none', 'packbits', 'webp', 'zstd'
  ],
  TIFF_PREDICTOR: ['float', 'horizontal', 'none']
}
