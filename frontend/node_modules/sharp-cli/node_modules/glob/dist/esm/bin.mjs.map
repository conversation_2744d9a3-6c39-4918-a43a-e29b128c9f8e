{"version": 3, "file": "bin.mjs", "sourceRoot": "", "sources": ["../../src/bin.mts"], "names": [], "mappings": ";AACA,OAAO,EAAE,eAAe,EAAE,MAAM,kBAAkB,CAAA;AAClD,OAAO,EAAE,UAAU,EAAE,MAAM,IAAI,CAAA;AAC/B,OAAO,EAAE,IAAI,EAAE,MAAM,WAAW,CAAA;AAChC,OAAO,EAAE,eAAe,EAAE,MAAM,wBAAwB,CAAA;AACxD,OAAO,EAAE,IAAI,EAAE,MAAM,MAAM,CAAA;AAC3B,OAAO,EAAE,UAAU,EAAE,MAAM,YAAY,CAAA;AAEvC,MAAM,EAAE,OAAO,EAAE,GAAG,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,iBAAiB,CAAC,CAAA;AAEvE,MAAM,CAAC,GAAG,IAAI,CAAC;IACb,KAAK,EAAE,4CAA4C;CACpD,CAAC;KACC,WAAW,CACV;YACQ,OAAO;;;;GAIhB,CACA;KACA,GAAG,CAAC;IACH,GAAG,EAAE;QACH,KAAK,EAAE,GAAG;QACV,IAAI,EAAE,SAAS;QACf,WAAW,EAAE;0CACuB;KACrC;CACF,CAAC;KACD,GAAG,CAAC;IACH,OAAO,EAAE;QACP,KAAK,EAAE,GAAG;QACV,IAAI,EAAE,SAAS;QACf,WAAW,EAAE;iCACc;KAC5B;CACF,CAAC;KACD,IAAI,CAAC;IACJ,GAAG,EAAE;QACH,KAAK,EAAE,GAAG;QACV,WAAW,EAAE;;;;;;;;;;;;;;;;;;;;;OAqBZ;KACF;IACD,QAAQ,EAAE;QACR,KAAK,EAAE,GAAG;QACV,WAAW,EAAE,0BAA0B;KACxC;IACD,cAAc,EAAE;QACd,KAAK,EAAE,GAAG;QACV,WAAW,EAAE,kCAAkC;KAChD;IACD,IAAI,EAAE;QACJ,KAAK,EAAE,GAAG;QACV,WAAW,EAAE,uCAAuC;KACrD;IACD,KAAK,EAAE;QACL,KAAK,EAAE,GAAG;QACV,WAAW,EAAE;;;;;OAKZ;KACF;IAED,MAAM,EAAE;QACN,KAAK,EAAE,GAAG;QACV,WAAW,EAAE,kDAAkD;KAChE;IACD,QAAQ,EAAE;QACR,KAAK,EAAE,GAAG;QACV,WAAW,EAAE;;;+DAG4C;KAC1D;IACD,IAAI,EAAE;QACJ,KAAK,EAAE,GAAG;QACV,WAAW,EAAE;wDACqC;KACnD;IACD,YAAY,EAAE;QACZ,KAAK,EAAE,GAAG;QACV,WAAW,EAAE;;;;OAIZ;KACF;IAED,GAAG,EAAE;QACH,WAAW,EAAE;;OAEZ;KACF;IACD,OAAO,EAAE;QACP,WAAW,EAAE,8BAA8B;KAC5C;IACD,MAAM,EAAE;QACN,WAAW,EAAE;;;;;;;;;OASZ;KACF;IACD,KAAK,EAAE;QACL,WAAW,EAAE;;;;OAIZ;KACF;IACD,KAAK,EAAE;QACL,WAAW,EAAE,kDAAkD;KAChE;IACD,UAAU,EAAE;QACV,WAAW,EAAE;0DACuC;KACrD;IACD,wBAAwB,EAAE;QACxB,WAAW,EAAE;;sDAEmC;KACjD;CACF,CAAC;KACD,GAAG,CAAC;IACH,WAAW,EAAE;QACX,KAAK,EAAE,GAAG;QACV,WAAW,EAAE;sCACmB;KACjC;CACF,CAAC;KACD,GAAG,CAAC;IACH,GAAG,EAAE;QACH,KAAK,EAAE,GAAG;QACV,WAAW,EAAE,+CAA+C;QAC5D,OAAO,EAAE,OAAO,CAAC,GAAG,EAAE;KACvB;IACD,IAAI,EAAE;QACJ,KAAK,EAAE,GAAG;QACV,WAAW,EAAE;;;;;;;;;;;;;;;;;;;OAmBZ;KACF;IACD,QAAQ,EAAE;QACR,WAAW,EAAE;;uEAEoD;QACjE,YAAY,EAAE;YACZ,KAAK;YACL,SAAS;YACT,QAAQ;YACR,SAAS;YACT,OAAO;YACP,OAAO;YACP,SAAS;YACT,OAAO;YACP,OAAO;YACP,QAAQ;YACR,QAAQ;SACT;KACF;CACF,CAAC;KACD,OAAO,CAAC;IACP,MAAM,EAAE;QACN,KAAK,EAAE,GAAG;QACV,WAAW,EAAE,yBAAyB;KACvC;CACF,CAAC;KACD,IAAI,CAAC;IACJ,KAAK,EAAE;QACL,KAAK,EAAE,GAAG;QACV,WAAW,EAAE;yEACsD;KACpE;IACD,OAAO,EAAE;QACP,KAAK,EAAE,GAAG;QACV,WAAW,EAAE,uBAAuB,OAAO,GAAG;KAC/C;IACD,IAAI,EAAE;QACJ,KAAK,EAAE,GAAG;QACV,WAAW,EAAE,6BAA6B;KAC3C;CACF,CAAC,CAAA;AAEJ,IAAI,CAAC;IACH,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC,KAAK,EAAE,CAAA;IACzC,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;QACnB,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAA;QACpB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;IACjB,CAAC;IACD,IAAI,MAAM,CAAC,IAAI,EAAE,CAAC;QAChB,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAA;QACtB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;IACjB,CAAC;IACD,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO;QAC7C,MAAM,sBAAsB,CAAA;IAC9B,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO;QAC5C,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;IAClC,MAAM,QAAQ,GACZ,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAA;IACpE,MAAM,OAAO,GACX,MAAM,CAAC,GAAG,CAAC,CAAC;QACV,EAAE;QACJ,CAAC,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAA;IAC5D,MAAM,MAAM,GAAG,UAAU,CAAC,QAAQ,EAAE;QAClC,QAAQ,EAAE,MAAM,CAAC,QAAQ;QACzB,GAAG,EAAE,MAAM,CAAC,GAAG;QACf,GAAG,EAAE,MAAM,CAAC,GAAG;QACf,WAAW,EAAE,MAAM,CAAC,cAAc,CAAC;QACnC,MAAM,EAAE,MAAM,CAAC,MAAM;QACrB,MAAM,EAAE,MAAM,CAAC,MAAM;QACrB,IAAI,EAAE,MAAM,CAAC,IAAI;QACjB,SAAS,EAAE,MAAM,CAAC,YAAY,CAAC;QAC/B,QAAQ,EAAE,MAAM,CAAC,WAAW,CAAC;QAC7B,OAAO,EAAE,MAAM,CAAC,OAAO;QACvB,MAAM,EAAE,MAAM,CAAC,MAAM;QACrB,KAAK,EAAE,MAAM,CAAC,KAAK;QACnB,KAAK,EAAE,MAAM,CAAC,KAAK;QACnB,UAAU,EAAE,MAAM,CAAC,UAAU;QAC7B,QAAQ,EAAE,MAAM,CAAC,QAAuC;QACxD,QAAQ,EAAE,MAAM,CAAC,QAAQ;QACzB,IAAI,EAAE,MAAM,CAAC,IAAI;QACjB,IAAI,EAAE,MAAM,CAAC,IAAI;QACjB,KAAK,EAAE,MAAM,CAAC,KAAK;QACnB,KAAK,EAAE,MAAM,CAAC,KAAK;KACpB,CAAC,CAAA;IAEF,MAAM,GAAG,GAAG,MAAM,CAAC,GAAG,CAAA;IACtB,IAAI,CAAC,GAAG,EAAE,CAAC;QACT,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;QACpC,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;IACxC,CAAC;SAAM,CAAC;QACN,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAA;QACvC,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC,eAAe,CAAC,GAAG,EAAE,OAAO,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,CAAA;IACxE,CAAC;AACH,CAAC;AAAC,OAAO,CAAC,EAAE,CAAC;IACX,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAA;IACxB,OAAO,CAAC,KAAK,CAAC,CAAC,YAAY,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAA;IACzD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;AACjB,CAAC", "sourcesContent": ["#!/usr/bin/env node\nimport { foregroundChild } from 'foreground-child'\nimport { existsSync } from 'fs'\nimport { jack } from 'jackspeak'\nimport { loadPackageJson } from 'package-json-from-dist'\nimport { join } from 'path'\nimport { globStream } from './index.js'\n\nconst { version } = loadPackageJson(import.meta.url, '../package.json')\n\nconst j = jack({\n  usage: 'glob [options] [<pattern> [<pattern> ...]]',\n})\n  .description(\n    `\n    Glob v${version}\n\n    Expand the positional glob expression arguments into any matching file\n    system paths found.\n  `,\n  )\n  .opt({\n    cmd: {\n      short: 'c',\n      hint: 'command',\n      description: `Run the command provided, passing the glob expression\n                    matches as arguments.`,\n    },\n  })\n  .opt({\n    default: {\n      short: 'p',\n      hint: 'pattern',\n      description: `If no positional arguments are provided, glob will use\n                    this pattern`,\n    },\n  })\n  .flag({\n    all: {\n      short: 'A',\n      description: `By default, the glob cli command will not expand any\n                    arguments that are an exact match to a file on disk.\n\n                    This prevents double-expanding, in case the shell expands\n                    an argument whose filename is a glob expression.\n\n                    For example, if 'app/*.ts' would match 'app/[id].ts', then\n                    on Windows powershell or cmd.exe, 'glob app/*.ts' will\n                    expand to 'app/[id].ts', as expected. However, in posix\n                    shells such as bash or zsh, the shell will first expand\n                    'app/*.ts' to a list of filenames. Then glob will look\n                    for a file matching 'app/[id].ts' (ie, 'app/i.ts' or\n                    'app/d.ts'), which is unexpected.\n\n                    Setting '--all' prevents this behavior, causing glob\n                    to treat ALL patterns as glob expressions to be expanded,\n                    even if they are an exact match to a file on disk.\n\n                    When setting this option, be sure to enquote arguments\n                    so that the shell will not expand them prior to passing\n                    them to the glob command process.\n      `,\n    },\n    absolute: {\n      short: 'a',\n      description: 'Expand to absolute paths',\n    },\n    'dot-relative': {\n      short: 'd',\n      description: `Prepend './' on relative matches`,\n    },\n    mark: {\n      short: 'm',\n      description: `Append a / on any directories matched`,\n    },\n    posix: {\n      short: 'x',\n      description: `Always resolve to posix style paths, using '/' as the\n                    directory separator, even on Windows. Drive letter\n                    absolute matches on Windows will be expanded to their\n                    full resolved UNC maths, eg instead of 'C:\\\\foo\\\\bar',\n                    it will expand to '//?/C:/foo/bar'.\n      `,\n    },\n\n    follow: {\n      short: 'f',\n      description: `Follow symlinked directories when expanding '**'`,\n    },\n    realpath: {\n      short: 'R',\n      description: `Call 'fs.realpath' on all of the results. In the case\n                    of an entry that cannot be resolved, the entry is\n                    omitted. This incurs a slight performance penalty, of\n                    course, because of the added system calls.`,\n    },\n    stat: {\n      short: 's',\n      description: `Call 'fs.lstat' on all entries, whether required or not\n                    to determine if it's a valid match.`,\n    },\n    'match-base': {\n      short: 'b',\n      description: `Perform a basename-only match if the pattern does not\n                    contain any slash characters. That is, '*.js' would be\n                    treated as equivalent to '**/*.js', matching js files\n                    in all directories.\n      `,\n    },\n\n    dot: {\n      description: `Allow patterns to match files/directories that start\n                    with '.', even if the pattern does not start with '.'\n      `,\n    },\n    nobrace: {\n      description: 'Do not expand {...} patterns',\n    },\n    nocase: {\n      description: `Perform a case-insensitive match. This defaults to\n                    'true' on macOS and Windows platforms, and false on\n                    all others.\n\n                    Note: 'nocase' should only be explicitly set when it is\n                    known that the filesystem's case sensitivity differs\n                    from the platform default. If set 'true' on\n                    case-insensitive file systems, then the walk may return\n                    more or less results than expected.\n      `,\n    },\n    nodir: {\n      description: `Do not match directories, only files.\n\n                    Note: to *only* match directories, append a '/' at the\n                    end of the pattern.\n      `,\n    },\n    noext: {\n      description: `Do not expand extglob patterns, such as '+(a|b)'`,\n    },\n    noglobstar: {\n      description: `Do not expand '**' against multiple path portions.\n                    Ie, treat it as a normal '*' instead.`,\n    },\n    'windows-path-no-escape': {\n      description: `Use '\\\\' as a path separator *only*, and *never* as an\n                    escape character. If set, all '\\\\' characters are\n                    replaced with '/' in the pattern.`,\n    },\n  })\n  .num({\n    'max-depth': {\n      short: 'D',\n      description: `Maximum depth to traverse from the current\n                    working directory`,\n    },\n  })\n  .opt({\n    cwd: {\n      short: 'C',\n      description: 'Current working directory to execute/match in',\n      default: process.cwd(),\n    },\n    root: {\n      short: 'r',\n      description: `A string path resolved against the 'cwd', which is\n                    used as the starting point for absolute patterns that\n                    start with '/' (but not drive letters or UNC paths\n                    on Windows).\n\n                    Note that this *doesn't* necessarily limit the walk to\n                    the 'root' directory, and doesn't affect the cwd\n                    starting point for non-absolute patterns. A pattern\n                    containing '..' will still be able to traverse out of\n                    the root directory, if it is not an actual root directory\n                    on the filesystem, and any non-absolute patterns will\n                    still be matched in the 'cwd'.\n\n                    To start absolute and non-absolute patterns in the same\n                    path, you can use '--root=' to set it to the empty\n                    string. However, be aware that on Windows systems, a\n                    pattern like 'x:/*' or '//host/share/*' will *always*\n                    start in the 'x:/' or '//host/share/' directory,\n                    regardless of the --root setting.\n      `,\n    },\n    platform: {\n      description: `Defaults to the value of 'process.platform' if\n                    available, or 'linux' if not. Setting --platform=win32\n                    on non-Windows systems may cause strange behavior!`,\n      validOptions: [\n        'aix',\n        'android',\n        'darwin',\n        'freebsd',\n        'haiku',\n        'linux',\n        'openbsd',\n        'sunos',\n        'win32',\n        'cygwin',\n        'netbsd',\n      ],\n    },\n  })\n  .optList({\n    ignore: {\n      short: 'i',\n      description: `Glob patterns to ignore`,\n    },\n  })\n  .flag({\n    debug: {\n      short: 'v',\n      description: `Output a huge amount of noisy debug information about\n                    patterns as they are parsed and used to match files.`,\n    },\n    version: {\n      short: 'V',\n      description: `Output the version (${version})`,\n    },\n    help: {\n      short: 'h',\n      description: 'Show this usage information',\n    },\n  })\n\ntry {\n  const { positionals, values } = j.parse()\n  if (values.version) {\n    console.log(version)\n    process.exit(0)\n  }\n  if (values.help) {\n    console.log(j.usage())\n    process.exit(0)\n  }\n  if (positionals.length === 0 && !values.default)\n    throw 'No patterns provided'\n  if (positionals.length === 0 && values.default)\n    positionals.push(values.default)\n  const patterns =\n    values.all ? positionals : positionals.filter(p => !existsSync(p))\n  const matches =\n    values.all ?\n      []\n    : positionals.filter(p => existsSync(p)).map(p => join(p))\n  const stream = globStream(patterns, {\n    absolute: values.absolute,\n    cwd: values.cwd,\n    dot: values.dot,\n    dotRelative: values['dot-relative'],\n    follow: values.follow,\n    ignore: values.ignore,\n    mark: values.mark,\n    matchBase: values['match-base'],\n    maxDepth: values['max-depth'],\n    nobrace: values.nobrace,\n    nocase: values.nocase,\n    nodir: values.nodir,\n    noext: values.noext,\n    noglobstar: values.noglobstar,\n    platform: values.platform as undefined | NodeJS.Platform,\n    realpath: values.realpath,\n    root: values.root,\n    stat: values.stat,\n    debug: values.debug,\n    posix: values.posix,\n  })\n\n  const cmd = values.cmd\n  if (!cmd) {\n    matches.forEach(m => console.log(m))\n    stream.on('data', f => console.log(f))\n  } else {\n    stream.on('data', f => matches.push(f))\n    stream.on('end', () => foregroundChild(cmd, matches, { shell: true }))\n  }\n} catch (e) {\n  console.error(j.usage())\n  console.error(e instanceof Error ? e.message : String(e))\n  process.exit(1)\n}\n"]}