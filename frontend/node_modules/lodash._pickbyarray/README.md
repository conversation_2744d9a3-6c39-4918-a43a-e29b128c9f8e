# lodash._pickbyarray v3.0.2

The [modern build](https://github.com/lodash/lodash/wiki/Build-Differences) of [lodash’s](https://lodash.com/) internal `pickByArray` exported as a [Node.js](http://nodejs.org/)/[io.js](https://iojs.org/) module.

## Installation

Using npm:

```bash
$ {sudo -H} npm i -g npm
$ npm i --save lodash._pickbyarray
```

In Node.js/io.js:

```js
var pickByArray = require('lodash._pickbyarray');
```

See the [package source](https://github.com/lodash/lodash/blob/3.0.2-npm-packages/lodash._pickbyarray) for more details.
