# WePrint AI Frontend Production Environment Configuration

# API Configuration
REACT_APP_API_URL=https://api.weprint.ai/api
REACT_APP_WS_URL=wss://api.weprint.ai

# Payment Configuration
REACT_APP_MULTICAIXA_RETURN_URL=https://weprint.ai/payment/return
REACT_APP_MULTICAIXA_CANCEL_URL=https://weprint.ai/payment/cancel

# Application Configuration
REACT_APP_NAME=WePrint AI
REACT_APP_VERSION=1.0.0
REACT_APP_ENVIRONMENT=production

# Feature Flags
REACT_APP_ENABLE_PWA=true
REACT_APP_ENABLE_NOTIFICATIONS=true
REACT_APP_ENABLE_ANALYTICS=true

# Upload Configuration
REACT_APP_MAX_FILE_SIZE=10485760
REACT_APP_ALLOWED_FILE_TYPES=.pdf,.doc,.docx,.txt,.jpg,.jpeg,.png

# UI Configuration
REACT_APP_DEFAULT_LANGUAGE=pt
REACT_APP_CURRENCY=AOA
REACT_APP_TIMEZONE=Africa/Luanda
