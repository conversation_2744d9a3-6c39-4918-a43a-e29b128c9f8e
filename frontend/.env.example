# WePrint AI Frontend Environment Variables
# Copy this file to .env.local and update the values

# API Configuration
REACT_APP_API_URL=http://localhost:8000/api

# App Configuration
REACT_APP_APP_NAME=WePrint AI
REACT_APP_VERSION=1.0.0

# Feature Flags
REACT_APP_ENABLE_ANALYTICS=false
REACT_APP_ENABLE_DEBUG=false

# Payment Configuration (if using Stripe)
REACT_APP_STRIPE_PUBLIC_KEY=pk_test_your_stripe_public_key_here

# File Upload Configuration
REACT_APP_MAX_FILE_SIZE=10485760
REACT_APP_ALLOWED_FILE_TYPES=pdf,doc,docx,txt,jpg,jpeg,png
