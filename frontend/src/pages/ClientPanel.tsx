import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Layout, Button } from '../components';
import { apiService } from '../services/api';

interface Order {
  id: string;
  orderNumber: string;
  fileName: string;
  status: 'pending' | 'processing' | 'printing' | 'shipped' | 'delivered' | 'cancelled';
  total: number;
  createdAt: string;
  estimatedDelivery?: string;
}

interface Customer {
  name: string;
  email: string;
  phone: string;
  totalOrders: number;
  totalSpent: number;
}

const ClientPanel: React.FC = () => {
  const navigate = useNavigate();
  const [customer, setCustomer] = useState<Customer | null>(null);
  const [orders, setOrders] = useState<Order[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'orders' | 'profile'>('orders');

  useEffect(() => {
    const loadCustomerData = async () => {
      // Get customer email from localStorage or URL params
      // In a real app, this would come from authentication
      const urlParams = new URLSearchParams(window.location.search);
      const customerEmail = urlParams.get('email') || localStorage.getItem('customerEmail') || '<EMAIL>';

      try {
        // Load customer orders from backend
        const ordersResponse = await apiService.get(`/orders/customer/${customerEmail}`);

        if (ordersResponse.success && ordersResponse.data) {
          const backendOrders = ordersResponse.data as any[];

          // Transform backend orders to frontend format
          const transformedOrders: Order[] = backendOrders.map(order => ({
            id: order.id.toString(),
            orderNumber: order.orderNumber,
            fileName: order.file?.originalName || 'Arquivo não encontrado',
            status: order.status,
            total: order.price || 0,
            createdAt: new Date(order.createdAt).toLocaleDateString('pt-BR'),
            estimatedDelivery: order.estimatedCompletionDate ?
              new Date(order.estimatedCompletionDate).toLocaleDateString('pt-BR') : undefined
          }));

          setOrders(transformedOrders);

          // Calculate customer stats
          const totalOrders = transformedOrders.length;
          const totalSpent = transformedOrders.reduce((sum, order) => sum + order.total, 0);

          setCustomer({
            name: backendOrders[0]?.customerName || 'Cliente',
            email: customerEmail,
            phone: backendOrders[0]?.customerPhone || '',
            totalOrders,
            totalSpent
          });
        } else {
          // No orders found, set empty customer
          setCustomer({
            name: 'Cliente',
            email: customerEmail,
            phone: '',
            totalOrders: 0,
            totalSpent: 0
          });
          setOrders([]);
        }
      } catch (error) {
        console.error('Error loading customer data:', error);
        // Fallback to empty state
        setCustomer({
          name: 'Cliente',
          email: customerEmail,
          phone: '',
          totalOrders: 0,
          totalSpent: 0
        });
        setOrders([]);
      } finally {
        setIsLoading(false);
      }
    };

    loadCustomerData();
  }, []);

  const getStatusColor = (status: Order['status']) => {
    const colors = {
      pending: 'bg-yellow-100 text-yellow-800',
      processing: 'bg-blue-100 text-blue-800',
      printing: 'bg-purple-100 text-purple-800',
      shipped: 'bg-indigo-100 text-indigo-800',
      delivered: 'bg-green-100 text-green-800',
      cancelled: 'bg-red-100 text-red-800',
    };
    return colors[status];
  };

  const getStatusText = (status: Order['status']) => {
    const texts = {
      pending: 'Pendente',
      processing: 'Em Processamento',
      printing: 'Imprimindo',
      shipped: 'Enviado',
      delivered: 'Entregue',
      cancelled: 'Cancelado',
    };
    return texts[status];
  };

  const formatPrice = (price: number) => {
    return `${price.toFixed(2)} AOA`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-AO');
  };

  const handleNewOrder = () => {
    navigate('/upload');
  };

  const handleViewOrder = (orderId: string) => {
    // In a real app, this would navigate to order details
    alert(`Ver detalhes do pedido ${orderId}`);
  };

  const handleReorderOrder = (orderId: string) => {
    // In a real app, this would duplicate the order
    alert(`Repetir pedido ${orderId}`);
  };

  if (isLoading) {
    return (
      <Layout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Carregando painel...</p>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="max-w-6xl mx-auto py-8">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Painel do Cliente
          </h1>
          <p className="text-lg text-gray-600">
            Bem-vindo de volta, {customer?.name}!
          </p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-lg p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-blue-100">
                <svg className="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" />
                  <path fillRule="evenodd" d="M4 5a2 2 0 012-2v1a1 1 0 001 1h6a1 1 0 001-1V3a2 2 0 012 2v6.5a1.5 1.5 0 01-1.5 1.5h-9A1.5 1.5 0 014 11.5V5zM7 7a1 1 0 012 0v3a1 1 0 11-2 0V7zm5 0a1 1 0 10-2 0v3a1 1 0 102 0V7z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total de Pedidos</p>
                <p className="text-2xl font-bold text-gray-900">{customer?.totalOrders}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-lg p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-green-100">
                <svg className="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z" />
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Gasto</p>
                <p className="text-2xl font-bold text-gray-900">{formatPrice(customer?.totalSpent || 0)}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-lg p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-purple-100">
                <svg className="w-6 h-6 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Status</p>
                <p className="text-2xl font-bold text-gray-900">Premium</p>
              </div>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="bg-white rounded-lg shadow-lg">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex">
              <button
                onClick={() => setActiveTab('orders')}
                className={`py-4 px-6 text-sm font-medium border-b-2 ${
                  activeTab === 'orders'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Meus Pedidos
              </button>
              <button
                onClick={() => setActiveTab('profile')}
                className={`py-4 px-6 text-sm font-medium border-b-2 ${
                  activeTab === 'profile'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Perfil
              </button>
            </nav>
          </div>

          <div className="p-6">
            {activeTab === 'orders' && (
              <div>
                <div className="flex justify-between items-center mb-6">
                  <h2 className="text-xl font-semibold text-gray-900">
                    Histórico de Pedidos
                  </h2>
                  <Button onClick={handleNewOrder}>
                    Novo Pedido
                  </Button>
                </div>

                <div className="space-y-4">
                  {orders.map((order) => (
                    <div key={order.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-3">
                          <h3 className="font-medium text-gray-900">
                            Pedido #{order.orderNumber}
                          </h3>
                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(order.status)}`}>
                            {getStatusText(order.status)}
                          </span>
                        </div>
                        <div className="text-right">
                          <p className="font-semibold text-gray-900">{formatPrice(order.total)}</p>
                          <p className="text-sm text-gray-600">{formatDate(order.createdAt)}</p>
                        </div>
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm text-gray-600 mb-1">
                            Arquivo: {order.fileName}
                          </p>
                          {order.estimatedDelivery && (
                            <p className="text-sm text-gray-600">
                              Entrega estimada: {formatDate(order.estimatedDelivery)}
                            </p>
                          )}
                        </div>

                        <div className="flex space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleViewOrder(order.id)}
                          >
                            Ver Detalhes
                          </Button>
                          {order.status === 'delivered' && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleReorderOrder(order.id)}
                            >
                              Repetir
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {activeTab === 'profile' && customer && (
              <div>
                <h2 className="text-xl font-semibold text-gray-900 mb-6">
                  Informações do Perfil
                </h2>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Nome Completo
                    </label>
                    <input
                      type="text"
                      value={customer.name}
                      className="w-full p-3 border border-gray-300 rounded-md bg-gray-50"
                      readOnly
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Email
                    </label>
                    <input
                      type="email"
                      value={customer.email}
                      className="w-full p-3 border border-gray-300 rounded-md bg-gray-50"
                      readOnly
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Telefone
                    </label>
                    <input
                      type="tel"
                      value={customer.phone}
                      className="w-full p-3 border border-gray-300 rounded-md bg-gray-50"
                      readOnly
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Status da Conta
                    </label>
                    <div className="flex items-center space-x-2">
                      <span className="px-3 py-1 bg-purple-100 text-purple-800 text-sm font-medium rounded-full">
                        Premium
                      </span>
                      <span className="text-sm text-gray-600">
                        Desde Janeiro 2024
                      </span>
                    </div>
                  </div>
                </div>

                <div className="mt-6 p-4 bg-blue-50 rounded-lg">
                  <h3 className="font-medium text-blue-900 mb-2">Benefícios Premium</h3>
                  <ul className="text-sm text-blue-800 space-y-1">
                    <li>• Entrega prioritária</li>
                    <li>• Desconto de 10% em todos os pedidos</li>
                    <li>• Suporte técnico dedicado</li>
                    <li>• Acesso antecipado a novos serviços</li>
                  </ul>
                </div>

                <div className="mt-6">
                  <Button variant="outline">
                    Editar Perfil
                  </Button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default ClientPanel;
