import React from 'react';

const OptionsForm: React.FC = () => (
  <div className="flex flex-col items-center justify-center min-h-screen">
    <h2 className="text-2xl font-semibold mb-4">Opções de Impressão</h2>
    <form className="space-y-4">
      <select className="block w-64 p-2 border rounded">
        <option>Formato</option>
        <option>A4</option>
        <option>A3</option>
        <option>Cartão</option>
      </select>
      <select className="block w-64 p-2 border rounded">
        <option>Papel</option>
        <option>Offset</option>
        <option>Couchê</option>
      </select>
      <select className="block w-64 p-2 border rounded">
        <option>Acabamento</option>
        <option>Brilho</option>
        <option>Fosco</option>
      </select>
      <button className="px-4 py-2 bg-blue-600 text-white rounded">Ver Resumo</button>
    </form>
  </div>
);

export default OptionsForm;
