import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Layout, Button } from '../components';

interface PrintOptions {
  format: string;
  paperType: string;
  finish: string;
  copies: number;
  hasColor: boolean;
  complexity: 'low' | 'medium' | 'high';
  notes: string;
}

interface PriceBreakdown {
  basePrice: number;
  paperCost: number;
  finishCost: number;
  complexityCost: number;
  colorCost: number;
  total: number;
}

const OptionsForm: React.FC = () => {
  const navigate = useNavigate();
  const [uploadedFile, setUploadedFile] = useState<any>(null);
  const [options, setOptions] = useState<PrintOptions>({
    format: 'A4',
    paperType: 'standard',
    finish: 'none',
    copies: 1,
    hasColor: false,
    complexity: 'low',
    notes: '',
  });
  const [priceBreakdown, setPriceBreakdown] = useState<PriceBreakdown>({
    basePrice: 0,
    paperCost: 0,
    finishCost: 0,
    complexityCost: 0,
    colorCost: 0,
    total: 0,
  });

  useEffect(() => {
    // Get uploaded file info from localStorage
    const fileInfo = localStorage.getItem('uploadedFile');
    if (fileInfo) {
      setUploadedFile(JSON.parse(fileInfo));
    } else {
      // Redirect back to upload if no file
      navigate('/upload');
    }
  }, [navigate]);

  const calculatePrice = React.useCallback(() => {
    let basePrice = 2.5; // Base price per page in AOA

    // Format pricing
    const formatPrices: { [key: string]: number } = {
      'A4': 1.0,
      'A3': 2.0,
      'A5': 0.8,
      'Letter': 1.0,
    };

    // Paper type pricing
    const paperPrices: { [key: string]: number } = {
      'standard': 1.0,
      'premium': 1.5,
      'photo': 2.0,
      'cardstock': 1.8,
    };

    // Finish pricing
    const finishPrices: { [key: string]: number } = {
      'none': 1.0,
      'glossy': 1.3,
      'matte': 1.2,
      'laminated': 1.8,
    };

    // Complexity pricing
    const complexityPrices: { [key: string]: number } = {
      'low': 1.0,
      'medium': 1.3,
      'high': 1.6,
    };

    const formatMultiplier = formatPrices[options.format] || 1.0;
    const paperMultiplier = paperPrices[options.paperType] || 1.0;
    const finishMultiplier = finishPrices[options.finish] || 1.0;
    const complexityMultiplier = complexityPrices[options.complexity] || 1.0;
    const colorMultiplier = options.hasColor ? 1.5 : 1.0;

    const paperCost = basePrice * formatMultiplier * (paperMultiplier - 1);
    const finishCost = basePrice * formatMultiplier * (finishMultiplier - 1);
    const complexityCost = basePrice * formatMultiplier * (complexityMultiplier - 1);
    const colorCost = options.hasColor ? basePrice * formatMultiplier * 0.5 : 0;

    const unitPrice = basePrice * formatMultiplier * paperMultiplier * finishMultiplier * complexityMultiplier * colorMultiplier;
    const total = unitPrice * options.copies;

    setPriceBreakdown({
      basePrice: basePrice * formatMultiplier,
      paperCost,
      finishCost,
      complexityCost,
      colorCost,
      total,
    });
  }, [options]);

  useEffect(() => {
    calculatePrice();
  }, [calculatePrice]);

  const handleOptionChange = (field: keyof PrintOptions, value: any) => {
    setOptions(prev => ({ ...prev, [field]: value }));
  };

  const handleContinue = () => {
    // Store options in localStorage
    localStorage.setItem('printOptions', JSON.stringify(options));
    localStorage.setItem('priceBreakdown', JSON.stringify(priceBreakdown));
    navigate('/summary');
  };

  const formatPrice = (price: number) => {
    return `${price.toFixed(2)} AOA`;
  };

  if (!uploadedFile) {
    return <div>Carregando...</div>;
  }

  return (
    <Layout>
      <div className="max-w-4xl mx-auto py-8">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Opções de Impressão
          </h1>
          <p className="text-lg text-gray-600">
            Configure as opções para o seu documento: {uploadedFile.name}
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Options Form */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">
              Configurações
            </h2>

            <div className="space-y-6">
              {/* Format */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Formato do Papel
                </label>
                <select
                  value={options.format}
                  onChange={(e) => handleOptionChange('format', e.target.value)}
                  className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="A4">A4 (210 × 297 mm)</option>
                  <option value="A3">A3 (297 × 420 mm)</option>
                  <option value="A5">A5 (148 × 210 mm)</option>
                  <option value="Letter">Letter (216 × 279 mm)</option>
                </select>
              </div>

              {/* Paper Type */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Tipo de Papel
                </label>
                <select
                  value={options.paperType}
                  onChange={(e) => handleOptionChange('paperType', e.target.value)}
                  className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="standard">Papel Standard (75g/m²)</option>
                  <option value="premium">Papel Premium (90g/m²)</option>
                  <option value="photo">Papel Fotográfico (200g/m²)</option>
                  <option value="cardstock">Cartolina (250g/m²)</option>
                </select>
              </div>

              {/* Finish */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Acabamento
                </label>
                <select
                  value={options.finish}
                  onChange={(e) => handleOptionChange('finish', e.target.value)}
                  className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="none">Sem Acabamento</option>
                  <option value="glossy">Brilhante</option>
                  <option value="matte">Fosco</option>
                  <option value="laminated">Plastificado</option>
                </select>
              </div>

              {/* Copies */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Número de Cópias
                </label>
                <input
                  type="number"
                  min="1"
                  max="1000"
                  value={options.copies}
                  onChange={(e) => handleOptionChange('copies', parseInt(e.target.value) || 1)}
                  className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              {/* Color */}
              <div>
                <label className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    checked={options.hasColor}
                    onChange={(e) => handleOptionChange('hasColor', e.target.checked)}
                    className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />
                  <span className="text-sm font-medium text-gray-700">
                    Impressão a Cores
                  </span>
                </label>
              </div>

              {/* Complexity */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Complexidade do Documento
                </label>
                <select
                  value={options.complexity}
                  onChange={(e) => handleOptionChange('complexity', e.target.value as 'low' | 'medium' | 'high')}
                  className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="low">Baixa (Texto simples)</option>
                  <option value="medium">Média (Texto + Imagens)</option>
                  <option value="high">Alta (Design complexo)</option>
                </select>
              </div>

              {/* Notes */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Observações (Opcional)
                </label>
                <textarea
                  value={options.notes}
                  onChange={(e) => handleOptionChange('notes', e.target.value)}
                  rows={3}
                  className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Instruções especiais para a impressão..."
                />
              </div>
            </div>
          </div>

          {/* Price Breakdown */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">
              Resumo de Preços
            </h2>

            <div className="space-y-4">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Preço base ({options.format}):</span>
                <span>{formatPrice(priceBreakdown.basePrice)}</span>
              </div>

              {priceBreakdown.paperCost > 0 && (
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Papel premium:</span>
                  <span>+{formatPrice(priceBreakdown.paperCost)}</span>
                </div>
              )}

              {priceBreakdown.finishCost > 0 && (
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Acabamento:</span>
                  <span>+{formatPrice(priceBreakdown.finishCost)}</span>
                </div>
              )}

              {priceBreakdown.complexityCost > 0 && (
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Complexidade:</span>
                  <span>+{formatPrice(priceBreakdown.complexityCost)}</span>
                </div>
              )}

              {priceBreakdown.colorCost > 0 && (
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Impressão a cores:</span>
                  <span>+{formatPrice(priceBreakdown.colorCost)}</span>
                </div>
              )}

              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Quantidade:</span>
                <span>{options.copies} cópia(s)</span>
              </div>

              <hr className="my-4" />

              <div className="flex justify-between text-lg font-semibold">
                <span>Total:</span>
                <span className="text-blue-600">{formatPrice(priceBreakdown.total)}</span>
              </div>
            </div>

            <div className="mt-6 p-4 bg-blue-50 rounded-lg">
              <h3 className="font-medium text-blue-900 mb-2">Informações</h3>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• Entrega grátis em Luanda</li>
                <li>• Prazo: 2-5 dias úteis</li>
                <li>• Garantia de qualidade</li>
                <li>• Suporte técnico incluído</li>
              </ul>
            </div>
          </div>
        </div>

        <div className="flex justify-between mt-8">
          <Button
            variant="outline"
            onClick={() => navigate('/upload')}
          >
            Voltar
          </Button>

          <Button
            onClick={handleContinue}
          >
            Ver Resumo
          </Button>
        </div>
      </div>
    </Layout>
  );
};

export default OptionsForm;
