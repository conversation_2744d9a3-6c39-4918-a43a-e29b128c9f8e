import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import AdminPanel from '../AdminPanel';
import { adminApiService } from '../../services/adminApi';
import { apiService } from '../../services/api';

// Mock the API services
jest.mock('../../services/adminApi');
jest.mock('../../services/api');

const mockAdminApiService = adminApiService as jest.Mocked<typeof adminApiService>;
const mockApiService = apiService as jest.Mocked<typeof apiService>;

// Mock data
const mockAdmin = {
  id: 1,
  name: 'Test Admin',
  email: '<EMAIL>',
  role: 'admin',
  status: 'active'
};

const mockDashboardStats = {
  overview: {
    totalRevenue: 50000,
    totalOrders: 100,
    totalCustomers: 75
  },
  orderStats: {
    byStatus: {
      pending: 10,
      processing: 5,
      completed: 80,
      cancelled: 5
    }
  },
  systemHealth: {
    uptime: 86400,
    memoryUsage: 65.5,
    databaseConnections: 5
  }
};

const mockOrders = [
  {
    id: 'order_12345678',
    userId: 'user_123',
    status: 'pending',
    totalAmount: 500,
    paymentStatus: 'pending',
    printJobs: [{
      id: 1,
      fileName: 'document1.pdf',
      fileSize: 1024,
      pages: 5,
      copies: 1,
      colorMode: 'bw',
      paperSize: 'A4',
      paperType: 'standard',
      doubleSided: false
    }],
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z'
  },
  {
    id: 'order_87654321',
    userId: 'user_456',
    status: 'delivered',
    totalAmount: 750,
    paymentStatus: 'paid',
    printJobs: [
      {
        id: 2,
        fileName: 'document2.pdf',
        fileSize: 2048,
        pages: 3,
        copies: 1,
        colorMode: 'color',
        paperSize: 'A4',
        paperType: 'standard',
        doubleSided: false
      },
      {
        id: 3,
        fileName: 'document3.pdf',
        fileSize: 1536,
        pages: 2,
        copies: 2,
        colorMode: 'bw',
        paperSize: 'A4',
        paperType: 'premium',
        doubleSided: true
      }
    ],
    createdAt: '2024-01-14T15:30:00Z',
    updatedAt: '2024-01-14T16:00:00Z'
  }
];

const mockPayments = [
  {
    id: 'payment_12345678',
    orderId: 1,
    amount: 500,
    status: 'pending',
    method: 'MULTICAIXA_EXPRESS',
    createdAt: '2024-01-15T10:00:00Z'
  },
  {
    id: 'payment_87654321',
    orderId: 2,
    amount: 750,
    status: 'completed',
    method: 'MULTICAIXA_EXPRESS',
    createdAt: '2024-01-14T15:30:00Z'
  }
];

const renderAdminPanel = () => {
  return render(
    <BrowserRouter>
      <AdminPanel />
    </BrowserRouter>
  );
};

describe('AdminPanel', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Setup default mock responses
    mockAdminApiService.isAuthenticated.mockReturnValue(false);
    mockAdminApiService.getStoredAdmin.mockReturnValue(null);

    mockAdminApiService.login.mockResolvedValue({
      success: true,
      data: {
        admin: mockAdmin,
        token: 'mock-token'
      }
    });

    mockAdminApiService.getDashboardStats.mockResolvedValue({
      success: true,
      data: mockDashboardStats
    });

    mockAdminApiService.getOrders.mockResolvedValue({
      success: true,
      data: {
        orders: mockOrders,
        total: 2,
        page: 1,
        totalPages: 1
      }
    });

    mockApiService.get.mockResolvedValue({
      success: true,
      data: mockPayments
    });
  });

  describe('Authentication', () => {
    it('should render login form initially', () => {
      renderAdminPanel();

      expect(screen.getByText('Painel Administrativo')).toBeInTheDocument();
      expect(screen.getByText('Faça login para acessar o painel')).toBeInTheDocument();
      expect(screen.getByPlaceholderText('Email')).toBeInTheDocument();
      expect(screen.getByPlaceholderText('Senha')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /entrar/i })).toBeInTheDocument();
    });

    it('should handle successful login', async () => {
      renderAdminPanel();

      const emailInput = screen.getByPlaceholderText('Email');
      const passwordInput = screen.getByPlaceholderText('Senha');
      const loginButton = screen.getByRole('button', { name: /entrar/i });

      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.change(passwordInput, { target: { value: 'admin123' } });
      fireEvent.click(loginButton);

      await waitFor(() => {
        expect(mockAdminApiService.login).toHaveBeenCalledWith({
          email: '<EMAIL>',
          password: 'admin123'
        });
      });

      await waitFor(() => {
        expect(screen.getByText('Logado como: Test Admin (<EMAIL>)')).toBeInTheDocument();
        expect(screen.getByRole('button', { name: /sair/i })).toBeInTheDocument();
      });
    });

    it('should handle login failure', async () => {
      mockAdminApiService.login.mockResolvedValue({
        success: false,
        error: 'Credenciais inválidas'
      });

      // Mock window.alert
      const alertSpy = jest.spyOn(window, 'alert').mockImplementation(() => {});

      renderAdminPanel();

      const emailInput = screen.getByPlaceholderText('Email');
      const passwordInput = screen.getByPlaceholderText('Senha');
      const loginButton = screen.getByRole('button', { name: /entrar/i });

      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.change(passwordInput, { target: { value: 'wrongpassword' } });
      fireEvent.click(loginButton);

      await waitFor(() => {
        expect(alertSpy).toHaveBeenCalledWith('Credenciais inválidas');
      });

      alertSpy.mockRestore();
    });

    it('should handle logout', async () => {
      mockAdminApiService.logout.mockResolvedValue({ success: true });

      renderAdminPanel();

      // Login first
      const emailInput = screen.getByPlaceholderText('Email');
      const passwordInput = screen.getByPlaceholderText('Senha');
      const loginButton = screen.getByRole('button', { name: /entrar/i });

      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.change(passwordInput, { target: { value: 'admin123' } });
      fireEvent.click(loginButton);

      await waitFor(() => {
        expect(screen.getByRole('button', { name: /sair/i })).toBeInTheDocument();
      });

      // Logout
      const logoutButton = screen.getByRole('button', { name: /sair/i });
      fireEvent.click(logoutButton);

      await waitFor(() => {
        expect(mockAdminApiService.logout).toHaveBeenCalled();
        expect(screen.getByText('Faça login para acessar o painel')).toBeInTheDocument();
      });
    });
  });

  describe('Dashboard Overview', () => {
    beforeEach(async () => {
      renderAdminPanel();

      // Login
      const emailInput = screen.getByPlaceholderText('Email');
      const passwordInput = screen.getByPlaceholderText('Senha');
      const loginButton = screen.getByRole('button', { name: /entrar/i });

      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.change(passwordInput, { target: { value: 'admin123' } });
      fireEvent.click(loginButton);

      await waitFor(() => {
        expect(screen.getByText('Logado como: Test Admin (<EMAIL>)')).toBeInTheDocument();
      });
    });

    it('should display dashboard statistics', async () => {
      await waitFor(() => {
        expect(screen.getByText('Receita Total')).toBeInTheDocument();
        expect(screen.getByText('50 000 Kz')).toBeInTheDocument();
        expect(screen.getByText('Pagamentos Concluídos')).toBeInTheDocument();
        expect(screen.getByText('80')).toBeInTheDocument();
      });
    });

    it('should display navigation tabs', () => {
      expect(screen.getByRole('button', { name: /visão geral/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /pagamentos/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /pedidos/i })).toBeInTheDocument();
    });

    it('should switch between tabs', async () => {
      const paymentsTab = screen.getByRole('button', { name: /pagamentos/i });
      fireEvent.click(paymentsTab);

      await waitFor(() => {
        expect(screen.getByText('Gestão de Pagamentos')).toBeInTheDocument();
      });

      const ordersTab = screen.getByRole('button', { name: /pedidos/i });
      fireEvent.click(ordersTab);

      await waitFor(() => {
        expect(screen.getByText('Gestão de Pedidos')).toBeInTheDocument();
      });
    });
  });

  describe('Orders Management', () => {
    beforeEach(async () => {
      renderAdminPanel();

      // Login and navigate to orders tab
      const emailInput = screen.getByPlaceholderText('Email');
      const passwordInput = screen.getByPlaceholderText('Senha');
      const loginButton = screen.getByRole('button', { name: /entrar/i });

      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.change(passwordInput, { target: { value: 'admin123' } });
      fireEvent.click(loginButton);

      await waitFor(() => {
        expect(screen.getByRole('button', { name: /pedidos/i })).toBeInTheDocument();
      });

      const ordersTab = screen.getByRole('button', { name: /pedidos/i });
      fireEvent.click(ordersTab);
    });

    it('should display orders list', async () => {
      await waitFor(() => {
        expect(screen.getByText('Gestão de Pedidos')).toBeInTheDocument();
        expect(screen.getByText('Pedido #12345678')).toBeInTheDocument();
        expect(screen.getByText('Pedido #87654321')).toBeInTheDocument();
      });
    });

    it('should display order status badges', async () => {
      await waitFor(() => {
        // Check for status badges specifically (not payment status)
        const statusBadges = screen.getAllByText('pending');
        expect(statusBadges.length).toBeGreaterThan(0);
        expect(screen.getByText('delivered')).toBeInTheDocument();
      });
    });

    it('should display order prices', async () => {
      await waitFor(() => {
        expect(screen.getByText('500 Kz')).toBeInTheDocument();
        expect(screen.getByText('750 Kz')).toBeInTheDocument();
      });
    });
  });

  describe('Payments Management', () => {
    beforeEach(async () => {
      renderAdminPanel();

      // Login and navigate to payments tab
      const emailInput = screen.getByPlaceholderText('Email');
      const passwordInput = screen.getByPlaceholderText('Senha');
      const loginButton = screen.getByRole('button', { name: /entrar/i });

      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.change(passwordInput, { target: { value: 'admin123' } });
      fireEvent.click(loginButton);

      await waitFor(() => {
        expect(screen.getByRole('button', { name: /pagamentos/i })).toBeInTheDocument();
      });

      const paymentsTab = screen.getByRole('button', { name: /pagamentos/i });
      fireEvent.click(paymentsTab);
    });

    it('should display payments list', async () => {
      await waitFor(() => {
        expect(screen.getByText('Gestão de Pagamentos')).toBeInTheDocument();
        expect(screen.getByText('#12345678')).toBeInTheDocument();
        expect(screen.getByText('#87654321')).toBeInTheDocument();
      });
    });

    it('should display payment status', async () => {
      await waitFor(() => {
        expect(screen.getByText('pending')).toBeInTheDocument();
        expect(screen.getByText('completed')).toBeInTheDocument();
      });
    });
  });

  describe('Loading States', () => {
    it('should show loading spinner during login', async () => {
      // Mock a delayed response
      mockAdminApiService.login.mockImplementation(() => 
        new Promise(resolve => setTimeout(() => resolve({
          success: true,
          data: { admin: mockAdmin, token: 'mock-token' }
        }), 100))
      );

      renderAdminPanel();

      const emailInput = screen.getByPlaceholderText('Email');
      const passwordInput = screen.getByPlaceholderText('Senha');
      const loginButton = screen.getByRole('button', { name: /entrar/i });

      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.change(passwordInput, { target: { value: 'admin123' } });
      fireEvent.click(loginButton);

      // Should show loading state (spinner)
      expect(document.querySelector('.animate-spin')).toBeInTheDocument();

      await waitFor(() => {
        expect(screen.getByText('Logado como: Test Admin (<EMAIL>)')).toBeInTheDocument();
      });
    });

    it('should show loading spinner during data fetch', async () => {
      // Mock delayed dashboard stats
      mockAdminApiService.getDashboardStats.mockImplementation(() =>
        new Promise(resolve => setTimeout(() => resolve({
          success: true,
          data: mockDashboardStats
        }), 100))
      );

      renderAdminPanel();

      // Login
      const emailInput = screen.getByPlaceholderText('Email');
      const passwordInput = screen.getByPlaceholderText('Senha');
      const loginButton = screen.getByRole('button', { name: /entrar/i });

      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.change(passwordInput, { target: { value: 'admin123' } });
      fireEvent.click(loginButton);

      // Should show loading spinner
      await waitFor(() => {
        expect(document.querySelector('.animate-spin')).toBeInTheDocument();
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle dashboard stats loading error', async () => {
      mockAdminApiService.getDashboardStats.mockRejectedValue(new Error('Network error'));

      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

      renderAdminPanel();

      // Login
      const emailInput = screen.getByPlaceholderText('Email');
      const passwordInput = screen.getByPlaceholderText('Senha');
      const loginButton = screen.getByRole('button', { name: /entrar/i });

      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.change(passwordInput, { target: { value: 'admin123' } });
      fireEvent.click(loginButton);

      await waitFor(() => {
        expect(consoleSpy).toHaveBeenCalledWith('Error loading dashboard data:', expect.any(Error));
      });

      consoleSpy.mockRestore();
    });

    it('should handle orders loading error', async () => {
      mockAdminApiService.getOrders.mockRejectedValue(new Error('Network error'));

      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

      renderAdminPanel();

      // Login
      const emailInput = screen.getByPlaceholderText('Email');
      const passwordInput = screen.getByPlaceholderText('Senha');
      const loginButton = screen.getByRole('button', { name: /entrar/i });

      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.change(passwordInput, { target: { value: 'admin123' } });
      fireEvent.click(loginButton);

      await waitFor(() => {
        expect(consoleSpy).toHaveBeenCalledWith('Error loading dashboard data:', expect.any(Error));
      });

      consoleSpy.mockRestore();
    });
  });
});
