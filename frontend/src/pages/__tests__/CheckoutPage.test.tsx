import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { BrowserRouter } from 'react-router-dom';
import CheckoutPage from '../CheckoutPage';

// Mock the API service
jest.mock('../../services/api', () => ({
  createOrder: jest.fn(),
  calculatePrice: jest.fn(),
}));

// Mock react-router-dom
const mockNavigate = jest.fn();
const mockLocation = {
  state: {
    fileId: 1,
    options: {
      format: 'A4',
      paperType: 'standard',
      finish: 'none',
      copies: 1,
      pages: 5
    }
  }
};

jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
  useLocation: () => mockLocation,
}));

const renderWithRouter = (component: React.ReactElement) => {
  return render(
    <BrowserRouter>
      {component}
    </BrowserRouter>
  );
};

describe('CheckoutPage Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    const { calculatePrice } = require('../../services/api');
    calculatePrice.mockResolvedValue({
      data: {
        price: {
          total: 1500,
          breakdown: {
            base: 1000,
            paperType: 200,
            finish: 100,
            copies: 200
          }
        }
      }
    });
  });

  it('renders checkout form correctly', async () => {
    renderWithRouter(<CheckoutPage />);
    
    await waitFor(() => {
      expect(screen.getByText(/informações do cliente/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/nome completo/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/email/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/telefone/i)).toBeInTheDocument();
    });
  });

  it('displays order summary', async () => {
    renderWithRouter(<CheckoutPage />);
    
    await waitFor(() => {
      expect(screen.getByText(/resumo do pedido/i)).toBeInTheDocument();
      expect(screen.getByText(/formato: a4/i)).toBeInTheDocument();
      expect(screen.getByText(/papel: standard/i)).toBeInTheDocument();
      expect(screen.getByText(/cópias: 1/i)).toBeInTheDocument();
    });
  });

  it('calculates and displays price', async () => {
    renderWithRouter(<CheckoutPage />);
    
    await waitFor(() => {
      expect(screen.getByText(/1\.500/)).toBeInTheDocument();
      expect(screen.getByText(/aoa/i)).toBeInTheDocument();
    });
  });

  it('validates required fields', async () => {
    renderWithRouter(<CheckoutPage />);
    
    await waitFor(() => {
      const submitButton = screen.getByRole('button', { name: /finalizar pedido/i });
      fireEvent.click(submitButton);
    });
    
    await waitFor(() => {
      expect(screen.getByText(/nome é obrigatório/i)).toBeInTheDocument();
      expect(screen.getByText(/email é obrigatório/i)).toBeInTheDocument();
      expect(screen.getByText(/telefone é obrigatório/i)).toBeInTheDocument();
    });
  });

  it('validates email format', async () => {
    renderWithRouter(<CheckoutPage />);
    
    await waitFor(() => {
      const emailInput = screen.getByLabelText(/email/i);
      fireEvent.change(emailInput, { target: { value: 'invalid-email' } });
      fireEvent.blur(emailInput);
    });
    
    await waitFor(() => {
      expect(screen.getByText(/email inválido/i)).toBeInTheDocument();
    });
  });

  it('validates phone format', async () => {
    renderWithRouter(<CheckoutPage />);
    
    await waitFor(() => {
      const phoneInput = screen.getByLabelText(/telefone/i);
      fireEvent.change(phoneInput, { target: { value: '123' } });
      fireEvent.blur(phoneInput);
    });
    
    await waitFor(() => {
      expect(screen.getByText(/telefone inválido/i)).toBeInTheDocument();
    });
  });

  it('shows payment method options', async () => {
    renderWithRouter(<CheckoutPage />);
    
    await waitFor(() => {
      expect(screen.getByText(/método de pagamento/i)).toBeInTheDocument();
      expect(screen.getByText(/pagamento na entrega/i)).toBeInTheDocument();
      expect(screen.getByText(/multicaixa express/i)).toBeInTheDocument();
      expect(screen.getByText(/transferência bancária/i)).toBeInTheDocument();
    });
  });

  it('handles cash on delivery selection', async () => {
    renderWithRouter(<CheckoutPage />);
    
    await waitFor(() => {
      const cashOption = screen.getByDisplayValue('CASH_ON_DELIVERY');
      expect(cashOption).toBeChecked(); // Should be default
      
      const submitButton = screen.getByRole('button', { name: /finalizar pedido/i });
      expect(submitButton).toBeInTheDocument();
    });
  });

  it('handles other payment method selection', async () => {
    renderWithRouter(<CheckoutPage />);
    
    await waitFor(() => {
      const multicaixaOption = screen.getByDisplayValue('MULTICAIXA_EXPRESS');
      fireEvent.click(multicaixaOption);
      
      const submitButton = screen.getByRole('button', { name: /continuar para pagamento/i });
      expect(submitButton).toBeInTheDocument();
    });
  });

  it('submits order successfully with cash on delivery', async () => {
    const { createOrder } = require('../../services/api');
    createOrder.mockResolvedValue({
      data: {
        order: {
          id: 1,
          orderNumber: 'WP-001',
          status: 'pending'
        }
      }
    });

    renderWithRouter(<CheckoutPage />);
    
    await waitFor(() => {
      fireEvent.change(screen.getByLabelText(/nome completo/i), {
        target: { value: 'João Silva' }
      });
      fireEvent.change(screen.getByLabelText(/email/i), {
        target: { value: '<EMAIL>' }
      });
      fireEvent.change(screen.getByLabelText(/telefone/i), {
        target: { value: '+244923456789' }
      });
    });
    
    const submitButton = screen.getByRole('button', { name: /finalizar pedido/i });
    fireEvent.click(submitButton);
    
    await waitFor(() => {
      expect(createOrder).toHaveBeenCalledWith(expect.objectContaining({
        fileId: 1,
        customerName: 'João Silva',
        customerEmail: '<EMAIL>',
        customerPhone: '+244923456789',
        paymentMethod: 'CASH_ON_DELIVERY'
      }));
    });
  });

  it('handles order creation error', async () => {
    const { createOrder } = require('../../services/api');
    createOrder.mockRejectedValue(new Error('Order creation failed'));

    renderWithRouter(<CheckoutPage />);
    
    await waitFor(() => {
      fireEvent.change(screen.getByLabelText(/nome completo/i), {
        target: { value: 'João Silva' }
      });
      fireEvent.change(screen.getByLabelText(/email/i), {
        target: { value: '<EMAIL>' }
      });
      fireEvent.change(screen.getByLabelText(/telefone/i), {
        target: { value: '+244923456789' }
      });
    });
    
    const submitButton = screen.getByRole('button', { name: /finalizar pedido/i });
    fireEvent.click(submitButton);
    
    await waitFor(() => {
      expect(screen.getByText(/erro ao criar pedido/i)).toBeInTheDocument();
    });
  });

  it('shows loading state during submission', async () => {
    const { createOrder } = require('../../services/api');
    createOrder.mockImplementation(() => 
      new Promise(resolve => setTimeout(() => resolve({ data: { order: { id: 1 } } }), 100))
    );

    renderWithRouter(<CheckoutPage />);
    
    await waitFor(() => {
      fireEvent.change(screen.getByLabelText(/nome completo/i), {
        target: { value: 'João Silva' }
      });
      fireEvent.change(screen.getByLabelText(/email/i), {
        target: { value: '<EMAIL>' }
      });
      fireEvent.change(screen.getByLabelText(/telefone/i), {
        target: { value: '+244923456789' }
      });
    });
    
    const submitButton = screen.getByRole('button', { name: /finalizar pedido/i });
    fireEvent.click(submitButton);
    
    expect(screen.getByText(/processando/i)).toBeInTheDocument();
    expect(submitButton).toBeDisabled();
  });

  it('allows adding notes', async () => {
    renderWithRouter(<CheckoutPage />);
    
    await waitFor(() => {
      const notesTextarea = screen.getByLabelText(/observações/i);
      fireEvent.change(notesTextarea, {
        target: { value: 'Entrega urgente por favor' }
      });
      
      expect(notesTextarea).toHaveValue('Entrega urgente por favor');
    });
  });
});
