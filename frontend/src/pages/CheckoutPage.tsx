import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { PaymentFlow } from '../components';
import { MulticaixaService } from '../services/multicaixa';
import { apiService } from '../services/api';

interface CheckoutFormData {
  name: string;
  deliveryAddress: string;
  phone: string;
  email: string;
  paymentMethod: 'MULTICAIXA_EXPRESS' | 'CASH_ON_DELIVERY' | 'BANK_TRANSFER';
}

const CheckoutPage: React.FC = () => {
  const navigate = useNavigate();
  const [step, setStep] = useState<'form' | 'payment' | 'success'>('form');
  const [formData, setFormData] = useState<CheckoutFormData>({
    name: '',
    deliveryAddress: '',
    phone: '',
    email: '',
    paymentMethod: 'CASH_ON_DELIVERY',
  });
  const [orderId, setOrderId] = useState<string>('');
  const [orderAmount, setOrderAmount] = useState<number>(0);

  // Load order amount from localStorage on component mount
  React.useEffect(() => {
    const storedOptions = localStorage.getItem('printOptions');
    if (storedOptions) {
      try {
        const options = JSON.parse(storedOptions);
        // Calculate total based on stored options
        const basePrice = 50; // Base price per page in AOA
        const pages = options.pages || 1;
        const copies = options.copies || 1;
        const colorMultiplier = options.color === 'color' ? 2 : 1;
        const qualityMultiplier = options.quality === 'high' ? 1.5 : 1;

        const total = Math.round(basePrice * pages * copies * colorMultiplier * qualityMultiplier);
        setOrderAmount(total);
      } catch (error) {
        console.error('Error parsing stored options:', error);
        setOrderAmount(100); // Fallback amount
      }
    } else {
      setOrderAmount(100); // Default amount if no options stored
    }
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmitForm = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form
    if (!formData.name || !formData.phone || !formData.email) {
      alert('Por favor, preencha todos os campos obrigatórios.');
      return;
    }

    try {
      // Get file and print options from localStorage
      const backendFileInfo = localStorage.getItem('backendFileInfo');
      const printOptions = localStorage.getItem('printOptions');

      if (!backendFileInfo || !printOptions) {
        alert('Dados do arquivo ou opções de impressão não encontrados. Reinicie o processo.');
        navigate('/upload');
        return;
      }

      const fileInfo = JSON.parse(backendFileInfo);
      const options = JSON.parse(printOptions);

      // Create order through backend API
      const orderData = {
        fileId: fileInfo.id,
        customerName: formData.name,
        customerEmail: formData.email,
        customerPhone: formData.phone,
        format: options.format,
        paperType: options.paperType,
        finish: options.finish,
        copies: options.copies,
        notes: `Endereço de entrega: ${formData.deliveryAddress}`
      };

      const orderResponse = await apiService.post('/orders', orderData);

      if (!orderResponse.success) {
        throw new Error(orderResponse.error || 'Erro ao criar pedido');
      }

      const createdOrder = orderResponse.data as any;
      setOrderId(createdOrder.id.toString());

      // Store order info for payment
      localStorage.setItem('currentOrder', JSON.stringify(createdOrder));

      // Handle payment method
      if (formData.paymentMethod === 'MULTICAIXA_EXPRESS') {
        setStep('payment');
      } else if (formData.paymentMethod === 'CASH_ON_DELIVERY') {
        // For cash on delivery, go directly to success
        setStep('success');
      } else {
        // Handle other payment methods
        alert('Método de pagamento não implementado ainda.');
      }
    } catch (error) {
      console.error('Error creating order:', error);
      alert(`Erro ao criar pedido: ${error instanceof Error ? error.message : 'Tente novamente.'}`);
    }
  };

  const handlePaymentComplete = (paymentId: string) => {
    console.log('Payment completed:', paymentId);
    setStep('success');
  };

  const handlePaymentFailed = (error: string) => {
    console.error('Payment failed:', error);
    alert(`Erro no pagamento: ${error}`);
    setStep('form');
  };

  const handleBackToForm = () => {
    setStep('form');
  };

  const handleGoHome = () => {
    navigate('/');
  };

  // Form Step
  if (step === 'form') {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-md mx-auto">
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-2xl font-semibold text-gray-900 mb-6 text-center">
              Finalizar Pedido
            </h2>

            {/* Order Summary */}
            <div className="mb-6 p-4 bg-blue-50 rounded-lg">
              <h3 className="font-medium text-blue-900 mb-2">Resumo do Pedido</h3>
              <div className="flex justify-between text-sm text-blue-800">
                <span>Total:</span>
                <span className="font-semibold">
                  {MulticaixaService.formatAoaAmount(orderAmount)}
                </span>
              </div>
            </div>

            <form onSubmit={handleSubmitForm} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Nome Completo *
                </label>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Seu nome completo"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Email *
                </label>
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="<EMAIL>"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Telefone *
                </label>
                <input
                  type="tel"
                  name="phone"
                  value={formData.phone}
                  onChange={handleInputChange}
                  className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="+244 900 000 000"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Endereço de Entrega
                </label>
                <input
                  type="text"
                  name="deliveryAddress"
                  value={formData.deliveryAddress}
                  onChange={handleInputChange}
                  className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Endereço para entrega (opcional)"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Método de Pagamento
                </label>
                <select
                  name="paymentMethod"
                  value={formData.paymentMethod}
                  onChange={handleInputChange}
                  className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="CASH_ON_DELIVERY">💰 Pagamento na Entrega (Recomendado)</option>
                  <option value="MULTICAIXA_EXPRESS">📱 Multicaixa Express</option>
                  <option value="BANK_TRANSFER">🏦 Transferência Bancária</option>
                </select>
              </div>

              <button
                type="submit"
                className="w-full bg-blue-600 text-white py-3 px-4 rounded-md font-medium hover:bg-blue-700 transition-colors"
              >
                {formData.paymentMethod === 'CASH_ON_DELIVERY' ? 'Finalizar Pedido' : 'Continuar para Pagamento'}
              </button>
            </form>
          </div>
        </div>
      </div>
    );
  }

  // Payment Step
  if (step === 'payment') {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-md mx-auto">
          <div className="mb-4">
            <button
              onClick={handleBackToForm}
              className="flex items-center gap-2 text-blue-600 hover:text-blue-700"
            >
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
              </svg>
              Voltar
            </button>
          </div>

          <PaymentFlow
            orderId={orderId}
            amount={orderAmount}
            currency="AOA"
            description="Serviços de Impressão WePrint AI"
            customerEmail={formData.email}
            customerPhone={formData.phone}
            onPaymentComplete={handlePaymentComplete}
            onPaymentFailed={handlePaymentFailed}
            onCancel={handleBackToForm}
          />
        </div>
      </div>
    );
  }

  // Success Step
  if (step === 'success') {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-md mx-auto">
          <div className="bg-white rounded-lg shadow-lg p-6 text-center">
            <svg className="w-20 h-20 text-green-500 mx-auto mb-4" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>

            <h2 className="text-2xl font-semibold text-gray-900 mb-2">
              Pedido Confirmado!
            </h2>

            <p className="text-gray-600 mb-2">
              Obrigado, {formData.name}!
            </p>

            <p className="text-sm text-gray-500 mb-6">
              {formData.paymentMethod === 'CASH_ON_DELIVERY'
                ? 'O seu pedido foi registado com sucesso. Prepare o pagamento para a entrega. Receberá uma confirmação por email em breve.'
                : 'O seu pedido foi processado com sucesso. Receberá uma confirmação por email em breve.'
              }
            </p>

            <div className="space-y-3">
              <button
                onClick={handleGoHome}
                className="w-full bg-blue-600 text-white py-3 px-4 rounded-md font-medium hover:bg-blue-700 transition-colors"
              >
                Voltar ao Início
              </button>

              <button
                onClick={() => navigate('/orders')}
                className="w-full border border-gray-300 text-gray-700 py-3 px-4 rounded-md font-medium hover:bg-gray-50 transition-colors"
              >
                Ver Meus Pedidos
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return null;
};

export default CheckoutPage;
