import React from 'react';

const CheckoutPage: React.FC = () => (
  <div className="flex flex-col items-center justify-center min-h-screen">
    <h2 className="text-2xl font-semibold mb-4">Checkout</h2>
    <form className="space-y-4">
      <input className="block w-64 p-2 border rounded" placeholder="Nome" />
      <input className="block w-64 p-2 border rounded" placeholder="Endereço de Entrega" />
      <input className="block w-64 p-2 border rounded" placeholder="Telefone" />
      <select className="block w-64 p-2 border rounded">
        <option>Método de Pagamento</option>
        <option>Multicaixa</option>
        <option>Mobile Money</option>
        <option>PayPal</option>
        <option>Cartão</option>
      </select>
      <button className="px-4 py-2 bg-green-600 text-white rounded">Confirmar</button>
    </form>
  </div>
);

export default CheckoutPage;
