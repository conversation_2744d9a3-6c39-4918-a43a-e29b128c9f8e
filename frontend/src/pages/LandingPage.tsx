import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Layout, Button } from '../components';

const LandingPage: React.FC = () => {
  const navigate = useNavigate();

  const handleStartOrder = () => {
    navigate('/upload');
  };

  const handleLogin = () => {
    navigate('/painel');
  };

  return (
    <Layout>
      <div className="flex flex-col items-center justify-center min-h-[60vh] text-center">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-5xl font-bold text-gray-900 mb-6">
            WePrint AI
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            A gráfica inteligente que vai até ti. Impressão profissional com tecnologia AI para otimizar qualidade e custos.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Button
              onClick={handleStartOrder}
              size="lg"
              className="w-full sm:w-auto"
            >
              Começar Encomenda
            </Button>
            <Button
              onClick={handleLogin}
              variant="outline"
              size="lg"
              className="w-full sm:w-auto"
            >
              Login/Cadastro
            </Button>
          </div>
        </div>

        <div className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
          <div className="text-center">
            <div className="bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
              <span className="text-2xl">🚀</span>
            </div>
            <h3 className="text-lg font-semibold mb-2">Rápido e Fácil</h3>
            <p className="text-gray-600">Upload, configure e receba em casa</p>
          </div>

          <div className="text-center">
            <div className="bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
              <span className="text-2xl">🤖</span>
            </div>
            <h3 className="text-lg font-semibold mb-2">Tecnologia AI</h3>
            <p className="text-gray-600">Otimização automática de qualidade</p>
          </div>

          <div className="text-center">
            <div className="bg-purple-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
              <span className="text-2xl">📦</span>
            </div>
            <h3 className="text-lg font-semibold mb-2">Entrega Grátis</h3>
            <p className="text-gray-600">Levamos até à sua porta</p>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default LandingPage;
