import React, { useEffect } from 'react';
import HeroSection from '../components/landing/HeroSection';
import ServicesSection from '../components/landing/ServicesSection';
import HowItWorksSection from '../components/landing/HowItWorksSection';
import BenefitsSection from '../components/landing/BenefitsSection';
import CTASection from '../components/landing/CTASection';
import Footer from '../components/landing/Footer';

const LandingPage: React.FC = () => {
  useEffect(() => {
    // Set page title
    document.title = 'WePrint - Impressão Digital de Qualidade em Angola';

    // Add meta description
    const metaDescription = document.querySelector('meta[name="description"]');
    if (metaDescription) {
      metaDescription.setAttribute('content', 'Serviço de impressão digital profissional em Angola. Upload online, qualidade premium, entrega rápida. Documentos, fotos, apresentações e mais.');
    } else {
      const meta = document.createElement('meta');
      meta.name = 'description';
      meta.content = 'Serviço de impressão digital profissional em Angola. Upload online, qualidade premium, entrega rápida. Documentos, fotos, apresentações e mais.';
      document.head.appendChild(meta);
    }

    // Add structured data for SEO
    const structuredData = {
      "@context": "https://schema.org",
      "@type": "LocalBusiness",
      "name": "WePrint",
      "description": "Serviço de impressão digital profissional em Angola",
      "url": "https://weprint.ai",
      "telephone": "+244900000000",
      "address": {
        "@type": "PostalAddress",
        "addressLocality": "Luanda",
        "addressCountry": "AO"
      },
      "openingHours": "Mo-Fr 08:00-18:00",
      "priceRange": "$$",
      "aggregateRating": {
        "@type": "AggregateRating",
        "ratingValue": "4.9",
        "reviewCount": "150"
      }
    };

    const script = document.createElement('script');
    script.type = 'application/ld+json';
    script.text = JSON.stringify(structuredData);
    document.head.appendChild(script);

    return () => {
      // Cleanup
      document.head.removeChild(script);
    };
  }, []);

  return (
    <div className="min-h-screen bg-white">
      {/* Navigation Bar */}
      <nav className="fixed top-0 left-0 right-0 z-50 bg-white bg-opacity-95 backdrop-blur-sm shadow-lg">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            {/* Logo */}
            <div className="flex items-center">
              <div className="text-2xl font-black text-weprint-black">
                WE<span className="text-weprint-magenta">PRINT</span>
              </div>
            </div>

            {/* Navigation Links */}
            <div className="hidden md:flex items-center space-x-8">
              <a
                href="#services"
                className="text-gray-700 hover:text-weprint-magenta transition-colors duration-300 font-medium"
                onClick={(e) => {
                  e.preventDefault();
                  document.getElementById('services')?.scrollIntoView({ behavior: 'smooth' });
                }}
              >
                Serviços
              </a>
              <a
                href="#how-it-works"
                className="text-gray-700 hover:text-weprint-cyan transition-colors duration-300 font-medium"
                onClick={(e) => {
                  e.preventDefault();
                  document.getElementById('how-it-works')?.scrollIntoView({ behavior: 'smooth' });
                }}
              >
                Como Funciona
              </a>
              <a
                href="#benefits"
                className="text-gray-700 hover:text-weprint-yellow transition-colors duration-300 font-medium"
                onClick={(e) => {
                  e.preventDefault();
                  document.getElementById('benefits')?.scrollIntoView({ behavior: 'smooth' });
                }}
              >
                Vantagens
              </a>
              <button
                onClick={() => window.location.href = '/upload'}
                className="bg-weprint-gradient text-white font-bold px-6 py-2 rounded-full hover:shadow-lg transition-all duration-300"
              >
                Imprimir Agora
              </button>
            </div>

            {/* Mobile Menu Button */}
            <div className="md:hidden">
              <button
                onClick={() => window.location.href = '/upload'}
                className="bg-weprint-gradient text-white font-bold px-4 py-2 rounded-full text-sm"
              >
                Imprimir
              </button>
            </div>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main>
        <HeroSection />

        <div id="services">
          <ServicesSection />
        </div>

        <div id="how-it-works">
          <HowItWorksSection />
        </div>

        <div id="benefits">
          <BenefitsSection />
        </div>

        <CTASection />
      </main>

      <Footer />

      {/* Floating WhatsApp Button */}
      <div className="fixed bottom-6 right-6 z-50">
        <button
          onClick={() => window.open('https://wa.me/244900000000', '_blank')}
          className="w-14 h-14 bg-green-500 text-white rounded-full shadow-2xl hover:shadow-3xl transform hover:scale-110 transition-all duration-300 flex items-center justify-center animate-bounce-gentle"
          title="Falar no WhatsApp"
        >
          <span className="text-2xl">💬</span>
        </button>
      </div>

      {/* Scroll to Top Button */}
      <div className="fixed bottom-6 left-6 z-50">
        <button
          onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
          className="w-12 h-12 bg-weprint-magenta text-white rounded-full shadow-lg hover:shadow-xl transform hover:scale-110 transition-all duration-300 flex items-center justify-center opacity-80 hover:opacity-100"
          title="Voltar ao topo"
        >
          <span className="text-lg">↑</span>
        </button>
      </div>
    </div>
  );
};

export default LandingPage;
