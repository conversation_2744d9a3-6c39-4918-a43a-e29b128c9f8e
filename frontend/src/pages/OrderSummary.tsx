import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Layout, Button } from '../components';

interface FileInfo {
  name: string;
  size: string;
  type: string;
}

interface PrintOptions {
  format: string;
  paperType: string;
  finish: string;
  copies: number;
  hasColor: boolean;
  complexity: 'low' | 'medium' | 'high';
  notes: string;
}

interface PriceBreakdown {
  basePrice: number;
  paperCost: number;
  finishCost: number;
  complexityCost: number;
  colorCost: number;
  total: number;
}

const OrderSummary: React.FC = () => {
  const navigate = useNavigate();
  const [fileInfo, setFileInfo] = useState<FileInfo | null>(null);
  const [printOptions, setPrintOptions] = useState<PrintOptions | null>(null);
  const [priceBreakdown, setPriceBreakdown] = useState<PriceBreakdown | null>(null);

  useEffect(() => {
    // Get data from localStorage
    const file = localStorage.getItem('uploadedFile');
    const options = localStorage.getItem('printOptions');
    const price = localStorage.getItem('priceBreakdown');

    if (!file || !options || !price) {
      // Redirect to upload if missing data
      navigate('/upload');
      return;
    }

    setFileInfo(JSON.parse(file));
    setPrintOptions(JSON.parse(options));
    setPriceBreakdown(JSON.parse(price));
  }, [navigate]);

  const formatPrice = (price: number) => {
    return `${price.toFixed(2)} AOA`;
  };

  const getFormatDisplayName = (format: string) => {
    const formats: { [key: string]: string } = {
      'A4': 'A4 (210 × 297 mm)',
      'A3': 'A3 (297 × 420 mm)',
      'A5': 'A5 (148 × 210 mm)',
      'Letter': 'Letter (216 × 279 mm)',
    };
    return formats[format] || format;
  };

  const getPaperDisplayName = (paperType: string) => {
    const papers: { [key: string]: string } = {
      'standard': 'Papel Standard (75g/m²)',
      'premium': 'Papel Premium (90g/m²)',
      'photo': 'Papel Fotográfico (200g/m²)',
      'cardstock': 'Cartolina (250g/m²)',
    };
    return papers[paperType] || paperType;
  };

  const getFinishDisplayName = (finish: string) => {
    const finishes: { [key: string]: string } = {
      'none': 'Sem Acabamento',
      'glossy': 'Brilhante',
      'matte': 'Fosco',
      'laminated': 'Plastificado',
    };
    return finishes[finish] || finish;
  };

  const getComplexityDisplayName = (complexity: string) => {
    const complexities: { [key: string]: string } = {
      'low': 'Baixa (Texto simples)',
      'medium': 'Média (Texto + Imagens)',
      'high': 'Alta (Design complexo)',
    };
    return complexities[complexity] || complexity;
  };

  const handleContinue = () => {
    navigate('/checkout');
  };

  const handleEditOptions = () => {
    navigate('/options');
  };

  if (!fileInfo || !printOptions || !priceBreakdown) {
    return (
      <Layout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Carregando resumo...</p>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="max-w-4xl mx-auto py-8">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Resumo do Pedido
          </h1>
          <p className="text-lg text-gray-600">
            Revise os detalhes antes de finalizar
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* File Information */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">
              Arquivo
            </h2>

            <div className="flex items-center space-x-4 mb-6">
              <div className="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center">
                {fileInfo.type.startsWith('image/') ? (
                  <svg className="w-8 h-8 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
                  </svg>
                ) : (
                  <svg className="w-8 h-8 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clipRule="evenodd" />
                  </svg>
                )}
              </div>

              <div className="flex-1">
                <p className="font-medium text-gray-900">{fileInfo.name}</p>
                <p className="text-sm text-gray-600">{fileInfo.size}</p>
              </div>
            </div>

            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Formato:</span>
                <span className="font-medium">{getFormatDisplayName(printOptions.format)}</span>
              </div>

              <div className="flex justify-between">
                <span className="text-gray-600">Papel:</span>
                <span className="font-medium">{getPaperDisplayName(printOptions.paperType)}</span>
              </div>

              <div className="flex justify-between">
                <span className="text-gray-600">Acabamento:</span>
                <span className="font-medium">{getFinishDisplayName(printOptions.finish)}</span>
              </div>

              <div className="flex justify-between">
                <span className="text-gray-600">Cópias:</span>
                <span className="font-medium">{printOptions.copies}</span>
              </div>

              <div className="flex justify-between">
                <span className="text-gray-600">Cores:</span>
                <span className="font-medium">
                  {printOptions.hasColor ? 'Colorido' : 'Preto e Branco'}
                </span>
              </div>

              <div className="flex justify-between">
                <span className="text-gray-600">Complexidade:</span>
                <span className="font-medium">{getComplexityDisplayName(printOptions.complexity)}</span>
              </div>

              {printOptions.notes && (
                <div className="pt-3 border-t">
                  <span className="text-gray-600 block mb-1">Observações:</span>
                  <p className="text-sm text-gray-800 bg-gray-50 p-3 rounded">
                    {printOptions.notes}
                  </p>
                </div>
              )}
            </div>

            <Button
              variant="outline"
              onClick={handleEditOptions}
              className="w-full mt-6"
            >
              Editar Opções
            </Button>
          </div>

          {/* Price Breakdown */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">
              Detalhes do Preço
            </h2>

            <div className="space-y-4">
              <div className="flex justify-between">
                <span className="text-gray-600">Preço base ({printOptions.format}):</span>
                <span>{formatPrice(priceBreakdown.basePrice)}</span>
              </div>

              {priceBreakdown.paperCost > 0 && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Papel premium:</span>
                  <span className="text-green-600">+{formatPrice(priceBreakdown.paperCost)}</span>
                </div>
              )}

              {priceBreakdown.finishCost > 0 && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Acabamento:</span>
                  <span className="text-green-600">+{formatPrice(priceBreakdown.finishCost)}</span>
                </div>
              )}

              {priceBreakdown.complexityCost > 0 && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Complexidade:</span>
                  <span className="text-green-600">+{formatPrice(priceBreakdown.complexityCost)}</span>
                </div>
              )}

              {priceBreakdown.colorCost > 0 && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Impressão a cores:</span>
                  <span className="text-green-600">+{formatPrice(priceBreakdown.colorCost)}</span>
                </div>
              )}

              <div className="flex justify-between">
                <span className="text-gray-600">Quantidade:</span>
                <span>{printOptions.copies} cópia(s)</span>
              </div>

              <hr className="my-4" />

              <div className="flex justify-between text-xl font-bold">
                <span>Total:</span>
                <span className="text-blue-600">{formatPrice(priceBreakdown.total)}</span>
              </div>
            </div>

            <div className="mt-6 p-4 bg-green-50 rounded-lg">
              <h3 className="font-medium text-green-900 mb-2">✅ Incluído no Preço</h3>
              <ul className="text-sm text-green-800 space-y-1">
                <li>• Entrega grátis em Luanda</li>
                <li>• Embalagem protectora</li>
                <li>• Garantia de qualidade</li>
                <li>• Suporte técnico</li>
              </ul>
            </div>

            <div className="mt-4 p-4 bg-blue-50 rounded-lg">
              <h3 className="font-medium text-blue-900 mb-2">📅 Prazo de Entrega</h3>
              <p className="text-sm text-blue-800">
                {printOptions.complexity === 'high' ? '4-5 dias úteis' :
                 printOptions.complexity === 'medium' ? '2-3 dias úteis' :
                 '1-2 dias úteis'}
              </p>
            </div>
          </div>
        </div>

        <div className="flex justify-between mt-8">
          <Button
            variant="outline"
            onClick={() => navigate('/options')}
          >
            Voltar às Opções
          </Button>

          <Button
            onClick={handleContinue}
            size="lg"
          >
            Finalizar Pedido
          </Button>
        </div>
      </div>
    </Layout>
  );
};

export default OrderSummary;
