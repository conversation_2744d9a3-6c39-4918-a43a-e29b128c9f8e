// Utility functions for the WePrint AI application

/**
 * Format currency values
 */
export const formatCurrency = (amount: number, currency: string = 'EUR'): string => {
  return new Intl.NumberFormat('pt-PT', {
    style: 'currency',
    currency,
  }).format(amount);
};

/**
 * Format dates
 */
export const formatDate = (date: string | Date, options?: Intl.DateTimeFormatOptions): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  const defaultOptions: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  };
  
  return new Intl.DateTimeFormat('pt-PT', { ...defaultOptions, ...options }).format(dateObj);
};

/**
 * Format file sizes
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * Validate email format
 */
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * Generate unique IDs
 */
export const generateId = (): string => {
  return Math.random().toString(36).substr(2, 9);
};

/**
 * Debounce function
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

/**
 * Calculate print job price based on options
 */
export const calculatePrintPrice = (
  pages: number,
  copies: number,
  options: {
    colorMode: 'color' | 'grayscale' | 'blackwhite';
    paperSize: 'A4' | 'A3' | 'Letter' | 'Legal';
    paperType: 'standard' | 'premium' | 'photo';
    doubleSided: boolean;
    binding?: 'none' | 'staple' | 'spiral' | 'perfect';
  }
): number => {
  // Base prices (in cents)
  const basePrices = {
    blackwhite: { A4: 5, A3: 10, Letter: 5, Legal: 7 },
    grayscale: { A4: 7, A3: 14, Letter: 7, Legal: 10 },
    color: { A4: 15, A3: 30, Letter: 15, Legal: 20 },
  };
  
  const paperTypeMultiplier = {
    standard: 1,
    premium: 1.5,
    photo: 2,
  };
  
  const bindingCosts = {
    none: 0,
    staple: 50,
    spiral: 200,
    perfect: 500,
  };
  
  const basePrice = basePrices[options.colorMode][options.paperSize];
  const paperMultiplier = paperTypeMultiplier[options.paperType];
  const doubleSidedDiscount = options.doubleSided ? 0.8 : 1;
  const bindingCost = bindingCosts[options.binding || 'none'];
  
  const printCost = pages * copies * basePrice * paperMultiplier * doubleSidedDiscount;
  const totalCost = printCost + bindingCost;
  
  return Math.round(totalCost) / 100; // Convert to euros
};

/**
 * Get file extension from filename
 */
export const getFileExtension = (filename: string): string => {
  return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2);
};

/**
 * Check if file type is supported
 */
export const isSupportedFileType = (filename: string): boolean => {
  const supportedTypes = ['pdf', 'doc', 'docx', 'txt', 'jpg', 'jpeg', 'png'];
  const extension = getFileExtension(filename).toLowerCase();
  return supportedTypes.includes(extension);
};
