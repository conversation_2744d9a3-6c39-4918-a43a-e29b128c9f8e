import { apiService } from './api';
import { 
  MulticaixaPaymentRequest, 
  MulticaixaPaymentResponse, 
  MulticaixaStatusResponse,
  Payment,
  ApiResponse 
} from '../types';

/**
 * MulticaixaService - Frontend service for Multicaixa Express payment integration
 * Handles communication with backend Multicaixa APIs
 */
export class MulticaixaService {
  
  /**
   * Create a new Multicaixa payment
   */
  static async createPayment(
    paymentData: MulticaixaPaymentRequest
  ): Promise<MulticaixaPaymentResponse> {
    try {
      const response = await apiService.post<{
        paymentId: string;
        paymentUrl: string;
        qrCode: string;
        transactionId: string;
        expiresAt: string;
      }>('/payments/create', paymentData);

      if (response.success && response.data) {
        return {
          success: true,
          paymentId: response.data.paymentId,
          paymentUrl: response.data.paymentUrl,
          qrCode: response.data.qrCode,
          transactionId: response.data.transactionId,
          expiresAt: response.data.expiresAt,
        };
      } else {
        return {
          success: false,
          error: response.error || 'Failed to create payment',
        };
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Network error',
      };
    }
  }

  /**
   * Check payment status manually
   */
  static async checkPaymentStatus(paymentId: string): Promise<MulticaixaStatusResponse> {
    try {
      const response = await apiService.post<{
        status: string;
        transactionId: string;
      }>(`/payments/${paymentId}/check-status`);

      if (response.success && response.data) {
        return {
          success: true,
          status: response.data.status as any,
          transactionId: response.data.transactionId,
        };
      } else {
        return {
          success: false,
          error: response.error || 'Failed to check payment status',
        };
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Network error',
      };
    }
  }

  /**
   * Get payment details by ID
   */
  static async getPayment(paymentId: string): Promise<ApiResponse<Payment>> {
    try {
      return await apiService.get<Payment>(`/payments/${paymentId}`);
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Network error',
      };
    }
  }

  /**
   * Get all payments for an order
   */
  static async getOrderPayments(orderId: string): Promise<ApiResponse<Payment[]>> {
    try {
      return await apiService.get<Payment[]>(`/orders/${orderId}/payments`);
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Network error',
      };
    }
  }

  /**
   * Request a refund for a payment
   */
  static async requestRefund(
    paymentId: string, 
    amount?: number, 
    reason?: string
  ): Promise<ApiResponse<{ refundId: string; status: string }>> {
    try {
      return await apiService.post<{ refundId: string; status: string }>(
        `/payments/${paymentId}/refund`,
        { amount, reason }
      );
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Network error',
      };
    }
  }

  /**
   * Convert EUR to AOA using current exchange rate
   */
  static convertEurToAoa(eurAmount: number, exchangeRate: number = 850): number {
    return Math.round(eurAmount * exchangeRate);
  }

  /**
   * Convert AOA to EUR using current exchange rate
   */
  static convertAoaToEur(aoaAmount: number, exchangeRate: number = 850): number {
    return Math.round((aoaAmount / exchangeRate) * 100) / 100;
  }

  /**
   * Format AOA amount for display
   */
  static formatAoaAmount(amount: number): string {
    return new Intl.NumberFormat('pt-AO', {
      style: 'currency',
      currency: 'AOA',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  }

  /**
   * Validate payment amount (minimum 100 AOA)
   */
  static validatePaymentAmount(amount: number, currency: string = 'AOA'): boolean {
    if (currency === 'AOA') {
      return amount >= 100;
    }
    // For EUR, convert to AOA first
    const aoaAmount = this.convertEurToAoa(amount);
    return aoaAmount >= 100;
  }

  /**
   * Get payment status display text
   */
  static getStatusDisplayText(status: string): string {
    const statusMap: Record<string, string> = {
      'PENDING': 'Pendente',
      'PROCESSING': 'Processando',
      'COMPLETED': 'Concluído',
      'FAILED': 'Falhado',
      'CANCELLED': 'Cancelado',
      'REFUNDED': 'Reembolsado',
      'EXPIRED': 'Expirado',
    };
    return statusMap[status] || status;
  }

  /**
   * Get payment status color for UI
   */
  static getStatusColor(status: string): string {
    const colorMap: Record<string, string> = {
      'PENDING': 'text-yellow-600',
      'PROCESSING': 'text-blue-600',
      'COMPLETED': 'text-green-600',
      'FAILED': 'text-red-600',
      'CANCELLED': 'text-gray-600',
      'REFUNDED': 'text-purple-600',
      'EXPIRED': 'text-orange-600',
    };
    return colorMap[status] || 'text-gray-600';
  }

  /**
   * Check if payment is in a final state
   */
  static isPaymentFinal(status: string): boolean {
    return ['COMPLETED', 'FAILED', 'CANCELLED', 'REFUNDED', 'EXPIRED'].includes(status);
  }

  /**
   * Check if payment can be refunded
   */
  static canRefund(status: string): boolean {
    return status === 'COMPLETED';
  }
}

export default MulticaixaService;
