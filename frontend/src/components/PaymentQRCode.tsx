import React, { useState } from 'react';

interface PaymentQRCodeProps {
  qrCode: string;
  paymentUrl: string;
  amount: number;
  currency?: string;
  expiresAt?: string;
  className?: string;
}

const PaymentQRCode: React.FC<PaymentQRCodeProps> = ({
  qrCode,
  paymentUrl,
  amount,
  currency = 'AOA',
  expiresAt,
  className = ''
}) => {
  const [copied, setCopied] = useState(false);

  const handleCopyUrl = async () => {
    try {
      await navigator.clipboard.writeText(paymentUrl);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Failed to copy URL:', error);
    }
  };

  const formatAmount = (amount: number, currency: string) => {
    if (currency === 'AOA') {
      return new Intl.NumberFormat('pt-AO', {
        style: 'currency',
        currency: 'AOA',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0,
      }).format(amount);
    }
    return `${amount} ${currency}`;
  };

  const formatExpiryTime = (expiresAt: string) => {
    const expiryDate = new Date(expiresAt);
    const now = new Date();
    const diffMs = expiryDate.getTime() - now.getTime();
    
    if (diffMs <= 0) {
      return 'Expirado';
    }

    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMinutes / 60);
    
    if (diffHours > 0) {
      return `Expira em ${diffHours}h ${diffMinutes % 60}m`;
    }
    return `Expira em ${diffMinutes}m`;
  };

  return (
    <div className={`bg-white rounded-lg shadow-lg p-6 max-w-md mx-auto ${className}`}>
      {/* Header */}
      <div className="text-center mb-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          Pagamento Multicaixa Express
        </h3>
        <p className="text-2xl font-bold text-blue-600">
          {formatAmount(amount, currency)}
        </p>
        {expiresAt && (
          <p className="text-sm text-orange-600 mt-1">
            {formatExpiryTime(expiresAt)}
          </p>
        )}
      </div>

      {/* QR Code */}
      <div className="flex justify-center mb-6">
        <div className="bg-white p-4 rounded-lg border-2 border-gray-200">
          <img 
            src={qrCode} 
            alt="QR Code de Pagamento" 
            className="w-48 h-48 object-contain"
          />
        </div>
      </div>

      {/* Instructions */}
      <div className="text-center mb-6">
        <p className="text-sm text-gray-600 mb-2">
          Escaneie o código QR com a app Multicaixa Express
        </p>
        <p className="text-xs text-gray-500">
          ou use o link de pagamento abaixo
        </p>
      </div>

      {/* Payment URL */}
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Link de Pagamento:
        </label>
        <div className="flex items-center gap-2">
          <input
            type="text"
            value={paymentUrl}
            readOnly
            className="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm bg-gray-50 text-gray-600"
          />
          <button
            onClick={handleCopyUrl}
            className={`px-3 py-2 text-sm font-medium rounded-md transition-colors ${
              copied
                ? 'bg-green-100 text-green-700 border border-green-300'
                : 'bg-blue-100 text-blue-700 border border-blue-300 hover:bg-blue-200'
            }`}
          >
            {copied ? (
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
            ) : (
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z" />
                <path d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z" />
              </svg>
            )}
          </button>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex gap-3">
        <a
          href={paymentUrl}
          target="_blank"
          rel="noopener noreferrer"
          className="flex-1 bg-blue-600 text-white text-center py-2 px-4 rounded-md font-medium hover:bg-blue-700 transition-colors"
        >
          Abrir Multicaixa
        </a>
        <button
          onClick={handleCopyUrl}
          className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md font-medium hover:bg-gray-50 transition-colors"
        >
          Copiar Link
        </button>
      </div>

      {/* Security Notice */}
      <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
        <div className="flex items-start gap-2">
          <svg className="w-5 h-5 text-yellow-600 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
          <div>
            <p className="text-sm font-medium text-yellow-800">Importante</p>
            <p className="text-xs text-yellow-700 mt-1">
              Não partilhe este código QR ou link com terceiros. Use apenas a app oficial Multicaixa Express.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PaymentQRCode;
