import React, { useState, useEffect } from 'react';
import { useMulticaixa, usePaymentStatusPolling } from '../hooks/useMulticaixa';
import { MulticaixaService } from '../services/multicaixa';
import PaymentStatus from './PaymentStatus';
import PaymentQRCode from './PaymentQRCode';
import { MulticaixaPaymentRequest } from '../types';

interface PaymentFlowProps {
  orderId: string;
  amount: number;
  currency?: string;
  description?: string;
  customerEmail?: string;
  customerPhone?: string;
  onPaymentComplete?: (paymentId: string) => void;
  onPaymentFailed?: (error: string) => void;
  onCancel?: () => void;
  className?: string;
}

const PaymentFlow: React.FC<PaymentFlowProps> = ({
  orderId,
  amount,
  currency = 'AOA',
  description,
  customerEmail,
  customerPhone,
  onPaymentComplete,
  onPaymentFailed,
  onCancel,
  className = ''
}) => {
  const [paymentId, setPaymentId] = useState<string | null>(null);
  const [transactionId, setTransactionId] = useState<string | null>(null);
  const [expiresAt, setExpiresAt] = useState<string | null>(null);
  
  const {
    loading,
    error,
    paymentUrl,
    qrCode,
    status,
    createPayment,
    clearError,
    reset
  } = useMulticaixa();

  const {
    isPolling,
    status: pollingStatus,
    startPolling,
    stopPolling
  } = usePaymentStatusPolling(paymentId, 5000, 60);

  // Use polling status if available, otherwise use hook status
  const currentStatus = pollingStatus || status;

  useEffect(() => {
    // Handle payment completion
    if (currentStatus === 'COMPLETED' && paymentId) {
      stopPolling();
      onPaymentComplete?.(paymentId);
    }
    
    // Handle payment failure
    if (['FAILED', 'EXPIRED', 'CANCELLED'].includes(currentStatus || '')) {
      stopPolling();
      if (currentStatus === 'FAILED') {
        onPaymentFailed?.('Payment failed');
      } else if (currentStatus === 'EXPIRED') {
        onPaymentFailed?.('Payment expired');
      }
    }
  }, [currentStatus, paymentId, onPaymentComplete, onPaymentFailed, stopPolling]);

  const handleCreatePayment = async () => {
    clearError();
    
    // Validate amount
    if (!MulticaixaService.validatePaymentAmount(amount, currency)) {
      const minAmount = currency === 'AOA' ? '100 AOA' : '1 EUR (≈850 AOA)';
      onPaymentFailed?.(`Valor mínimo de pagamento: ${minAmount}`);
      return;
    }

    const paymentData: MulticaixaPaymentRequest = {
      orderId,
      amount,
      currency,
      description,
      customerEmail,
      customerPhone,
    };

    const response = await createPayment(paymentData);
    
    if (response.success) {
      setPaymentId(response.paymentId || null);
      setTransactionId(response.transactionId || null);
      setExpiresAt(response.expiresAt || null);
      
      // Start polling for status updates
      if (response.paymentId) {
        startPolling();
      }
    } else {
      onPaymentFailed?.(response.error || 'Failed to create payment');
    }
  };

  const handleCancel = () => {
    stopPolling();
    reset();
    setPaymentId(null);
    setTransactionId(null);
    setExpiresAt(null);
    onCancel?.();
  };

  const handleRetry = () => {
    reset();
    setPaymentId(null);
    setTransactionId(null);
    setExpiresAt(null);
    handleCreatePayment();
  };

  const formatAmount = (amount: number, currency: string) => {
    if (currency === 'AOA') {
      return MulticaixaService.formatAoaAmount(amount);
    }
    return `${amount} ${currency}`;
  };

  // Initial state - show payment details and create button
  if (!paymentId && !loading) {
    return (
      <div className={`bg-white rounded-lg shadow-lg p-6 max-w-md mx-auto ${className}`}>
        <div className="text-center mb-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            Confirmar Pagamento
          </h3>
          <p className="text-3xl font-bold text-blue-600 mb-2">
            {formatAmount(amount, currency)}
          </p>
          {description && (
            <p className="text-sm text-gray-600">{description}</p>
          )}
        </div>

        {error && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
            <p className="text-sm text-red-700">{error}</p>
          </div>
        )}

        <div className="flex gap-3">
          <button
            onClick={handleCreatePayment}
            disabled={loading}
            className="flex-1 bg-blue-600 text-white py-3 px-4 rounded-md font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {loading ? 'Criando...' : 'Pagar com Multicaixa'}
          </button>
          {onCancel && (
            <button
              onClick={handleCancel}
              className="px-4 py-3 border border-gray-300 text-gray-700 rounded-md font-medium hover:bg-gray-50 transition-colors"
            >
              Cancelar
            </button>
          )}
        </div>
      </div>
    );
  }

  // Loading state
  if (loading) {
    return (
      <div className={`bg-white rounded-lg shadow-lg p-6 max-w-md mx-auto ${className}`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Criando pagamento...</p>
        </div>
      </div>
    );
  }

  // Payment created - show QR code and status
  if (paymentUrl && qrCode) {
    return (
      <div className={`space-y-4 ${className}`}>
        {/* Status Header */}
        <div className="bg-white rounded-lg shadow-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-gray-900">
                Status do Pagamento
              </h3>
              {transactionId && (
                <p className="text-sm text-gray-500">ID: {transactionId}</p>
              )}
            </div>
            <PaymentStatus status={currentStatus || 'PENDING'} />
          </div>
          
          {isPolling && (
            <div className="mt-3 flex items-center gap-2 text-sm text-blue-600">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
              Verificando status...
            </div>
          )}
        </div>

        {/* QR Code */}
        {currentStatus !== 'COMPLETED' && (
          <PaymentQRCode
            qrCode={qrCode}
            paymentUrl={paymentUrl}
            amount={amount}
            currency={currency}
            expiresAt={expiresAt || undefined}
          />
        )}

        {/* Success Message */}
        {currentStatus === 'COMPLETED' && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-6 text-center">
            <svg className="w-16 h-16 text-green-500 mx-auto mb-4" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
            <h3 className="text-lg font-semibold text-green-900 mb-2">
              Pagamento Concluído!
            </h3>
            <p className="text-green-700">
              O seu pagamento foi processado com sucesso.
            </p>
          </div>
        )}

        {/* Error State */}
        {['FAILED', 'EXPIRED', 'CANCELLED'].includes(currentStatus || '') && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
            <svg className="w-16 h-16 text-red-500 mx-auto mb-4" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
            <h3 className="text-lg font-semibold text-red-900 mb-2">
              {currentStatus === 'EXPIRED' ? 'Pagamento Expirado' : 'Pagamento Falhado'}
            </h3>
            <p className="text-red-700 mb-4">
              {currentStatus === 'EXPIRED' 
                ? 'O tempo limite para pagamento foi excedido.'
                : 'Ocorreu um erro durante o processamento do pagamento.'
              }
            </p>
            <div className="flex gap-3 justify-center">
              <button
                onClick={handleRetry}
                className="bg-blue-600 text-white py-2 px-4 rounded-md font-medium hover:bg-blue-700 transition-colors"
              >
                Tentar Novamente
              </button>
              {onCancel && (
                <button
                  onClick={handleCancel}
                  className="border border-gray-300 text-gray-700 py-2 px-4 rounded-md font-medium hover:bg-gray-50 transition-colors"
                >
                  Cancelar
                </button>
              )}
            </div>
          </div>
        )}
      </div>
    );
  }

  return null;
};

export default PaymentFlow;
