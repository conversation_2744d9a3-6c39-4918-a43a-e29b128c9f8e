import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import LandingPage from './pages/LandingPage';
import UploadPage from './pages/UploadPage';
import PreviewPage from './pages/PreviewPage';
import OptionsForm from './pages/OptionsForm';
import OrderSummary from './pages/OrderSummary';
import CheckoutPage from './pages/CheckoutPage';
import ClientPanel from './pages/ClientPanel';
import AdminPanel from './pages/AdminPanel';

const App: React.FC = () => (
  <Router>
    <Routes>
      <Route path="/" element={<LandingPage />} />
      <Route path="/upload" element={<UploadPage />} />
      <Route path="/preview" element={<PreviewPage />} />
      <Route path="/options" element={<OptionsForm />} />
      <Route path="/summary" element={<OrderSummary />} />
      <Route path="/checkout" element={<CheckoutPage />} />
      <Route path="/painel" element={<ClientPanel />} />
      <Route path="/admin" element={<AdminPanel />} />
    </Routes>
  </Router>
);

export default App;
