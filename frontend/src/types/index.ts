// Common types for the WePrint AI application

export interface User {
  id: string;
  name: string;
  email: string;
  role: 'client' | 'admin';
  createdAt: string;
  updatedAt: string;
}

export interface PrintJob {
  id: string;
  userId: string;
  fileName: string;
  fileUrl: string;
  status: 'pending' | 'processing' | 'ready' | 'completed' | 'cancelled';
  options: PrintOptions;
  totalPrice: number;
  createdAt: string;
  updatedAt: string;
}

export interface PrintOptions {
  copies: number;
  paperSize: 'A4' | 'A3' | 'Letter' | 'Legal';
  orientation: 'portrait' | 'landscape';
  colorMode: 'color' | 'grayscale' | 'blackwhite';
  paperType: 'standard' | 'premium' | 'photo';
  binding?: 'none' | 'staple' | 'spiral' | 'perfect';
  doubleSided: boolean;
}

export interface Order {
  id: string;
  userId: string;
  printJobs: PrintJob[];
  totalAmount: number;
  status: 'pending' | 'confirmed' | 'in_progress' | 'ready' | 'delivered' | 'cancelled';
  deliveryAddress?: string;
  paymentStatus: 'pending' | 'paid' | 'failed' | 'refunded';
  createdAt: string;
  updatedAt: string;
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Payment System Types - Multicaixa Express
export interface Payment {
  id: string;
  orderId: string;
  amount: number;
  currency: string;
  status: PaymentStatus;
  paymentMethod: PaymentMethod;
  multicaixaTransactionId?: string;
  multicaixaPaymentUrl?: string;
  processingFee?: number;
  expiredAt?: string;
  createdAt: string;
  updatedAt: string;
}

export type PaymentStatus =
  | 'PENDING'
  | 'PROCESSING'
  | 'COMPLETED'
  | 'FAILED'
  | 'CANCELLED'
  | 'REFUNDED'
  | 'EXPIRED';

export type PaymentMethod =
  | 'MULTICAIXA_EXPRESS'
  | 'CASH'
  | 'BANK_TRANSFER';

export interface MulticaixaPaymentRequest {
  orderId: string;
  amount: number;
  currency?: string;
  description?: string;
  customerEmail?: string;
  customerPhone?: string;
}

export interface MulticaixaPaymentResponse {
  success: boolean;
  paymentId?: string;
  paymentUrl?: string;
  qrCode?: string;
  transactionId?: string;
  expiresAt?: string;
  error?: string;
}

export interface MulticaixaStatusResponse {
  success: boolean;
  status?: PaymentStatus;
  transactionId?: string;
  error?: string;
}
