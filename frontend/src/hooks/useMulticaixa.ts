import { useState, useCallback, useEffect } from 'react';
import { MulticaixaService } from '../services/multicaixa';
import { 
  MulticaixaPaymentRequest, 
  MulticaixaPaymentResponse, 
  Payment,
  PaymentStatus 
} from '../types';

interface UseMulticaixaState {
  loading: boolean;
  error: string | null;
  payment: Payment | null;
  paymentUrl: string | null;
  qrCode: string | null;
  status: PaymentStatus | null;
}

interface UseMulticaixaActions {
  createPayment: (data: MulticaixaPaymentRequest) => Promise<MulticaixaPaymentResponse>;
  checkStatus: (paymentId: string) => Promise<void>;
  requestRefund: (paymentId: string, amount?: number, reason?: string) => Promise<boolean>;
  clearError: () => void;
  reset: () => void;
}

/**
 * Custom hook for managing Multicaixa payments
 */
export const useMulticaixa = (): UseMulticaixaState & UseMulticaixaActions => {
  const [state, setState] = useState<UseMulticaixaState>({
    loading: false,
    error: null,
    payment: null,
    paymentUrl: null,
    qrCode: null,
    status: null,
  });

  /**
   * Create a new Multicaixa payment
   */
  const createPayment = useCallback(async (data: MulticaixaPaymentRequest): Promise<MulticaixaPaymentResponse> => {
    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const response = await MulticaixaService.createPayment(data);
      
      if (response.success) {
        setState(prev => ({
          ...prev,
          loading: false,
          paymentUrl: response.paymentUrl || null,
          qrCode: response.qrCode || null,
          status: 'PENDING',
        }));
      } else {
        setState(prev => ({
          ...prev,
          loading: false,
          error: response.error || 'Failed to create payment',
        }));
      }

      return response;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Network error';
      setState(prev => ({
        ...prev,
        loading: false,
        error: errorMessage,
      }));
      
      return {
        success: false,
        error: errorMessage,
      };
    }
  }, []);

  /**
   * Check payment status
   */
  const checkStatus = useCallback(async (paymentId: string): Promise<void> => {
    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const response = await MulticaixaService.checkPaymentStatus(paymentId);
      
      if (response.success && response.status) {
        setState(prev => ({
          ...prev,
          loading: false,
          status: response.status!,
        }));
      } else {
        setState(prev => ({
          ...prev,
          loading: false,
          error: response.error || 'Failed to check payment status',
        }));
      }
    } catch (error) {
      setState(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : 'Network error',
      }));
    }
  }, []);

  /**
   * Request a refund
   */
  const requestRefund = useCallback(async (
    paymentId: string, 
    amount?: number, 
    reason?: string
  ): Promise<boolean> => {
    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const response = await MulticaixaService.requestRefund(paymentId, amount, reason);
      
      if (response.success) {
        setState(prev => ({
          ...prev,
          loading: false,
          status: 'REFUNDED',
        }));
        return true;
      } else {
        setState(prev => ({
          ...prev,
          loading: false,
          error: response.error || 'Failed to request refund',
        }));
        return false;
      }
    } catch (error) {
      setState(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : 'Network error',
      }));
      return false;
    }
  }, []);

  /**
   * Clear error state
   */
  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }));
  }, []);

  /**
   * Reset all state
   */
  const reset = useCallback(() => {
    setState({
      loading: false,
      error: null,
      payment: null,
      paymentUrl: null,
      qrCode: null,
      status: null,
    });
  }, []);

  return {
    ...state,
    createPayment,
    checkStatus,
    requestRefund,
    clearError,
    reset,
  };
};

/**
 * Hook for polling payment status
 */
export const usePaymentStatusPolling = (
  paymentId: string | null,
  intervalMs: number = 5000,
  maxAttempts: number = 60
) => {
  const [attempts, setAttempts] = useState(0);
  const [isPolling, setIsPolling] = useState(false);
  const { checkStatus, status, loading, error } = useMulticaixa();

  useEffect(() => {
    if (!paymentId || MulticaixaService.isPaymentFinal(status || '')) {
      setIsPolling(false);
      return;
    }

    if (attempts >= maxAttempts) {
      setIsPolling(false);
      return;
    }

    setIsPolling(true);
    const interval = setInterval(async () => {
      await checkStatus(paymentId);
      setAttempts(prev => prev + 1);
    }, intervalMs);

    return () => {
      clearInterval(interval);
      setIsPolling(false);
    };
  }, [paymentId, status, attempts, maxAttempts, intervalMs, checkStatus]);

  const startPolling = useCallback(() => {
    setAttempts(0);
    setIsPolling(true);
  }, []);

  const stopPolling = useCallback(() => {
    setIsPolling(false);
  }, []);

  return {
    isPolling,
    attempts,
    maxAttempts,
    status,
    loading,
    error,
    startPolling,
    stopPolling,
  };
};

export default useMulticaixa;
