# README — Banco de Dados

Este diretório contém scripts de inicialização e migração do banco de dados PostgreSQL para o MVP WePrint AI.

## Estrutura

- `init.sql`: Criação das tabelas essenciais (`files`, `orders`, `status`).

## Como usar

- O banco será inicializado automaticamente pelo Docker Compose ao subir o serviço `db`.
- Para rodar manualmente:
  ```bash
  psql -U postgres -d weprint -f db/init.sql
  ```

## Observações
- Adicione scripts de migração incremental conforme o projeto evoluir.
- Documente alterações de schema neste diretório.
