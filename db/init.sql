# Estrutura inicial do banco de dados para o MVP WePrint AI

-- <PERSON>bela de ficheiros enviados
CREATE TABLE files (
    id SERIAL PRIMARY KEY,
    filename VARCHAR(255) NOT NULL,
    mimetype VARCHAR(100) NOT NULL,
    url TEXT NOT NULL,
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- <PERSON>bela de encomendas
CREATE TABLE orders (
    id SERIAL PRIMARY KEY,
    file_id INTEGER REFERENCES files(id),
    format VARCHAR(50),
    paper_type VARCHAR(50),
    finish VARCHAR(50),
    price NUMERIC(10,2),
    status VARCHAR(30) DEFAULT 'pendente',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabela de status (histórico)
CREATE TABLE status (
    id SERIAL PRIMARY KEY,
    order_id INTEGER REFERENCES orders(id),
    status VARCHAR(30),
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
