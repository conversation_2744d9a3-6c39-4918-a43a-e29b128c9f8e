-- ============================================================================
-- WePrint AI Database Schema
-- Estrutura inicial do banco de dados para o MVP WePrint AI
-- ============================================================================

-- Extensões necessárias
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- ============================================================================
-- Tabela de arquivos enviados
-- ============================================================================
CREATE TABLE files (
    id SERIAL PRIMARY KEY,
    filename VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    mimetype VARCHAR(100) NOT NULL,
    size INTEGER NOT NULL DEFAULT 0,
    url TEXT NOT NULL,
    uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE NULL,

    -- Índices
    CONSTRAINT files_filename_unique UNIQUE (filename),
    CONSTRAINT files_size_positive CHECK (size >= 0)
);

-- ============================================================================
-- Tabela de pedidos/encomendas
-- ============================================================================
CREATE TABLE orders (
    id SERIAL PRIMARY KEY,
    order_number VARCHAR(20) NOT NULL UNIQUE,
    file_id INTEGER NOT NULL REFERENCES files(id) ON DELETE RESTRICT,

    -- Informações do cliente
    customer_name VARCHAR(255),
    customer_email VARCHAR(255),
    customer_phone VARCHAR(20),

    -- Especificações de impressão
    format VARCHAR(20) NOT NULL DEFAULT 'A4',
    paper_type VARCHAR(20) NOT NULL DEFAULT 'standard',
    finish VARCHAR(20) NOT NULL DEFAULT 'none',
    copies INTEGER NOT NULL DEFAULT 1,
    pages INTEGER,

    -- Preço e status
    price NUMERIC(10,2) NOT NULL DEFAULT 0.00,
    status VARCHAR(30) NOT NULL DEFAULT 'pending',
    notes TEXT,

    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,

    -- Constraints
    CONSTRAINT orders_copies_positive CHECK (copies > 0),
    CONSTRAINT orders_pages_positive CHECK (pages IS NULL OR pages > 0),
    CONSTRAINT orders_price_positive CHECK (price >= 0),
    CONSTRAINT orders_format_valid CHECK (format IN ('A4', 'A3', 'A5', 'letter', 'legal', 'custom')),
    CONSTRAINT orders_paper_valid CHECK (paper_type IN ('standard', 'premium', 'photo', 'cardstock')),
    CONSTRAINT orders_finish_valid CHECK (finish IN ('none', 'laminated', 'spiral_bound', 'stapled')),
    CONSTRAINT orders_status_valid CHECK (status IN ('pending', 'confirmed', 'in_production', 'ready', 'delivered', 'cancelled'))
);

-- ============================================================================
-- Tabela de histórico de status
-- ============================================================================
CREATE TABLE status_history (
    id SERIAL PRIMARY KEY,
    order_id INTEGER NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
    status VARCHAR(30) NOT NULL,
    notes TEXT,
    updated_by VARCHAR(255),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,

    -- Constraints
    CONSTRAINT status_history_status_valid CHECK (status IN ('pending', 'confirmed', 'in_production', 'ready', 'delivered', 'cancelled'))
);

-- ============================================================================
-- Índices para performance
-- ============================================================================

-- Índices para files
CREATE INDEX idx_files_uploaded_at ON files(uploaded_at);
CREATE INDEX idx_files_mimetype ON files(mimetype);
CREATE INDEX idx_files_deleted_at ON files(deleted_at) WHERE deleted_at IS NOT NULL;

-- Índices para orders
CREATE INDEX idx_orders_file_id ON orders(file_id);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_orders_created_at ON orders(created_at);
CREATE INDEX idx_orders_customer_email ON orders(customer_email);
CREATE INDEX idx_orders_order_number ON orders(order_number);

-- Índices para status_history
CREATE INDEX idx_status_history_order_id ON status_history(order_id);
CREATE INDEX idx_status_history_updated_at ON status_history(updated_at);
CREATE INDEX idx_status_history_status ON status_history(status);

-- ============================================================================
-- Triggers para updated_at automático
-- ============================================================================

-- Função para atualizar updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger para orders
CREATE TRIGGER update_orders_updated_at
    BEFORE UPDATE ON orders
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- ============================================================================
-- Trigger para histórico de status automático
-- ============================================================================

-- Função para inserir no histórico quando status muda
CREATE OR REPLACE FUNCTION insert_status_history()
RETURNS TRIGGER AS $$
BEGIN
    -- Só insere se o status realmente mudou
    IF OLD.status IS DISTINCT FROM NEW.status THEN
        INSERT INTO status_history (order_id, status, notes, updated_by)
        VALUES (NEW.id, NEW.status, NEW.notes, 'system');
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger para orders
CREATE TRIGGER insert_status_history_trigger
    AFTER UPDATE ON orders
    FOR EACH ROW
    EXECUTE FUNCTION insert_status_history();

-- ============================================================================
-- Dados iniciais para desenvolvimento
-- ============================================================================

-- Inserir status inicial para novos pedidos
CREATE OR REPLACE FUNCTION insert_initial_status()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO status_history (order_id, status, notes, updated_by)
    VALUES (NEW.id, NEW.status, 'Pedido criado', 'system');
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger para novos pedidos
CREATE TRIGGER insert_initial_status_trigger
    AFTER INSERT ON orders
    FOR EACH ROW
    EXECUTE FUNCTION insert_initial_status();

-- ============================================================================
-- Views úteis
-- ============================================================================

-- View para pedidos com informações do arquivo
CREATE VIEW orders_with_files AS
SELECT
    o.*,
    f.filename,
    f.original_name,
    f.mimetype,
    f.size as file_size,
    f.uploaded_at as file_uploaded_at
FROM orders o
JOIN files f ON o.file_id = f.id
WHERE f.deleted_at IS NULL;

-- View para últimos status dos pedidos
CREATE VIEW orders_latest_status AS
SELECT DISTINCT ON (order_id)
    order_id,
    status,
    notes,
    updated_by,
    updated_at
FROM status_history
ORDER BY order_id, updated_at DESC;

-- ============================================================================
-- Comentários nas tabelas
-- ============================================================================

COMMENT ON TABLE files IS 'Arquivos enviados pelos usuários para impressão';
COMMENT ON TABLE orders IS 'Pedidos de impressão com especificações e status';
COMMENT ON TABLE status_history IS 'Histórico de mudanças de status dos pedidos';

COMMENT ON COLUMN files.filename IS 'Nome único do arquivo no sistema';
COMMENT ON COLUMN files.original_name IS 'Nome original do arquivo enviado pelo usuário';
COMMENT ON COLUMN files.size IS 'Tamanho do arquivo em bytes';
COMMENT ON COLUMN files.deleted_at IS 'Data de exclusão lógica do arquivo';

COMMENT ON COLUMN orders.order_number IS 'Número único do pedido para referência';
COMMENT ON COLUMN orders.copies IS 'Número de cópias a serem impressas';
COMMENT ON COLUMN orders.pages IS 'Número de páginas do documento (calculado automaticamente)';
COMMENT ON COLUMN orders.price IS 'Preço total do pedido em centavos';

-- ============================================================================
-- Fim do script
-- ============================================================================
