-- ============================================================================
-- Migration: Add Notifications System
-- Description: Creates tables and functions for the notification system
-- Version: 002
-- Date: 2024-12-19
-- ============================================================================

-- Create notification types enum
CREATE TYPE notification_type AS ENUM (
  'order_created',
  'order_confirmed', 
  'order_processing',
  'order_ready',
  'order_completed',
  'order_cancelled',
  'file_uploaded',
  'payment_received',
  'system_alert'
);

-- Create notification channels enum
CREATE TYPE notification_channel AS ENUM (
  'email',
  'sms',
  'push',
  'in_app'
);

-- Create notification status enum
CREATE TYPE notification_status AS ENUM (
  'pending',
  'sent',
  'delivered',
  'failed',
  'read'
);

-- Create notifications table
CREATE TABLE notifications (
  id SERIAL PRIMARY KEY,
  type notification_type NOT NULL,
  channel notification_channel NOT NULL,
  status notification_status NOT NULL DEFAULT 'pending',
  
  -- Recipient information
  recipient_email VARCHAR(255) NOT NULL,
  recipient_name VARCHAR(255),
  
  -- Message content
  subject VARCHAR(500) NOT NULL,
  content TEXT NOT NULL,
  template_data JSONB,
  
  -- Related entities
  order_id INTEGER REFERENCES orders(id) ON DELETE SET NULL,
  file_id INTEGER REFERENCES files(id) ON DELETE SET NULL,
  
  -- Delivery tracking
  sent_at TIMESTAMP WITH TIME ZONE,
  delivered_at TIMESTAMP WITH TIME ZONE,
  read_at TIMESTAMP WITH TIME ZONE,
  
  -- Error handling
  error_message TEXT,
  retry_count INTEGER NOT NULL DEFAULT 0,
  max_retries INTEGER NOT NULL DEFAULT 3,
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create notification templates table
CREATE TABLE notification_templates (
  id VARCHAR(100) PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  type notification_type NOT NULL,
  channel notification_channel NOT NULL,
  
  -- Template content
  subject VARCHAR(500) NOT NULL,
  html_content TEXT NOT NULL,
  text_content TEXT NOT NULL,
  variables TEXT[] NOT NULL DEFAULT '{}',
  
  -- Status
  is_active BOOLEAN NOT NULL DEFAULT true,
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_notifications_status ON notifications(status);
CREATE INDEX idx_notifications_type ON notifications(type);
CREATE INDEX idx_notifications_channel ON notifications(channel);
CREATE INDEX idx_notifications_recipient_email ON notifications(recipient_email);
CREATE INDEX idx_notifications_order_id ON notifications(order_id);
CREATE INDEX idx_notifications_file_id ON notifications(file_id);
CREATE INDEX idx_notifications_created_at ON notifications(created_at);
CREATE INDEX idx_notifications_sent_at ON notifications(sent_at);
CREATE INDEX idx_notifications_pending_retry ON notifications(status, retry_count, max_retries) 
  WHERE status IN ('pending', 'failed');

-- Create indexes for templates
CREATE INDEX idx_notification_templates_type ON notification_templates(type);
CREATE INDEX idx_notification_templates_channel ON notification_templates(channel);
CREATE INDEX idx_notification_templates_active ON notification_templates(is_active);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_notification_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for notifications table
CREATE TRIGGER trigger_update_notification_updated_at
  BEFORE UPDATE ON notifications
  FOR EACH ROW
  EXECUTE FUNCTION update_notification_updated_at();

-- Create trigger for notification_templates table
CREATE TRIGGER trigger_update_notification_template_updated_at
  BEFORE UPDATE ON notification_templates
  FOR EACH ROW
  EXECUTE FUNCTION update_notification_updated_at();

-- Create function to automatically create notifications on order status changes
CREATE OR REPLACE FUNCTION create_order_status_notification()
RETURNS TRIGGER AS $$
BEGIN
  -- Only create notification if status actually changed
  IF OLD.status IS DISTINCT FROM NEW.status THEN
    INSERT INTO notifications (
      type,
      channel,
      recipient_email,
      recipient_name,
      subject,
      content,
      order_id,
      template_data
    ) VALUES (
      CASE NEW.status
        WHEN 'pending' THEN 'order_created'::notification_type
        WHEN 'confirmed' THEN 'order_confirmed'::notification_type
        WHEN 'processing' THEN 'order_processing'::notification_type
        WHEN 'ready' THEN 'order_ready'::notification_type
        WHEN 'completed' THEN 'order_completed'::notification_type
        WHEN 'cancelled' THEN 'order_cancelled'::notification_type
        ELSE 'order_created'::notification_type
      END,
      'email'::notification_channel,
      NEW.customer_email,
      NEW.customer_name,
      'Atualização do Pedido ' || NEW.order_number,
      'O status do seu pedido foi atualizado para: ' || NEW.status,
      NEW.id,
      jsonb_build_object(
        'orderNumber', NEW.order_number,
        'customerName', NEW.customer_name,
        'customerEmail', NEW.customer_email,
        'status', NEW.status,
        'price', NEW.price,
        'format', NEW.format,
        'paperType', NEW.paper_type,
        'finish', NEW.finish,
        'copies', NEW.copies,
        'estimatedCompletion', NEW.estimated_completion
      )
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for automatic order notifications
CREATE TRIGGER trigger_order_status_notification
  AFTER UPDATE ON orders
  FOR EACH ROW
  EXECUTE FUNCTION create_order_status_notification();

-- Create view for notification statistics
CREATE VIEW notification_stats AS
SELECT 
  type,
  channel,
  status,
  COUNT(*) as count,
  COUNT(*) FILTER (WHERE created_at >= CURRENT_DATE - INTERVAL '7 days') as count_last_7_days,
  COUNT(*) FILTER (WHERE created_at >= CURRENT_DATE - INTERVAL '30 days') as count_last_30_days,
  AVG(EXTRACT(EPOCH FROM (sent_at - created_at))) FILTER (WHERE sent_at IS NOT NULL) as avg_send_time_seconds,
  AVG(EXTRACT(EPOCH FROM (delivered_at - sent_at))) FILTER (WHERE delivered_at IS NOT NULL) as avg_delivery_time_seconds
FROM notifications
GROUP BY type, channel, status;

-- Create view for daily notification activity
CREATE VIEW daily_notification_activity AS
SELECT 
  DATE(created_at) as date,
  type,
  channel,
  COUNT(*) as total_created,
  COUNT(*) FILTER (WHERE status = 'sent') as total_sent,
  COUNT(*) FILTER (WHERE status = 'delivered') as total_delivered,
  COUNT(*) FILTER (WHERE status = 'failed') as total_failed,
  COUNT(*) FILTER (WHERE status = 'read') as total_read,
  ROUND(
    (COUNT(*) FILTER (WHERE status = 'delivered')::DECIMAL / 
     NULLIF(COUNT(*) FILTER (WHERE status = 'sent'), 0)) * 100, 2
  ) as delivery_rate_percent,
  ROUND(
    (COUNT(*) FILTER (WHERE status = 'read')::DECIMAL / 
     NULLIF(COUNT(*) FILTER (WHERE status = 'delivered'), 0)) * 100, 2
  ) as read_rate_percent
FROM notifications
WHERE created_at >= CURRENT_DATE - INTERVAL '90 days'
GROUP BY DATE(created_at), type, channel
ORDER BY date DESC, type, channel;

-- Insert default notification templates
INSERT INTO notification_templates (id, name, type, channel, subject, html_content, text_content, variables) VALUES
('order_created_email', 'Pedido Criado - Email', 'order_created', 'email', 
 'Pedido {{orderNumber}} - Recebido com Sucesso',
 '<h2>Pedido Recebido!</h2><p>Olá {{customerName}}, recebemos o seu pedido {{orderNumber}} com sucesso!</p>',
 'Pedido Recebido! Olá {{customerName}}, recebemos o seu pedido {{orderNumber}} com sucesso!',
 ARRAY['orderNumber', 'customerName', 'customerEmail', 'price', 'format']
),
('order_ready_email', 'Pedido Pronto - Email', 'order_ready', 'email',
 'Pedido {{orderNumber}} - Pronto para Levantamento',
 '<h2>Pedido Pronto!</h2><p>Olá {{customerName}}, o seu pedido {{orderNumber}} está pronto para levantamento!</p>',
 'Pedido Pronto! Olá {{customerName}}, o seu pedido {{orderNumber}} está pronto para levantamento!',
 ARRAY['orderNumber', 'customerName', 'customerEmail']
),
('order_completed_email', 'Pedido Concluído - Email', 'order_completed', 'email',
 'Pedido {{orderNumber}} - Concluído',
 '<h2>Pedido Concluído!</h2><p>Olá {{customerName}}, o seu pedido {{orderNumber}} foi concluído com sucesso!</p>',
 'Pedido Concluído! Olá {{customerName}}, o seu pedido {{orderNumber}} foi concluído com sucesso!',
 ARRAY['orderNumber', 'customerName', 'customerEmail']
);

-- Create function to clean up old notifications
CREATE OR REPLACE FUNCTION cleanup_old_notifications(days_old INTEGER DEFAULT 90)
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  DELETE FROM notifications 
  WHERE created_at < NOW() - (days_old || ' days')::INTERVAL
    AND status IN ('delivered', 'read', 'failed');
  
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Grant permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON notifications TO weprint_user;
GRANT SELECT, INSERT, UPDATE, DELETE ON notification_templates TO weprint_user;
GRANT USAGE ON SEQUENCE notifications_id_seq TO weprint_user;
GRANT SELECT ON notification_stats TO weprint_user;
GRANT SELECT ON daily_notification_activity TO weprint_user;

-- Add comments for documentation
COMMENT ON TABLE notifications IS 'Stores all notifications sent by the system';
COMMENT ON TABLE notification_templates IS 'Stores reusable notification templates';
COMMENT ON VIEW notification_stats IS 'Provides aggregated statistics for notifications';
COMMENT ON VIEW daily_notification_activity IS 'Provides daily activity metrics for notifications';
COMMENT ON FUNCTION cleanup_old_notifications IS 'Removes old notifications to keep the table size manageable';

-- Migration completed successfully
SELECT 'Notifications system migration completed successfully' as result;
