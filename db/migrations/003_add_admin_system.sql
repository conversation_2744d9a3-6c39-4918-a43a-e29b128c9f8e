-- ============================================================================
-- Migration 003: Admin System
-- Description: Creates admin users and activity logging system
-- ============================================================================

-- Create admin role enum
CREATE TYPE admin_role AS ENUM ('admin', 'super_admin');

-- Create admin status enum  
CREATE TYPE admin_status AS ENUM ('active', 'inactive', 'suspended');

-- ============================================================================
-- Admins Table
-- ============================================================================

CREATE TABLE admins (
    id SERIAL PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    password VARCHAR(255) NOT NULL,
    role admin_role NOT NULL DEFAULT 'admin',
    status admin_status NOT NULL DEFAULT 'active',
    last_login TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL
);

-- Indexes for admins table
CREATE INDEX idx_admins_email ON admins(email) WHERE deleted_at IS NULL;
CREATE INDEX idx_admins_role ON admins(role) WHERE deleted_at IS NULL;
CREATE INDEX idx_admins_status ON admins(status) WHERE deleted_at IS NULL;
CREATE INDEX idx_admins_created_at ON admins(created_at) WHERE deleted_at IS NULL;
CREATE INDEX idx_admins_deleted_at ON admins(deleted_at);

-- ============================================================================
-- Admin Activity Logs Table
-- ============================================================================

CREATE TABLE admin_activity_logs (
    id SERIAL PRIMARY KEY,
    admin_id INTEGER NOT NULL,
    admin_email VARCHAR(255) NOT NULL,
    action VARCHAR(100) NOT NULL,
    resource VARCHAR(100) NOT NULL,
    resource_id INTEGER NULL,
    details JSONB NULL,
    ip_address INET NULL,
    user_agent TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for admin activity logs
CREATE INDEX idx_admin_activity_logs_admin_id ON admin_activity_logs(admin_id);
CREATE INDEX idx_admin_activity_logs_action ON admin_activity_logs(action);
CREATE INDEX idx_admin_activity_logs_resource ON admin_activity_logs(resource);
CREATE INDEX idx_admin_activity_logs_created_at ON admin_activity_logs(created_at);
CREATE INDEX idx_admin_activity_logs_admin_email ON admin_activity_logs(admin_email);

-- Composite indexes for common queries
CREATE INDEX idx_admin_activity_logs_admin_action ON admin_activity_logs(admin_id, action);
CREATE INDEX idx_admin_activity_logs_resource_action ON admin_activity_logs(resource, action);
CREATE INDEX idx_admin_activity_logs_date_admin ON admin_activity_logs(created_at, admin_id);

-- ============================================================================
-- Triggers for Updated At
-- ============================================================================

-- Trigger function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply trigger to admins table
CREATE TRIGGER update_admins_updated_at 
    BEFORE UPDATE ON admins 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- ============================================================================
-- Views for Admin Statistics
-- ============================================================================

-- View for admin activity statistics
CREATE VIEW admin_activity_stats AS
SELECT 
    admin_id,
    admin_email,
    COUNT(*) as total_actions,
    COUNT(DISTINCT action) as unique_actions,
    COUNT(DISTINCT resource) as unique_resources,
    MIN(created_at) as first_activity,
    MAX(created_at) as last_activity,
    COUNT(CASE WHEN created_at >= CURRENT_DATE THEN 1 END) as actions_today,
    COUNT(CASE WHEN created_at >= CURRENT_DATE - INTERVAL '7 days' THEN 1 END) as actions_this_week,
    COUNT(CASE WHEN created_at >= CURRENT_DATE - INTERVAL '30 days' THEN 1 END) as actions_this_month
FROM admin_activity_logs
GROUP BY admin_id, admin_email;

-- View for daily admin activity
CREATE VIEW daily_admin_activity AS
SELECT 
    DATE(created_at) as activity_date,
    admin_id,
    admin_email,
    COUNT(*) as daily_actions,
    COUNT(DISTINCT action) as unique_actions,
    COUNT(DISTINCT resource) as unique_resources,
    array_agg(DISTINCT action ORDER BY action) as actions_performed
FROM admin_activity_logs
GROUP BY DATE(created_at), admin_id, admin_email
ORDER BY activity_date DESC, admin_id;

-- View for resource access patterns
CREATE VIEW resource_access_patterns AS
SELECT 
    resource,
    action,
    COUNT(*) as access_count,
    COUNT(DISTINCT admin_id) as unique_admins,
    MIN(created_at) as first_access,
    MAX(created_at) as last_access,
    COUNT(CASE WHEN created_at >= CURRENT_DATE - INTERVAL '7 days' THEN 1 END) as recent_access_count
FROM admin_activity_logs
GROUP BY resource, action
ORDER BY access_count DESC;

-- ============================================================================
-- Functions for Admin Management
-- ============================================================================

-- Function to get admin activity summary
CREATE OR REPLACE FUNCTION get_admin_activity_summary(
    p_admin_id INTEGER DEFAULT NULL,
    p_days INTEGER DEFAULT 30
)
RETURNS TABLE (
    admin_id INTEGER,
    admin_email VARCHAR(255),
    total_actions BIGINT,
    unique_actions BIGINT,
    unique_resources BIGINT,
    most_common_action TEXT,
    most_common_resource TEXT,
    last_activity TIMESTAMP
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        aal.admin_id,
        aal.admin_email,
        COUNT(*) as total_actions,
        COUNT(DISTINCT aal.action) as unique_actions,
        COUNT(DISTINCT aal.resource) as unique_resources,
        (
            SELECT action 
            FROM admin_activity_logs aal2 
            WHERE aal2.admin_id = aal.admin_id 
            AND aal2.created_at >= CURRENT_DATE - INTERVAL '1 day' * p_days
            GROUP BY action 
            ORDER BY COUNT(*) DESC 
            LIMIT 1
        ) as most_common_action,
        (
            SELECT resource 
            FROM admin_activity_logs aal3 
            WHERE aal3.admin_id = aal.admin_id 
            AND aal3.created_at >= CURRENT_DATE - INTERVAL '1 day' * p_days
            GROUP BY resource 
            ORDER BY COUNT(*) DESC 
            LIMIT 1
        ) as most_common_resource,
        MAX(aal.created_at) as last_activity
    FROM admin_activity_logs aal
    WHERE (p_admin_id IS NULL OR aal.admin_id = p_admin_id)
    AND aal.created_at >= CURRENT_DATE - INTERVAL '1 day' * p_days
    GROUP BY aal.admin_id, aal.admin_email
    ORDER BY total_actions DESC;
END;
$$ LANGUAGE plpgsql;

-- Function to cleanup old activity logs
CREATE OR REPLACE FUNCTION cleanup_old_admin_logs(days_to_keep INTEGER DEFAULT 365)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM admin_activity_logs 
    WHERE created_at < CURRENT_DATE - INTERVAL '1 day' * days_to_keep;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- ============================================================================
-- Security Constraints
-- ============================================================================

-- Ensure email format is valid
ALTER TABLE admins ADD CONSTRAINT check_email_format 
    CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$');

-- Ensure name is not empty
ALTER TABLE admins ADD CONSTRAINT check_name_not_empty 
    CHECK (LENGTH(TRIM(name)) > 0);

-- Ensure password is not empty (will be hashed)
ALTER TABLE admins ADD CONSTRAINT check_password_not_empty 
    CHECK (LENGTH(password) > 0);

-- Ensure action and resource are not empty in activity logs
ALTER TABLE admin_activity_logs ADD CONSTRAINT check_action_not_empty 
    CHECK (LENGTH(TRIM(action)) > 0);

ALTER TABLE admin_activity_logs ADD CONSTRAINT check_resource_not_empty 
    CHECK (LENGTH(TRIM(resource)) > 0);

-- ============================================================================
-- Default Data
-- ============================================================================

-- Insert default super admin (password: 'admin123' - should be changed immediately)
-- Password hash for 'admin123' using bcrypt with salt rounds 12
INSERT INTO admins (email, name, password, role, status) VALUES 
(
    '<EMAIL>', 
    'Super Administrator', 
    '$2b$12$LQv3c1yqBwEHFl4HqQNqyOEFQY9L5sFUjc5QjKjKjKjKjKjKjKjKj.', -- This should be properly hashed
    'super_admin', 
    'active'
);

-- ============================================================================
-- Comments and Documentation
-- ============================================================================

COMMENT ON TABLE admins IS 'Administrative users with access to the admin panel';
COMMENT ON COLUMN admins.email IS 'Unique email address for admin login';
COMMENT ON COLUMN admins.name IS 'Display name of the administrator';
COMMENT ON COLUMN admins.password IS 'Bcrypt hashed password';
COMMENT ON COLUMN admins.role IS 'Administrative role determining permissions';
COMMENT ON COLUMN admins.status IS 'Current status of the admin account';
COMMENT ON COLUMN admins.last_login IS 'Timestamp of last successful login';
COMMENT ON COLUMN admins.deleted_at IS 'Soft delete timestamp';

COMMENT ON TABLE admin_activity_logs IS 'Audit log of all administrative actions';
COMMENT ON COLUMN admin_activity_logs.admin_id IS 'ID of the admin who performed the action';
COMMENT ON COLUMN admin_activity_logs.admin_email IS 'Email of the admin (for audit trail)';
COMMENT ON COLUMN admin_activity_logs.action IS 'Type of action performed';
COMMENT ON COLUMN admin_activity_logs.resource IS 'Resource that was acted upon';
COMMENT ON COLUMN admin_activity_logs.resource_id IS 'ID of the specific resource instance';
COMMENT ON COLUMN admin_activity_logs.details IS 'Additional details about the action in JSON format';
COMMENT ON COLUMN admin_activity_logs.ip_address IS 'IP address from which the action was performed';
COMMENT ON COLUMN admin_activity_logs.user_agent IS 'User agent string of the client';

COMMENT ON VIEW admin_activity_stats IS 'Aggregated statistics of admin activity';
COMMENT ON VIEW daily_admin_activity IS 'Daily breakdown of admin activity';
COMMENT ON VIEW resource_access_patterns IS 'Analysis of how resources are accessed';

COMMENT ON FUNCTION get_admin_activity_summary IS 'Get comprehensive activity summary for admins';
COMMENT ON FUNCTION cleanup_old_admin_logs IS 'Remove old activity logs to manage database size';

-- ============================================================================
-- Migration Complete
-- ============================================================================

-- Log the migration
INSERT INTO admin_activity_logs (admin_id, admin_email, action, resource, details) 
VALUES (0, 'system', 'migration_executed', 'database', '{"migration": "003_add_admin_system", "timestamp": "' || CURRENT_TIMESTAMP || '"}');
