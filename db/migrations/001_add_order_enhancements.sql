-- ============================================================================
-- Migration 001: Add Order Enhancements for Phase 4
-- Adds new columns to orders table for enhanced functionality
-- ============================================================================

-- Add new columns to orders table
ALTER TABLE orders 
ADD COLUMN IF NOT EXISTS has_color BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS complexity VARCHAR(10) DEFAULT 'low',
ADD COLUMN IF NOT EXISTS estimated_completion TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP WITH TIME ZONE NULL;

-- Add constraints for new columns
ALTER TABLE orders 
ADD CONSTRAINT IF NOT EXISTS orders_complexity_valid 
CHECK (complexity IN ('low', 'medium', 'high'));

-- Update status_history table to support new workflow
ALTER TABLE status_history 
ADD COLUMN IF NOT EXISTS from_status VARCHAR(30),
ADD COLUMN IF NOT EXISTS to_status VARCHAR(30);

-- Update status constraints to match new status values
ALTER TABLE orders 
DROP CONSTRAINT IF EXISTS orders_status_valid;

ALTER TABLE orders 
ADD CONSTRAINT orders_status_valid 
CHECK (status IN ('pending', 'confirmed', 'processing', 'ready', 'completed', 'cancelled'));

ALTER TABLE status_history 
DROP CONSTRAINT IF EXISTS status_history_status_valid;

ALTER TABLE status_history 
ADD CONSTRAINT status_history_status_valid 
CHECK (status IN ('pending', 'confirmed', 'processing', 'ready', 'completed', 'cancelled'));

-- Update finish constraint to match new values
ALTER TABLE orders 
DROP CONSTRAINT IF EXISTS orders_finish_valid;

ALTER TABLE orders 
ADD CONSTRAINT orders_finish_valid 
CHECK (finish IN ('none', 'binding', 'lamination', 'stapling', 'hole-punch'));

-- Add new indexes for performance
CREATE INDEX IF NOT EXISTS idx_orders_has_color ON orders(has_color);
CREATE INDEX IF NOT EXISTS idx_orders_complexity ON orders(complexity);
CREATE INDEX IF NOT EXISTS idx_orders_estimated_completion ON orders(estimated_completion);
CREATE INDEX IF NOT EXISTS idx_orders_deleted_at ON orders(deleted_at) WHERE deleted_at IS NOT NULL;

-- Add index for status_history transitions
CREATE INDEX IF NOT EXISTS idx_status_history_transitions ON status_history(from_status, to_status);

-- Update the status history trigger to include from/to status
CREATE OR REPLACE FUNCTION insert_status_history()
RETURNS TRIGGER AS $$
BEGIN
    -- Só insere se o status realmente mudou
    IF OLD.status IS DISTINCT FROM NEW.status THEN
        INSERT INTO status_history (order_id, from_status, to_status, status, notes, updated_by)
        VALUES (NEW.id, OLD.status, NEW.status, NEW.status, NEW.notes, 'system');
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Update initial status trigger to include from/to status
CREATE OR REPLACE FUNCTION insert_initial_status()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO status_history (order_id, from_status, to_status, status, notes, updated_by)
    VALUES (NEW.id, NULL, NEW.status, NEW.status, 'Pedido criado', 'system');
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create new view for orders with enhanced information
CREATE OR REPLACE VIEW orders_enhanced AS
SELECT
    o.*,
    f.filename,
    f.original_name,
    f.mimetype,
    f.size as file_size,
    f.uploaded_at as file_uploaded_at,
    CASE 
        WHEN o.estimated_completion < NOW() AND o.status NOT IN ('completed', 'cancelled') 
        THEN TRUE 
        ELSE FALSE 
    END as is_overdue,
    CASE 
        WHEN o.has_color THEN 'Color'
        ELSE 'Black & White'
    END as print_type,
    CASE o.complexity
        WHEN 'low' THEN 'Simple'
        WHEN 'medium' THEN 'Standard'
        WHEN 'high' THEN 'Complex'
    END as complexity_label
FROM orders o
JOIN files f ON o.file_id = f.id
WHERE f.deleted_at IS NULL AND o.deleted_at IS NULL;

-- Create view for order statistics
CREATE OR REPLACE VIEW order_statistics AS
SELECT
    COUNT(*) as total_orders,
    COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_orders,
    COUNT(CASE WHEN status = 'confirmed' THEN 1 END) as confirmed_orders,
    COUNT(CASE WHEN status = 'processing' THEN 1 END) as processing_orders,
    COUNT(CASE WHEN status = 'ready' THEN 1 END) as ready_orders,
    COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_orders,
    COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_orders,
    COALESCE(SUM(price), 0) as total_revenue,
    COALESCE(AVG(price), 0) as average_order_value,
    COUNT(CASE WHEN estimated_completion < NOW() AND status NOT IN ('completed', 'cancelled') THEN 1 END) as overdue_orders
FROM orders
WHERE deleted_at IS NULL;

-- Add comments for new columns
COMMENT ON COLUMN orders.has_color IS 'Indica se a impressão é colorida (true) ou preto e branco (false)';
COMMENT ON COLUMN orders.complexity IS 'Nível de complexidade do trabalho: low, medium, high';
COMMENT ON COLUMN orders.estimated_completion IS 'Data estimada de conclusão do pedido';
COMMENT ON COLUMN orders.deleted_at IS 'Data de exclusão lógica do pedido';

COMMENT ON COLUMN status_history.from_status IS 'Status anterior na transição';
COMMENT ON COLUMN status_history.to_status IS 'Novo status na transição';

-- Update existing orders with default values for new columns
UPDATE orders 
SET 
    has_color = FALSE,
    complexity = 'low',
    estimated_completion = created_at + INTERVAL '2 days'
WHERE has_color IS NULL OR complexity IS NULL OR estimated_completion IS NULL;

-- ============================================================================
-- Migration complete
-- ============================================================================
