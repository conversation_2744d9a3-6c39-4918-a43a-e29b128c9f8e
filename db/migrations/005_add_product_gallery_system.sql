-- ============================================================================
-- Migration 005: Add Product Gallery System
-- Adds tables for product gallery, categories, materials, and custom printing
-- ============================================================================

-- ============================================================================
-- PRODUCT CATEGORIES TABLE
-- ============================================================================
CREATE TABLE IF NOT EXISTS product_categories (
  id SERIAL PRIMARY KEY,
  name VARCHAR(100) NOT NULL UNIQUE,
  slug VARCHAR(100) NOT NULL UNIQUE,
  description TEXT,
  image_url VARCHAR(500),
  parent_id INTEGER REFERENCES product_categories(id) ON DELETE SET NULL,
  sort_order INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for categories
CREATE INDEX IF NOT EXISTS idx_product_categories_slug ON product_categories(slug);
CREATE INDEX IF NOT EXISTS idx_product_categories_parent ON product_categories(parent_id);
CREATE INDEX IF NOT EXISTS idx_product_categories_active ON product_categories(is_active);

-- ============================================================================
-- MATERIALS TABLE
-- ============================================================================
CREATE TABLE IF NOT EXISTS materials (
  id SERIAL PRIMARY KEY,
  name VARCHAR(100) NOT NULL UNIQUE,
  slug VARCHAR(100) NOT NULL UNIQUE,
  description TEXT,
  properties JSONB DEFAULT '{}', -- texture, durability, etc.
  is_active BOOLEAN DEFAULT true,
  sort_order INTEGER DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for materials
CREATE INDEX IF NOT EXISTS idx_materials_slug ON materials(slug);
CREATE INDEX IF NOT EXISTS idx_materials_active ON materials(is_active);

-- ============================================================================
-- PRODUCT SIZES TABLE
-- ============================================================================
CREATE TABLE IF NOT EXISTS product_sizes (
  id SERIAL PRIMARY KEY,
  name VARCHAR(50) NOT NULL, -- "20x30cm", "A4", "Custom"
  width_cm DECIMAL(8,2) NOT NULL,
  height_cm DECIMAL(8,2) NOT NULL,
  is_custom BOOLEAN DEFAULT false,
  is_active BOOLEAN DEFAULT true,
  sort_order INTEGER DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  CONSTRAINT product_sizes_dimensions_positive CHECK (width_cm > 0 AND height_cm > 0)
);

-- Create indexes for sizes
CREATE INDEX IF NOT EXISTS idx_product_sizes_active ON product_sizes(is_active);
CREATE INDEX IF NOT EXISTS idx_product_sizes_dimensions ON product_sizes(width_cm, height_cm);

-- ============================================================================
-- PRODUCTS TABLE (Gallery Items)
-- ============================================================================
CREATE TABLE IF NOT EXISTS products (
  id SERIAL PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  slug VARCHAR(255) NOT NULL UNIQUE,
  description TEXT,
  category_id INTEGER NOT NULL REFERENCES product_categories(id) ON DELETE RESTRICT,
  
  -- Image information
  image_url VARCHAR(500) NOT NULL,
  image_width INTEGER,
  image_height INTEGER,
  image_size_bytes INTEGER,
  
  -- AI Enhancement
  has_ai_enhancement BOOLEAN DEFAULT false,
  ai_enhanced_url VARCHAR(500),
  
  -- Metadata
  artist_name VARCHAR(255),
  artist_credit TEXT,
  tags TEXT[], -- Array of tags for filtering
  
  -- Pricing and availability
  base_price DECIMAL(10,2) DEFAULT 0.00,
  is_featured BOOLEAN DEFAULT false,
  is_active BOOLEAN DEFAULT true,
  view_count INTEGER DEFAULT 0,
  
  -- Timestamps
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  CONSTRAINT products_base_price_positive CHECK (base_price >= 0),
  CONSTRAINT products_image_dimensions_positive CHECK (
    (image_width IS NULL AND image_height IS NULL) OR 
    (image_width > 0 AND image_height > 0)
  )
);

-- Create indexes for products
CREATE INDEX IF NOT EXISTS idx_products_slug ON products(slug);
CREATE INDEX IF NOT EXISTS idx_products_category ON products(category_id);
CREATE INDEX IF NOT EXISTS idx_products_active ON products(is_active);
CREATE INDEX IF NOT EXISTS idx_products_featured ON products(is_featured);
CREATE INDEX IF NOT EXISTS idx_products_tags ON products USING GIN(tags);
CREATE INDEX IF NOT EXISTS idx_products_created_at ON products(created_at DESC);

-- ============================================================================
-- MATERIAL PRICES TABLE
-- ============================================================================
CREATE TABLE IF NOT EXISTS material_prices (
  id SERIAL PRIMARY KEY,
  material_id INTEGER NOT NULL REFERENCES materials(id) ON DELETE CASCADE,
  size_id INTEGER NOT NULL REFERENCES product_sizes(id) ON DELETE CASCADE,
  price DECIMAL(10,2) NOT NULL,
  currency VARCHAR(3) DEFAULT 'AOA',
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  CONSTRAINT material_prices_price_positive CHECK (price >= 0),
  CONSTRAINT material_prices_unique UNIQUE (material_id, size_id)
);

-- Create indexes for material prices
CREATE INDEX IF NOT EXISTS idx_material_prices_material ON material_prices(material_id);
CREATE INDEX IF NOT EXISTS idx_material_prices_size ON material_prices(size_id);
CREATE INDEX IF NOT EXISTS idx_material_prices_active ON material_prices(is_active);

-- ============================================================================
-- CUSTOM ORDERS TABLE (for specialized requests)
-- ============================================================================
CREATE TABLE IF NOT EXISTS custom_orders (
  id SERIAL PRIMARY KEY,
  order_id INTEGER NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
  product_id INTEGER REFERENCES products(id) ON DELETE SET NULL,
  material_id INTEGER NOT NULL REFERENCES materials(id) ON DELETE RESTRICT,
  size_id INTEGER REFERENCES product_sizes(id) ON DELETE SET NULL,

  -- Custom specifications
  custom_width_cm DECIMAL(8,2),
  custom_height_cm DECIMAL(8,2),
  custom_specifications TEXT,
  special_instructions TEXT,

  -- Pricing
  quoted_price DECIMAL(10,2),
  final_price DECIMAL(10,2),

  -- AI Enhancement
  original_image_url VARCHAR(500),
  ai_enhanced_image_url VARCHAR(500),
  enhancement_applied BOOLEAN DEFAULT false,

  -- Status
  status VARCHAR(50) DEFAULT 'pending', -- pending, quoted, approved, in_production, completed

  -- Timestamps
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

  CONSTRAINT custom_orders_quoted_price_positive CHECK (quoted_price IS NULL OR quoted_price >= 0),
  CONSTRAINT custom_orders_final_price_positive CHECK (final_price IS NULL OR final_price >= 0),
  CONSTRAINT custom_orders_custom_dimensions_positive CHECK (
    (custom_width_cm IS NULL AND custom_height_cm IS NULL) OR
    (custom_width_cm > 0 AND custom_height_cm > 0)
  ),
  CONSTRAINT custom_orders_status_valid CHECK (
    status IN ('pending', 'quoted', 'approved', 'in_production', 'completed', 'cancelled')
  )
);

-- Create indexes for custom orders
CREATE INDEX IF NOT EXISTS idx_custom_orders_order ON custom_orders(order_id);
CREATE INDEX IF NOT EXISTS idx_custom_orders_product ON custom_orders(product_id);
CREATE INDEX IF NOT EXISTS idx_custom_orders_material ON custom_orders(material_id);
CREATE INDEX IF NOT EXISTS idx_custom_orders_status ON custom_orders(status);

-- ============================================================================
-- UPDATE ORDERS TABLE FOR PRODUCT GALLERY INTEGRATION
-- ============================================================================

-- Add product gallery related columns to orders table
ALTER TABLE orders
ADD COLUMN IF NOT EXISTS product_id INTEGER REFERENCES products(id) ON DELETE SET NULL,
ADD COLUMN IF NOT EXISTS material_id INTEGER REFERENCES materials(id) ON DELETE SET NULL,
ADD COLUMN IF NOT EXISTS size_id INTEGER REFERENCES product_sizes(id) ON DELETE SET NULL,
ADD COLUMN IF NOT EXISTS is_custom_order BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS ai_enhancement_requested BOOLEAN DEFAULT false;

-- Create indexes for new order columns
CREATE INDEX IF NOT EXISTS idx_orders_product ON orders(product_id);
CREATE INDEX IF NOT EXISTS idx_orders_material ON orders(material_id);
CREATE INDEX IF NOT EXISTS idx_orders_size ON orders(size_id);
CREATE INDEX IF NOT EXISTS idx_orders_custom ON orders(is_custom_order);

-- ============================================================================
-- TRIGGERS FOR AUTOMATIC TIMESTAMP UPDATES
-- ============================================================================

-- Function to update timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for all new tables
CREATE TRIGGER update_product_categories_updated_at BEFORE UPDATE ON product_categories
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_materials_updated_at BEFORE UPDATE ON materials
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_product_sizes_updated_at BEFORE UPDATE ON product_sizes
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_products_updated_at BEFORE UPDATE ON products
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_material_prices_updated_at BEFORE UPDATE ON material_prices
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_custom_orders_updated_at BEFORE UPDATE ON custom_orders
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ============================================================================
-- DEFAULT DATA - CATEGORIES
-- ============================================================================

-- Insert default product categories based on reference analysis
INSERT INTO product_categories (name, slug, description, sort_order) VALUES
('Arte Abstrata', 'arte-abstrata', 'Obras de arte abstrata modernas e contemporâneas', 1),
('Anime', 'anime', 'Ilustrações e arte inspirada em anime e mangá', 2),
('Animais', 'animais', 'Fotografias e ilustrações de animais domésticos e selvagens', 3),
('Paisagens', 'paisagens', 'Paisagens naturais, urbanas e panorâmicas', 4),
('Retratos', 'retratos', 'Retratos artísticos e fotografias de pessoas', 5),
('Natureza', 'natureza', 'Flora, fauna e elementos naturais', 6),
('Arquitetura', 'arquitetura', 'Edifícios, estruturas e design arquitetônico', 7),
('Vintage', 'vintage', 'Arte e fotografias com estilo retrô e vintage', 8),
('Minimalista', 'minimalista', 'Designs limpos e minimalistas', 9),
('Colorido', 'colorido', 'Obras vibrantes e cheias de cor', 10)
ON CONFLICT (slug) DO NOTHING;

-- ============================================================================
-- DEFAULT DATA - MATERIALS
-- ============================================================================

-- Insert default materials based on user requirements
INSERT INTO materials (name, slug, description, properties, sort_order) VALUES
(
  'MDF',
  'mdf',
  'Impressão direta em madeira MDF de alta qualidade',
  '{"texture": "wood", "durability": "high", "finish": "matte", "weight": "medium"}',
  1
),
(
  'Canvas',
  'canvas',
  'Impressão em tela canvas premium para quadros tradicionais',
  '{"texture": "canvas", "durability": "high", "finish": "matte", "weight": "light"}',
  2
),
(
  'Vinil Perfurado',
  'vinil-perfurado',
  'Vinil perfurado para aplicações especiais e sinalização',
  '{"texture": "smooth", "durability": "medium", "finish": "matte", "weight": "light", "perforated": true}',
  3
),
(
  'Lona',
  'lona',
  'Lona resistente para impressões externas e grandes formatos',
  '{"texture": "fabric", "durability": "very_high", "finish": "matte", "weight": "heavy", "waterproof": true}',
  4
)
ON CONFLICT (slug) DO NOTHING;

-- ============================================================================
-- DEFAULT DATA - SIZES
-- ============================================================================

-- Insert common print sizes
INSERT INTO product_sizes (name, width_cm, height_cm, sort_order) VALUES
('20x30cm', 20.00, 30.00, 1),
('30x40cm', 30.00, 40.00, 2),
('40x60cm', 40.00, 60.00, 3),
('50x70cm', 50.00, 70.00, 4),
('60x80cm', 60.00, 80.00, 5),
('70x100cm', 70.00, 100.00, 6),
('A4', 21.00, 29.70, 7),
('A3', 29.70, 42.00, 8),
('A2', 42.00, 59.40, 9),
('A1', 59.40, 84.10, 10),
('Personalizado', 0.00, 0.00, 99)
ON CONFLICT (name) DO NOTHING;

-- Update custom size
UPDATE product_sizes SET is_custom = true WHERE name = 'Personalizado';

-- ============================================================================
-- DEFAULT DATA - MATERIAL PRICES (in AOA)
-- ============================================================================

-- Insert default pricing matrix (prices in Angolan Kwanza)
INSERT INTO material_prices (material_id, size_id, price)
SELECT
  m.id as material_id,
  s.id as size_id,
  CASE
    WHEN m.slug = 'mdf' AND s.name = '20x30cm' THEN 2500.00
    WHEN m.slug = 'mdf' AND s.name = '30x40cm' THEN 4000.00
    WHEN m.slug = 'mdf' AND s.name = '40x60cm' THEN 6500.00
    WHEN m.slug = 'mdf' AND s.name = '50x70cm' THEN 9000.00
    WHEN m.slug = 'canvas' AND s.name = '20x30cm' THEN 2000.00
    WHEN m.slug = 'canvas' AND s.name = '30x40cm' THEN 3500.00
    WHEN m.slug = 'canvas' AND s.name = '40x60cm' THEN 5500.00
    WHEN m.slug = 'canvas' AND s.name = '50x70cm' THEN 7500.00
    WHEN m.slug = 'vinil-perfurado' AND s.name = '20x30cm' THEN 1500.00
    WHEN m.slug = 'vinil-perfurado' AND s.name = '30x40cm' THEN 2500.00
    WHEN m.slug = 'lona' AND s.name = '40x60cm' THEN 4000.00
    WHEN m.slug = 'lona' AND s.name = '50x70cm' THEN 6000.00
    ELSE 0.00
  END as price
FROM materials m
CROSS JOIN product_sizes s
WHERE s.name != 'Personalizado'
  AND (
    (m.slug = 'mdf' AND s.name IN ('20x30cm', '30x40cm', '40x60cm', '50x70cm')) OR
    (m.slug = 'canvas' AND s.name IN ('20x30cm', '30x40cm', '40x60cm', '50x70cm')) OR
    (m.slug = 'vinil-perfurado' AND s.name IN ('20x30cm', '30x40cm')) OR
    (m.slug = 'lona' AND s.name IN ('40x60cm', '50x70cm'))
  )
ON CONFLICT (material_id, size_id) DO NOTHING;

-- ============================================================================
-- PERMISSIONS AND GRANTS
-- ============================================================================

-- Grant permissions to weprint_user (if exists)
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM pg_roles WHERE rolname = 'weprint_user') THEN
    GRANT SELECT, INSERT, UPDATE, DELETE ON product_categories TO weprint_user;
    GRANT SELECT, INSERT, UPDATE, DELETE ON materials TO weprint_user;
    GRANT SELECT, INSERT, UPDATE, DELETE ON product_sizes TO weprint_user;
    GRANT SELECT, INSERT, UPDATE, DELETE ON products TO weprint_user;
    GRANT SELECT, INSERT, UPDATE, DELETE ON material_prices TO weprint_user;
    GRANT SELECT, INSERT, UPDATE, DELETE ON custom_orders TO weprint_user;

    GRANT USAGE ON SEQUENCE product_categories_id_seq TO weprint_user;
    GRANT USAGE ON SEQUENCE materials_id_seq TO weprint_user;
    GRANT USAGE ON SEQUENCE product_sizes_id_seq TO weprint_user;
    GRANT USAGE ON SEQUENCE products_id_seq TO weprint_user;
    GRANT USAGE ON SEQUENCE material_prices_id_seq TO weprint_user;
    GRANT USAGE ON SEQUENCE custom_orders_id_seq TO weprint_user;
  END IF;
END $$;

-- ============================================================================
-- LOG MIGRATION
-- ============================================================================

-- Log this migration in admin activity logs (if table exists)
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'admin_activity_logs') THEN
    INSERT INTO admin_activity_logs (admin_id, admin_email, action, resource, details)
    VALUES (0, 'system', 'migration_executed', 'database',
            ('{"migration": "005_add_product_gallery_system", "timestamp": "' || CURRENT_TIMESTAMP || '", "tables_created": ["product_categories", "materials", "product_sizes", "products", "material_prices", "custom_orders"], "default_data": "inserted"}')::jsonb);
  END IF;
END $$;

-- ============================================================================
-- MIGRATION COMPLETE
-- ============================================================================
