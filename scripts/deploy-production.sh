#!/bin/bash

# WePrint AI Production Deployment Script
# This script handles the complete production deployment process

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="weprint-ai"
BACKUP_DIR="./backups"
LOG_FILE="./deploy.log"

# Functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}✅ $1${NC}" | tee -a "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}❌ $1${NC}" | tee -a "$LOG_FILE"
    exit 1
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check if Docker is installed and running
    if ! command -v docker &> /dev/null; then
        error "Docker is not installed. Please install Docker first."
    fi
    
    if ! docker info &> /dev/null; then
        error "Docker is not running. Please start Docker first."
    fi
    
    # Check if Docker Compose is available
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        error "Docker Compose is not available. Please install Docker Compose."
    fi
    
    # Check if .env.production exists
    if [ ! -f ".env.production" ]; then
        error ".env.production file not found. Please create it with production configuration."
    fi
    
    success "Prerequisites check passed"
}

# Create backup
create_backup() {
    log "Creating backup..."
    
    mkdir -p "$BACKUP_DIR"
    BACKUP_NAME="backup-$(date +'%Y%m%d-%H%M%S')"
    
    # Backup database if running
    if docker ps | grep -q "${PROJECT_NAME}-db-prod"; then
        log "Backing up database..."
        docker exec "${PROJECT_NAME}-db-prod" pg_dump -U postgres weprint_ai_prod > "${BACKUP_DIR}/${BACKUP_NAME}-database.sql"
        success "Database backup created: ${BACKUP_DIR}/${BACKUP_NAME}-database.sql"
    fi
    
    # Backup uploads if they exist
    if docker volume ls | grep -q "${PROJECT_NAME}_uploads_prod"; then
        log "Backing up uploads..."
        docker run --rm -v "${PROJECT_NAME}_uploads_prod:/data" -v "$(pwd)/${BACKUP_DIR}:/backup" alpine tar czf "/backup/${BACKUP_NAME}-uploads.tar.gz" -C /data .
        success "Uploads backup created: ${BACKUP_DIR}/${BACKUP_NAME}-uploads.tar.gz"
    fi
}

# Setup SSL certificates
setup_ssl() {
    log "Setting up SSL certificates..."
    
    mkdir -p ssl
    
    if [ ! -f "ssl/weprint.ai.crt" ] || [ ! -f "ssl/weprint.ai.key" ]; then
        warning "SSL certificates not found. Creating self-signed certificates for testing..."
        warning "For production, please replace with valid SSL certificates from a CA."
        
        openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
            -keyout ssl/weprint.ai.key \
            -out ssl/weprint.ai.crt \
            -subj "/C=AO/ST=Luanda/L=Luanda/O=WePrint AI/CN=weprint.ai"
        
        success "Self-signed SSL certificates created"
    else
        success "SSL certificates found"
    fi
}

# Build and deploy
deploy() {
    log "Starting deployment..."
    
    # Pull latest code (if using git)
    if [ -d ".git" ]; then
        log "Pulling latest code..."
        git pull origin main || warning "Git pull failed or not in a git repository"
    fi
    
    # Build and start services
    log "Building and starting services..."
    
    # Use docker-compose or docker compose based on availability
    if command -v docker-compose &> /dev/null; then
        COMPOSE_CMD="docker-compose"
    else
        COMPOSE_CMD="docker compose"
    fi
    
    # Stop existing services
    log "Stopping existing services..."
    $COMPOSE_CMD -f docker-compose.prod.yml down || true
    
    # Build new images
    log "Building new images..."
    $COMPOSE_CMD -f docker-compose.prod.yml build --no-cache
    
    # Start services
    log "Starting services..."
    $COMPOSE_CMD -f docker-compose.prod.yml up -d
    
    # Wait for services to be healthy
    log "Waiting for services to be healthy..."
    sleep 30
    
    # Check service health
    check_health
}

# Check service health
check_health() {
    log "Checking service health..."
    
    # Check database
    if docker ps | grep -q "${PROJECT_NAME}-db-prod"; then
        if docker exec "${PROJECT_NAME}-db-prod" pg_isready -U postgres -d weprint_ai_prod &> /dev/null; then
            success "Database is healthy"
        else
            error "Database health check failed"
        fi
    fi
    
    # Check backend
    if docker ps | grep -q "${PROJECT_NAME}-backend-prod"; then
        if curl -f http://localhost:8000/health &> /dev/null; then
            success "Backend is healthy"
        else
            error "Backend health check failed"
        fi
    fi
    
    # Check frontend
    if docker ps | grep -q "${PROJECT_NAME}-frontend-prod"; then
        if curl -f http://localhost/health &> /dev/null; then
            success "Frontend is healthy"
        else
            warning "Frontend health check failed (this might be normal if SSL is not configured)"
        fi
    fi
}

# Main deployment process
main() {
    log "🚀 Starting WePrint AI Production Deployment"
    
    check_prerequisites
    create_backup
    setup_ssl
    deploy
    
    success "🎉 Deployment completed successfully!"
    log "📍 Frontend: https://weprint.ai"
    log "📍 API: https://api.weprint.ai"
    log "📍 Health Check: http://localhost:8000/health"
    
    warning "⚠️  Remember to:"
    warning "   1. Update DNS records to point to this server"
    warning "   2. Replace self-signed SSL certificates with valid ones"
    warning "   3. Configure email settings in .env.production"
    warning "   4. Set up proper backup procedures"
    warning "   5. Configure monitoring alerts"
}

# Run main function
main "$@"
