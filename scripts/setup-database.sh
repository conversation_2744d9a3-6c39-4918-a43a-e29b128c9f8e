#!/bin/bash

# ============================================================================
# WePrint AI Database Setup Script
# ============================================================================

set -e

echo "🗄️  Setting up WePrint AI Database..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Database configuration
DB_NAME="weprint"
DB_USER="postgres"
DB_PASSWORD="postgres"
DB_HOST="localhost"
DB_PORT="5432"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if PostgreSQL is installed
check_postgresql() {
    print_status "Checking PostgreSQL installation..."
    
    if command -v psql &> /dev/null; then
        print_success "PostgreSQL client found"
        psql --version
    else
        print_error "PostgreSQL client not found"
        print_status "Please install PostgreSQL:"
        echo "  macOS: brew install postgresql@15"
        echo "  Ubuntu: sudo apt-get install postgresql postgresql-contrib"
        echo "  CentOS: sudo yum install postgresql postgresql-server"
        exit 1
    fi
}

# Check if PostgreSQL server is running
check_postgresql_server() {
    print_status "Checking PostgreSQL server..."
    
    if pg_isready -h $DB_HOST -p $DB_PORT &> /dev/null; then
        print_success "PostgreSQL server is running"
    else
        print_warning "PostgreSQL server is not running"
        print_status "Starting PostgreSQL server..."
        
        # Try different methods to start PostgreSQL
        if command -v brew &> /dev/null; then
            # macOS with Homebrew
            brew services start postgresql@15 || brew services start postgresql
        elif command -v systemctl &> /dev/null; then
            # Linux with systemd
            sudo systemctl start postgresql
        elif command -v service &> /dev/null; then
            # Linux with service
            sudo service postgresql start
        else
            print_error "Could not start PostgreSQL automatically"
            print_status "Please start PostgreSQL manually and run this script again"
            exit 1
        fi
        
        # Wait a bit for the server to start
        sleep 3
        
        if pg_isready -h $DB_HOST -p $DB_PORT &> /dev/null; then
            print_success "PostgreSQL server started successfully"
        else
            print_error "Failed to start PostgreSQL server"
            exit 1
        fi
    fi
}

# Create database and user
setup_database() {
    print_status "Setting up database and user..."
    
    # Check if database exists
    if psql -h $DB_HOST -p $DB_PORT -U postgres -lqt | cut -d \| -f 1 | grep -qw $DB_NAME; then
        print_warning "Database '$DB_NAME' already exists"
        read -p "Do you want to recreate it? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            print_status "Dropping existing database..."
            psql -h $DB_HOST -p $DB_PORT -U postgres -c "DROP DATABASE IF EXISTS $DB_NAME;"
        else
            print_status "Using existing database"
            return 0
        fi
    fi
    
    # Create database
    print_status "Creating database '$DB_NAME'..."
    psql -h $DB_HOST -p $DB_PORT -U postgres -c "CREATE DATABASE $DB_NAME;"
    
    print_success "Database '$DB_NAME' created successfully"
}

# Run database schema
setup_schema() {
    print_status "Setting up database schema..."
    
    # Check if init.sql exists
    if [ ! -f "db/init.sql" ]; then
        print_error "Database schema file 'db/init.sql' not found"
        print_status "Please run this script from the project root directory"
        exit 1
    fi
    
    # Run the schema
    print_status "Executing database schema..."
    psql -h $DB_HOST -p $DB_PORT -U postgres -d $DB_NAME -f db/init.sql
    
    print_success "Database schema created successfully"
}

# Test database connection
test_connection() {
    print_status "Testing database connection..."
    
    # Test basic connection
    if psql -h $DB_HOST -p $DB_PORT -U postgres -d $DB_NAME -c "SELECT version();" &> /dev/null; then
        print_success "Database connection successful"
    else
        print_error "Database connection failed"
        exit 1
    fi
    
    # Test tables
    print_status "Checking database tables..."
    TABLES=$(psql -h $DB_HOST -p $DB_PORT -U postgres -d $DB_NAME -t -c "SELECT tablename FROM pg_tables WHERE schemaname = 'public';" | tr -d ' ' | grep -v '^$')
    
    if [ -n "$TABLES" ]; then
        print_success "Database tables found:"
        echo "$TABLES" | while read table; do
            echo "  - $table"
        done
    else
        print_warning "No tables found in database"
    fi
}

# Update environment file
update_env() {
    print_status "Updating environment configuration..."
    
    ENV_FILE="backend/.env"
    
    if [ -f "$ENV_FILE" ]; then
        # Update database URL
        sed -i.bak "s|DATABASE_URL=.*|DATABASE_URL=postgres://$DB_USER:$DB_PASSWORD@$DB_HOST:$DB_PORT/$DB_NAME|" "$ENV_FILE"
        print_success "Environment file updated"
    else
        print_warning "Environment file not found at $ENV_FILE"
        print_status "Please create the environment file manually"
    fi
}

# Main execution
main() {
    echo "🚀 Starting WePrint AI Database Setup"
    echo "======================================"
    
    check_postgresql
    check_postgresql_server
    setup_database
    setup_schema
    test_connection
    update_env
    
    echo ""
    echo "======================================"
    print_success "Database setup completed successfully!"
    echo ""
    print_status "Next steps:"
    echo "  1. Start the backend: cd backend && npm run dev"
    echo "  2. Test the connection: curl http://localhost:8000/health"
    echo ""
    print_status "Database connection details:"
    echo "  Host: $DB_HOST"
    echo "  Port: $DB_PORT"
    echo "  Database: $DB_NAME"
    echo "  User: $DB_USER"
    echo ""
}

# Run main function
main "$@"
