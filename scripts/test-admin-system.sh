#!/bin/bash

# WePrint AI - Admin System Testing Script
# This script runs comprehensive tests for the administrative system

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Test results tracking
BACKEND_ADMIN_TESTS_PASSED=false
FRONTEND_ADMIN_TESTS_PASSED=false
ADMIN_API_TESTS_PASSED=false

echo -e "${BLUE}🔧 WePrint AI - Admin System Testing${NC}"
echo -e "${BLUE}======================================${NC}"
echo ""

# Function to print section headers
print_section() {
    echo -e "${PURPLE}$1${NC}"
    echo -e "${PURPLE}$(printf '=%.0s' $(seq 1 ${#1}))${NC}"
}

# Function to print test results
print_result() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
        return 0
    else
        echo -e "${RED}❌ $2${NC}"
        return 1
    fi
}

# Function to check if services are running
check_services() {
    print_section "🔍 Checking Services"
    
    # Check if PostgreSQL is running
    if ! pg_isready -h localhost -p 5432 >/dev/null 2>&1; then
        echo -e "${RED}❌ PostgreSQL is not running${NC}"
        echo -e "${YELLOW}Please start PostgreSQL service${NC}"
        exit 1
    fi
    echo -e "${GREEN}✅ PostgreSQL is running${NC}"
    
    # Check if backend is running (optional for tests)
    if curl -s http://localhost:8000/api/health >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Backend server is running${NC}"
    else
        echo -e "${YELLOW}⚠️  Backend server is not running (tests will start their own instance)${NC}"
    fi
    
    echo ""
}

# Function to setup test environment
setup_test_env() {
    print_section "🛠️  Setting Up Test Environment"
    
    # Ensure test database exists
    echo "Setting up test database..."
    cd backend
    
    # Create test database if it doesn't exist
    createdb weprint_test 2>/dev/null || echo "Test database already exists"
    
    # Run migrations for test database
    if [ -f "package.json" ]; then
        echo "Installing backend dependencies..."
        npm install --silent
        
        echo "Running database migrations for test environment..."
        NODE_ENV=test npm run migrate 2>/dev/null || echo "Migrations completed or already up to date"
    fi
    
    cd ..
    
    # Setup frontend test environment
    echo "Setting up frontend test environment..."
    cd frontend
    
    if [ -f "package.json" ]; then
        echo "Installing frontend dependencies..."
        npm install --silent
    fi
    
    cd ..
    
    echo -e "${GREEN}✅ Test environment setup complete${NC}"
    echo ""
}

# Function to run backend admin tests
run_backend_admin_tests() {
    print_section "🔙 Running Backend Admin System Tests"
    
    cd backend
    
    echo "Running admin authentication tests..."
    if npm test -- --testPathPattern="admin-system.test.ts" --testNamePattern="Admin Authentication" --silent; then
        echo -e "${GREEN}✅ Admin authentication tests passed${NC}"
    else
        echo -e "${RED}❌ Admin authentication tests failed${NC}"
        cd ..
        return 1
    fi
    
    echo "Running admin dashboard tests..."
    if npm test -- --testPathPattern="admin-system.test.ts" --testNamePattern="Admin Dashboard" --silent; then
        echo -e "${GREEN}✅ Admin dashboard tests passed${NC}"
    else
        echo -e "${RED}❌ Admin dashboard tests failed${NC}"
        cd ..
        return 1
    fi
    
    echo "Running admin order management tests..."
    if npm test -- --testPathPattern="admin-system.test.ts" --testNamePattern="Admin Order Management" --silent; then
        echo -e "${GREEN}✅ Admin order management tests passed${NC}"
    else
        echo -e "${RED}❌ Admin order management tests failed${NC}"
        cd ..
        return 1
    fi
    
    echo "Running admin user management tests..."
    if npm test -- --testPathPattern="admin-system.test.ts" --testNamePattern="Admin User Management" --silent; then
        echo -e "${GREEN}✅ Admin user management tests passed${NC}"
    else
        echo -e "${RED}❌ Admin user management tests failed${NC}"
        cd ..
        return 1
    fi
    
    echo "Running admin security tests..."
    if npm test -- --testPathPattern="admin-system.test.ts" --testNamePattern="Admin Security" --silent; then
        echo -e "${GREEN}✅ Admin security tests passed${NC}"
    else
        echo -e "${RED}❌ Admin security tests failed${NC}"
        cd ..
        return 1
    fi
    
    echo "Running complete admin system test suite..."
    if npm test -- --testPathPattern="admin-system.test.ts" --coverage --silent; then
        echo -e "${GREEN}✅ All backend admin tests passed${NC}"
        BACKEND_ADMIN_TESTS_PASSED=true
    else
        echo -e "${RED}❌ Some backend admin tests failed${NC}"
        cd ..
        return 1
    fi
    
    cd ..
    return 0
}

# Function to run frontend admin tests
run_frontend_admin_tests() {
    print_section "🎨 Running Frontend Admin Panel Tests"
    
    cd frontend
    
    echo "Running admin panel component tests..."
    if npm test -- --testPathPattern="AdminPanel.test.tsx" --watchAll=false --silent; then
        echo -e "${GREEN}✅ Admin panel component tests passed${NC}"
        FRONTEND_ADMIN_TESTS_PASSED=true
    else
        echo -e "${RED}❌ Admin panel component tests failed${NC}"
        cd ..
        return 1
    fi
    
    cd ..
    return 0
}

# Function to run admin API integration tests
run_admin_api_tests() {
    print_section "🔗 Running Admin API Integration Tests"

    echo "Testing admin API endpoints..."

    # Kill any existing backend processes
    echo "Stopping any existing backend processes..."
    pkill -f "npm.*start" 2>/dev/null || true
    pkill -f "node.*index" 2>/dev/null || true
    sleep 2

    # Start backend server for API testing on test port
    cd backend
    PORT=8001 NODE_ENV=test npm start &
    BACKEND_PID=$!

    # Wait for server to start
    echo "Waiting for backend server to start on port 8001..."
    sleep 8
    
    # Test admin API health
    if curl -s http://localhost:8001/api/admin/health | grep -q "healthy"; then
        echo -e "${GREEN}✅ Admin API health check passed${NC}"
    else
        echo -e "${RED}❌ Admin API health check failed${NC}"
        kill $BACKEND_PID 2>/dev/null || true
        cd ..
        return 1
    fi

    # Test admin API info
    if curl -s http://localhost:8001/api/admin | grep -q "WePrint AI Admin API"; then
        echo -e "${GREEN}✅ Admin API info endpoint passed${NC}"
    else
        echo -e "${RED}❌ Admin API info endpoint failed${NC}"
        kill $BACKEND_PID 2>/dev/null || true
        cd ..
        return 1
    fi

    # Test admin authentication endpoint
    if curl -s -X POST http://localhost:8001/api/admin/auth/login \
        -H "Content-Type: application/json" \
        -d '{"email":"<EMAIL>","password":"invalid"}' | grep -q "error"; then
        echo -e "${GREEN}✅ Admin authentication endpoint responding${NC}"
        ADMIN_API_TESTS_PASSED=true
    else
        echo -e "${RED}❌ Admin authentication endpoint not responding${NC}"
        kill $BACKEND_PID 2>/dev/null || true
        cd ..
        return 1
    fi
    
    # Clean up
    kill $BACKEND_PID 2>/dev/null || true
    cd ..
    return 0
}

# Function to generate admin test report
generate_admin_test_report() {
    print_section "📊 Admin System Test Report"
    
    # Create reports directory
    mkdir -p test-reports
    
    # Generate detailed report
    cat > test-reports/admin-system-test-report.md << EOF
# WePrint AI - Admin System Test Report

**Test Date:** $(date)
**Test Environment:** $(uname -s) $(uname -r)

## Test Results Summary

| Test Category | Status | Details |
|---------------|--------|---------|
| Backend Admin Tests | $([ "$BACKEND_ADMIN_TESTS_PASSED" = true ] && echo "✅ PASSED" || echo "❌ FAILED") | Admin authentication, dashboard, order management, user management, security |
| Frontend Admin Tests | $([ "$FRONTEND_ADMIN_TESTS_PASSED" = true ] && echo "✅ PASSED" || echo "❌ FAILED") | Admin panel components, authentication UI, dashboard UI |
| Admin API Tests | $([ "$ADMIN_API_TESTS_PASSED" = true ] && echo "✅ PASSED" || echo "❌ FAILED") | API endpoints, health checks, authentication endpoints |

## Admin System Features Tested

### ✅ Admin Authentication System
- Admin login/logout functionality
- JWT token management
- Password change functionality
- Session management
- Token refresh mechanism

### ✅ Admin Dashboard
- Dashboard statistics display
- System health monitoring
- Recent activity tracking
- Performance metrics
- Data export functionality

### ✅ Admin Order Management
- Order listing and filtering
- Order details viewing
- Order status updates
- Order modification
- Order statistics

### ✅ Admin User Management
- Admin user listing
- Admin user creation
- Role management (admin/super_admin)
- Status management (active/inactive)
- Password reset functionality

### ✅ Admin Security & Permissions
- Role-based access control
- Authentication middleware
- Rate limiting
- Activity logging
- Self-modification prevention

### ✅ Admin Frontend Components
- Login form functionality
- Dashboard overview display
- Navigation between sections
- Data loading states
- Error handling

## Recommendations

$([ "$BACKEND_ADMIN_TESTS_PASSED" = true ] && [ "$FRONTEND_ADMIN_TESTS_PASSED" = true ] && [ "$ADMIN_API_TESTS_PASSED" = true ] && echo "🎉 **All admin system tests passed!** The administrative system is ready for production use." || echo "⚠️ **Some tests failed.** Please review the failed tests and fix any issues before deploying to production.")

## Next Steps

1. **Production Deployment**: Admin system is ready for production deployment
2. **User Training**: Provide training for admin users on the panel functionality
3. **Monitoring**: Set up monitoring for admin activities and system health
4. **Backup**: Ensure admin data and configurations are included in backup procedures
5. **Security Review**: Regular security audits of admin access and permissions

---
*Generated by WePrint AI Admin System Test Suite*
EOF

    echo -e "${GREEN}✅ Admin test report generated: test-reports/admin-system-test-report.md${NC}"
}

# Main execution
main() {
    echo -e "${CYAN}Starting WePrint AI Admin System Testing...${NC}"
    echo ""
    
    # Check services
    check_services
    
    # Setup test environment
    setup_test_env
    
    # Run backend admin tests
    if run_backend_admin_tests; then
        echo -e "${GREEN}✅ Backend admin tests completed successfully${NC}"
    else
        echo -e "${RED}❌ Backend admin tests failed${NC}"
    fi
    echo ""
    
    # Run frontend admin tests
    if run_frontend_admin_tests; then
        echo -e "${GREEN}✅ Frontend admin tests completed successfully${NC}"
    else
        echo -e "${RED}❌ Frontend admin tests failed${NC}"
    fi
    echo ""
    
    # Run admin API tests
    if run_admin_api_tests; then
        echo -e "${GREEN}✅ Admin API tests completed successfully${NC}"
    else
        echo -e "${RED}❌ Admin API tests failed${NC}"
    fi
    echo ""
    
    # Generate report
    generate_admin_test_report
    echo ""
    
    # Final summary
    print_section "🎯 Final Admin System Test Summary"
    
    if [ "$BACKEND_ADMIN_TESTS_PASSED" = true ] && [ "$FRONTEND_ADMIN_TESTS_PASSED" = true ] && [ "$ADMIN_API_TESTS_PASSED" = true ]; then
        echo -e "${GREEN}🎉 ALL ADMIN SYSTEM TESTS PASSED!${NC}"
        echo -e "${GREEN}The WePrint AI administrative system is fully functional and ready for production use.${NC}"
        echo ""
        echo -e "${CYAN}Admin System Features Validated:${NC}"
        echo -e "  ✅ Admin authentication and authorization"
        echo -e "  ✅ Dashboard statistics and monitoring"
        echo -e "  ✅ Order management and status updates"
        echo -e "  ✅ User management and permissions"
        echo -e "  ✅ Security controls and activity logging"
        echo -e "  ✅ Frontend admin panel functionality"
        echo ""
        exit 0
    else
        echo -e "${RED}❌ SOME ADMIN SYSTEM TESTS FAILED${NC}"
        echo -e "${YELLOW}Please review the test output and fix any issues before proceeding.${NC}"
        echo ""
        exit 1
    fi
}

# Run main function
main "$@"
