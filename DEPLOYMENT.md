# 🚀 WePrint AI - Deployment Guide

This guide explains how to deploy WePrint AI using Docker and Docker Compose.

## 📋 Prerequisites

- Docker (version 20.10 or higher)
- Docker Compose (version 2.0 or higher)
- At least 2GB of available RAM
- At least 5GB of available disk space

## 🛠️ Quick Start

### 1. Clone the Repository
```bash
git clone <repository-url>
cd WePrint-AI
```

### 2. Deploy with One Command
```bash
./scripts/deploy.sh
```

This will:
- Build all Docker images
- Start all services (frontend, backend, database)
- Wait for services to be healthy
- Show deployment status

### 3. Access the Application
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **Admin Panel**: http://localhost:3000/admin
  - Email: `<EMAIL>`
  - Password: `admin123`

## 🔧 Manual Deployment

If you prefer manual control:

```bash
# Stop any existing containers
docker-compose down

# Build and start services
docker-compose up -d --build

# Check status
docker-compose ps

# View logs
docker-compose logs -f
```

## 📊 Service Management

### Start Services
```bash
./scripts/deploy.sh deploy
# or
docker-compose up -d
```

### Stop Services
```bash
./scripts/deploy.sh stop
# or
docker-compose down
```

### Restart Services
```bash
./scripts/deploy.sh restart
# or
docker-compose restart
```

### View Logs
```bash
./scripts/deploy.sh logs
# or
docker-compose logs -f
```

### Check Status
```bash
./scripts/deploy.sh status
# or
docker-compose ps
```

### Clean Everything
```bash
./scripts/deploy.sh clean
```
⚠️ **Warning**: This removes all containers, images, and volumes!

## 🏗️ Architecture

The deployment consists of three main services:

### Frontend (React + Nginx)
- **Port**: 3000 (mapped to container port 80)
- **Technology**: React 18 + TypeScript + Tailwind CSS
- **Web Server**: Nginx (production)
- **Features**: PWA, responsive design, admin panel

### Backend (Node.js + Express)
- **Port**: 8000
- **Technology**: Node.js 18 + TypeScript + Express
- **Features**: REST API, file upload, JWT auth, payment integration
- **Health Check**: `/health` endpoint

### Database (PostgreSQL)
- **Port**: 5432
- **Technology**: PostgreSQL 15
- **Features**: Persistent data storage, automatic migrations
- **Data**: Orders, files, users, notifications

## 🔒 Environment Configuration

### Production Environment Variables

The application uses the following key environment variables:

#### Backend (.env.production)
```env
NODE_ENV=production
DATABASE_URL=*******************************************/weprint
JWT_SECRET=weprint-jwt-secret-key-2025
CORS_ORIGIN=http://localhost:3000
```

#### Frontend
```env
REACT_APP_API_URL=http://localhost:8000/api
```

### Security Considerations

🔐 **Important**: Change these default values in production:
- Database password
- JWT secret
- Admin credentials
- API keys

## 📁 Data Persistence

The deployment uses Docker volumes for data persistence:

- **postgres_data**: Database files
- **backend_uploads**: Uploaded files

Data persists between container restarts and updates.

## 🔍 Troubleshooting

### Common Issues

#### Port Already in Use
```bash
# Check what's using the port
lsof -ti:3000
lsof -ti:8000
lsof -ti:5432

# Kill processes if needed
kill -9 <PID>
```

#### Database Connection Issues
```bash
# Check database health
docker-compose exec db pg_isready -U postgres -d weprint

# View database logs
docker-compose logs db
```

#### Frontend Not Loading
```bash
# Check frontend logs
docker-compose logs frontend

# Rebuild frontend
docker-compose build frontend --no-cache
```

#### Backend API Errors
```bash
# Check backend logs
docker-compose logs backend

# Check backend health
curl http://localhost:8000/health
```

### Health Checks

All services include health checks:

```bash
# Check all service health
docker-compose ps

# Manual health checks
curl http://localhost:3000/health  # Frontend
curl http://localhost:8000/health  # Backend
docker-compose exec db pg_isready -U postgres -d weprint  # Database
```

## 🔄 Updates and Maintenance

### Update Application
```bash
# Pull latest changes
git pull

# Rebuild and restart
./scripts/deploy.sh deploy
```

### Backup Database
```bash
# Create backup
docker-compose exec db pg_dump -U postgres weprint > backup.sql

# Restore backup
docker-compose exec -T db psql -U postgres weprint < backup.sql
```

### View Resource Usage
```bash
# Container stats
docker stats

# Disk usage
docker system df
```

## 🌐 Production Deployment

For production deployment:

1. **Use a reverse proxy** (Nginx, Traefik, or Cloudflare)
2. **Enable HTTPS** with SSL certificates
3. **Set up monitoring** (logs, metrics, alerts)
4. **Configure backups** (database, files)
5. **Update environment variables** (secrets, URLs)
6. **Set up CI/CD** for automated deployments

### Example Nginx Configuration (Production)
```nginx
server {
    listen 80;
    server_name yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl;
    server_name yourdomain.com;
    
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    
    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    location /api {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 📞 Support

If you encounter issues:

1. Check the logs: `./scripts/deploy.sh logs`
2. Verify service status: `./scripts/deploy.sh status`
3. Review this documentation
4. Check the GitHub issues page

---

**WePrint AI** - Intelligent Printing Service 🖨️
