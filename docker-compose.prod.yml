version: '3.8'

services:
  # PostgreSQL Database
  database:
    image: postgres:17-alpine
    container_name: weprint-ai-db-prod
    restart: unless-stopped
    environment:
      POSTGRES_DB: weprint_ai_prod
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${DB_PASSWORD}
      POSTGRES_HOST_AUTH_METHOD: md5
    volumes:
      - postgres_data_prod:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    networks:
      - weprint-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d weprint_ai_prod"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: production
    container_name: weprint-ai-backend-prod
    restart: unless-stopped
    environment:
      NODE_ENV: production
      PORT: 8000
      DB_HOST: database
      DB_PORT: 5432
      DB_NAME: weprint_ai_prod
      DB_USER: postgres
      DB_PASSWORD: ${DB_PASSWORD}
      JWT_SECRET: ${JWT_SECRET}
      MULTICAIXA_API_KEY: ${MULTICAIXA_API_KEY}
      MULTICAIXA_ENTITY_ID: ${MULTICAIXA_ENTITY_ID}
      EMAIL_HOST: ${EMAIL_HOST}
      EMAIL_PORT: ${EMAIL_PORT}
      EMAIL_USER: ${EMAIL_USER}
      EMAIL_PASS: ${EMAIL_PASS}
      CORS_ORIGIN: https://weprint.ai
    volumes:
      - uploads_prod:/app/uploads
      - logs_prod:/app/logs
    ports:
      - "8000:8000"
    networks:
      - weprint-network
    depends_on:
      database:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Frontend Application
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: production
    container_name: weprint-ai-frontend-prod
    restart: unless-stopped
    environment:
      REACT_APP_API_URL: https://api.weprint.ai/api
      REACT_APP_WS_URL: wss://api.weprint.ai
      REACT_APP_MULTICAIXA_RETURN_URL: https://weprint.ai/payment/return
      REACT_APP_MULTICAIXA_CANCEL_URL: https://weprint.ai/payment/cancel
      REACT_APP_ENVIRONMENT: production
    ports:
      - "80:80"
      - "443:443"
    networks:
      - weprint-network
    depends_on:
      backend:
        condition: service_healthy
    volumes:
      - ./ssl:/etc/nginx/ssl:ro
      - ./nginx/nginx.prod.conf:/etc/nginx/nginx.conf:ro

  # Redis for Caching (Production Enhancement)
  redis:
    image: redis:7-alpine
    container_name: weprint-ai-redis-prod
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data_prod:/data
    ports:
      - "6379:6379"
    networks:
      - weprint-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Monitoring with Prometheus (Optional)
  prometheus:
    image: prom/prometheus:latest
    container_name: weprint-ai-prometheus-prod
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data_prod:/prometheus
    ports:
      - "9090:9090"
    networks:
      - weprint-network

volumes:
  postgres_data_prod:
    driver: local
  uploads_prod:
    driver: local
  logs_prod:
    driver: local
  redis_data_prod:
    driver: local
  prometheus_data_prod:
    driver: local

networks:
  weprint-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
