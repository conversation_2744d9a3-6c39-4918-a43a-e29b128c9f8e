<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste de Integração WePrint AI</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            color: #333;
            margin-top: 0;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖨️ Teste de Integração WePrint AI</h1>
        <p>Este teste verifica se a integração frontend-backend está funcionando corretamente.</p>

        <div class="test-section">
            <h3>1. Teste de Upload de Arquivo</h3>
            <input type="file" id="fileInput" accept=".pdf,.jpg,.jpeg,.png">
            <button onclick="testFileUpload()">Testar Upload</button>
            <div id="uploadResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>2. Teste de Criação de Pedido</h3>
            <button onclick="testOrderCreation()">Criar Pedido de Teste</button>
            <div id="orderResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>3. Teste de Login Admin</h3>
            <button onclick="testAdminLogin()">Testar Login Admin</button>
            <div id="adminResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>4. Teste de Consulta de Pedidos do Cliente</h3>
            <input type="email" id="customerEmail" placeholder="Email do cliente" value="<EMAIL>">
            <button onclick="testCustomerOrders()">Consultar Pedidos</button>
            <div id="customerResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>5. Teste Completo do Fluxo</h3>
            <button onclick="testCompleteFlow()">Executar Fluxo Completo</button>
            <div id="flowResult" class="result"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000/api';
        let adminToken = null;
        let lastUploadedFileId = null;

        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `result ${type}`;
        }

        async function testFileUpload() {
            const fileInput = document.getElementById('fileInput');
            const file = fileInput.files[0];
            
            if (!file) {
                showResult('uploadResult', 'Por favor, selecione um arquivo primeiro.', 'error');
                return;
            }

            try {
                const formData = new FormData();
                formData.append('file', file);

                const response = await fetch(`${API_BASE}/files/upload`, {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();
                
                if (result.success) {
                    lastUploadedFileId = result.data.file.id;
                    showResult('uploadResult', `✅ Upload realizado com sucesso!\nID do arquivo: ${result.data.file.id}\nNome: ${result.data.file.originalName}\nTamanho: ${result.data.file.formattedSize}`, 'success');
                } else {
                    showResult('uploadResult', `❌ Erro no upload: ${result.error}`, 'error');
                }
            } catch (error) {
                showResult('uploadResult', `❌ Erro de conexão: ${error.message}`, 'error');
            }
        }

        async function testOrderCreation() {
            if (!lastUploadedFileId) {
                showResult('orderResult', '❌ Faça upload de um arquivo primeiro.', 'error');
                return;
            }

            try {
                const orderData = {
                    fileId: lastUploadedFileId,
                    customerName: 'Teste Frontend',
                    customerEmail: '<EMAIL>',
                    customerPhone: '+244900000000',
                    format: 'A4',
                    paperType: 'standard',
                    finish: 'none',
                    copies: 1,
                    notes: 'Pedido criado via teste de integração'
                };

                const response = await fetch(`${API_BASE}/orders`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(orderData)
                });

                const result = await response.json();
                
                if (result.success) {
                    showResult('orderResult', `✅ Pedido criado com sucesso!\nNúmero: ${result.data.order.orderNumber}\nID: ${result.data.order.id}\nStatus: ${result.data.order.status}`, 'success');
                } else {
                    showResult('orderResult', `❌ Erro ao criar pedido: ${result.error}`, 'error');
                }
            } catch (error) {
                showResult('orderResult', `❌ Erro de conexão: ${error.message}`, 'error');
            }
        }

        async function testAdminLogin() {
            try {
                const loginData = {
                    email: '<EMAIL>',
                    password: 'admin123'
                };

                const response = await fetch(`${API_BASE}/admin/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(loginData)
                });

                const result = await response.json();
                
                if (result.success) {
                    adminToken = result.data.token;
                    showResult('adminResult', `✅ Login admin realizado com sucesso!\nNome: ${result.data.admin.name}\nRole: ${result.data.admin.role}\nToken obtido: ${adminToken.substring(0, 50)}...`, 'success');
                } else {
                    showResult('adminResult', `❌ Erro no login admin: ${result.error}`, 'error');
                }
            } catch (error) {
                showResult('adminResult', `❌ Erro de conexão: ${error.message}`, 'error');
            }
        }

        async function testCustomerOrders() {
            const email = document.getElementById('customerEmail').value;
            
            if (!email) {
                showResult('customerResult', '❌ Digite um email de cliente.', 'error');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/orders/customer/${encodeURIComponent(email)}`);
                const result = await response.json();
                
                if (result.success) {
                    const orders = result.data.orders;
                    if (orders.length > 0) {
                        const ordersList = orders.map(order => 
                            `- ${order.orderNumber} (${order.status}) - ${order.customerName}`
                        ).join('\n');
                        showResult('customerResult', `✅ ${orders.length} pedido(s) encontrado(s):\n${ordersList}`, 'success');
                    } else {
                        showResult('customerResult', '✅ Nenhum pedido encontrado para este cliente.', 'info');
                    }
                } else {
                    showResult('customerResult', `❌ Erro ao consultar pedidos: ${result.error}`, 'error');
                }
            } catch (error) {
                showResult('customerResult', `❌ Erro de conexão: ${error.message}`, 'error');
            }
        }

        async function testCompleteFlow() {
            showResult('flowResult', '🔄 Executando fluxo completo...', 'info');
            
            try {
                // 1. Criar arquivo de teste
                const testFile = new Blob(['Teste de integração WePrint AI'], { type: 'text/plain' });
                const formData = new FormData();
                formData.append('file', testFile, 'teste-integracao.txt');

                // 2. Upload
                const uploadResponse = await fetch(`${API_BASE}/files/upload`, {
                    method: 'POST',
                    body: formData
                });
                const uploadResult = await uploadResponse.json();
                
                if (!uploadResult.success) {
                    throw new Error(`Upload falhou: ${uploadResult.error}`);
                }

                // 3. Criar pedido
                const orderData = {
                    fileId: uploadResult.data.file.id,
                    customerName: 'Teste Fluxo Completo',
                    customerEmail: '<EMAIL>',
                    customerPhone: '+244900000000',
                    format: 'A4',
                    paperType: 'standard',
                    finish: 'none',
                    copies: 1,
                    notes: 'Teste do fluxo completo de integração'
                };

                const orderResponse = await fetch(`${API_BASE}/orders`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(orderData)
                });
                const orderResult = await orderResponse.json();
                
                if (!orderResult.success) {
                    throw new Error(`Criação de pedido falhou: ${orderResult.error}`);
                }

                // 4. Verificar pedido criado
                const customerResponse = await fetch(`${API_BASE}/orders/customer/<EMAIL>`);
                const customerResult = await customerResponse.json();
                
                if (!customerResult.success) {
                    throw new Error(`Consulta de pedidos falhou: ${customerResult.error}`);
                }

                showResult('flowResult', `✅ Fluxo completo executado com sucesso!
                
1. ✅ Arquivo enviado: ${uploadResult.data.file.originalName}
2. ✅ Pedido criado: ${orderResult.data.order.orderNumber}
3. ✅ Consulta realizada: ${customerResult.data.orders.length} pedido(s) encontrado(s)

🎉 Integração frontend-backend funcionando perfeitamente!`, 'success');

            } catch (error) {
                showResult('flowResult', `❌ Erro no fluxo completo: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>
