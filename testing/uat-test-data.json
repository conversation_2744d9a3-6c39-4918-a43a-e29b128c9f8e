{"testUsers": {"customers": [{"name": "<PERSON>", "email": "<EMAIL>", "phone": "+244 900 123 456", "address": "<PERSON><PERSON> Independência, 123, <PERSON><PERSON>", "scenario": "Basic customer - first time user"}, {"name": "<PERSON>", "email": "<EMAIL>", "phone": "+244 912 345 678", "address": "Avenida 4 <PERSON>, 456, <PERSON><PERSON>", "scenario": "Business customer - bulk orders"}, {"name": "<PERSON>", "email": "<EMAIL>", "phone": "+244 923 456 789", "address": "Bairro Maianga, Rua A, 789, Luanda", "scenario": "Repeat customer - mobile user"}, {"name": "<PERSON>", "email": "<EMAIL>", "phone": "+244 934 567 890", "address": "Zona Industrial, Lote 12, Viana", "scenario": "Customer with special requirements"}], "admins": [{"email": "<EMAIL>", "password": "Admin123!@#", "role": "super_admin", "name": "Sistema Administrador"}, {"email": "<EMAIL>", "password": "Operador123!", "role": "admin", "name": "Operador Principal"}]}, "testDocuments": [{"name": "documento-teste-1.pdf", "size": "1.2MB", "pages": 5, "type": "PDF", "description": "Documento simples para teste básico"}, {"name": "apresentacao-empresa.pdf", "size": "8.5MB", "pages": 25, "type": "PDF", "description": "Apresentação com imagens para teste de qualidade"}, {"name": "relatorio-anual.docx", "size": "3.2MB", "pages": 15, "type": "DOCX", "description": "Documento Word para teste de conversão"}, {"name": "catalogo-produtos.pdf", "size": "12.8MB", "pages": 50, "type": "PDF", "description": "Documento grande para teste de performance"}, {"name": "imagem-teste.jpg", "size": "2.1MB", "pages": 1, "type": "JPG", "description": "Imagem para teste de impressão fotográfica"}], "testOrders": [{"scenario": "Ordem básica - P&B", "customer": "<PERSON>", "document": "documento-teste-1.pdf", "options": {"format": "A4", "paperType": "standard", "color": "bw", "finish": "none", "copies": 1, "pages": "all"}, "expectedPrice": 250, "paymentMethod": "cash_on_delivery"}, {"scenario": "Ordem colorida - Premium", "customer": "<PERSON>", "document": "apresentacao-empresa.pdf", "options": {"format": "A4", "paperType": "premium", "color": "color", "finish": "glossy", "copies": 3, "pages": "all"}, "expectedPrice": 3750, "paymentMethod": "multicaixa"}, {"scenario": "Ordem múl<PERSON> c<PERSON>", "customer": "<PERSON>", "document": "relatorio-anual.docx", "options": {"format": "A4", "paperType": "standard", "color": "bw", "finish": "none", "copies": 10, "pages": "1-10"}, "expectedPrice": 500, "paymentMethod": "cash_on_delivery"}, {"scenario": "Ordem fotográfica", "customer": "<PERSON>", "document": "imagem-teste.jpg", "options": {"format": "A4", "paperType": "photo", "color": "color", "finish": "glossy", "copies": 5, "pages": "all"}, "expectedPrice": 1250, "paymentMethod": "multicaixa"}], "testScenarios": {"performance": [{"name": "Upload de arquivo grande", "description": "Testar upload de arquivo de 10MB+", "file": "catalogo-produtos.pdf", "expectedTime": "< 30 segundos", "criteria": "Upload completa sem erros"}, {"name": "<PERSON><PERSON><PERSON><PERSON> uploads simultâneos", "description": "5 usuários fazendo upload ao mesmo tempo", "concurrentUsers": 5, "expectedTime": "< 45 segundos cada", "criteria": "Todos os uploads completam com sucesso"}, {"name": "Navegação rápida", "description": "Tempo de carregamento das páginas", "pages": ["home", "upload", "options", "checkout", "admin"], "expectedTime": "< 3 segundos", "criteria": "<PERSON><PERSON> as páginas carregam rapidamente"}], "security": [{"name": "Tentativas de login inválidas", "description": "Testar bloqueio após tentativas falhadas", "attempts": 5, "expectedResult": "Conta bloqueada temporariamente"}, {"name": "Acesso a arquivos não autorizados", "description": "Tentar acessar arquivos de outros usuários", "method": "URL manipulation", "expectedResult": "<PERSON><PERSON>"}, {"name": "Injeção SQL", "description": "Testar campos de entrada contra SQL injection", "inputs": ["'; DROP TABLE orders; --", "1' OR '1'='1"], "expectedResult": "Entrada sanitizada, sem efeito"}], "mobile": [{"name": "Upload mobile", "description": "Upload de arquivo via dispositivo móvel", "devices": ["iPhone", "Android"], "expectedResult": "Upload funciona normalmente"}, {"name": "Navegação touch", "description": "Navegação usando gestos touch", "actions": ["scroll", "tap", "pinch", "swipe"], "expectedResult": "Interface responde corretamente"}, {"name": "Formulários mobile", "description": "Preenchimento de formulários em mobile", "forms": ["customer_info", "print_options"], "expectedResult": "Formulários são fáceis de preencher"}]}, "expectedResults": {"functionalTests": {"passRate": ">95%", "criticalIssues": 0, "highIssues": "<3", "mediumIssues": "<10"}, "performanceTests": {"pageLoadTime": "<3s", "uploadTime": "<30s for 10MB", "apiResponseTime": "<1s", "concurrentUsers": "50 without degradation"}, "securityTests": {"vulnerabilities": 0, "dataLeaks": 0, "unauthorizedAccess": 0, "injectionAttacks": "All blocked"}, "usabilityTests": {"taskCompletionRate": ">90%", "userSatisfaction": ">4/5", "errorRate": "<5%", "supportTickets": "<2% of orders"}}, "testEnvironment": {"urls": {"frontend": "https://staging.weprint.ai", "admin": "https://staging.weprint.ai/admin", "api": "https://api-staging.weprint.ai"}, "credentials": {"adminUser": "<EMAIL>", "adminPassword": "TestAdmin123!", "testDatabase": "weprint_ai_staging"}, "browsers": ["Chrome (latest)", "Firefox (latest)", "Safari (latest)", "Edge (latest)", "Mobile Chrome", "Mobile Safari"], "devices": ["Desktop (1920x1080)", "Lapt<PERSON> (1366x768)", "Tablet (768x1024)", "Mobile (375x667)", "Mobile Large (414x896)"]}}