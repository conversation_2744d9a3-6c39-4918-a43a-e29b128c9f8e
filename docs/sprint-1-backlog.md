# Sprint 1 — Backlog Técnico (MVP WePrint AI)

## Objetivo
Permitir que o utilizador faça upload de ficheiro, visualize preview, escolha formato e submeta encomenda. Admin pode ver e atualizar status das encomendas.

---

## Tarefas

### 1. Setup do Projeto
- Inicializar repositório (Git)
- Estrutura de pastas (frontend, backend, docs)
- Configuração inicial do React (frontend)
- Configuração inicial do backend (Node.js ou FastAPI)
- Setup do banco de dados (PostgreSQL)
- Docker básico para frontend e backend

### 2. Frontend
- Landing page com CTA
- Tela de upload de ficheiro
- Preview do ficheiro (imagem/PDF)
- Formulário de escolha de formato e opções
- Tela de resumo e submissão

### 3. Backend
- Endpoint para upload de ficheiros
- Endpoint para criar nova encomenda
- Estrutura inicial do banco de dados (encomendas, ficheiros, usuários)

### 4. Painel Admin Simples
- Listagem de encomendas
- Atualização manual de status

### 5. Infraestrutura
- Docker Compose para orquestração local
- Deploy local/teste

---

## Critérios de Aceitação
- Upload e preview de ficheiros funcionando
- Submissão de encomenda com opções básicas
- Admin pode ver e atualizar status das encomendas
