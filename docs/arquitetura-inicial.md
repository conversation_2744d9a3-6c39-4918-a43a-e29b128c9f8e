# Arquitetura Inicial — MVP WePrint AI

## 1. <PERSON><PERSON><PERSON>
O MVP será composto por três camadas principais:
- Frontend (React.js + Tailwind CSS)
- Backend (Node.js/Express ou FastAPI)
- <PERSON><PERSON> (PostgreSQL)

Docker será utilizado para orquestração local e facilitar o deploy.

---

## 2. Estrutura de Pastas Sugerida

```
WePrint-AI/
├── frontend/           # React.js app
├── backend/            # API Node.js ou FastAPI
├── docs/               # Documentação
├── db/                 # Scripts de banco de dados/migrations
├── docker-compose.yml  # Orquestração
└── README.md
```

---

## 3. Componentes Principais

### Frontend
- LandingPage
- UploadPage
- PreviewPage
- OptionsForm (formato, papel, acabamento)
- OrderSummary
- CheckoutPage
- ClientPanel
- AdminPanel

### Backend
- Auth (opcional no MVP)
- FileUploadController
- OrderController
- AdminController
- StatusController

### Banco de Dados
- users (opcional no MVP)
- orders
- files
- status

---

## 4. Fluxo de Dados (Simplificado)
1. Usuário faz upload → arquivo salvo (backend/files)
2. Backend gera preview e retorna para frontend
3. Usuário escolhe opções e submete pedido
4. Pedido salvo no banco de dados
5. Admin acessa painel, visualiza e atualiza status
6. Cliente acompanha status pelo painel

---

## 5. Próximos Passos Recomendados
1. Criar estrutura de pastas e arquivos iniciais
2. Definir modelos de dados (orders, files, status)
3. Implementar upload e preview básico
4. Implementar criação e listagem de pedidos
5. Implementar painel admin simples
6. Testar fluxo ponta-a-ponta localmente

---

## 6. Observações
- Começar com autenticação opcional, focando no fluxo de encomenda.
- Utilizar bibliotecas/componentes prontos para upload e preview.
- Manter código e documentação organizados desde o início.
