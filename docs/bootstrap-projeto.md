# Bootstrap do Projeto — WePrint AI

Este documento detalha os comandos e arquivos iniciais para criar a estrutura do MVP, incluindo instruções para inicialização do frontend (React + Tailwind), backend (Node.js/Express) e banco de dados (PostgreSQL via Docker).

---

## 1. Pré-requisitos
- Node.js >= 18
- <PERSON><PERSON> e Docker Compose
- Yarn ou npm

---

## 2. Inicialização do Frontend

### 2.1. Criar app React
```bash
cd frontend
npx create-react-app . --template cra-template-pwa-typescript
```

### 2.2. Instalar Tailwind CSS
```bash
yarn add -D tailwindcss postcss autoprefixer
npx tailwindcss init -p
```

### 2.3. Configurar Tailwind
- Edite `tailwind.config.js` e `src/index.css` conforme a documentação oficial.

### 2.4. Estrutura inicial de páginas/componentes
- `src/pages/LandingPage.tsx`
- `src/pages/UploadPage.tsx`
- `src/pages/PreviewPage.tsx`
- `src/pages/OptionsForm.tsx`
- `src/pages/OrderSummary.tsx`
- `src/pages/CheckoutPage.tsx`
- `src/pages/ClientPanel.tsx`
- `src/pages/AdminPanel.tsx`

---

## 3. Inicialização do Backend

### 3.1. Criar projeto Node.js/Express
```bash
cd backend
npm init -y
npm install express multer pg cors dotenv
npm install --save-dev typescript ts-node @types/express @types/node @types/cors @types/multer
npx tsc --init
```

### 3.2. Estrutura inicial de arquivos
- `src/index.ts` (entrypoint)
- `src/controllers/FileUploadController.ts`
- `src/controllers/OrderController.ts`
- `src/controllers/AdminController.ts`
- `src/controllers/StatusController.ts`
- `src/db.ts` (conexão PostgreSQL)
- `.env` (variáveis de ambiente)

---

## 4. Banco de Dados
- O banco será inicializado automaticamente pelo Docker Compose usando o script `db/init.sql`.
- Para acessar o banco localmente:
```bash
docker exec -it <nome_do_container_db> psql -U postgres -d weprint
```

---

## 5. Orquestração com Docker Compose
- Para subir todos os serviços:
```bash
docker-compose up --build
```
- O frontend estará disponível em `http://localhost:3000`.
- O backend estará disponível em `http://localhost:8000`.
- O banco de dados estará disponível em `localhost:5432`.

---

## 6. Observações
- Documente cada componente/página com comentários claros.
- Utilize README.md em cada subdiretório para instruções específicas.
- Mantenha o código limpo e modular.
- Atualize este documento conforme o projeto evoluir.
