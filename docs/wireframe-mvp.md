# Wireframe MVP — <PERSON><PERSON><PERSON>t AI

## 1. Landing Page

```
+------------------------------------------------------+
| LOGO                A gráfica que vai até ti         |
+------------------------------------------------------+
|                                                      |
|        [Começar Encomenda]                           |
|        [Login/Cadastro]                              |
|                                                      |
+------------------------------------------------------+
```

### Fluxo do Usuário
1. Acessa a landing page.
2. Pode clicar em "Começar Encomenda" (fluxo principal) ou "Login/Cadastro" (opcional).

---

## 2. Fluxo de Encomenda

```
+-----------------------------+
| [Upload de ficheiro]        |
| [<PERSON><PERSON><PERSON> via IA]              |
+-----------------------------+
         |
         v
+-----------------------------+
| Preview do ficheiro/imagem  |
+-----------------------------+
         |
         v
+-----------------------------+
| Escolha de formato          |
| Opções de papel/acabamento  |
+-----------------------------+
         |
         v
+-----------------------------+
| Resumo do pedido            |
| (mockup, specs, preço)      |
+-----------------------------+
         |
         v
+-----------------------------+
| Checkout                    |
| (dados de entrega, pagamento)|
+-----------------------------+
```

### Detalhamento do Fluxo
1. **Upload de ficheiro**: Aceita PDF, PNG, JPG, DOCX. Validação e preview instantâneo.
2. **Criar via IA**: Usuário insere prompt, recebe imagem gerada (futuro).
3. **Preview**: Mockup realista do ficheiro para validação visual.
4. **Escolha de formato**: Seleção de tamanho, papel, acabamento via dropdowns.
5. **Resumo**: Exibe mockup, specs, preço estimado.
6. **Checkout**: Formulário de entrega, escolha de pagamento, confirmação.

---

## 3. Painel do Cliente

```
+------------------------------------------------------+
| Painel do Cliente                                    |
+------------------------------------------------------+
| Histórico de encomendas                              |
| Estado da impressão/entrega                          |
| [Repetir pedido]                                     |
+------------------------------------------------------+
```

### Funcionalidades
- Visualizar todas as encomendas anteriores
- Ver status atual (em produção, enviado, entregue)
- Repetir pedido com 1 clique

---

## 4. Admin Básico

```
+------------------------------------------------------+
| Painel Admin                                         |
+------------------------------------------------------+
| Listagem de encomendas                               |
| Atualizar status (em produção, enviado, entregue)     |
+------------------------------------------------------+
```

### Funcionalidades
- Visualizar todas as encomendas
- Atualizar status manualmente

---

## 5. Fluxo Principal (MVP)

1. Usuário acessa landing page
2. Clica em "Começar Encomenda"
3. Faz upload de ficheiro OU cria via IA (futuro)
4. Visualiza preview
5. Escolhe formato e opções
6. Vê resumo e submete encomenda
7. Admin visualiza e atualiza status
8. Usuário pode consultar painel para ver status

---

## 6. Wireframes Visuais (ASCII)

### Landing Page
```
+------------------------------------------------------+
| LOGO                A gráfica que vai até ti         |
+------------------------------------------------------+
|                                                      |
|        [Começar Encomenda]                           |
|        [Login/Cadastro]                              |
|                                                      |
+------------------------------------------------------+
```

### Encomenda - Upload/Criar
```
+-----------------------------+
| [Upload de ficheiro]        |
| [Criar via IA]              |
+-----------------------------+
         |
         v
+-----------------------------+
| Preview do ficheiro/imagem  |
+-----------------------------+
         |
         v
+-----------------------------+
| Escolha de formato          |
| Opções de papel/acabamento  |
+-----------------------------+
         |
         v
+-----------------------------+
| Resumo do pedido            |
| (mockup, specs, preço)      |
+-----------------------------+
         |
         v
+-----------------------------+
| Checkout                    |
| (dados de entrega, pagamento)|
+-----------------------------+
```

### Painel do Cliente
```
+------------------------------------------------------+
| Painel do Cliente                                    |
+------------------------------------------------------+
| Histórico de encomendas                              |
| Estado da impressão/entrega                          |
| [Repetir pedido]                                     |
+------------------------------------------------------+
```

### Admin
```
+------------------------------------------------------+
| Painel Admin                                         |
+------------------------------------------------------+
| Listagem de encomendas                               |
| Atualizar status (em produção, enviado, entregue)     |
+------------------------------------------------------+
```

---

## 7. Observações para MVP
- O fluxo "Criar via IA" pode ser placeholder no MVP, com foco inicial no upload.
- O preview deve ser responsivo e permitir zoom.
- O painel do cliente e admin podem ser simples listas/tabelas.

---

## 8. Possíveis Extensões Futuras
- Integração com IA para geração de imagens
- Pagamento online integrado
- Notificações automáticas de status
- Roteamento para múltiplos centros de produção
