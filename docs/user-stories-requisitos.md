# User Stories e Requisitos Funcionais — WePrint AI MVP

## 1. Landing Page

### User Stories
- <PERSON> visitante, quero ver uma landing page clara para entender o serviço e iniciar uma encomenda.
- Como visitante, quero poder acessar o login/cadastro facilmente.

### Requisitos Funcionais
- Exibir logo, slogan e botões de ação.
- Botão "Começar Encomenda" leva ao fluxo de encomenda.
- Botão "Login/Cadastro" leva ao formulário de autenticação.

---

## 2. Fluxo de Encomenda

### User Stories
- Como usuário, quero fazer upload de ficheiro para impressão.
- Como usuário, quero visualizar um preview do ficheiro antes de avançar.
- Como usuário, quero escolher formato, papel e acabamento.
- Como usuário, quero ver um resumo do pedido antes de finalizar.
- Como usuário, quero submeter a encomenda e receber confirmação.

### Requisitos Funcionais
- Upload de ficheiros (PDF, PNG, JPG, DOCX).
- Preview instantâneo do ficheiro.
- Seleção de formato, papel e acabamento.
- Exibição de mockup, specs e preço estimado.
- Formulário de entrega e pagamento.
- Confirmação de submissão.

---

## 3. Painel do Cliente

### User Stories
- Como cliente, quero ver o histórico das minhas encomendas.
- Como cliente, quero acompanhar o status da impressão/entrega.
- Como cliente, quero repetir um pedido facilmente.

### Requisitos Funcionais
- Listagem de encomendas do usuário.
- Exibição de status (em produção, enviado, entregue).
- Botão para repetir pedido.

---

## 4. Admin Básico

### User Stories
- Como operador/admin, quero ver todas as encomendas recebidas.
- Como operador/admin, quero atualizar o status das encomendas.

### Requisitos Funcionais
- Listagem de todas as encomendas.
- Atualização manual de status.

---

## 5. Regras Gerais do MVP
- Foco inicial no upload de ficheiros (IA como placeholder).
- Preview responsivo e com zoom.
- Listagens simples para cliente e admin.

---

## 6. Critérios de Aceitação Gerais
- Todos os fluxos descritos devem ser navegáveis.
- Upload, preview, escolha de opções e submissão devem funcionar sem erros.
- Admin deve conseguir atualizar status e cliente visualizar.
