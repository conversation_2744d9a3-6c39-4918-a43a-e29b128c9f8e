# WePrint AI - Production Deployment Guide

## 🚀 Production Deployment Overview

This guide covers the complete production deployment process for WePrint AI, including infrastructure setup, security configuration, monitoring, and maintenance procedures.

## 📋 Prerequisites

### System Requirements
- **Server**: Ubuntu 20.04+ or CentOS 8+ (minimum 4GB RAM, 2 CPU cores, 50GB storage)
- **Docker**: Version 20.10+
- **Docker Compose**: Version 2.0+
- **Domain**: Registered domain name (e.g., weprint.ai)
- **SSL Certificate**: Valid SSL certificate from a trusted CA

### Required Accounts
- **Multicaixa Express**: Payment processing account
- **Email Service**: SMTP credentials (Gmail, SendGrid, etc.)
- **Domain Registrar**: Access to DNS management

## 🔧 Pre-Deployment Setup

### 1. Server Preparation
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Install additional tools
sudo apt install -y curl wget git nginx certbot python3-certbot-nginx
```

### 2. Clone Repository
```bash
git clone https://github.com/your-org/weprint-ai.git
cd weprint-ai
```

### 3. Environment Configuration
```bash
# Copy and configure production environment
cp .env.production.example .env.production

# Edit with your production values
nano .env.production
```

**Required Environment Variables:**
```env
# Database
DB_PASSWORD=your_secure_database_password
JWT_SECRET=your_super_secure_jwt_secret_minimum_32_chars

# Payment
MULTICAIXA_API_KEY=your_multicaixa_api_key
MULTICAIXA_ENTITY_ID=your_multicaixa_entity_id

# Email
EMAIL_HOST=smtp.gmail.com
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password

# Security
REDIS_PASSWORD=your_redis_password
SESSION_SECRET=your_session_secret
```

## 🔐 SSL Certificate Setup

### Option 1: Let's Encrypt (Recommended)
```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Obtain SSL certificate
sudo certbot --nginx -d weprint.ai -d www.weprint.ai -d api.weprint.ai

# Auto-renewal setup
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### Option 2: Custom SSL Certificate
```bash
# Create SSL directory
mkdir -p ssl

# Copy your certificates
cp your-certificate.crt ssl/weprint.ai.crt
cp your-private-key.key ssl/weprint.ai.key

# Set proper permissions
chmod 600 ssl/weprint.ai.key
chmod 644 ssl/weprint.ai.crt
```

## 🚀 Deployment Process

### 1. Run Production Deployment Script
```bash
# Make script executable
chmod +x scripts/deploy-production.sh

# Run deployment
./scripts/deploy-production.sh
```

### 2. Manual Deployment (Alternative)
```bash
# Build and start services
docker-compose -f docker-compose.prod.yml down
docker-compose -f docker-compose.prod.yml build --no-cache
docker-compose -f docker-compose.prod.yml up -d

# Check service health
docker-compose -f docker-compose.prod.yml ps
curl -f http://localhost:8000/health
```

## 🌐 DNS Configuration

Configure your DNS records to point to your server:

```
A     weprint.ai          -> YOUR_SERVER_IP
A     www.weprint.ai      -> YOUR_SERVER_IP
A     api.weprint.ai      -> YOUR_SERVER_IP
CNAME admin.weprint.ai    -> weprint.ai
```

## 📊 Monitoring Setup

### 1. Access Monitoring Dashboard
- **Prometheus**: http://your-server:9090
- **Application Logs**: `docker-compose -f docker-compose.prod.yml logs -f`

### 2. Configure Alerts
Edit `monitoring/alert_rules.yml` to customize alerting thresholds.

### 3. Health Checks
```bash
# Backend health
curl https://api.weprint.ai/health

# Frontend health
curl https://weprint.ai/health

# Database health
docker exec weprint-ai-db-prod pg_isready -U postgres
```

## 🔒 Security Checklist

- [ ] SSL certificates installed and configured
- [ ] Strong passwords for all services
- [ ] Firewall configured (ports 80, 443, 22 only)
- [ ] Regular security updates scheduled
- [ ] Database access restricted to application only
- [ ] API rate limiting enabled
- [ ] CORS properly configured
- [ ] Security headers implemented

## 🔄 Backup Strategy

### 1. Automated Backups
```bash
# Database backup
docker exec weprint-ai-db-prod pg_dump -U postgres weprint_ai_prod > backup-$(date +%Y%m%d).sql

# File uploads backup
docker run --rm -v weprint-ai_uploads_prod:/data -v $(pwd)/backups:/backup alpine tar czf /backup/uploads-$(date +%Y%m%d).tar.gz -C /data .
```

### 2. Backup Schedule
Set up automated backups using cron:
```bash
# Daily database backup at 2 AM
0 2 * * * /path/to/backup-script.sh

# Weekly full backup
0 3 * * 0 /path/to/full-backup-script.sh
```

## 🔧 Maintenance

### 1. Regular Updates
```bash
# Pull latest code
git pull origin main

# Rebuild and restart services
docker-compose -f docker-compose.prod.yml build --no-cache
docker-compose -f docker-compose.prod.yml up -d
```

### 2. Log Management
```bash
# View logs
docker-compose -f docker-compose.prod.yml logs -f [service_name]

# Clean old logs
docker system prune -f
```

### 3. Performance Monitoring
- Monitor CPU, memory, and disk usage
- Check response times and error rates
- Review database performance
- Monitor payment processing success rates

## 🆘 Troubleshooting

### Common Issues

**Service Won't Start:**
```bash
# Check logs
docker-compose -f docker-compose.prod.yml logs [service_name]

# Check system resources
docker system df
free -h
df -h
```

**Database Connection Issues:**
```bash
# Check database status
docker exec weprint-ai-db-prod pg_isready -U postgres

# Reset database connection
docker-compose -f docker-compose.prod.yml restart backend
```

**SSL Certificate Issues:**
```bash
# Check certificate validity
openssl x509 -in ssl/weprint.ai.crt -text -noout

# Renew Let's Encrypt certificate
sudo certbot renew
```

## 📞 Support

For production support:
- **Email**: <EMAIL>
- **Documentation**: https://docs.weprint.ai
- **Monitoring**: https://status.weprint.ai

## 🎯 Post-Deployment Checklist

- [ ] All services running and healthy
- [ ] SSL certificates valid and auto-renewing
- [ ] DNS records properly configured
- [ ] Monitoring and alerting active
- [ ] Backup procedures tested
- [ ] Performance baseline established
- [ ] Security scan completed
- [ ] User acceptance testing passed
- [ ] Documentation updated
- [ ] Team trained on production procedures
