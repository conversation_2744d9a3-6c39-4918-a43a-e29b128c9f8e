# README — Documentação do Projeto WePrint AI

Este diretório contém toda a documentação do projeto, incluindo:
- Wireframes e fluxos do MVP
- Backlog de sprints
- User stories e requisitos funcionais
- Arquitetura inicial
- Bootstrap e instruções de setup

## Estrutura Recomendada

- `wireframe-mvp.md`: Wireframes e fluxos visuais
- `sprint-1-backlog.md`: Backlog técnico da primeira sprint
- `user-stories-requisitos.md`: User stories e requisitos funcionais
- `arquitetura-inicial.md`: Arquitetura técnica do MVP
- `bootstrap-projeto.md`: Passos para inicialização do projeto

## Boas Práticas
- Atualize a documentação a cada mudança relevante no projeto.
- Use exemplos e diagramas sempre que possível.
- Mantenha o histórico de decisões técnicas.
